const path = require('path');

module.exports = ({ onGetConfig }, options) => {
  onGetConfig('csr', (config) => {
    config.merge({
      resolve: {
        fallback: {
          path: false,
          url: false,
        },
      },
      devServer: {
        client: {
          overlay: {
            errors: true,
            warnings: false,
            runtimeErrors: true,
          }
        }
      }
    });
  });
};
