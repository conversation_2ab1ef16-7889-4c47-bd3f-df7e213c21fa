
import { IPlugin, IPluginAPI } from '@ali/compass-app';
import webpackBundleAnalyzer from 'webpack-bundle-analyzer';
import duplicatePackageCheckerWebpackPlugin from 'duplicate-package-checker-webpack-plugin';

const BundleAnalyzerPlugin = webpackBundleAnalyzer.BundleAnalyzerPlugin; 

const plugin: IPlugin = () => ({
  name: 'webpack-bundle-analyzer-test',
  setup(api: IPluginAPI) {
    const { onGetConfig } = api;
    onGetConfig((config) => {
      config.plugin('webpack-bundle-analyzer')
        .use(BundleAnalyzerPlugin, [{
          analyzerMode: 'server',
          analyzerHost: '127.0.0.1',
          analyzerPort: 8888,
          reportFilename: 'bundle-report.html',
          defaultSizes: 'gzip',
          openAnalyzer: true,
          generateStatsFile: false,
          statsFilename: 'stats.json',
          statsOptions: null,
          excludeAssets: null,
        }]);
      config.plugin('duplicate-package-checker-webpack-plugin').use(duplicatePackageCheckerWebpackPlugin);
    });
  },
});

export default plugin;
