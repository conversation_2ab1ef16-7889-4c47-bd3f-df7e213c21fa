{
  "compileOnSave": false,
  "buildOnSave": false,
  "compilerOptions": {
    "baseUrl": ".",
    "outDir": "build",
    "module": "esnext",
    "target": "es6",
    "jsx": "react",
    "jsxFactory": "React.createElement",
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "lib": ["es6", "dom"],
    "sourceMap": true,
    "allowJs": true,
    "rootDir": "./",
    "forceConsistentCasingInFileNames": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitAny": false,
    "importHelpers": true,
    "strictNullChecks": true,
    "noUnusedLocals": true,
    "skipLibCheck": true,
    "types": ["node"],
    "paths": {
      "@/*": ["./src/*"],
    }
  },
  "files": ["src/logic/type/eva.d.ts"],
  "include": ["src", ".temp"],
  "exclude": ["node_modules", "build"]
}
