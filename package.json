{"name": "@ali/uc-pegasus-source-project-ucquark-farm", "version": "1.0.0", "description": "uc-pegasus-source-project-ucquark-farm", "appType": "UC", "scripts": {"clean": "echo \"clean nothing\"", "test": "echo \"Error: no test specified\" && exit 1", "compile:env": "rm -rf lib/env && tsc --project tsconfig.env.json", "publish:daily": "def p -d", "start": "compass-app start", "build": "compass-app build", "start:inspect": "node --inspect-brk ../../node_modules/.bin/compass-app start", "build:inspect": "node --inspect-brk ../../node_modules/.bin/compass-app build", "compressFont": "tsx ./src/components/font-compress/script.ts", "previewFont": "tsx ./src/components/font-compress/preview.ts"}, "repository": {"type": "git", "url": "**************************:uc-pegasus-source-project/ucquark-farm.git"}, "publishConfig": {"registry": "https://registry.antfin-inc.com"}, "author": "", "license": "ISC", "devDependencies": {"@ali/compass-plugin-broccoli": "0.2.4-alpha.0", "@ali/compass-plugin-def": "0.2.4-alpha.0", "@commitlint/cli": "^17.0.3", "@iceworks/spec": "^1.6.0", "@types/fontmin": "^0.9.4", "@types/node": "^18.11.9", "@types/react": "^18.0.25", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "fontmin": "^1.1.0", "prettier": "^2.7.1", "stylelint": "^14.10.0", "tsx": "^4.16.2", "typescript": "^4.9.5"}, "dependencies": {"@ali/act-base-share": "^0.1.71-beta.5", "@ali/act-base-toolkit": "^0.1.64", "@ali/broccoli-simulate-browser-env": "^1.0.25", "@ali/cms-res": "^1.4.0", "@ali/compass-app": "0.2.4-alpha.0", "@ali/compass-env": "0.2.4-alpha.0", "@ali/fact-stat": "^4.7.0", "@ali/farm-game-object": "^0.1.57", "@ali/farm-react-component": "^0.1.53", "@ali/farm-utils": "^0.1.53", "@ali/itrace-browser": "^2.3.27", "@ali/itrace-fluency": "^1.0.3", "@ali/itrace-interface": "^2.1.12", "@ali/logonline": "^1.7.6", "@ali/pcom-driver": "^1.1.4", "@ali/pcom-iz-use": "^5.0.16", "@ali/pcom-mx": "^5.2.5", "@ali/uc-toolkit": "^3.33.0", "@ali/wormhole-context": "^1.1.8", "@eva/eva.js": "^1.2.8", "@eva/plugin-a11y": "^1.2.8", "@eva/plugin-renderer": "^1.2.0-alpha.3", "@eva/plugin-renderer-event": "^1.2.0-alpha.3", "@eva/plugin-renderer-graphics": "^1.2.0-alpha.3", "@eva/plugin-renderer-img": "^1.2.0-alpha.3", "@eva/plugin-renderer-lottie": "^1.2.8", "@eva/plugin-renderer-mask": "^1.2.0-alpha.3", "@eva/plugin-renderer-nine-patch": "^1.2.0-alpha.3", "@eva/plugin-renderer-render": "^1.2.0-alpha.3", "@eva/plugin-renderer-spine": "^1.2.0-alpha.3", "@eva/plugin-renderer-sprite-animation": "^1.2.0-alpha.3", "@eva/plugin-renderer-text": "^1.2.0-alpha.3", "@eva/plugin-sound": "^1.2.7", "@eva/plugin-transition": "^1.2.0-alpha.3", "@eva/renderer-adapter": "^1.2.0-alpha.3", "@types/react-lazy-load-image-component": "^1.6.4", "ahooks": "^3.8.0", "classnames": "^2.5.1", "eventemitter3": "^5.0.1", "idx": "^2.5.6", "lodash.delay": "^4.1.1", "lodash.throttle": "^4.1.1", "lottie-web": "^5.12.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-lazy-load-image-component": "^1.6.2", "universal-env": "^3.3.3", "universal-transition": "^1.1.1"}, "resolutions": {"@ali/wh-preview": "5.2.14"}, "pegasus": {"project": {"templateEngine": "javascript", "bizGroup": {"vipserver": "http.fc.vipserver", "host": "page.suikong-test"}}}}