# @ali/uc-pegasus-source-project-ucquark-farm

## UC芭芭农场前端项目

* 需求文档 https://aliyuque.antfin.com/buofh2/gku356/ngk37ttmsd27lv0i#L2vJf
* 视觉稿 https://mergo.alibaba-inc.com/#/project/detail?id=795&tab=mark
* 接口文档 https://leaf.uc.cn/projects/yes-farm
* 线上环境链接 https://broccoli.uc.cn/apps/ucfarm/routes/farm?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmtkp&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&webCompassApp=true&$kps_info


## Getting Started

### `npm run start`

Runs the app in development mode.

Open [http://localhost:3333/index](http://localhost:3333/index) to view it in the browser.

The page will reload if you make edits.

### `npm run build`

Builds the app for production to the `build` folder.

### 发布
#### 日常环境
`def p -d`
#### 线上环境
`def p -o`

## 样式适配
- 如果设计稿宽度是750，可直接使用 `rpx` 单位进行适配。
- 如果设计稿宽度不为750，可通过在 `build.json`的`buildConfig.designWidth` 中设置设计稿宽度。支持通过vw($px)的方式自适应，全局已注入vw方法(只支持sass）

## 约定

### 依赖锁定

一般情况下，都建议固定依赖版本（稳定压倒一切），按如下流程执行，想了解细节[详见](https://space.o2.alibaba-inc.com/doc/cege99?version=v3)

- 本地使用npm、yarn 或 tnpm 安装依赖产生相应的lockfile文件，以下命令均可
  - `npm i`
  - `yarn`
  - `tnpm i --by=npm --enable-lockfile`：和npm i基本一样
  - pnpm不建议使用：因为DEF云构建只支持pnpm@5，版本过低
- 去除abc.json中的 `tnpm i` 命令
- DEF项目在线配置链路：设置 -> 发布设置 -> 开启依赖锁定能力

### 首屏数据
1. 在`pages/index/data.ts`文件中`getInitialData`方法中调用接口获取首屏数据。
2. 通过getUserInfo获取用户信息，node端渲染时只有kps（需要客户端的kp公参）。
3. 首屏数据遵循最小够用原则，避免首屏无关的接口调用。

### 异步组件
1. 首屏相关组件 不要使用异步组件。
2. 异步组件可使用`@/components/async`;

附：
[《工程设计文档》](https://yuque.antfin.com/web-compass/yv8box/grie7f)
