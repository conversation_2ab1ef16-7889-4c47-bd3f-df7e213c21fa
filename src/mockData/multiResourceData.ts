export default {
  "uc_baba_farm_bangbangzhong": {
    "taskList": [
      {
        "id": 316004,
        "name": "帮帮种6助力值",
        "desc": "帮帮种6助力值",
        "event": "baba_farm_sub_invite_help",
        "target": 6,
        "progress": 0,
        "taskType": "everyday",
        "sort": 1,
        "url": "",
        "state": 1,
        "beginTime": 1744387200000,
        "completeTime": 0,
        "rewardItems": [
          {
            "id": 716,
            "name": "肥料",
            "mark": "code",
            "amount": 2000,
            "randomAmount": false,
            "userTagList": []
          }
        ],
        "recommend": 1,
        "extra": "{}",
        "token": "",
        "dayTimes": {
          "progress": 0,
          "target": 6
        },
        "ext": {},
        "prizes": [],
        "taskSource": "DEFAULT",
        "taskClassify": "default",
        "groupCode": "default",
        "endTime": 2057846400000,
        "cycleTotalDay": 0,
        "continuousDay": 0,
        "needReceive": false,
        "publishId": 15895,
        "useUtCompleteTask": false,
        "allRewardItems": [],
        "appearTypeStr": "show"
      },
      {
        "id": 316005,
        "name": "帮帮种50助力值",
        "desc": "帮帮种50助力值",
        "event": "baba_farm_sub_invite_help",
        "target": 50,
        "progress": 0,
        "taskType": "everyday",
        "sort": 1,
        "url": "",
        "state": 0,
        "beginTime": 1744387200000,
        "completeTime": 0,
        "rewardItems": [
          {
            "id": 719,
            "name": "肥料",
            "mark": "code",
            "amount": 20000,
            "randomAmount": false,
            "userTagList": []
          }
        ],
        "recommend": 1,
        "extra": "{}",
        "token": "",
        "dayTimes": {
          "progress": 0,
          "target": 50
        },
        "ext": {},
        "prizes": [],
        "taskSource": "DEFAULT",
        "taskClassify": "default",
        "groupCode": "default",
        "endTime": 2057846400000,
        "cycleTotalDay": 0,
        "continuousDay": 0,
        "needReceive": false,
        "publishId": 15895,
        "useUtCompleteTask": false,
        "allRewardItems": [],
        "appearTypeStr": "show"
      },
      {
        "id": 316006,
        "name": "帮帮种100助力值",
        "desc": "帮帮种100助力值",
        "event": "baba_farm_sub_invite_help",
        "target": 100,
        "progress": 0,
        "taskType": "everyday",
        "sort": 1,
        "url": "",
        "state": 0,
        "beginTime": 1744387200000,
        "completeTime": 0,
        "rewardItems": [
          {
            "id": 720,
            "name": "肥料",
            "mark": "code",
            "amount": 20000,
            "randomAmount": false,
            "userTagList": []
          }
        ],
        "recommend": 1,
        "extra": "{}",
        "token": "",
        "dayTimes": {
          "progress": 0,
          "target": 100
        },
        "ext": {},
        "prizes": [],
        "taskSource": "DEFAULT",
        "taskClassify": "default",
        "groupCode": "default",
        "endTime": 2057846400000,
        "cycleTotalDay": 0,
        "continuousDay": 0,
        "needReceive": false,
        "publishId": 15895,
        "useUtCompleteTask": false,
        "allRewardItems": [],
        "appearTypeStr": "show"
      },
      {
        "id": 316003,
        "name": "帮帮种助力值蓄水总任务",
        "desc": "帮帮种助力值蓄水总任务",
        "event": "store",
        "target": 10000,
        "progress": 0,
        "taskType": "everyday",
        "sort": 1,
        "url": "",
        "state": 0,
        "beginTime": 1744387200000,
        "completeTime": 0,
        "rewardItems": [],
        "recommend": 1,
        "extra": "{}",
        "token": "",
        "ext": {},
        "prizes": [],
        "taskSource": "DEFAULT",
        "taskClassify": "default",
        "groupCode": "default",
        "endTime": 2057846400000,
        "cycleTotalDay": 0,
        "continuousDay": 0,
        "needReceive": false,
        "publishId": 15895,
        "useUtCompleteTask": false,
        "allRewardItems": [],
        "subStoreTasks": [
          316004,
          317002,
          317003,
          316005,
          316006
        ],
        "appearTypeStr": "hideByBizServer"
      },
      {
        "id": 317002,
        "name": "帮帮种10助力值",
        "desc": "帮帮种10助力值",
        "event": "baba_farm_sub_invite_help",
        "target": 10,
        "progress": 0,
        "taskType": "everyday",
        "sort": 1,
        "url": "",
        "state": 0,
        "beginTime": 1744387200000,
        "completeTime": 0,
        "rewardItems": [
          {
            "id": 717,
            "name": "肥料",
            "mark": "code",
            "amount": 2000,
            "randomAmount": false,
            "userTagList": []
          }
        ],
        "recommend": 1,
        "extra": "{}",
        "token": "",
        "dayTimes": {
          "progress": 0,
          "target": 10
        },
        "ext": {},
        "prizes": [],
        "taskSource": "DEFAULT",
        "taskClassify": "default",
        "groupCode": "default",
        "endTime": 2057846400000,
        "cycleTotalDay": 0,
        "continuousDay": 0,
        "needReceive": false,
        "publishId": 15895,
        "useUtCompleteTask": false,
        "allRewardItems": [],
        "appearTypeStr": "show"
      },
      {
        "id": 317003,
        "name": "帮帮种30助力值",
        "desc": "帮帮种30助力值",
        "event": "baba_farm_sub_invite_help",
        "target": 30,
        "progress": 0,
        "taskType": "everyday",
        "sort": 1,
        "url": "",
        "state": 0,
        "beginTime": 1744387200000,
        "completeTime": 0,
        "rewardItems": [
          {
            "id": 718,
            "name": "肥料",
            "mark": "code",
            "amount": 2000,
            "randomAmount": false,
            "userTagList": []
          }
        ],
        "recommend": 1,
        "extra": "{}",
        "token": "",
        "dayTimes": {
          "progress": 0,
          "target": 30
        },
        "ext": {},
        "prizes": [],
        "taskSource": "DEFAULT",
        "taskClassify": "default",
        "groupCode": "default",
        "endTime": 2057846400000,
        "cycleTotalDay": 0,
        "continuousDay": 0,
        "needReceive": false,
        "publishId": 15895,
        "useUtCompleteTask": false,
        "allRewardItems": [],
        "appearTypeStr": "show"
      }
    ],
    "attributes": {},
    "hiddenTaskIdList": []
  }
}