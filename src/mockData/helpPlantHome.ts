export default {
  "curTime": **************, // 当前毫秒时间戳
  "accountUnusual": false, // 是否账号异常
  "frontData": {},
  "inviteInfo": {
    "inviteCode": "2323232", // 邀请码
    "defaultScore": 34, // 默认助力值
    "defaultInviterScore": 3434, // 默认分享人助力值
    "assistInfo": [
      {
        "score": 5, // 助力值
        "inviterScore": 10, // 分享人助力值
        "assistUserType": 'BIZ_NEW', // 助力用户类型，ALL_USER 普通用户  BIZ_NEW 业务新用户 ，BIZ_RECALL_USER 业务回流用户
      },
      {
        "score": 3, // 助力值
        "inviterScore": 5, // 分享人助力值
        "assistUserType": 'BIZ_RECALL_USER', // 助力用户类型，ALL_USER 普通用户  BIZ_NEW 业务新用户 ，BIZ_RECALL_USER 业务回流用户
      },
      {
        "score": 1, // 助力值
        "inviterScore": 2, // 分享人助力值
        "assistUserType": 'ALL_USER', // 助力用户类型，ALL_USER 普通用户  BIZ_NEW 业务新用户 ，BIZ_RECALL_USER 业务回流用户
      }
    ]
  },
  "rankRewardList": [
    {
      "tag": "bbz",
      "status": "", // 状态：NONE 未获取
      "type": "RANKING", // 奖励分类', 奖励分类', RANKING 排行榜类
      "typeValue": "1-1", // 奖励分类值，格式：排名最小值-排名最大值，1-3表示第1名到第3名都是这个奖品
      "prizeName": " 奖励名称", // 奖励名称 --前端显示多个奖品名，约定英文逗号分隔
      "prizeMark": "", // 奖励标识
      "prizeIcon": "", // 奖励图标
      "prizeValue": "8800", // 奖励配置值
      "prizeActualValue": "", // 奖励实际值
      "rewardSource": "CURRENCY" // 奖励来源：CURRENCY 资产，AWARD 权益
    },
    {
      "tag": "bbz",
      "status": "", // 状态：NONE 未获取
      "type": "RANKING", // 奖励分类', 奖励分类', RANKING 排行榜类
      "typeValue": "2-2", // 奖励分类值，格式：排名最小值-排名最大值，1-3表示第1名到第3名都是这个奖品
      "prizeName": " 奖励名称", // 奖励名称 --前端显示多个奖品名，约定英文逗号分隔
      "prizeMark": "", // 奖励标识
      "prizeIcon": "", // 奖励图标
      "prizeValue": "5000", // 奖励配置值
      "prizeActualValue": "", // 奖励实际值
      "rewardSource": "CURRENCY" // 奖励来源：CURRENCY 资产，AWARD 权益
    },
    {
      "tag": "bbz",
      "status": "", // 状态：NONE 未获取
      "type": "RANKING", // 奖励分类', 奖励分类', RANKING 排行榜类
      "typeValue": "3-3", // 奖励分类值，格式：排名最小值-排名最大值，1-3表示第1名到第3名都是这个奖品
      "prizeName": " 奖励名称", // 奖励名称 --前端显示多个奖品名，约定英文逗号分隔
      "prizeMark": "", // 奖励标识
      "prizeIcon": "", // 奖励图标
      "prizeValue": "1880", // 奖励配置值
      "prizeActualValue": "", // 奖励实际值
      "rewardSource": "CURRENCY" // 奖励来源：CURRENCY 资产，AWARD 权益
    }
  ]
}