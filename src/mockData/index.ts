import homeData from "@/mockData/home";
import signin from "@/mockData/signin";
import taskList from "@/mockData/taskList";
import resources from "@/mockData/resources";
import queryFlowList from '@/mockData/queryFlowList';
import queryShareInfoList from '@/mockData/queryShareInfoList';
import helpPlantHomeData from "@/mockData/helpPlantHome";
import rankListData from "@/mockData/rankListData";
import multiResourceData from "@/mockData/multiResourceData";
import queryScoreList from "./queryScoreList";
import hcTest from "./hc";
import queryCardInfo from './queryCardInfo';

import { getParam } from "@/lib/qs";
import queryRankHistory from "./queryRankHistory";

const publishEnv: any = PUBLISH_ENV || 'local';

const mockParam = getParam('isMock');

export const mockData = {
  homeData,
  signin,
  taskList,
  resources,
  queryFlowList,
  queryShareInfoList,
  helpPlantHomeData,
  rankListData,
  multiResourceData,
  queryScoreList,
  queryRankHistory,
  hcTest,
  queryCardInfo
}

export const isMock = () => {
  return publishEnv !== 'prod' && mockParam
}
