const taskList = {
  timestamp: 1726588800000,
  values: [
    {
      id: 1781526,
      name: '添加桌面任务',
      desc: '添加桌面任务',
      event: 'baba_farm_desktop',
      target: 1,
      progress: 0,
      sort: 1,
      icon: 'https://yes-file.uc.cn/file/1712454893273_1176380907_5166.png',
      url: '',
      btnName: '',
      state: 0,
      beginTime: 1726574658815,
      completeTime: 0,
      rewardItems: [
        {
          name: '5肥料',
          mark: 'code',
          amount: 5,
          icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
        },
      ],
      recommend: 1,
      extra: '',
      token: '',
      prizes: [],
    },
    {
      beginTime: 1725926400000,
      btnName: "",
      completeTime: 0,
      continuousDay: 1,
      cycleTotalDay: 2,
      desc: "芭芭农场限时天任务",
      endTime: 1726531200000,
      event: "baba_farm_time_limit_day",
      ext: {},
      extra: "",
      groupCode: "",
      icon: "",
      id: 1784141,
      name: "芭芭农场限时天任务",
      needReceive: false,
      prizes: [{
        win: true, // 是否中奖，"win": true, // 是否成功发奖，false表示没发成功、true表示发成功
        errorCode: "", // 错误码，当win=false时
        rewardType: "lottery", // 奖品大类 （lottery=抽奖 currency=货币 award=权益）
        rewardItem: {
          mark: "cash", // 奖品小类  （coin前缀为金币元宝，cash前缀为现金 等）
          name: "现金", // 奖品名称
          amount: 400, // 奖品金额或价值 奖励类型为现金时该金额单位为分
          icon: "https://yes-file.uc.cn/file/1726307058768_4265201838_1851.png" // icon
        }
      }],
      progress: 0,
      preTaskList: [
        {
          id: 1781562,
          name: '搜索浏览任务',
          desc: '搜索浏览任务',
          event: 'baba_farm_search',
          target: 1,
          progress: 0,
          sort: 2,
          icon: 'https://yes-file.uc.cn/file/1712454977958_3281699398_2474.png',
          url: 'ext:open_search:event={event}&mission_id={taskId}',
          btnName: '',
          state: 0,
          beginTime: 1714039065632,
          completeTime: 0,
          dayTimesInterval: 1,
          rewardItems: [
            {
              name: '6肥料',
              mark: 'code',
              amount: 6,
              icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
            },
          ],
          recommend: 1,
          extra: '{"scene":"search","taskTime":20000,"from":"bab_farm_from","entry":"bab_farm_entry"}',
          token: '',
          dayTimes: {
            progress: 0,
            target: 20,
          },
          prizes: [],
        },
        {
          id: 1781525,
          name: '信息流视频任务',
          desc: '信息流视频任务',
          event: 'baba_farm_watch',
          target: 1,
          progress: 0,
          sort: 3,
          icon: 'https://yes-file.uc.cn/file/1712455048570_4160564911_8288.png',
          url: 'ext:info_flow_open_channel:scene=video_page_merge&event={event}&mission_id={taskId}',
          btnName: '',
          state: 0,
          beginTime: 1714039065632,
          completeTime: 0,
          dayTimesInterval: 1,
          rewardItems: [
            {
              name: '4肥料',
              mark: 'code',
              amount: 5,
              icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
            },
          ],
          recommend: 1,
          extra: '{"scene":"iflow","taskTime":20000,"from":"bab_farm_from","entry":"bab_farm_entry"}',
          token: '',
          dayTimes: {
            progress: 0,
            target: 20,
          },
          prizes: [],
        },
        {
          id: 1781543,
          name: '信息流浏览任务',
          desc: '信息流浏览任务',
          event: 'baba_farm_browse',
          target: 1,
          progress: 0,
          sort: 4,
          icon: 'https://yes-file.uc.cn/file/1712454977958_3281699398_2474.png',
          url: 'ext:info_flow_open_channel:ch_id=888888&type=single&event=baba_farm_browse&mission_id={taskId}',
          btnName: '',
          state: 0,
          beginTime: 1714039065632,
          completeTime: 0,
          dayTimesInterval: 1,
          rewardItems: [
            {
              name: '3肥料',
              mark: 'code',
              amount: 4,
              icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
            },
          ],
          recommend: 2,
          extra: '{"scene":"iflow","taskTime":20000,"from":"bab_farm_from","entry":"bab_farm_entry"}',
          token: '',
          dayTimes: {
            progress: 0,
            target: 25,
          },
          prizes: [],
        },
      ],
      rewardItems: [
        {
          name: '肥料',
          mark: 'code',
          amount: 5,
          icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
        },
        {
          name: "现金",
          mark: "code",
          amount: 5,
          icon: "https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png"
        },
        {
          name: "现金",
          mark: "code",
          amount: 5,
          icon: "https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png"
        }
      ],
      recommend: 1,
      token: '',
      sort: 1,
      state: 0,
      target: 2,
      taskClassify: "",
      taskSource: "DEFAULT",
      taskType: "everyday",
      url: "",
    },
    {
      id: 1781545,
      name: '小说阅读任务',
      desc: '小说阅读任务',
      event: 'baba_farm_novel_read',
      target: 1,
      progress: 0,
      sort: 1,
      icon: 'https://yes-file.uc.cn/file/1712454977958_3281699398_2474.png',
      url: 'ext:open_novelbox:index=1&from=babafarm_task&babafarm_missionId={taskId}',
      btnName: '',
      state: 0,
      beginTime: 1714039065632,
      completeTime: 0,
      dayTimesInterval: 1,
      rewardItems: [
        {
          name: '3肥料',
          mark: 'code',
          amount: 3,
          icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
        },
      ],
      recommend: 1,
      extra: '{"scene":"novel","taskTime":70000,"from":"bab_farm_from","entry":"bab_farm_entry"}',
      token: '',
      dayTimes: {
        progress: 0,
        target: 20,
      },
      prizes: [],
    },
    {
      id: 1781542,
      name: '邀请助力任务',
      desc: '邀请助力任务',
      event: 'baba_farm_invite',
      target: 1,
      progress: 0,
      sort: 1,
      icon: 'https://yes-file.uc.cn/file/1712455048570_4160564911_8288.png',
      url: '',
      btnName: '',
      state: 0,
      beginTime: 1714039065632,
      completeTime: 0,
      dayTimesInterval: 0,
      rewardItems: [
        {
          name: '2肥料',
          mark: 'code',
          amount: 2,
          icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
        },
      ],
      recommend: 1,
      extra: '',
      token: '',
      dayTimes: {
        progress: 0,
        target: 2,
      },
      ext: {
        incrementAssistNum: 0,
      },
      prizes: [],
    },
    {
      id: 1781562,
      name: '搜索浏览任务',
      desc: '搜索浏览任务',
      event: 'baba_farm_search',
      target: 1,
      progress: 0,
      sort: 2,
      icon: 'https://yes-file.uc.cn/file/1712454977958_3281699398_2474.png',
      url: 'ext:open_search:event={event}&mission_id={taskId}',
      btnName: '',
      state: 0,
      beginTime: 1714039065632,
      completeTime: 0,
      dayTimesInterval: 1,
      rewardItems: [
        {
          name: '6肥料',
          mark: 'code',
          amount: 6,
          icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
        },
      ],
      recommend: 1,
      extra: '{"scene":"search","taskTime":20000,"from":"bab_farm_from","entry":"bab_farm_entry"}',
      token: '',
      dayTimes: {
        progress: 0,
        target: 20,
      },
      prizes: [],
    },
    {
      id: 1781525,
      name: '信息流视频任务',
      desc: '信息流视频任务',
      event: 'baba_farm_watch',
      target: 1,
      progress: 0,
      sort: 3,
      icon: 'https://yes-file.uc.cn/file/1712455048570_4160564911_8288.png',
      url: 'ext:info_flow_open_channel:scene=video_page_merge&event={event}&mission_id={taskId}',
      btnName: '',
      state: 0,
      beginTime: 1714039065632,
      completeTime: 0,
      dayTimesInterval: 1,
      rewardItems: [
        {
          name: '4肥料',
          mark: 'code',
          amount: 5,
          icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
        },
      ],
      recommend: 1,
      extra: '{"scene":"iflow","taskTime":20000,"from":"bab_farm_from","entry":"bab_farm_entry"}',
      token: '',
      dayTimes: {
        progress: 0,
        target: 20,
      },
      prizes: [],
    },
    {
      id: 1781543,
      name: '信息流浏览任务',
      desc: '信息流浏览任务',
      event: 'baba_farm_browse',
      target: 1,
      progress: 0,
      sort: 4,
      icon: 'https://yes-file.uc.cn/file/1712454977958_3281699398_2474.png',
      url: 'ext:info_flow_open_channel:ch_id=888888&type=single&event=baba_farm_browse&mission_id={taskId}',
      btnName: '',
      state: 0,
      beginTime: 1714039065632,
      completeTime: 0,
      dayTimesInterval: 1,
      rewardItems: [
        {
          name: '3肥料',
          mark: 'code',
          amount: 4,
          icon: 'https://yes-file.uc.cn/file/1712454303749_1738845701_2803.png',
        },
      ],
      recommend: 2,
      extra: '{"scene":"iflow","taskTime":20000,"from":"bab_farm_from","entry":"bab_farm_entry"}',
      token: '',
      dayTimes: {
        progress: 0,
        target: 25,
      },
      prizes: [],
    },
  ],
};

export default taskList;
