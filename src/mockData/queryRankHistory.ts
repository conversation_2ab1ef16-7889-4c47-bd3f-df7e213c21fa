
const queryRankHistory = {
  totalAward: 34, // 累计肥料奖励值
  rankingInfo: {
    curTime: 13434343434343, // 当前毫秒时间戳
    title: "第一周",
    description: "推书第一周榜单",
    date: "20230530", // 积分计算开始日期
    tag: "day/week/total",
    displayStartTime: 1685376000000, // 展示开始时间
    startTime: 1685376000000, // 积分计算开始时间
    endTime: 1685376000000, // 积分计算结束时间
    status: "", // PUBLISHED 已发布，DRAWING 领奖中，CANCELED 已取消
    totalNumber: 100, // 总人数 --24.7.4新增
    displayRankNum: 100, // 排名显示数量
    teamMembers: [
      {
        nickname: "队友名字", // 队友名字 --24.7.4新增
        avatar: 'https://image.uc.cn/o/uop/1Ht08/;;0,uop/g/uop/avatar/e189d9d47f7d21059e13d044f9825572.jpg;3,160',
        own: true,
        score: 100 // 积分
      }
    ],
    myInfo: {
      nickname: "我的会员呢",
      avatar: 'https://image.uc.cn/o/uop/1Ht08/;;0,uop/g/uop/avatar/e189d9d47f7d21059e13d044f9825572.jpg;3,160',
      score: 100, // 积分
      newestScore: 100, // 最新积分
      ranking: 1, // 排名
      own: true,
      rate: 0.99, // 排名占比
      rewardStatus: "NONE", // 未领奖 REWARDED=已领奖   NONE=未领奖
      label: "评论最多", // 标签
      honor: "语惊四座", // 头衔
      wsgUid: "sfs353fsfsf", // 无效保镖加密的uid，有特殊字符，要传参使用时记得encode
      userKey: "10*55",
      prizeItems: [
        {
          prizeName: "网盘svip",
          prizeIcon: "https://image.uc.cn/s/uae/g/3o/quark-res-admin20221227/1672124048144_20221027111109.jpg",
          prizeMark: "drive_svip",
          prizeAmount: 1,
          prizeType: "AWARD"
        }
      ]
    },
    rankings: new Array(20).fill({
      label: "评论最多", // 标签
      honor: "语惊四座", // 头衔
      wsgUid: "sfs353fsfsf", // 无效保镖加密的uid，有特殊字符，要传参使用时记得encode
      nickname: "昵称",
      avatar: "头像",
      ranking: 100, // 排名
      score: 3434.34, // 积分
      own: true, // 自己的，true
      rewardStatus: "NONE", // 未领奖  未领奖 REWARDED=已领奖   NONE=未领奖
      rankingVersion: 15343434343434, // 榜单更新版本号 --250326
      prizeItems: [
        {
          prizeName: "网盘svip",
          prizeIcon: "https://image.uc.cn/s/uae/g/3o/quark-res-admin20221227/1672124048144_20221027111109.jpg",
          prizeMark: "drive_svip",
          prizeAmount: 1,
          prizeType: "AWARD"
        }
      ]
    })
  }
}
export type IQueryRankHistory = typeof queryRankHistory
export default queryRankHistory
