export default {
  success: true,
  code: "OK",
  data: {
    timestamp: 1729476783049,
    attributes: {
      dialogCancelBtnText: "放弃红包",
      dialogStyle: {
        topBgImg: "https://yes-file.uc.cn/file/1729502528727_2105459487_7436.png",
        containerBgColor: "#fff9f3",
        bottomBgImg: "https://yes-file.uc.cn/file/1729502533283_1945855044_2056.png"
      },
      dialogSuccessBtnTextMap: {
        立即领取: "立即领取",
        cash: "立即提现",
        point: "立即领取"
      },
      dialogTitle: "被红包任务砸中"
    },
    taskList: [
      {
        id: 1784843,
        name: "淘宝下载并绑定登录任务",
        desc: "淘宝下载并绑定登录任务",
        event: "high_value_task",
        target: 1,
        progress: 0,
        taskType: "period",
        sort: 1,
        icon: "",
        url: "",
        btnName: "",
        state: 5,
        beginTime: 1729094400000,
        completeTime: 0,
        rewardItems: [
          {
            name: "肥料",
            mark: "point",
            amount: 1000,
            randomAmount: false,
            icon: "https://yes-file.uc.cn/file/1729150746817_2407405462_7383.png"
          }
        ],
        recommend: 1,
        extra: "",
        token: "",
        ext: {},
        prizes: [],
        taskSource: "DEFAULT",
        taskClassify: "money",
        groupCode: "business_active",
        endTime: 1732896001000,
        cycleTotalDay: 0,
        continuousDay: 0,
        preTaskList: [
          {
            id: 1783422,
            name: "手淘新登用户",
            desc: "手淘新登用户",
            event: "rta_call_taobao_nu",
            target: 1,
            progress: 0,
            taskType: "everyday",
            sort: 1,
            icon: "",
            url: "",
            btnName: "去下载淘宝",
            state: 0,
            beginTime: 1729440000000,
            completeTime: 0,
            rewardItems: [],
            recommend: 1,
            extra: "{\"category\":\"L1\",\"defaultHidden\":\"2\"}",
            token: "AANk/bkZsnDsfGiADVTHYZSgV+TuboGr1MmDB5W2uKKKTIufh37qGuaaHCREonhNmGE2Yb58bMVrIJGpHUs+HwQ3",
            ext: {},
            prizes: [],
            taskSource: "DEFAULT",
            taskClassify: "active",
            groupCode: "taobao_target_user",
            endTime: 1882800001000,
            cycleTotalDay: 0,
            continuousDay: 0,
            needReceive: false
          },
          {
            id: 1784822,
            name: "UC登录并绑定淘宝",
            desc: "UC登录并绑定淘宝",
            event: "uc_login",
            target: 1,
            progress: 0,
            taskType: "period",
            sort: 1,
            icon: "",
            url: "",
            btnName: "UC登录并绑定淘宝",
            state: 0,
            beginTime: 1729440000000,
            completeTime: 0,
            rewardItems: [],
            recommend: 1,
            extra: "",
            token: "",
            ext: {},
            prizes: [],
            taskSource: "DEFAULT",
            taskClassify: "active",
            groupCode: "business_active",
            endTime: 1732896000000,
            cycleTotalDay: 0,
            continuousDay: 0,
            needReceive: false
          }
        ],
        needReceive: false
      },
      {
        id: 1784883,
        name: "下载饿了么",
        desc: "下载饿了么",
        event: "call_app_download",
        target: 1,
        progress: 0,
        taskType: "period",
        sort: 1,
        icon: "",
        url: "",
        btnName: "下载饿了么",
        state: 0,
        beginTime: 1729440000000,
        completeTime: 0,
        rewardItems: [
          {
            name: "肥料",
            mark: "point",
            amount: 10,
            randomAmount: false,
            icon: "https://yes-file.uc.cn/file/1729151132959_2407405462_4661.png"
          }
        ],
        recommend: 1,
        extra: "",
        token: "",
        ext: {},
        prizes: [],
        taskSource: "DEFAULT",
        taskClassify: "money",
        groupCode: "business_active",
        endTime: 1732896001000,
        cycleTotalDay: 0,
        continuousDay: 0,
        needReceive: true
      },
      {
        id: 1784241,
        name: "去大众点评领现金红包",
        desc: "去大众点评领现金红包",
        event: "call_app_link",
        target: 1,
        progress: 0,
        taskType: "everyday",
        sort: 1,
        icon: "",
        url: "https://m.dianping.com/faaslocal/h5applink/page?utm_source=uc_exchange_dzdp&task=2&taskId=755565&actCode=&titleBar=1",
        btnName: "去大众点评领现金红包",
        state: 0,
        beginTime: 1729440000000,
        completeTime: 0,
        rewardItems: [
          {
            name: "肥料",
            mark: "point",
            amount: 2,
            randomAmount: false,
            icon: 'https://img.alicdn.com/imgextra/i4/O1CN01Af6iWP1seKcAAmXHn_!!6000000005791-2-tps-384-339.png'
          }
        ],
        recommend: 1,
        extra: "{\"scheme\":\"dianping://picassobox?picassoid=picasso-user-benefits/main-bundle.js&notitlebar=true&taskid=752462&utm_source=uc_exchange_dzdp&actcode=&backurl=uclink://www.uc.cn/19b64348381e629f44f43b8506f24e92?src_pkg=bdmaplite&src_ch=bdmaplite&action=open_url&url=https%3A%2F%2Fbroccoli.uc.cn%2Fapps%2FNKFx5XAhx%2Froutes%2Fwalfare%3FwebCompass%3Dtrue%26uc_param_str%3Ddsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt%26uc_biz_str%3DS%253Acustom%257CC%253Afull_screen%257COPT%253AIMMERSIVE%25401%26entry%3bdmaplite\",\"pkgName\":\"com.dianping.v1\",\"andCheckInstall\":true,\"iosCheckInstall\":false,\"onlyCheckInstallOnPop\":true}",
        token: "",
        ext: {},
        prizes: [],
        taskSource: "DEFAULT",
        taskClassify: "",
        groupCode: "",
        endTime: 1758816001000,
        cycleTotalDay: 0,
        continuousDay: 0,
        needReceive: false
      },
    ]
  },
  msg: "",
  timestamp: 1729476783050,
  traceId: "2131332717294767829614460e14fe"
}
