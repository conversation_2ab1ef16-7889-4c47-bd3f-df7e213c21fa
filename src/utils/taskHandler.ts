import { TaskInfo, TASK_EVENT_TYPE, TASK_STATUS, TASK_LOCATION } from '@/pages/index/components/TaskPop/TaskList/types';
import { checkTaskFinished, canContinueGoLinkTask, logToFinishTask, getTaskAward } from '@/pages/index/components/TaskPop/TaskList/util';
import { dongfengTaskReport, taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { execWithLock } from '@/lib/utils/lock';
import stat from '@/lib/stat';
import dispatch from '@/logic/store';
import { mx } from '@ali/pcom-iz-use';


interface TaskHandlerParams {
  currentTask: TaskInfo | null;
  source?: 'bubble' | 'list';
  modalType?: string;
  resource_location: string;
}

/**
 * 获取任务通用埋点参数
 */
export const getCommonTrackParams = (task: TaskInfo, source?: string) => {
  const app = mx.store.get('app');
  const taobaoRtaInfo = app.taobaoRtaInfo;
  const isTaobaoRta = [
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO, 
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, 
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD
  ].includes(task.event) && taobaoRtaInfo;

  return {
    task_id: task.id,
    task_name: task.name,
    isfinish: checkTaskFinished(task) ? 1 : 0,
    task_count: task?.dayTimes?.progress || '',
    taskclassify: task.taskClassify,
    groupcode: task.groupCode,
    award_amount: getTaskAward(task),
    task_progress: task?.dayTimes?.progress || '',
    tasklist_source: source === 'bubble' ? 'resourceNiche' : source,
    ...(isTaobaoRta ? {
      taobao_rta_type: taobaoRtaInfo.category,
      sid: taobaoRtaInfo.adInfo?.sid,
      rta_price: taobaoRtaInfo.adInfo?.price
    } : {})
  };
};

/**
 * 任务处理：只关注任务的处理逻辑
 */
export const handleTask = async ({
  currentTask,
  source = 'list',
  modalType,
  resource_location = TASK_LOCATION.RETAIN_POPUP,
}: TaskHandlerParams) => {
  if (!currentTask) return;

  try {
    const requestId = geneTaskRequestId();
    dongfengTaskReport(currentTask, 'click');

    const { state, id } = currentTask;
    const user = mx.store.get('user');
    const highValueTask = mx.store.get('highValueTask');
    const taobaoRtaConfig = mx.store.get('app.frontData.taobaoRtaConfig');
    
    stat.click('task_click', {
      c: source === 'bubble' ? 'card' : source,
      d: 'task',
      pop_source: source === 'bubble' ? 'balloon' : source,
      page_status: user?.bindTaobao ? 1 : 0,
      requestId,
      scene: taobaoRtaConfig?.sceneId,
      resource_location, 
      ...getCommonTrackParams(currentTask, source)
    });

    if (checkTaskFinished(currentTask)) {
      canContinueGoLinkTask(currentTask);
      return;
    }

    if (highValueTask.currentTaskInfo?.id !== id) {
      const loginStatus = await dispatch.user.checkLoginAndBind(0, 7);
      if (!loginStatus && highValueTask.currentTaskInfo?.id !== currentTask.id) {
        return;
      }
    }

    if (state === TASK_STATUS.TASK_NOT_COMPLETED && 
        [TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD].includes(currentTask.event)) {
      const result = await dispatch.task.checkAppDownloadFinish(currentTask, highValueTask.currentTaskInfo?.id !== id);
      if (result) {
        return;
      }
    }

    if (state === TASK_STATUS.TASK_COMPLETED) {
      return dispatch.task.finishTask({
        taskId: currentTask.id,
        type: "award",
        useUtCompleteTask: !!currentTask.useUtCompleteTask,
        publishId: currentTask.publishId,
      });
    }

    execWithLock('finish_task_lock', async () => {
      logToFinishTask(currentTask, source);
      await taskActionHandler(currentTask, requestId, {location: resource_location});
    }, 2000);
  } catch (error) {
    console.error('Task handling failed:', error);
  }
};
