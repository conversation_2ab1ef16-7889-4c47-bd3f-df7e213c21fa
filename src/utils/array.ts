/**
 * 获取多个数组中指定字段的交集
 * @param arrays - 数组列表
 * @param key - 用于比较的字段名，如果不传则直接比较数组元素
 * @returns 从第一个数组中获取的交集结果
 */
export function getArraysIntersection<T, U = T>(
  arrays: [T[], ...U[][]],
  key?: keyof T
): T[] {
  if (!arrays?.length) return [];
  if (arrays.length === 1) return arrays[0];

  const firstArray = arrays[0];
  if (!firstArray?.length) return [];

  // 从其他数组中收集所有的值
  const otherArraysValues = arrays.slice(1).map(arr => 
    new Set(key ? arr.map(item => item[key]) : arr)
  );

  // 从第一个数组中筛选出在所有其他数组中都存在的元素
  return firstArray.filter(item => {
    const value = key ? item[key] : item;
    return otherArraysValues.every(set => set.has(value));
  });
}

/**
 * 获取多个数字数组的交集
 * @param arrays - 数字数组列表
 * @returns 从第一个数组中获取的交集结果
 */
export function getNumberArraysIntersection(
  arrays: [number[], ...number[][]]
): number[] {
  return getArraysIntersection(arrays);
}

/**
 * 获取多个对象数组中指定 ID 字段的交集
 * @example
 * // 场景：从任务列表中找出同时存在于 taskInfo 和 bubbleInfo 中的未完成任务
 * interface Task {
 *   id: number;
 *   name: string;
 *   status: number;
 * }
 * 
 * interface TaskInfo {
 *   id: number;
 *   type: string;
 * }
 * 
 * interface BubbleInfo {
 *   taskId: number;
 *   img: string;
 * }
 * 
 * const tasks: Task[] = [
 *   { id: 1, name: '任务1', status: 0 },
 *   { id: 2, name: '任务2', status: 1 }
 * ];
 * 
 * const taskInfos: TaskInfo[] = [
 *   { id: 1, type: 'daily' },
 *   { id: 3, type: 'weekly' }
 * ];
 * 
 * const bubbleInfos: BubbleInfo[] = [
 *   { taskId: 1, img: 'url1' },
 *   { taskId: 4, img: 'url2' }
 * ];
 * 
 * // 转换 bubbleInfo 以匹配 id 字段
 * const bubbleTaskIds = bubbleInfos.map(item => ({ id: item.taskId }));
 * 
 * // 获取交集
 * const validTasks = getArraysIdIntersection([
 *   tasks.filter(task => task.status === 0), // 未完成的任务
 *   taskInfos,
 *   bubbleTaskIds
 * ]);
 * 
 * // 结果: [{ id: 1, name: '任务1', status: 0 }]
 * // 注意：返回的是第一个数组（tasks）中的完整对象
 * 
 * @param arrays - 对象数组列表，第一个数组的对象结构将作为返回值的类型
 * @param idKey - ID 字段名，默认为 'id'
 * @returns 从第一个数组中获取的交集结果
 */
export function getArraysIdIntersection<T extends Record<string, any>, U = T>(
  arrays: [T[], ...U[][]],
  idKey: keyof T = 'id'
): T[] {
  return getArraysIntersection(arrays, idKey);
}