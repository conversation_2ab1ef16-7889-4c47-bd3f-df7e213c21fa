// @ts-nocheck
import { Game, Component } from '@eva/eva.js';
import { Render as _Render } from '@eva/plugin-renderer-render';
import { Event as _Event } from '@eva/plugin-renderer-event';
import { Img as _Img } from '@eva/plugin-renderer-img';
import { SpriteAnimation as _SpriteAnimation } from '@eva/plugin-renderer-sprite-animation';
import { Mask as _Mask } from '@eva/plugin-renderer-mask';
import { Transition as _Transition } from '@eva/plugin-transition';
import { Spine as _Spine } from '@eva/plugin-renderer-spine';
import { Graphics as _Graphics } from '@eva/plugin-renderer-graphics';

declare module '@eva/eva.js' {
  interface AnimationStruct {
    name: string;
    component: Component;
    values: Array<{
      time: number;
      value: number;
      tween?: string;
    }>;
  }
  interface GameObject {
    addTouchHandler: (params: any) => void;

    getCenterPosition: () => { x: number; y: number };

    addComponentType: (compType, initParams) => Component;

    getComponentType: (comType) => any;

    setRenderParams: (v) => _Render;

    addComponent: (name: string, params?: any) => Component;

    getComponent: (compType) => Component;
  }

  const COMPONENT: {
    [key: string]: string;
  };


  interface SpriteAnimationParams {
    resource: string;
    autoPlay: boolean;
    speed: number;
  }


  export function createSpriteAnimation(
    name: string,
    transform: TransformParams,
    params: SpriteAnimationParams,
  ): GameObject;

  interface Img {
    setUrl: (surl: string) => void;
    resource?;
    gameObject;
  }

  export const Render: typeof _Render;
  export const Event: typeof _Event;
  export const SpriteAnimation: typeof _SpriteAnimation;
  export const Mask: typeof _Mask;
  export const Spine: typeof _Spine;
}

declare module '@eva/plugin-renderer-text' {
  interface Text {
    createGameObject: (params: any) => Component;
  }
}
declare module '@eva/plugin-renderer-img' {
  interface Img {
    setUrl: (surl: string) => void;
    resource: string;
    gameObject;
  }
}
