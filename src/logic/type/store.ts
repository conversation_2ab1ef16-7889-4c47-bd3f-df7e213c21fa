export enum StoreName {
  MainInfo = 'mainInfo', // 所有数据
  GameInfo = 'mainInfo.gameInfo', // 游戏区数据
  ExtraData = 'extraData', // extra 接口
  GameHeight = 'gameHeight', // 游戏区高度
  DeviceScreenMode = 'deviceScreenMode', // 设备模式
  ScreenOffsetY = 'gameScreenOffsetY', // iphoneX与iphone8屏幕偏移
  SysConfig = 'gameSysConfig', // 系统配置
  IsAnt = 'isAnt', // 是否是支付宝
  ShowAccelTips = 'showAccelTips', // 显示加速tips
  ProgressBarVisible = 'app.progressBarVisible', // 浇水进度条是否显示
  Main = 'mainInfo.gameInfo.sceneInfo.main', // 是否是主场景
  GameTreeType = 'game_seed_type', // 当前树显示的类型
  HideGame = 'hideGame', // 隐藏游戏区
  DeviceLevel = 'user.deviceInfo.deviceLevel', // 设备等级
  CurrentTime = 'mainInfo.__meta.timestamp', // home接口服务器当前时间
  SeedImg = 'mainInfo.gameInfo.plantInfo.seedStage.seedImg', // 种子图片
  StageLevel = 'mainInfo.gameInfo.plantInfo.seedStage.stageLevel', // 树等级
  SceneCode = 'mainInfo.gameInfo.sceneInfo.sceneCode', // 场景code
  SceneType = 'mainInfo.gameInfo.sceneInfo.sceneType', // 组队场景类型
  CanExchange = 'mainInfo.gameInfo.plantInfo.canExchange', // 是否可兑换
  Status = 'mainInfo.gameInfo.sceneInfo.status', // 活动状态
  AnimSkeleton = 'mainInfo.gameInfo.plantInfo.seedStage.animSkeleton',
  AnimImg = 'mainInfo.gameInfo.plantInfo.seedStage.animImg', // 种子动画
  // AnimImgTexture = 'mainInfo.gameInfo.plantInfo.seedStage.animImgTexture', // 种子动画
  // SeedImgTexture = 'mainInfo.gameInfo.plantInfo.seedStage.seedImgTexture', // 种子动画
  AnimAtalas = 'mainInfo.gameInfo.plantInfo.seedStage.animAtalas',
  CanWater = 'mainInfo.gameInfo.canWater', // 是否可以浇水
  HappyPoint = 'mainInfo.gameInfo.accountInfo.happyPoint', // 我的福气值
  WateringCost = 'mainInfo.gameInfo.accountInfo.wateringCost', // 每次浇水消耗
  HasMigrate = 'mainInfo.gameInfo.accountInfo.hasMigrate', // 是否升级
  DailyFuqiInfo = 'mainInfo.gameInfo.dailyFuqiInfo', // 明日福气逻辑
  RandomSeed = 'mainInfo.gameInfo.accountInfo.randomSeed', // 随机种子
  TipsHeight = 'mainInfo.gameInfo.plantInfo.seedStage.tipsHeight', // 气泡高度
  TeamTips = 'realExtraData.extraData.teamInfo.teamTips', // 亲密度气泡
  StageName = 'mainInfo.gameInfo.plantInfo.seedStage.stageName', // 果树阶段名
  TeamType = 'mainInfo.gameInfo.teamPlantInfo.teamType', // 队伍类型
  TeamName = 'mainInfo.gameInfo.teamPlantInfo.teamName', // 队伍名字
  TeamPlant = 'mainInfo.gameInfo.plantInfo.teamPlant', // 是否合种
  SeedName = 'mainInfo.gameInfo.plantInfo.seedStage.seedName', // 种子名称
  SeedSubTitle = 'mainInfo.gameInfo.plantInfo.seedStage.seedSubTitle',
  GrownUpImg = 'mainInfo.gameInfo.plantInfo.seedStage.grownUpImg',
  Unit = 'mainInfo.gameInfo.plantInfo.seedStage.unit', // 树种成后奖励的单位
  TeamSize = 'mainInfo.gameInfo.teamPlantInfo.teamSize', // 队伍人数
  CurrentProgress = 'mainInfo.gameInfo.plantInfo.seedStage.currentProgress', // 当前进度条进度
  TotalProgress = 'mainInfo.gameInfo.plantInfo.seedStage.totalProgress', // 进度条总进度
  StageText = 'mainInfo.gameInfo.plantInfo.seedStage.stageText', // 树阶段文案
  // BubblePositionX = 'mainInfo.gameInfo.plantInfo.seedStage.bubblePositionX', // 树上奖励气泡游戏区左边距离
  // BubblePositionY = 'mainInfo.gameInfo.plantInfo.seedStage.bubblePositionY', // 树上奖励气泡距离游戏区底部
  ActivityPreEndTipTitle = 'mainInfo.gameInfo.plantInfo.extMap.activityPreEndTipTitle', // 木牌上活动结束前提示标题
  ActivityPreEndTipText = 'mainInfo.gameInfo.plantInfo.extMap.activityPreEndTipText', // 木牌上活动结束前提示子文案
  CostName = 'mainInfo.gameInfo.accountInfo.costName', // 浇灌单位
  TreeScale = 'resourceData.treeConfig.treeScale', // 树缩放比例

  // 树后
  TreeBackConfig = 'resourceData.treeBackConfig',
  // GameBg = 'resourceData.treeBackConfig.bgImg', // 游戏区背景
  NightBgImg = 'resourceData.treeBackConfig.nightBgImg', // 夜间模式背景
  // GameBgLow = 'resourceData.treeBackConfig.lowBgImg', // 游戏区背景
  NightBgImgLow = 'resourceData.treeBackConfig.lowNightBgImg', // 夜间模式背景
  BgSpineImg = 'resourceData.treeBackConfig.bgSpineImg', // 背景动画
  BgSpineSke = 'resourceData.treeBackConfig.bgSpineSke', // 背景动画Ske
  BgSpineAtlas = 'resourceData.treeBackConfig.bgSpineAtlas', // 背景动画Atlas

  // 树
  GoPlantTreeImg = 'resourceData.treeConfig.goPlantTreeImg', // 树牌
  ExpirePlantImg = 'resourceData.treeConfig.expirePlantImg', // 敬请期待树牌

  // 树前
  WaterAnim = 'resourceData.waterAnim', // 浇水按钮动画
  WaterAnimAtlas = 'resourceData.waterAnim.waterAnimAtlas',
  TreeStateInfo = 'realExtraData.treeStateInfo', // 树上奖励气泡
  AccRatio = 'accRatio', // 加速卡
  TreeAwardTipShow = 'treeAwardTipShow', // 当前树说话气泡是否是签到肥料或三餐肥料

  TeamMemberInfoList = 'realExtraData.extraData.teamInfo.teamMemberInfoList', // 队伍信息
  PlantMode = 'realExtraData.extraData.teamInfo.plantMode', // 组队模式

  SeedCode = 'app.mainInfo.gameInfo.plantInfo.seedStage.seedCode', // 种子编码
  MultiOpenGift = 'app.mainInfo.activityData.multiOpenGift', // 拆红包
  MultiOpenGiftStatus = 'app.mainInfo.activityData.multiOpenGift.status',
  MultiOpenGiftCompleteTimes = 'app.mainInfo.activityData.multiOpenGift.completeTimes',
  MultiOpenGiftNeedTimes = 'app.mainInfo.activityData.multiOpenGift.needTimes',
  GroupKey = 'app.mainInfo.activityData.multiOpenGift.groupKey',
  Round = 'app.mainInfo.activityData.multiOpenGift.round',

  // 兑换url
  ExchangeUrl = 'app.mainInfo.gameInfo.plantInfo.exchangeUrl',
  // 兑换小箱子跳转URL
  WaitExchangeUrl = 'app.mainInfo.gameInfo.plantInfo.extMap.waitExchangeUrl',
  // nu
  Fresher = 'app.mainInfo.userInfo.fresher',
}
