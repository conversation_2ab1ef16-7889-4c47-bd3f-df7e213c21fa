export enum TreeType {
  No,
  SeedImg,
  Spine,
  BrandImg,
}

export interface ExtNPCInfo {
  needWarning: string;
  tips: string[];
}

export interface SpineType {
  ske: string;
  atlas: string;
  image: string;
}

export enum SurpriseBoxType {
  // 换种子
  ChangeSeed = 'changeSeed',
}

export interface GameInfo {
  dailyFuqiInfo?: DailyFuqiInfo;
  sceneInfo?: SceneInfo;
  needSelectSeed?: boolean; // 需要弹窗选种子弹窗
  redpacket?: boolean; // 是否有红包
  hasPlantInfo?: boolean;
  canWater?: boolean;
  teamPlantInfo?: {
    teamType: string; // 合种类型
    teamName: string; // 队伍名称
    teamSize: string; // 队伍人数
    teamTypeCode: string; // 队伍类型
  };
  plantInfo?: {
    canExchange?: boolean;
    teamPlant?: boolean;
    contribution?: number;
    seasonSeed?: boolean;
    seedTips?: string;
    preDismiss?: boolean;
    seedStage?: {
      seedCode?: string;
      seedName?: string;
      currentProgress?: number;
      totalProgress?: number;
      stageLevel?: number;
      stageName?: string;
      redpacket?: boolean;
      seedImg?: string;
      seedSubTitle?: string;
      stageText?: string;
      // grownUpImg?: string;
      // upgradeText?: string;
      // upgradeImg?: string;
      // description?: string;
      animImg?: string;
      animAtalas?: string;
      animSkeleton?: string;
      tipsHeight?: number; // 轮播气泡高度
      // bubblePositionX?: number; // 树上奖励气泡游戏区左边距离
      // bubblePositionY?: number; // 树上奖励气泡距离游戏区底部
      unit?: string;
    };
    extMap?: {
      activityPreEndTipTitle: string;
      activityPreEndTipText: string;
    };
  };
  accountInfo?: {
    happyPoint?: string;
    wateringCost?: number;
    wateringLeftTimes?: number;
    sunAmount?: number; // 阳光数量
    hasMigrate?: boolean; // 是否完成切换
    randomSeed?: boolean; // 代表是否进行随机选种，出新手字段
  };
}

export enum StatusType {
  InActive = 0, // 进行中
  Preheat = 1, // 预热
  Await = 2, // 敬请期待
}

export interface ExtNpmInfo {
  code: string;
  data: {
    hasTask: boolean;
    tips: string[];
  };
}

export interface SceneInfo {
  main?: boolean; // 是否主场景
  sceneCode?: string;
  sceneFresher?: boolean;
  sceneName?: string;
  floorId?: string; // feeds流id
  desc?: string; // 选种子秒速
  status?: number; // 场景状态，0：进行中，1：预热，2：敬请期待
  leftTime?: number; // 如果场景有时间限制，则存在该字段，毫秒
  finished?: boolean; // 当前场景是否已种成，能否继续种
  enableTeamPlant?: boolean; // 该场景是否开启合种
  finishInfo?: {
    seedName?: string; // 种子名称
    seedPic?: string; // 种子图片
    desc?: string; // 描述
  };
  hasRefund?: boolean; // finishInfo 存在，则显示种植成功相关信息，如果不存在在判断hasRefund字段，如果true则显示refundInfo相关退福气信息
  refundInfo?: {
    // refundInfo相关退福气信息
    title?: string;
    desc?: string;
    pic?: string;
    subTitle?: string;
  };
  showReward?: boolean; // 是否展示权益库存
  totalReward?: number; // 总奖品数量
  rewardNum?: number; // 已领取奖品数量
  // adInfo?: {
  //   // 场景内广告信息，内部字段没有则不展示
  //   superBannerPic: string; // 超品图片
  //   superBannerUrl: string; // 超品链接
  //   superBannerTitle: string; // 超品文案
  //   superPopPic: string; // 超级事件弹窗图片
  //   superPopUrl: string; // 超级事件弹窗地址
  // };
}

export interface RefundPopInfo {
  popCode: string; // 弹窗code，SCENE_END_REFUND（活动结束弹框）SCENE_NO_REWARDS_REFUND（权益不足弹框）
  order: string; // 优先级
  title: string; // 弹窗主题
  subTitle: string; // 弹窗子主题
  pic: string; // 图片
  buttonText: string; // 按钮文案
  desc: string; // 相关信息
  extend?: {
    sceneCode?: string; // 需要退福气的场景code
  }; // 其他信息
}

export interface FullSceneInfo {
  sceneCode: string; // 场景code
  sceneName: string; // 场景名称
  new: boolean; // 是否透出新标
}

export interface DailyFuqiInfo {
  canCollect: boolean; // 是否可以收集
  title: string; // 显示文案，根据不同状态变化
  amount: number; // 当前可收集福气值
  titlePic: string; // 弹窗标题
  stepInfo?: {
    stepSeq: string;
    stepType: string;
    waitSeconds?: string;
  };
  rotateShow?: string;
  hasDouble?: string;
}

export interface NotifyInfo {
  notifyExchange: boolean; // 为true时提示福气切换
  notifyNewTeamPlant: boolean;
  exchangeInfo: {
    exchangeFuqi?: number; // 消耗福气
    exchangeFeiliao?: number; // 转换肥料
  };
  popList: any[];
}
export interface GuideInfo {
  catLost: string; // 1 猫走丢了
  catImg: string; // 猫图片
  catRewardManure: number; // 猫来了奖励肥料600
  catRewardAcc: number; // 猫来了奖励加速2倍
  enableAcc: string; // 加速效果
  accLeft: number;
}

export interface AccelInfo {
  ratio: number; // 提升比例
  expire: number;
  now?: number; // 肥料增投新增
  showPop: 'true' | 'false'; // 是否展示弹窗
  showProcess: 'true' | 'false'; // 是否展示道具卡浮标
  pop: {
    // 加速卡展示弹窗
    times: number; // 剩余的次数，为空或者大于等于9999可展示为“无限次”
    minStage: number; // 加速卡生效的最低阶段等级
    maxStage: number; // 加速卡生效的最高阶段等级
    expire: number; // 加速卡失效的时间戳 为空则永久有效
    plant: string; // 加速卡生效的作物，为空则对所有作物有效
    ratio: number; // 弹窗展示的加速卡的加速度
  };
}

export interface TeamMemberInfo {
  name: string;
  avatar: string;
  encryptId: string;
  captain: boolean;
  self: boolean;
  remarkName: string;
  shareUrl: string;
}

export interface SpeedUpPopInfo {
  times: number;
  maxStage: number;
  expire: number;
  minStage: number;
  ratio: number;
}

export interface TeamBubbleInfo {
  text: string; // 文案
  avatar?: string; // 头像
}

export interface WateringTipInfo {
  image: string;
  title: string;
  type: string;
  id: string;
  level: number;
  status: string;
  clickUrl?: string;
  awardCount: string;
  isExp: boolean;
  extData?: any;
}

export enum IntimacyTeamType {
  Default = 'default',
  Love = 'love',
  Friend = 'friend',
  Family = 'family',
  SingleNhj = '2020nhj',
  LoveNhj = 'love_nhj',
  LoveHelp = 'love_help',
  FriendNhj = 'friend_nhj',
  FamilyNhj = 'family_nhj',
  BeautyNhj = 'beauty_nhj',
}

export enum PlantMode {
  Help = 'help',
  Team = 'team',
}

export enum WateringBubbleType {
  Watering = 'watering',
}

// export enum UpgradBubblesStatus {
//   UNFINISHED = 'UNFINISHED',
//   READY_TO_OPEN = 'READY_TO_OPEN',
//   FINISHED = 'FINISHED',
// }

export enum WateringBubbleStatus {
  Unfinished = 'unfinished',
  ReadyToOpen = 'readyToOpen',
}

// export interface UpgradBubblesInfo {
//   image: string;
//   title: string;
//   type: WateringBubbleType;
//   status: UpgradBubblesStatus;
//   id: string;
//   level: string;
//   hadExpose: boolean;
// }

// export interface TargetBubbleInfo {
//   seedImgList: string[];
//   title: string;
//   clickUrl: string;
// }

export interface TreeStateInfo {
  seedName: string;
  subTitle: string;
  seedPic: string;
}

export enum NoRefreshListType {
  BOX = 'EXTRA_BOX',
  TreeGainState = 'treeGainState',
}

export interface IntimacyInfo {
  teamInfo: {
    intimacy: number; // 当前亲密度
    maxIntimacy: number; // 最大亲密度
    taskRewardInfo: string[]; // 个人任务完成信息
    teamTips: TeamBubbleInfo[]; // 队伍动态
    teamList: []; // 队员列表, 支付宝透出
    teamMaxCount: number; // 最大队伍人数
    teamType: IntimacyTeamType; // 队伍类型
    plantMode: PlantMode; // 种植模式
    teamMemberInfoList: TeamMemberInfo[]; // 队员列表
  };
  plantInfo: {
    speedUpInfo: {
      // 加速信息
      expire: number;
      pop: SpeedUpPopInfo[];
      ratio: string;
      showPop: boolean;
      showProcess: boolean;
    };
    cardIds: string[]; // 待发放加速卡列表
  };
  rewardInfo: {
    rewardBubbles: {
      wateringBubble: WateringTipInfo;
      // upgradBubbles: UpgradBubblesInfo[];
      // targetBubble: TargetBubbleInfo;
    };
  };
  offlineInfo: {
    // 召回信息
    title: string; // 标题
    subTitle: string; // 子标题
    teamName: string; // 队伍名称
    rankList: Array<{
      nick: string; // 昵称
      text: string; // 文案
      date: string; // 日期
    }>; // 排行榜
  };
  notifyInfo: {
    popList: []; // 队伍奖励达成
  };
  noRefreshList: NoRefreshListType[];
}

export interface TipInfo {
  actionTip: string;
  actionUrl: string;
  order: string;
  picUrl: string;
  sceneCode: string;
  tips: string;
  type: string;
}

export interface UserData {
  userInfo?: {
    fresher?: boolean; // 是否开启新手引导
    isInTeam?: boolean; // 是否是合种
  };
  // deviceInfo?: {
  //   deviceLevel?: string; // 设备等级
  //   gameHeight?: number; // 背景区域高度
  // };
  switchInfo?: {
    enableTeamPlant: boolean; // 是否开启合种
    enableNewTeamPlant: boolean; // 是否开启新合种
    enableIntimacy: boolean; // 是否开启亲密度任务
    enableSunFarm: boolean;
  };
  gameInfo?: GameInfo;
  notifyInfo?: NotifyInfo;
  guideInfo?: {
    guide: GuideInfo;
    periods: {
      // 引导周期
      dailyGuide: number; // 日常引导周期7天
    };
  };
  systemInfo?: {
    promotionSceneCode: string; // 判断是否大促场景
  };
}

export enum UIZindex {
  Loading = 101,
  toast = 100, // toast层级
  pop = 99, // 弹窗层级
  scrollPop = 98, // 道具任务列表层级
  hand = 97, // 教学手层级
  teaching = 96,
  inGameUI = 95, // 游戏区浮层层级
}

export enum IAccountUnusualEntry {
  farm = 'farm', // 农场主页访问
  bbzhong = 'bbzhong', // 帮帮种访问
  sign = 'sign', // 签到
  task = 'task', // 完成任务
  batch = 'batch', // 批量领奖
  other = 'other', // 其他
}
