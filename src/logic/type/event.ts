export enum MainAPI {
  LoginChange='LoginChange',

  ToExtendGame = 'toExtendGame', // 游戏区树前后obj通过这个事件传递
  GamePause = 'gamePause', // 游戏暂停
  InitMainData = 'InitMainData', // 初始化主接口数据
  MainDataLoaded = 'MainDataLoaded', // 主接口数据加载完成
  Water = 'toGame_waterBtnAnim', // 浇水
  WaterGame = 'toLogicGameWater_start', // 点击浇水
  CreateButtonGO = 'createButtonGO',

  ShowTaskPop = 'showTaskPop', // 显示任务浮层
  HideTaskPop = 'hideTaskPop', // 隐藏任务浮层
  ShowBindTaobaoPop = 'needBindTaobao', // 显示绑定淘宝浮层
  HideBindTaobaoPop = 'hideBindTaobaoPop', // 隐藏绑定淘宝浮层

  MultiWaterInit = 'toGame_multiWaterInit', // 初始化一键施肥动画
  StartWater = 'toGameWater_start', // 施肥逻辑开始
  EndWater = 'toGameWater_end',
  ClickTree = 'toGameTree_click',
  ClickTreeGame = 'toGameTree_click', // 点击树
  AfterWaterResult = 'toGameWater_update',
  AfterWaterAni = 'afterWaterAni', // 施肥动画结束，触发树抖动
  Refresh = 'toMain_refresh', // 刷新事件
  AccelMsg = 'toGameAccel_msg', // 接受加速卡信息事件
  PlayReturnFeiliaoAnim = 'toGame_playReturnFeiliaoAnim', // 播放返还福气动画
  ShowSelectTreeUI = 'toGame_showSelectTreeUI', // 弹出选合种树弹窗
  IsShowPlantTips = 'toGame_isShowPlantTips', // 是否显示tips
  // ChangeBgAnim = 'toGame_ChangeBgAnim', // 背景动画改变
  TreeFadeOut = 'toGame_treeFadeOut', // 树消失
  TreeFadeIn = 'toGame_treeFadeIn', // 树出现
  TreeShake = 'toGame_treeShake', // 树抖动
  TreeExchangeAnimComplete = 'toMain_treeExchangeAnimComplete', // 树成熟发事件
  ZhongxiaAnimEnd = 'toGame_zhongxiaAnimEnd', // 种下动画结束发事件
  UpdateHappyPoint = 'toGame_udpateHappyPoint', // 更新肥料值
  FeiLiaoDropAnim = 'toGame_feiLiaoDropAnim', // 返还福气后肥料掉落动画
  PlayClickTreeAnim = 'toGame_playClickTreeAnim', // 播放普通点击树动画
  InitSpriteText = 'toGame_initSpriteText',
  AddPageEnter = 'return.36n.addPageEnter',
  CheckGoBackTask = 'return.36n.goBack',
  ShowProgressBarAccRocketAnim = 'ShowProgressBarAccRocketAnim',
  OnChangeFastWaterSelect = 'onChangeFastWaterSelect',
  ShowSharePanel = 'showSharePanel', // 显示分享面板
  HideSharePanel = 'hideSharePanel', // 隐藏分享面板
  FuzzyBackground = 'FuzzyBackground', // 弹窗是否羽化背景
  OpenLoginProtocolPanel = 'OpenLoginProtocolPanel', // 显示登录授权面板
  HideLoginProtocolPanel = 'HideLoginProtocolPanel', // 隐藏登录授权面板
  LimitTaskPedantsRender = 'LimitTaskPedantsRender', // 挂件展示
  LimitTaskPedantsShow = 'LimitTaskPedantsShow', // 挂件展示
  LimitTaskPedantsHide = 'LimitTaskPedantsHide', // 挂件隐藏
  LimitTaskPedantsEmit = 'LimitTaskPedantsEmit', // 挂件事件触发
  CLOSE_DOUBLE_CARD_POP = 'Close_double_card_pop', // 关闭翻倍浮层弹窗
  RECEIVED_DOUBLE_CARD_AWARD = 'ReceivedDoubleCardAward', // 翻倍卡奖励领奖成功
  FINISH_DOUBLE_CARD_TASK = 'FinishDoubleCardTask', // 翻倍卡任务完成
}

/**
 * 果树事件
 */
export enum TreeEventApi {
  /**
   * 树点击时机事件
   */
  TREE_ON_TREE_CLICK = 'TREE_ON_TREE_CLICK',
  /**
   * 树升级时开始展示时机事件
   */
  TREE_ON_TREE_UPGRADE_FADE_IN = 'TREE_ON_TREE_UPGRADE_FADE_IN',
  /**
   * 树升级时消失时机事件
   */
  TREE_ON_TREE__UPGRADE_FADE_OUT = 'TREE_ON_TREE__UPGRADE_FADE_OUT',
  /**
   * 树升级时展示动画播放完成
   */
  TREE_ON_TREE__UPGRADE_FADE_END = 'TREE_ON_TREE__UPGRADE_FADE_END',
  /**
   * 树可兑换时动画播放完成时机
   */
  TREE_ON_TREE_EXCHANGE_ANIM_COMPLETE = 'TREE_ON_TREE_EXCHANGE_ANIM_COMPLETE',
  /**
   * 树种下播放完成时机
   */
  TREE_ON_TREE_PLANT_ANIM_END = 'TREE_ON_TREE_PLANT_ANIM_END',
  /**
   * 树进度条动画完成
   */
  TREE_ON_TREE_PROGRESS_END = 'TREE_ON_TREE_PROGRESS_END',
  /**
   * 树的进度条被点击
   */
  TREE_ON_TREE_PROGRESS_BAR_CLICK = 'TREE_ON_TREE_PROGRESS_BAR_CLICK',
  /**
   * 树种子奖品被点击
   */
  TREE_ON_TREE_PROGRESS_AWARD_CLICK = 'TREE_ON_TREE_PROGRESS_AWARD_CLICK',
  /**
   * 果树曝光
   */
  TREE_TREE_EXPOSURE = 'TREE_TREE_EXPOSURE',
  /**
   * 树木牌曝光
   */
  TREE_TREE_BOARD_EXPOSURE = 'TREE_TREE_BOARD_EXPOSURE',
  /**
   * 果树兑换盒子曝光
   */
  TREE_TREE_EXCHANGE_BOX_EXPOSURE = 'TREE_TREE_EXCHANGE_BOX_EXPOSURE',

  /**
   * 果树初始化完成
   */
  TREE_INIT_COMPLETE = 'TREE_INIT_COMPLETE',
}

