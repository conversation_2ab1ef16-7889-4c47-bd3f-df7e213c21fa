import mx from '@ali/pcom-mx';
import toast from '@/lib/universal-toast/component/toast';
import { MainAPI } from '@/logic/type/event';
import { wateringAPI } from '@/api/watering';
import tracker from "@/lib/tracker";
import dispatch from '@/logic/store';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import stat from "@/lib/stat";
import {TasklistSource} from '@/pages/index/utils'
import { preloadImg } from "@/lib/utils/preloadImg";

const wateringErrCodeMsg = {
  WateringTimeLimitError: '今天的浇水次数已经用完啦，明天再来吧',
  InternalDaoError: '网络异常，请稍后再试',
  ExceedFrequencyLimit: '操作频率过快，请3s秒后再试',
  SecurityCodeCheckError: '啊哦，网络环境有点不稳定，晚点重试一次吧~',
  UserFuqiNotEnoughError: '加油集肥料，果树才能更快种成喔~',
  UserHasNoSeedError: '还没有领养种子哦',
}

let isWatering;
export const startGameWater = async () => {
  if (isWatering) {
    console.log('上次施肥未响应');
    return;
  }
  isWatering = true;
  const waterTracker = tracker.Monitor(117, {sampleRate: 1})
  try {
    const waterRes = await wateringAPI(mx.store.get('user').kps || '');
    const { currentStage, canExchange, upgrade, completeFruit, exchangeUrl, statistics } = waterRes;
    const dailyAppWateringCount = statistics?.dailyAppWateringCount || 0;
    let wateringPopConfig;
    // console.log('waterRes:', waterRes);
    isWatering = false
    let tree_process = ''
    mx.event.emit(MainAPI.Water); // 播放浇水按钮点击动画效果

    // 触发限时任务,浇水后更新任务
    const hasWaterTask = mx.store.get('timeLimitTask').hasWaterTask;

    if (hasWaterTask) {
      dispatch.task.queryTaskList();
    }
    if (canExchange) {
      // 种成弹窗
      mx.event.emit('sxchange_gift', {
        isNeedModal: true,
        completeFruit,
        seedName: currentStage?.seedName,
        seedSubTitle: currentStage?.seedSubTitle,
        exchangeUrl: exchangeUrl,
      });
      tree_process = 'harvest'
      stat.updateParam({
        tree_level: currentStage.stageLevel,
      })
    } else if (upgrade) {
      // 升级
      // 先取出上一级的树木数据，否则会被覆盖掉
      const { upgradeImg: prevSeedImg, stageLevel: prevStageLevel } =
        mx.store.get('mainInfo.gameInfo.plantInfo.seedStage') || {};
      baseModal.open(MODAL_ID.UPGRADE, {
        prevStageLevel,
        prevSeedImg,
        stageLevel: currentStage.stageLevel,
        upgradeImg: currentStage.upgradeImg,
      });
      stat.updateParam({
        tree_level: currentStage.stageLevel,
      })
      tree_process = 'upgrade'
    } else {
      wateringPopConfig = dispatch.resource.getWateringPopConfig(dailyAppWateringCount);
      if (wateringPopConfig) {
        // 提前加载弹窗里的图片素材避免闪动
        preloadImg([wateringPopConfig.popImg || wateringPopConfig.icon])
        baseModal.open(MODAL_ID.WATERING, {
          ...wateringPopConfig,
        });
      }
    }
    waterTracker.success({
      msg: '施肥成功',
      c1: String(canExchange),
      c2: String(upgrade),
      c5: dailyAppWateringCount,
      c6: !!wateringPopConfig,
      bl1: JSON.stringify(waterRes),
      bl2: wateringPopConfig ? JSON.stringify(wateringPopConfig) : '',
    })
    stat.custom('manure_click', {
      c: 'function',
      d: 'manure',
      is_success: 'success',
      tree_process,
    })
    // dispatch.app.set({uc20ExitFlag: false})
    mx.event.emit(MainAPI.AfterWaterResult);
    dispatch.app.updateHomeData();
  } catch (e) {
    isWatering = false;
    // 避免超时但实际施肥成功的情况
    dispatch.app.updateHomeData();
    waterTracker.fail({
      msg: '施肥失败',
      c3: e.code || 'UNKNOWN',
      c4: e.msg,
      bl1: JSON.stringify(e)
    })
    stat.custom('manure_click', {
      c: 'function',
      d: 'manure',
      is_success: 'fail',
      error_code: e.code || 'UNKNOWN',
      tree_process: ''
    })
    if (e.code === 'UserFuqiNotEnoughError') {
      mx.event.emit(MainAPI.ShowTaskPop, {tasklist_source: TasklistSource.fertilizer_lack})
    }
    if (wateringErrCodeMsg[e.code]) {
      toast.show(wateringErrCodeMsg[e.code])
    } else {
      toast.show('施肥失败了，请重新试试');
    }
  }
};
