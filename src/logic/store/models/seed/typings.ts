export interface ISeedState {
  address: string;
  changeAddressUrl: string;
  desc: string;
  hasAddress: boolean;
  inventory: string;
  ruleUrl: string;
  sceneName: string;
  tips: string[];
  unlimitSeedList: Product[];
}

export interface Product {
  area_restrict: boolean;
  category: string;
  hasInventory: boolean;
  inventory_ratio: string;
  order: string;
  redPacket: boolean;
  season_seed: boolean;
  seedCode: string;
  seedPic: string;
  seed_subTitle: string;
  seedTitle: string;
}
