import mx from '@ali/pcom-mx';
import { ISeedState } from './typings';
import config from '@/config';
import network from '@/lib/network';
import { getErrorInfo, geneTaskRequestId } from '../utils';
import tracker from '@/lib/tracker';
import MxModel from "@/logic/store/models";

// 种子
const defaultState: ISeedState = {
  address: '',
  changeAddressUrl: '',
  desc: '',
  hasAddress: false,
  inventory: '',
  ruleUrl: '',
  sceneName: '',
  tips: [''],
  unlimitSeedList: [],
};

class Seed extends MxModel {
  path: string;
  state: ISeedState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'seed';
    this.init();
  }
  init(initData: Partial<ISeedState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<ISeedState>) {
    Object.keys(payload).forEach(key => {
      this.safeUpdate(`${this.path}.${key}`, payload[key])
    })
  }
  // 获取种子列表
  async querySeedList(address?: string) {
    const { kps } = mx.store.getStore().user;
    const seedListMonitor = tracker.Monitor(106);
    const requestId = geneTaskRequestId();
    try {
      const seedList = await network.get(`${config.farmHost}/seed/list`, {
        appId: config.appId,
        kps,
        address,
        requestId,
      });
      seedListMonitor.success({
        msg: '查询种子列表成功',
        bl1: JSON.stringify(seedList),
        w_trace_reqid: requestId
      });
      this.set({
        ...seedList,
      });
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      seedListMonitor.fail({
        msg: `查询种子列表失败-${msg}`,
        c2: errCode,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      console.log(error);
    }
  }
  // 选择种子
  async selectSeed(seedCode: string, address: string) {
    const { kps } = mx.store.getStore().user;
    const selectSeedMonitor = tracker.Monitor(107);
    const requestId = geneTaskRequestId();
    try {
      const res = await network.post(`${config.farmHost}/seed/select`, {
        appId: config.appId,
        kps,
        // address,
        seedCode,
        requestId,
      });
      selectSeedMonitor.success({
        msg: '选择种子成功',
        bl1: JSON.stringify(res),
        w_trace_reqid: requestId
      });
      return {code: 'ok', data: res}
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      selectSeedMonitor.fail({
        msg: `选择种子失败-${msg}`,
        c2: errCode,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      console.log(error);
      return {code: 'error', data: error}
    }
  }
}

const appStore = new Seed();
export default appStore;
