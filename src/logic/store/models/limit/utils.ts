import { preloadFileWithFetch, preloadImg } from "@/lib/utils/preloadImg";
import { RewardItem, TaskInfo, HAS_RECEIVE_TASK_STATUS } from "@/pages/index/components/TaskPop/TaskList/types";
import baseModal from '@/lib/modal';

import BenefitsFailBgImg from '@/components/modals/modal_limited_time_benefits_award/images/benefits-fail-bg.png';
import BenefitsSuccessBgImg from '@/components/modals/modal_limited_time_benefits_award/images/benefits-success-bg.png';
import FailAuraImg from '@/components/modals/modal_limited_time_benefits_award/images/fail-aura.png';
import LightAwardCircleImg from '@/components/modals/modal_limited_time_benefits_award/images/light-award-circle.png';
import SuccessAuraImg from '@/components/modals/modal_limited_time_benefits_award/images/success-aura.png';
import LightCircleImg from '@/components/modals/modal_limited_time_benefits_select/images/light-circle.png';
import LightImg from '@/components/modals/modal_limited_time_benefits_select/images/light.png';
import LimitedTimeBenefitsImg from '@/components/modals/modal_limited_time_benefits_task/images/limited_time_benefits.png';
import { MODAL_ID } from '@/components/modals/types';
import { getLocalStorageWithExpiry, setLocalStorageWithExpiry } from "@/lib/utils/localStorage";
import { getTaskAppInstallMap, checkMultipleInstallApp, getExtraInfo } from "../task/helper";
import mx from '@ali/pcom-mx';
import tracker from "@/lib/tracker";
import { checkTaskFinished } from "@/pages/index/components/TaskPop/TaskList/util";
import { convertCentsToPoint, convertCentsToYuan } from "@/lib/utils/formatNumber";
import { LocalStorageKey } from "@/lib/utils/localStorage_constant";

export function handlerRewardItems(info: RewardItem) {
  let detail = Object.assign({}, info);
  switch (detail.mark) {
    case 'cash':
      detail.name = `${detail.randomAmount ? '最高' : ''}${convertCentsToYuan(Number(detail.amount))}${
        detail.name
      }`;
      break;
    case 'point':
      detail.name = `${detail.randomAmount ? '最高' : ''}${convertCentsToPoint(Number(detail.amount))}`;
      break;
    default:
      detail.name = `${detail.randomAmount ? '最高' : ''}${detail.amount}${detail.name}`;
      break;
  }
  return detail
}

// 默认的lottie
const DefaultLottieJson = [
  'https://image.uc.cn/s/uae/g/1y/animate/202409/d330f2/data.json',
  'https://image.uc.cn/s/uae/g/1y/animate/202409/335a16/data.json',
  'https://image.uc.cn/s/uae/g/1y/animate/202409/1cc7cd/data.json',
];


const getLottieImg = (url: string, length: number) => {
  return Array.from({ length }, (_, index) => {
    return `${url}/images/img_${index}.png`;
  })
}
const DefaultImgList = [
  // // 触发限时动画 用户折损太大 暂时取掉
  // ...getLottieImg('https://image.uc.cn/s/uae/g/1y/animate/202409/1cc7cd', 21),
  // 挂件
  ...getLottieImg('https://image.uc.cn/s/uae/g/1y/animate/202409/335a16', 4),
  // 奖励弹窗顶部
  ...getLottieImg('https://image.uc.cn/s/uae/g/1y/animate/202409/d330f2', 3),
  BenefitsFailBgImg,
  BenefitsSuccessBgImg,
  FailAuraImg,
  LightAwardCircleImg,
  SuccessAuraImg,
  LightCircleImg,
  LightImg,
  LimitedTimeBenefitsImg,
];

// 降级后需要加载的lottie
const LpLottieJson = [
  'https://image.uc.cn/s/uae/g/1y/animate/202409/866598/data.json',
];

const LpImgList = [
  // 挂件
  ...getLottieImg('https://image.uc.cn/s/uae/g/1y/animate/202409/866598', 21),
  BenefitsFailBgImg,
  BenefitsSuccessBgImg,
  FailAuraImg,
  LightAwardCircleImg,
  SuccessAuraImg,
  LightCircleImg,
  LightImg,
  LimitedTimeBenefitsImg,
];

// 限时任务资源预加载
export function preOnloadLimitResource(deviceLevel: string) {
  const requestKey = localStorage.getItem(LocalStorageKey.LIMIT_RESOURCE_REQUEST) ?? '0';
  // 避免请求失败
  if (String(requestKey) === '3') {
    return;
  }
  // 低端机
  if (deviceLevel === '0.0') {
    try {
      LpLottieJson.forEach(item => {
        preloadFileWithFetch(item)
      });
      preloadImg(LpImgList);
      localStorage.setItem(LocalStorageKey.LIMIT_RESOURCE_REQUEST, String(Number(requestKey) + 1));
    } catch (error) {
      // 捕获一下error
      console.error(error);
    }
    return;
  }

  // 非低端机
  try {
    DefaultLottieJson.forEach(item => {
      preloadFileWithFetch(item)
    });
    preloadImg(DefaultImgList);
    localStorage.setItem(LocalStorageKey.LIMIT_RESOURCE_REQUEST, String(Number(requestKey) + 1));
  } catch (error) {
    // 捕获一下error
    console.error(error);
  }
}

/**
 * 解决登录导致，多次刷新刷导致弹窗多次触发的问题
 */
export const removeLimitDialog = (key: MODAL_ID) => {
  const baseCurrentOpenModal = baseModal.getCurrentOpenModalObj();
  const result = baseCurrentOpenModal[key];
  switch (key) {
    case MODAL_ID.LIMITED_TIME_BENEFITS_SELECT:
      baseModal.removeCacheModal(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
      !getLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT) && localStorage.removeItem(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
      break;
    case MODAL_ID.LIMITED_TIME_BENEFITS_TASK:
      baseModal.removeCacheModal(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
      !getLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_TASK) && localStorage.removeItem(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
      break;
    case MODAL_ID.LIMITED_TIME_BENEFITS_AWARD:
      baseModal.removeCacheModal(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD);
      break;
    default:
      break;
  }
  return !!result;
}

export const checkInstallForLimitTask = async (taskList: TaskInfo[]) => {
  let list: TaskInfo[] = [];
  const needCheckAppInstall = new Map();
  taskList.forEach(item => {
    const result = getTaskAppInstallMap(item, false);
    result && (needCheckAppInstall[result] = 1);
  });
  const checkAPPInstallResult = await checkMultipleInstallApp(Object.keys(needCheckAppInstall));

  for (const task of taskList) {
    // 任务有在做
    if (HAS_RECEIVE_TASK_STATUS?.includes(task?.state)) {
      list.push(task);
      continue;
    }

    const { showUninstallApp = false } = getExtraInfo(task);
    const pkg = getTaskAppInstallMap(task, true);
    const display = pkg ? !!checkAPPInstallResult[pkg]?.install : true;

    if (showUninstallApp && !display || !showUninstallApp && display) {
      list.push(task);
      continue;
    }
  }
  return list;
};


export const dealWithFirstExtraTask = async (taskInfo: TaskInfo, preTaskList: TaskInfo[], limitNum: number, curTime: number) => {
  if (!preTaskList?.length) {
    return [];
  }

  // 缓存的已过期或则缓存没有 则重新取通过安装检测条件的
  const passCheckInstallList = await checkInstallForLimitTask(preTaskList);
  // 先找到额外池已经完成的任务
  const findHasFinishTask = passCheckInstallList.filter(item => checkTaskFinished(item));
  // 需要展示的额外池任务列表
  const showExtraList = [...passCheckInstallList]?.splice(0, limitNum - findHasFinishTask.length);
  showExtraList.push(...findHasFinishTask);

  if (showExtraList?.length) {
    setLocalStorageWithExpiry(`time_limit_day_${taskInfo?.id}`, showExtraList, curTime);
  }
  tracker.log({
    category: 161,
    w_succ: 1,
    msg: '展示额外池任务列表',
    c1: `${limitNum}`,
    c2: `${taskInfo?.id}`,
    bl1: JSON.stringify(showExtraList),
    bl2: JSON.stringify(taskInfo),
  });
  return showExtraList;
};

/**
 * 获取可以展示的限时任务列表
 */
export const getShowLimitTaskList = () => {
  const timeLimitTask = mx.store.get('timeLimitTask');
  const { preTaskList = [], preExtraTaskList = [], extraTaskNumLimit, openExtraTask } = timeLimitTask;
  const limitTimeExtraPoolEvents: string[] = mx.store.get('app.mainInfo.frontData')?.limitTimeExtraPoolEvents || [];

  // 未开启额外池  或则 没有额外池任务 或则 限制了额外池数量为0
  if (!openExtraTask || !preExtraTaskList?.length) {
    return preTaskList;
  } else if (openExtraTask && !extraTaskNumLimit) {
    return preTaskList?.filter((task: TaskInfo) => !limitTimeExtraPoolEvents?.includes(task?.event));
  }
  // 开启额外池
  const showList = preTaskList?.filter((task: TaskInfo) => {
    const showExtra = preExtraTaskList?.some((extraTask: TaskInfo) => extraTask.id === task.id);
    // 是额外池的但是没通过检测安装的
    if (limitTimeExtraPoolEvents?.includes(task?.event) && !showExtra) {
      return false;
    }
    // 基础池 & 通过检测安装的
    return true;
  })

  return showList;
};

