import { RewardItem, TaskInfo } from "@/pages/index/components/TaskPop/TaskList/types";

/**
 * 限时任务
 * cycleTotalDay: 周期内累计完成的天数
 * preTaskList: 限时任务
 * needReceive: 是否领取过任务, true: 表示没有领取，false: 表示领取过了
 * target: 周期内累积需要完成的天数
 */
export interface ITimeLimitTaskState extends Pick<TaskInfo, 'cycleTotalDay' | 'preTaskList' | 'needReceive' | 'endTime' | 'rewardItems' | 'target' | 'id' | 'name'> {
  /** 周期 */
  dayRange: number;
  /** 是否显示显示任务, 用于顶部面板展示  */
  showLimitTask: boolean;
  /** 用于展示选择奖励，处理合并后的 */
  showRewardList: RewardItem[];

  /** 有签到任务 */
  hasSignTask: boolean;

  /** 有施肥任务 */
  hasWaterTask: boolean;

  /** 是否有限时任务 */
  hasLimitTask: boolean;
  
  /** 是否有限时任务 */
  hasLimitTaskDialog: boolean;

  /** 限时任务结束 */
  isLimitTaskEnd: boolean;

  /** 最多要求额外任务数量  */
  extraTaskNumLimit: number;

  /** 开启额外任务池 */
  openExtraTask: boolean;
  /** 通过安装检测的任务列表 */
  preExtraTaskList: TaskInfo[];
  timeLimitTask: TaskInfo[];
}
