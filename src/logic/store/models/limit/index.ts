import mx from '@ali/pcom-mx';
import config from '@/config';
import network from '@/lib/network';
import { getErrorInfo } from '../utils';
import tracker from '@/lib/tracker';
import MxModel from '@/logic/store/models';
import { ITimeLimitTaskState } from './types';
import baseModal from '@/lib/modal';
import { RewardItem, TASK_EVENT_TYPE, TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { MODAL_ID } from '@/components/modals/types';
import { calculateDaysBetweenDates, uniqueDateFormat } from '@/lib/utils/date';
import { isUc } from '@/lib/universal-ua';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { spamSign, ucparams } from '@/lib/ucapi';
import { getLocalStorageWithExpiry, setLocalStorageWithExpiry } from '@/lib/utils/localStorage';
import { MainAPI } from '@/logic/type/event';
import { StoreName } from '@/logic/type/store';
import dispatch from '@/logic/store';
import { handlerRewardItems, preOnloadLimitResource, removeLimitDialog, dealWithFirstExtraTask } from './utils';
import { checkTaskFinished } from '@/pages/index/components/TaskPop/TaskList/util';
import stat from '@/lib/stat';
import { getExtraInfo } from '../task/helper';
import { checkAssistEntry } from '../app/new_year_time';

const defaultState: ITimeLimitTaskState = {
  dayRange: 0, // 周期
  cycleTotalDay: 0,
  preTaskList: [],

  needReceive: true,
  showLimitTask: false,
  id: 0,
  name: '',
  target: 0,
  rewardItems: [],
  endTime: 0,
  showRewardList: [],
  hasSignTask: false,
  hasWaterTask: false,
  hasLimitTask: false,
  hasLimitTaskDialog: false,
  isLimitTaskEnd: false,
  openExtraTask: false,
  extraTaskNumLimit: 0,
  timeLimitTask: [],
  preExtraTaskList: [],
};

class TimeLimitTask extends MxModel {
  path: string;
  state: ITimeLimitTaskState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'timeLimitTask';
    this.init();
  }
  init(initData: Partial<ITimeLimitTaskState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<ITimeLimitTaskState>) {
    Object.keys(payload).forEach((key) => {
      mx.store.update(`${this.path}.${key}`, payload[key]);
    });
  }

  async decisionTaskDetail(task: TaskInfo, timestamp: number) {
    const { preTaskList = [], cycleTotalDay = 0, target, beginTime, endTime, rewardItems } = task;
    // 周期天数
    const dayRange = calculateDaysBetweenDates(beginTime, endTime);

    let info: {
      dayRange: number;
      showLimitTaskPanel: boolean;
      taskEnd: boolean;
      showRewardList: RewardItem[];
      hasSignTask: boolean;
      hasWaterTask: boolean;
      openExtraTask: boolean;
      extraTaskNumLimit: number;
    } = {
      dayRange,
      showLimitTaskPanel: false, // 是否展示顶部面板
      taskEnd: false, // 任务结束
      showRewardList: [],
      hasSignTask: false,
      hasWaterTask: false,
      openExtraTask: false, // 开启额外任务池
      extraTaskNumLimit: 0, // 最多要求额外任务数量
    };

    const { preSplitExtraTask = false, preExtraTaskNum = 0 } = getExtraInfo(task);
    info.openExtraTask = preSplitExtraTask;
    info.extraTaskNumLimit = preExtraTaskNum;

    // 当前时间剩余周期天数
    const endDayRange = calculateDaysBetweenDates(timestamp, endTime);
    if (task.needReceive) {
      // 未领取任务
      info.showLimitTaskPanel = false;
    } else if (dayRange <= 0 || preTaskList?.length <= 0) {
      // 任务条件不符合
      info.showLimitTaskPanel = false;
    } else if (endDayRange + cycleTotalDay < target) {
      // 剩余周期天数 不满足条件
      info.showLimitTaskPanel = false;
      info.taskEnd = true;
    } else {
      info.showLimitTaskPanel = true;
    }

    // 活动满足完成条件
    if (cycleTotalDay === target) {
      info.showLimitTaskPanel = false;
      info.taskEnd = true;
    }

    // 活动周期结束
    if (Number(endTime) <= Number(timestamp)) {
      info.taskEnd = true;
      info.showLimitTaskPanel = false;
    }

    // 任务触发数据处理
    rewardItems.forEach((item) => {
      let detail = handlerRewardItems(item);
      info.showRewardList.push(detail);
    });

    // 签到任务
    const sign = preTaskList.filter((item) => item.event === TASK_EVENT_TYPE.UCLITE_SIGN);
    if (sign.length && !checkTaskFinished(sign[0])) {
      info.hasSignTask = true;
    }

    // 施肥任务
    const water = preTaskList.filter((item) => item.event === TASK_EVENT_TYPE.TASK_SIGN_SUB_STORE);
    if (water.length && !checkTaskFinished(water[0])) {
      info.hasWaterTask = true;
    }
    return info;
  }

  /**
   * 获取限时任务信息
   */
  async getTaskDetail() {
    // 没有绑定淘宝
    const bindTaobao = mx.store.get('user.bindTaobao');
    // 没有选种||果树满级后，不触发限时任务
    const hasSeed = mx.store.get(StoreName.SeedImg);
    const canExchange = mx.store.get(StoreName.CanExchange);
    const kps = mx.store.get('user.kps');
    const deviceLevel = mx.store.get(StoreName.DeviceLevel);
    const limitTimeExtraPoolEvents: string[] = mx.store.get('app.mainInfo.frontData')?.limitTimeExtraPoolEvents || [];
    const isAssistEntry = checkAssistEntry();
    const {curTime: timestamp } = mx.store.get('task');
    // 限时任务
    const timeLimitTaskData = mx.store.get('timeLimitTask');
    const timeLimitTask = timeLimitTaskData?.timeLimitTask || [];
    const task = timeLimitTask[0];
    if (!task) {
      return;
    }
    // 接口数据下发
    stat.custom('hit-limit-task', {
      c: 'pop',
      d: 'award',
      status: 'data-delivery',
    });

    if (!bindTaobao || !hasSeed || canExchange) {
      tracker.log({
        category: 142, // 系统自动生成，请勿修改
        msg: '命中限时任务，不触发', // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 0, // 用于计算"触发成功";可选值为0或1
        c1: kps,
        c2: String(!!task?.rewardItems?.length),
        bl1: JSON.stringify({
          bindTaobao,
          hasSeed,
          canExchange,
        }),
      });
      // 接口数据下发-不满足条件
      stat.custom('hit-limit-task', {
        c: 'pop',
        d: 'award',
        status: 'task-miss',
      });
      this.set({
        hasLimitTask: false,
        hasLimitTaskDialog: false,
        isLimitTaskEnd: false,
      });
      return;
    }
    // 资源预加载
    preOnloadLimitResource(deviceLevel);
    let { endTime, cycleTotalDay, needReceive, preTaskList = [], target, prizes = [] } = task;
    const {
      dayRange,
      showRewardList,
      showLimitTaskPanel,
      taskEnd,
      hasSignTask,
      hasWaterTask,
      openExtraTask,
      extraTaskNumLimit,
    } = await dispatch.timeLimitTask.decisionTaskDetail(task, timestamp);

    // 额外池任务列表
    let extraPreTaskList: TaskInfo[] = [];
    // 开启额外池任务 并且 有额外池数量限制
    if (openExtraTask && extraTaskNumLimit) {
      extraPreTaskList = preTaskList?.filter((preTask) => limitTimeExtraPoolEvents?.includes(preTask?.event));
    }

    const preExtraTaskList = await dealWithFirstExtraTask(task, extraPreTaskList, extraTaskNumLimit, timestamp);

    this.set({
      ...task,
      dayRange,
      preTaskList,
      showLimitTask: showLimitTaskPanel,
      showRewardList,
      hasSignTask,
      hasWaterTask,
      hasLimitTask: !(!preTaskList.length || dayRange <= 0),
      hasLimitTaskDialog: !!this.needShowLimitDialog({
        taskEnd,
        showLimitTaskPanel,
        needReceive
      }),
      isLimitTaskEnd: taskEnd,
      extraTaskNumLimit,
      openExtraTask,
      preExtraTaskList,
    });

    // 周期小于一天的，不触发
    if (!preTaskList.length || dayRange <= 0) {
      // 接口数据下发-不满足条件
      tracker.log({
        category: 142, // 系统自动生成，请勿修改
        msg: '命中限时任务，条件不满足', // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 0, // 用于计算"触发成功";可选值为0或1
        c1: kps,
        c2: String(!!task?.rewardItems?.length),
        bl1: JSON.stringify(task),
      });
      stat.custom('hit-limit-task', {
        c: 'pop',
        d: 'award',
        status: 'task-miss-condition',
      });
      return;
    }
    let hasShow = removeLimitDialog(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
    // 限时任务选择奖励后打开限时任务面板(在助力弹窗之后) 一天一次, 助力不弹
    if (!isAssistEntry && !hasShow && showLimitTaskPanel && !getLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_TASK)) {
      baseModal.open(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
      // 宏任务执行，逻辑放在一起，方便后期处理弹窗优先级
      setTimeout(() => {
        setLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_TASK, true);
      }, 1000);
    }

    // 活动周期结束
    if (taskEnd) {
      const awardFlag = cycleTotalDay === target;
      tracker.log({
        category: 142, // 系统自动生成，请勿修改
        msg: '命中限时任务，周期结束', // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 1, // 用于计算"触发成功";可选值为0或1
        c1: kps,
        c2: String(!!task?.rewardItems?.length),
        c3: String(!!task?.prizes?.length),
        bl1: JSON.stringify(task),
        bl2: JSON.stringify(task?.prizes ?? [])
      });
      stat.custom('hit-limit-task', {
        c: 'pop',
        d: 'award',
        status: 'task-end',
        flag: awardFlag ? 1 : 0,
      });
      // 已经展示过了，就不展示了
      if (getLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD)) {
        return;
      }

      hasShow = removeLimitDialog(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD);
      if (hasShow) {
        return;
      }
      const successDetail = awardFlag
        ? handlerRewardItems(prizes?.[0]?.rewardItem)
        : {
          name: '',
          icon: '',
          mark: '',
        };
      const dialogDetail = !awardFlag ? showRewardList[0] : successDetail;
      // 限时任务挑战成功&失败
      baseModal.open(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD, {
        awardFlag,
        giftName: dialogDetail?.name || '',
        giftUrl: dialogDetail?.icon || '',
        mark: dialogDetail?.mark || '',
        titleText: awardFlag
          ? `获得${dayRange}天限时福利`
          : `${uniqueDateFormat(Number(endTime))}日前不足${target}天完成任务，下次再来`,
        lpDisable: deviceLevel === '0.0',
      });
      // 宏任务执行，逻辑放在一起，方便后期处理弹窗优先级
      setTimeout(() => {
        setLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD, true);
      }, 1000);
      return;
    }

    hasShow = removeLimitDialog(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
    // 领取过任务
    if (!isAssistEntry && !hasShow && needReceive && !getLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT)) {
      // 命中限时任务, 延迟渲染，等待资源加载完成
      setTimeout(() => {
        baseModal.open(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
        setLocalStorageWithExpiry(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT, true);
        mx.event.emit(MainAPI.LimitTaskPedantsHide);
      }, 200);
      stat.custom('hit-limit-task', {
        c: 'pop',
        d: 'award',
        status: 'task-select',
      });
    } else if (needReceive) {
      mx.event.emit(MainAPI.LimitTaskPedantsShow);
      stat.custom('hit-limit-task', {
        c: 'pop',
        d: 'award',
        status: 'task-select',
      });
    }
    stat.custom('hit-limit-task', {
      c: 'pop',
      d: 'award',
      status: 'task-doing',
    });
    tracker.log({
      category: 142, // 系统自动生成，请勿修改
      msg: '命中限时任务，周期进行中', // 将根据msg字段聚合展示在平台的top上报内容中
      w_succ: 1, // 用于计算"触发成功";可选值为0或1
      c1: kps,
      c2: String(!!task?.rewardItems?.length),
      bl1: JSON.stringify(task),
    });
  }

  /**
   * 选择挑战奖励
   */
  async selectLimitAward(rewardId) {
    if (!isUc) {
      return;
    }
    const { kps } = mx.store.getStore().user;
    const taskDetail = mx.store.getStore().timeLimitTask;
    const preExtraTaskList = taskDetail.preExtraTaskList;
    const selectLimitAwardMonitor = tracker.Monitor(143);
    const extParams = {
      preExtraTaskRealNum: preExtraTaskList?.length,
    };
    const ext = Object.keys(extParams)
      ?.map((key) => `${key}:${extParams[key]}`)
      ?.join(',');
    const requestId = geneTaskRequestId();
    try {
      const taskId = taskDetail.id;
      const ut = await dispatch.app.getAppUtRes();
      const signOriText = `${kps || ''}${ut}${config.appId}${taskId}${requestId}`;
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      const sign = await spamSign({ text: signOriText, salt });
      const selectLimitAwardRes = await network.post(`${config.farmHost}/task/receiveTask`, {
        appId: config.appId,
        kps,
        requestId,
        taskId,
        rewardId,
        sign,
        ext,
      });
      selectLimitAwardMonitor.success({
        msg: '选择限时福利挑战奖励成功',
        bl1: JSON.stringify(selectLimitAwardRes),
        w_trace_reqid: requestId
      });

      // 更新领取状态，更新接口
      await dispatch.task.queryTaskList();
      return { code: 'ok', data: selectLimitAwardRes };
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      selectLimitAwardMonitor.fail({
        msg: `选择限时福利挑战奖励失败-${msg}`,
        c1: errCode,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      console.log(error);
      return { code: 'error', data: error };
    }
  }

  /**
   * 锁定限时任务弹窗
   */
  lockLimitDialog() {
    const baseCurrentOpenModal = baseModal.getCurrentOpenModalObj();
    if (Object.keys(baseCurrentOpenModal).length) {
      Object.keys(baseCurrentOpenModal).forEach((item: MODAL_ID) => {
        switch (item) {
          case MODAL_ID.LIMITED_TIME_BENEFITS_SELECT:
            baseModal.removeCacheModal(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
            localStorage.removeItem(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
            break;
          case MODAL_ID.LIMITED_TIME_BENEFITS_TASK:
            baseModal.removeCacheModal(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
            localStorage.removeItem(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
            break;
          case MODAL_ID.LIMITED_TIME_BENEFITS_AWARD:
            baseModal.removeCacheModal(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD);
            break;
          default:
            break;
        }
      });
    }
  }

  /**
   * 是否需要展示限时任务弹窗
   */
  needShowLimitDialog(data: { 
    showLimitTaskPanel: boolean;
    taskEnd: boolean;
    needReceive: boolean;
  }) {
    const { showLimitTaskPanel, taskEnd, needReceive} = data;
    // 触发限时任务挂件
    if (needReceive) {
      return !localStorage.getItem(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
    }
    // 做任务面板
    if (showLimitTaskPanel) {
      return !localStorage.getItem(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
    }
    // 限时任务结束
    if (taskEnd) {
      return !localStorage.getItem(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD);
    }
    return false;
  }

  /**
   * 限时任务弹窗关闭，走一遍自动拉起任务面板的逻辑
   */
  closeLimitDialog() {
    this.set({
      hasLimitTaskDialog: false,
    });
    dispatch.app.openTaskPop();
  }
}

const appStore = new TimeLimitTask();
export default appStore;
