export enum RES_CODES {
  UCFARM_TASK_WINDOW = 'ucfarm_task_window',
  TASK_BOTTOM_BANNER = 'farm_main_taskpanel_bottom',
  /** 签到后开启套娃的entry */
  OPEN_TASK_DIALOG_ENTRY = 'open_task_dialog_entry',
  /** 任务列表完成开启套娃 */
  OPEN_TASK_LIST_MATRIOSKA = 'task_list_matrioska',
  /** 搜索渠道ID */
  FARM_SEARCH_CHANNEL_CONFIG = 'farm_search_channel_config',
  /* TANX下单任务弹窗频控配置 */
  TANX_ORDER_TASK_DIALOG = 'tanx_order_task_dialog',
  /* 换量回调延迟 */
  BBNC_DELAYED_CALLBACK = 'bbnc_delayed_callback',
  /** 重定向 */
  CMS_REDIRECT_CODE = 'bbnc_redirect_flz',
  /** 运营合作诉求,APP 检测安装 */
  APP_INSTALL_LIST= 'app_install_check',
  /** 汇川广告资源配置 */
  HU_AD_CONFIG = 'bbnc_huichuan_advertising_config',
  /** 资源位套娃弹窗 */
  OPEN_RESOURCE_MATRIOSKA = 'task_resource_matrioska',
  /** 自动签到功能开关 */
  BBNC_AUTO_SIGN_SWITCH = 'bbnc_auto_sign_switch',
  /** 隐藏帮帮种入口开关 */
  BBNC_BBZ_TASKCARD_HIDE = 'bbnc_bbz_taskcard_hide',
  /* 是否开启翻倍卡入口 */
  ENABLE_FDCARD = 'bbnc_enable_fdcard',
  /* 是否必中翻倍卡 */
  BBNC_GUARANTEED_FDCARD = 'bbnc_guaranteed_fdcard'

}

export interface ICmsResData<T> {
  priority: number;
  sumInfo: string;
  items: T[];
  data_id: string;
  test_data_id: string;
  test_id: string;
  data_type: string;
  start_time: string;
  end_time: string;
  app_key: string;
  img_pack: any;
  chk_sum: any;
  extra_data: any;
}

export interface ICmsState extends ICmsRes {
  initd: boolean;
}

export type ICmsRes = {
  [key in RES_CODES]: Array<ICmsResData<unknown>>;
}
export interface ITaskBottomBanner {
  banner_list: BannerItem[];
}

export interface ITanxOrderTaskDialog {
  countdownSeconds: string;
  maxDailyDisplayCount: string;
  showTimeGap: string;
}

export interface BannerItem {
  id: string;
  title: string;
  sub_title: string;
  icon: string;
  dplink: string;
  download_url: string;
  app: string;
  is_float: string;
  pkgName: string;
  taskId: string;
}

export interface HcAdCmsConfig {
  /** 汇川二方APP下载任务slotId列表 */
  huichuanCorpTaskSlotList: string[];
  /** 汇川三方游戏任务slotId列表 */
  huichuanGameTaskSlotList: string[];
  /** 汇川品牌任务广告slotId */
  huichuanBrandTaskSlotId: string;
  /** 汇川竞价广告缓存时间（小时） */
  huichuanBidAdCacheHr: number;
  /** 汇川品牌广告缓存时间（小时） */
  huichuanBrandAdCacheHr: number;
}
