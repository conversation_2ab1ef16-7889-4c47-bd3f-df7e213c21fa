import mx from '@ali/pcom-mx';
import MxModel from '@/logic/store/models';
import { ICmsRes, ICmsState, RES_CODES } from './typings';
import tracker from '@/lib/tracker';
import { getCmsData } from '@/api/home';
import { getErrorInfo } from '../utils';
import config from '@/config';

const defaultData = Object.keys(RES_CODES).reduce((res, next) => {
  res[RES_CODES[next]] = {
    items: [],
  };
  return res;
}, {});

class Cms extends MxModel {
  path = 'cms';
  state: ICmsState | {};

  constructor() {
    super();
    this.state = {
      initd: false,
    };
    this.init(defaultData);
  }

  init(initData: Partial<ICmsState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }

  get(payload: any) {
    return mx.store.get(`${this.path}.${payload}`);
  }

  update(payload: Partial<ICmsState>) {
    // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
    Object.entries(payload).forEach(([key, value]) => {
      this.safeUpdate(`${this.path}.${key}`, value);
    });
  }

  setCmsData(cmsResData: ICmsRes | null) {
    if (cmsResData && Object.keys(cmsResData).length) {
      this.update({
        ...cmsResData,
        initd: true
      });
    }
  }

  async initRes() {
    const { initd } = mx.store.getStore().cms;
    if (initd) {
      return;
    }
    try {
      const resp = await getCmsData();
      this.update(resp);
      tracker.log({
        category: 145,
        msg: '查询成功',
        w_succ: 1,
        c1: Object.values(RES_CODES).join(','),
        bl1: JSON.stringify(resp),
      });
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      tracker.log({
        category: 145,
        msg: `查询失败-${errCode} `,
        w_succ: 0,
        c1: Object.values(RES_CODES).join(','),
        bl1: msg,
      });
    }
  }

  /**
   * 是否展示套娃弹窗
   * list: 任务列表
   * permanent_ad: 首页常驻资源
   * taskBubble: 气泡组件
   */
  getCmsShowDialogValue(resource: 'permanent_ad' | 'list' | 'taskBubble' | string) {
    const cmsStore = mx.store.getStore()?.cms;
    const resourceConfig = cmsStore[RES_CODES.OPEN_RESOURCE_MATRIOSKA]?.items[0]?.resourConfig ?? [];
    // 任务列表
    if (resource.includes('list')) {
      return cmsStore[RES_CODES.OPEN_TASK_LIST_MATRIOSKA]?.items?.[0]?.openMatrioska === '1';
    }

    // 首页常驻激励资源位
    if (resource.includes('permanent_ad')) {
      const data = resourceConfig?.find((item) => config.homeAdResourceCode.includes(item.resourceCode)) ?? {};
      return data?.open === '1';
    }
    return false;
  }
}

const cmsStore = new Cms();
export default cmsStore;
