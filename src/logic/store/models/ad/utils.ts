import { getParam } from '@/lib/qs';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import { generateCacheKey } from '@/logic/hc-ad/utils';

const debug = getParam('debug');
export const getCorpTaskFromCache = (slotIds: string[]) => {
  const cacheTaskCorpAd = {};
  slotIds.forEach((slotId) => {
    const CACHE_KEY = generateCacheKey(slotId);
    // 判断本地广告缓存标识，缓存时间内直接返回缓存数据
    const currentTs = new Date().getTime();
    const hcAdCache = localStorage.getItem(CACHE_KEY);
    if (hcAdCache) {
      const cacheInfo = JSON.parse(hcAdCache);
      if (cacheInfo.expireTime > currentTs) {
        cacheInfo.ad.from_cache = true;
        cacheTaskCorpAd[slotId] = cacheInfo.ad;
      } else {
        localStorage.removeItem(CACHE_KEY);
      }
    }
  });
  debug && console.info('[hc-ad] 本地缓存的汇川广告:', cacheTaskCorpAd);
  return cacheTaskCorpAd;
};

/** 获取当前缓存匹配的信息 */
export const getTaskBrandFromStore = () => {
  const result = localStorage.getItem(LocalStorageKey.TASK_BRAND_BY_ACCOUNT_ID);
  if (result) {
    const parsedResult = JSON.parse(result);
    // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
    return new Map(Object.entries(parsedResult));
  }
  return new Map();
}

export const generateHcAdBrandKey = (adId: string) => {
  return `ad_brand_${adId}_completed`;
}
