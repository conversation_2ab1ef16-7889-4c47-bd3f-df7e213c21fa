import mx from '@ali/pcom-mx';
import MxModel from '@/logic/store/models';
import dispatch from '@/logic/store';
import { HcAdState } from './type';
import { HcAdCmsConfig } from '../cms/typings';
import { generateHcAdBrandKey, getCorpTaskFromCache, getTaskBrandFromStore } from './utils';
import { hcAdHelper } from '@/logic/hc-ad';
import { generateCacheKey, hcAdStorageHandler } from '@/logic/hc-ad/utils';
import { isIOS } from '@/lib/universal-ua';
import { queryApp } from '@/lib/ucapi';
import tracker from '@/lib/tracker';
import { getParam } from '@/lib/qs';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import { TASK_EVENT_TYPE, TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import stat from '@/lib/stat';
import { getTaskAward } from '@/pages/index/components/TaskPop/TaskList/util';

const debug = getParam('debug');
const defaultState: HcAdState = {
  taskBrandAd: null,
  taskCorpAd: {},
  taskBrandAdInit: false,
  taskCorpAdInit: false,
  taskCorpAdStoreAccountId: getTaskBrandFromStore(),
};


class HcAd extends MxModel {
  path: string;
  state: HcAdState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'hcAd';
    this.init();
  }
  init(initData: Partial<HcAdState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<HcAdState>) {
    Object.keys(payload).forEach((key) => {
      this.safeUpdate(`${this.path}.${key}`, payload[key]);
    });
  }

  /**
   * 请求所有汇川广告
   */
  async fetchAllTaskCorpAd(params: { forceUpdate: boolean}) {
    const {forceUpdate = false } = params;
    const { bbnc_huichuan_advertising_config } = mx.store.get('cms');
    const cmsConfig: HcAdCmsConfig = bbnc_huichuan_advertising_config.items[0] ?? {};
    const {huichuanCorpTaskSlotList = [], huichuanGameTaskSlotList = []} = cmsConfig;
    const slotList = [
      ...huichuanCorpTaskSlotList,
      ...huichuanGameTaskSlotList,
    ];
    if (!slotList.length) {
      return;
    }
    // 先从缓存中拿一次渲染，再发请求
    const cacheTaskCorpAd = getCorpTaskFromCache(slotList);
    if (Object.keys(cacheTaskCorpAd).length) {
      const currAdStore = mx.store.get('hcAd');
      dispatch.hcAd.set({
        taskCorpAd: { ...currAdStore.taskCorpAd, ...cacheTaskCorpAd },
      });
    }
    const { huichuanBidAdCacheHr } = cmsConfig;
    let fetchSlotKey: Array<Promise<any>> = [];
    slotList.forEach((slotId) => {
      fetchSlotKey.push(
        hcAdHelper.requestHuichuanAd({
          slotId,
          forceUpdate,
          cacheHourTime: Number(huichuanBidAdCacheHr ?? 0),
        }),
      );
    });

    try {
      const respData = await Promise.all(fetchSlotKey).then((resp) => resp);
      debug && console.info('[hc-ad] 汇川广告请求返回数组：', respData, slotList);
      const taskCorpAd = {};
      respData.forEach((item) => {
        if (item) {
          taskCorpAd[item.slot_id] = item;
        }
      });

      const currAdStore = mx.store.get('hcAd').taskCorpAd;
      dispatch.hcAd.set({
        taskCorpAd: { ...currAdStore, ...taskCorpAd },
        taskCorpAdInit: true,
      });
      const adMonitor = tracker.Monitor(181);
      const slot_keys = Object.keys(taskCorpAd);
      const fillSlots = slot_keys.join('`'); // 填充 slot_id 拼接字符串
      const originSlots = slotList.join('`'); // 请求 slot_id 拼接字符串

      if (slot_keys.length > 0) {
        // 有填充
        let fillFactSubmitted = false; // 表示新请求是否填充
        const cache_slots: string[] = []; // 本地缓存 slot_id 数组
        const new_req_slots: string[] = []; // 新请求 slot_id 数组
        slot_keys.forEach((slotKey) => {
          if (taskCorpAd[slotKey].from_cache) {
            cache_slots.push(slotKey);
          } else {
            new_req_slots.push(slotKey);
          }
        });
        const cacheSlots = cache_slots.join('`'); // 本地缓存 slot_id 拼接字符串
        const newFillSlots = new_req_slots.join('`'); // 新请求 slot_id 拼接字符串
        if (new_req_slots.length > 0) {
          adMonitor.success({
            msg: '新请求有填充',
            c1: fillSlots,
            c2: originSlots,
            c3: cacheSlots,
            c4: newFillSlots,
          });
          fillFactSubmitted = true;
          stat.custom('page_huichuan_ad_fetch', {
            is_fill: 1,
            fill_slots: fillSlots, // 填充的
            origin_slots: originSlots, // 请求的
            new_fill_slot: newFillSlots, // 新请求填充的
            cache_slots: cacheSlots, // 缓存的
          });
          debug && console.info('[hc-ad] 页面级别请求-新请求有填充');
        }
        if (!fillFactSubmitted) {
          stat.custom('page_huichuan_ad_fetch', {
            is_fill: 0,
            fill_slots: fillSlots,
            origin_slots: originSlots,
            cache_slots: cacheSlots,
          });
          if (slot_keys.length === slotList.length) {
            // 没有新请求填充的情况下，页面填充的slot跟发起slot一样，表示都是缓存，没有发起新请求，不打点
            adMonitor.success({
              msg: '无新请求-有广告缓存',
              c1: fillSlots,
              c2: originSlots,
              c3: cacheSlots,
            });
            debug && console.info('[hc-ad] 页面级别请求-无新请求-有广告缓存');
          } else {
            adMonitor.success({
              msg: '新请求无填充-有广告缓存',
              c1: fillSlots,
              c2: originSlots,
              c3: cacheSlots,
            });
            debug && console.info('[hc-ad]页面级别请求-新请求无填充-有广告缓存');
          }
        }
      } else {
        adMonitor.fail({
          msg: '新请求无填充-无广告缓存',
          c1: fillSlots,
          c2: originSlots,
        });
        stat.custom('page_huichuan_ad_fetch', {
          is_fill: 0,
          fill_slots: fillSlots,
          origin_slots: originSlots,
        });
        debug && console.info('[hc-ad]页面级别请求-新请求无填充-无广告缓存');
      }
    } catch (error) {
      debug && console.info('[hc-ad] 请求汇川广告时发生错误:', error);
    }
  }
  /**
   * 任务完成，更新效果广告
   */
  async fetchTaskCorpAd(params: { forceUpdate: boolean; slotId: string }) {
    const {forceUpdate = false, slotId} = params;
    const { bbnc_huichuan_advertising_config } = mx.store.get('cms');
    const cmsConfig: HcAdCmsConfig = bbnc_huichuan_advertising_config.items[0] ?? {};
    const { huichuanBidAdCacheHr } = cmsConfig;
    if (slotId) {
      const result = await hcAdHelper.requestHuichuanAd({
        slotId: slotId,
        forceUpdate,
        cacheHourTime: Number(huichuanBidAdCacheHr ?? 0),
      });
      if (result) {
        const currAdStore = mx.store.get('hcAd').taskCorpAd;
        dispatch.hcAd.set({
          taskCorpAd: { ...currAdStore, [slotId]: result },
        });
      }
      return result;
    }
  }

  /**
   * 请求品牌广告
   */
  async fetchTaskBrandAd(params: { forceUpdate: boolean }) {
    const { forceUpdate = false } = params;
    const { bbnc_huichuan_advertising_config } = mx.store.get('cms');
    const cmsConfig: HcAdCmsConfig = bbnc_huichuan_advertising_config.items[0] ?? {};
    const { huichuanBrandTaskSlotId, huichuanBrandAdCacheHr = 0 } = cmsConfig;
    if (!huichuanBrandTaskSlotId) {
      return;
    }
    const result = await hcAdHelper.requestHuichuanAd({
      slotId: huichuanBrandTaskSlotId,
      forceUpdate,
      cacheHourTime: Number(huichuanBrandAdCacheHr ?? 0),
    });
    // 判断是否该品牌广告ad_id是否已完成
    if (result?.ad[0]?.ad_id) {
      const key = generateHcAdBrandKey(result.ad[0].ad_id);
      debug && console.info(`[hc-ad]检查该品牌广告ad_id: ${result?.ad[0]?.ad_id}今天是否已经完成过了`);
      const completed = await hcAdStorageHandler.get(key);
      if (completed) {
        debug && console.log(`该品牌广告ad_id: ${result?.ad[0]?.ad_id}今天已经完成过了`);
        localStorage.removeItem(generateCacheKey(huichuanBrandTaskSlotId));
        dispatch.hcAd.set({
          taskBrandAd: null,
          taskBrandAdInit: true,
        });
      } else {
        dispatch.hcAd.set({
          taskBrandAd: result,
          taskBrandAdInit: true,
        });
        debug && console.info(`[hc-ad] 该品牌广告ad_id: ${result?.ad[0]?.ad_id}今天还没完成`);
      }
    }
  }
  /**
   * 品牌曝光
   */
  async taskBrandExposure(task: TaskInfo) {
    const { taskBrandAd } = mx.store.get('hcAd');
    taskBrandAd?.ad[0] && hcAdHelper.hcAdExpose(taskBrandAd.ad[0]);
    stat.custom('task_hcad_display', {
      task_id: task.id,
      task_name: task.name,
      account_id: task.accountId,
      slot_id: task.slotId,
      ad_type: task.event,
      taskclassify: task?.taskClassify,
      groupcode: task?.groupCode,
      award_amount: getTaskAward(task),
    });
  }
  /**
   * 品牌曝光
   */
  async taskBrandClick(task: TaskInfo) {
    const { taskBrandAd } = mx.store.get('hcAd');
    taskBrandAd?.ad[0] && (await hcAdHelper.hcAdClick(taskBrandAd.ad[0]));
    stat.custom('task_hcad_click', {
      task_id: task.id,
      task_name: task.name,
      account_id: task.accountId,
      slot_id: task.slotId,
      ad_type: task.event,
      taskclassify: task?.taskClassify,
      groupcode: task?.groupCode,
      award_amount: getTaskAward(task),
    });
  }
  /**
   * 效果曝光
   */
  corpTaskExposure(params: { accountId: string; slotId: string; task: TaskInfo }) {
    const { accountId, slotId, task } = params;
    const { taskCorpAd } = mx.store.get('hcAd');
    const slotAd = taskCorpAd[slotId];
    let targetAd;
    if (slotAd && slotAd?.ad && slotAd?.ad?.length) {
      slotAd.ad.forEach((adInfo) => {
        if (adInfo.ad_content.account_id === accountId) {
          targetAd = adInfo;
        }
      });
    }
    if (targetAd) {
      hcAdHelper.hcAdExpose(targetAd);
      stat.custom('task_hcad_display', {
        task_id: task.id,
        task_name: task.name,
        account_id: task.accountId,
        slot_id: task.slotId,
        ad_type: task.event,
        taskclassify: task?.taskClassify,
        groupcode: task?.groupCode,
        award_amount: getTaskAward(task),
      });
    }
  }
  /**
   * 效果点击
   */
  async corpTaskClick(params: { accountId: string; slotId: string; taskToken: string; task: TaskInfo }) {
    const { accountId, slotId, taskToken, task } = params;
    const { taskCorpAd } = mx.store.get('hcAd');
    const slotAd = taskCorpAd[slotId];
    let targetAd;
    if (slotAd && slotAd?.ad && slotAd?.ad?.length) {
      slotAd.ad.forEach((adInfo) => {
        if (adInfo.ad_content.account_id === accountId) {
          targetAd = adInfo;
        }
      });
    }
    debug && console.info('hc-ad', slotAd, targetAd);
    if (!targetAd) {
      return false;
    }
    const submitResult = await hcAdHelper.submitTaskRecord(slotAd.sid, taskToken, {
      account_id: accountId,
      slot_id: slotId,
    });

    debug && console.log('submitResult', submitResult);
    if (!submitResult) {
      return false;
    }
    hcAdHelper.hcAdClick(targetAd);
    stat.custom('task_hcad_click', {
      task_id: task.id,
      task_name: task.name,
      account_id: task.accountId,
      slot_id: task.slotId,
      ad_type: task.event,
      taskclassify: task?.taskClassify,
      groupcode: task?.groupCode,
      award_amount: getTaskAward(task),
    });
    if (!isIOS) {
      // ios需要用 schema 查询，暂无
      if (targetAd?.ad_content?.package_name) {
        const record = await hcAdStorageHandler.get(`hc_ad_${slotId}_${accountId}_click_expire`);
        if (!record) {
          const expireTime = Date.now() + 60 * 60 * 1000;
          hcAdStorageHandler.set(`hc_ad_${slotId}_${accountId}_click_expire`, expireTime);
          const queryRes = await queryApp({
            cache_first: '1',
            pkgs: [targetAd?.ad_content?.package_name],
          });
          // 是否已安装
          const clickInstallFlag = isIOS
            ? !!queryRes[targetAd.ad_content?.package_name]
            : !!queryRes[targetAd.ad_content?.package_name]?.versionName;
          hcAdStorageHandler.set(`hc_ad_${slotId}_${accountId}_click_install_status`, clickInstallFlag);
        }
      }
    }
    return true;
  }

  /**
   * 更新效果缓存数据
   */
  setTaskCorpFromStore(taskId: string, accountId: string) {
    const { taskCorpAdStoreAccountId } = mx.store.get('hcAd');
    if (!taskCorpAdStoreAccountId.has(String(taskId))) {
      taskCorpAdStoreAccountId.set(String(taskId), accountId);
    }
    localStorage.setItem(LocalStorageKey.TASK_BRAND_BY_ACCOUNT_ID, JSON.stringify(taskCorpAdStoreAccountId));
    dispatch.hcAd.set({
      taskCorpAdStoreAccountId,
    });
  }

  /**
   * 删除效果缓存数据
   */
  deleteTaskCorpFromStore(taskId: string, accountId: string) {
    const { taskCorpAdStoreAccountId } = mx.store.get('hcAd');
    if (taskCorpAdStoreAccountId.has(String(taskId))) {
      taskCorpAdStoreAccountId.delete(String(taskId), accountId);
    }
    localStorage.setItem(LocalStorageKey.TASK_BRAND_BY_ACCOUNT_ID, JSON.stringify(taskCorpAdStoreAccountId));
    dispatch.hcAd.set({
      taskCorpAdStoreAccountId,
    });
  }

  /**
   * 设置汇川品牌缓存数据
   */
  setTaskBrandFromStore(task: TaskInfo) {
    const key = generateHcAdBrandKey(task.adId ?? '');
    hcAdStorageHandler.set(key, true);
  }

  /**
   * 更新汇川
   */
  updateHcAdDetail(task: TaskInfo) {
    // 记录品牌广告完成记录，并重新获取新的品牌广告
    if (task.event === TASK_EVENT_TYPE.AD_HUICHUAN_BRAND && task?.adId) {
      const key = generateHcAdBrandKey(String(task?.adId ?? ''));
      debug && console.info(`[hc-ad]检查该品牌广告ad_id: ${task?.adId}今天是否已经完成过了`);
      console.log(`品牌广告ad_id: ${task?.adId}今天已完成，setDaily`);
      hcAdStorageHandler.setDaily(key, true);
      dispatch.hcAd.fetchTaskBrandAd({ forceUpdate: true });
    }


    // 记录效果广告完成记录,更新当前广告请求下发
    if (task.event === TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT && task?.slotId) {
      dispatch.hcAd.fetchTaskCorpAd({
        forceUpdate: true,
        slotId: task?.slotId
      })
    }
  }
}

const hcAdStore = new HcAd();
export default hcAdStore;
