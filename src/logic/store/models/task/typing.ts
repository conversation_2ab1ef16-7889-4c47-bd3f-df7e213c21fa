import { TaskInfo, RewardItem, TASK_STATUS } from "@/pages/index/components/TaskPop/TaskList/types";
import { ICurTask } from "../app/typings";

export interface ITaskState {
  curTime: number;
  todayCompleted: boolean; // 今天是否已完成签到
  totalCompletedDay: number; // 总签到天数
  cycleTotalCompletedDay: number; // 周期内签到总天数--循环周期会重置
  continuousCompletedDay: number; // 连续签到天数，断签从1开始
  incrementAssistNum: number;
  signTask: SignTaskInfo[];
  taskList: TaskInfo[];
  taskListHasAd: TaskInfo[]; // 包含汇川的数据
  inviteCode: string; // 分享任务邀请码
  showTaskToast: boolean;
  curHandleTask: TaskInfo | undefined;
  recommendTaskList: TaskInfo[];
  /** 激励广告任务预加载成功列表 */
  adTaskPreloadSuccessList: TaskInfo[];
  bannerTaskList: TaskInfo[];
  /** 激励广告预加载过的任务map */
  preloadAdTaskMap: Map<string, number>;
  progressiveIncentiveOrderTask: TaskInfo | null;
  hasSignList: boolean; // 是否已经有签到数据
}

export interface SignTaskInfo {
  signInDate: number; // 签到日期0点, 毫秒时间戳
  signInState: 'NOT_START' | 'START' | 'COMPLETED' | 'EXPIRED' | 'CAN_FILL_UP'; // NOT_START 未到时间，START 开始可签到，COMPLETED 完成签到，EXPIRED 未签到过期，CAN_FILL_UP 可补签
  curTask: ISingnCurTask;
  prizes: ISignPrize[];
}
export const enum SignType {
  AUTO = 'auto',
  HAND = 'hand'
}

export interface ISingnCurTask extends ICurTask {
  desc: string;
  icon: string;
  state: number;
  completeTime: number;
  extra: string;
}

export interface ISignPrize {
  rewardType: 'lottery' | 'currency' | 'award'; // // 奖品大类 （lottery=抽奖 currency=货币 award=权益）
  rewardItem: RewardItem;
}

export interface IFinishShareTaskRes{
  pointAmount: number; // 被邀请人肥料 --0 表示没
  inviterWateringCost: number; // 分享人肥料奖励 --0 表示没
  inviterNickname: string; // 分享人昵称
  inviterAvatar: string; // 分享人头像
  /** 邀请任务助力任务 */
  inviterTask: ICurTask;
  /** 被邀请任务助力任务 */
  inviteeTask: ICurTask;
  /** 助力用户类型 */
  assistUserType: string;
  inviteePrizes: Array<{
    win: boolean;
    rewardItem: RewardItem;
  }>;
  inviterPrizes: Array<{
    win: boolean;
    rewardItem: RewardItem;
  }>;
}

export enum EnumTaskType {
  /** 多次拆礼包 */
  MULTI_OPEN_GIFT = 'MULTI_OPEN_GIFT',
}

export const DRAW_CUSTOM_TASK_ERROR_CODE_TEXT_MAP = {
  REPEAT: '重复领取',
  others: '领取失败，请稍后再试',
};

export interface INoticeByTokenParam {
  appId?: string;
  token: string;
  from: string;
  kps?: string;
  sceneId?: string;
  convertTag?: string;
  deliveryId?: string;
  openId?: string;
  implId?: string;
  sign?: string;
  salt?: string;
  requestId?: string;
}

export interface ResCompleteTask {
  state: TASK_STATUS;
  prizes: IPrize[];
  nextTask: TaskInfo;
  curTask: TaskInfo;
}

export interface IPrize {
  win: boolean;
  rewardType: string;
  rewardItem: RewardItem;
  [key: string]: any;
}

export interface IReceiveTaskQuery {
  taskId: number;
  traceId?: string;
  showToast?: boolean;
  publishId?: number;
  useUtCompleteTask?: boolean;
}


export interface IFinishTaskQuery {
  taskId: number;
  type: string;
  traceId?: string;
  showToast?: boolean;
  useUtCompleteTask?: boolean;
  publishId?: number;
}

export interface IBatchRewardParams {
  taskIdList: number[];
  publishList: Array<{
    tid: number;
    publishId: number;
  }>;
  requestId: string;
}

export interface IBatchRewardRes {
  state: TASK_STATUS;
  succTids: number[];
  prizes: IPrize[];
}
