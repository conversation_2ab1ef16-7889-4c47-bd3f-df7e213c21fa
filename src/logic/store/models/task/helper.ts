import { ucparams, spamSign, queryApp } from "@/lib/ucapi";
import { geneTaskRequestId, getIsBangBangMainActivity, getIsDoubleActivity } from "../utils";
import { INoticeByTokenParam, ResCompleteTask } from "./typing";
import { checkInstallApp } from "@/lib/utils/app";
import { checkTaskFinished, dealTaskExtra, isAdVideoTask } from "@/pages/index/components/TaskPop/TaskList/util";
import { TASK_EVENT_TYPE, TASK_STATUS, TaskInfo, HAS_RECEIVE_TASK_STATUS } from "@/pages/index/components/TaskPop/TaskList/types";
import { isIOS } from "@/lib/universal-ua";
import stat from "@/lib/stat";
import dispatch from '@/logic/store';
import mx from '@ali/pcom-mx';
import { IHighValueTaskState } from "../highTask/types";
import { getIncentiveAdSlotData } from "@/lib/utils/incentive_ad_help";
import tracker from "@/lib/tracker";
import { IAd, ISlotAd } from "@/logic/hc-ad/type";
import { MODAL_ID } from "@/components/modals/types";
import { getParam } from "@/lib/qs";
import { checkEnableDoubleCard } from "../app/new_year_time";

let ut = '';

/** 转发二方业务token，完成任务 */
export async function geneSecondTokenParams(queryParams: INoticeByTokenParam) {
  if (!ut) {
    const utRes = await ucparams({ params: 'ut' });
    ut = utRes.ut || '';
  }
  const { from, token } = queryParams
  const requestId = geneTaskRequestId();
  const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
  const signOriText = `${decodeURIComponent(ut)}${from}${token}${requestId}`;
  const sign = await spamSign({ text: signOriText, salt });
  const params: INoticeByTokenParam = {
    ...queryParams,
    sign,
    salt,
    requestId,
  }
  return params;
}


/**
 * 处理未安装app是否显示改任务
 * @param taskInfo
 */
export const showUninstallAppTask = async (taskInfo: TaskInfo) => {
  if (!taskInfo?.extra) {
    return true
  }
  const extraObj = getExtraInfo(taskInfo)
  const { scheme, pkgName, andCheckInstall, iosCheckInstall, showUninstallApp } = extraObj;
  const conditions = (isIOS ? iosCheckInstall : andCheckInstall) || showUninstallApp;

  if (conditions && (scheme || pkgName)) {
    const [isInstall] = await checkInstallApp(scheme, pkgName);
    // 未安装不展示补充打点
    if (!isInstall) {
      stat.custom('task_hide_installed', {
        task_id: taskInfo?.id,
        task_name: taskInfo?.name,
        taskclassify: taskInfo?.taskClassify,
        groupcode: taskInfo?.groupCode,
      })
    }
    return !!isInstall
  }
  return true
}

export const dealwidthUnistallAppTask = async (taskList: TaskInfo[]) => {
  let list: TaskInfo[] = [];
  let recommendList: TaskInfo[] = [];
  const needCheckAppInstall = new Map();
  taskList.forEach(item => {
    const result = getTaskAppInstallMap(item);
    result && (needCheckAppInstall[result] = 1);
  });
  const checkAPPInstallResult = await checkMultipleInstallApp(Object.keys(needCheckAppInstall));

  for (const item of taskList) {
    const pkg = getTaskAppInstallMap(item);
    const display = pkg ? !!checkAPPInstallResult[pkg]?.install : true;

    const { onlyCheckInstallOnPop, showUninstallApp = false } = getExtraInfo(item);
    // 仅对非任务列表(套娃任务等场景)屏蔽“未安装APP”或者安装校验通过
    const showOnList = onlyCheckInstallOnPop ? true : display;

    // 开启了【对未安装用户展示任务】 并且 没有安装app
    // 或则任务已领取
    if (showUninstallApp && !display || HAS_RECEIVE_TASK_STATUS?.includes(item?.state)) {
      list.push({...item});
      recommendList.push({ ...item });
      continue;
    }

    // 只针对弹窗关闭了【对未安装用户展示任务】，
    if (showOnList && !showUninstallApp) {
      list.push({...item})
    }

    // 关闭了【对未安装用户展示任务】，则app已安装时该任务才进入弹窗
    if (display && !showUninstallApp) {
      recommendList.push({ ...item })
    }
  }

  return {
    taskList: list,
    recommendTaskList: recommendList
  };
};

/**
* 查找被隐藏的任务ID
*/
export function findMissingTaskIds(taskList1: TaskInfo[], taskList2: TaskInfo[]) {
  // 获取数组1: 任务IDs
  const obj1TaskIds = (taskList1 ?? []).map(task => task.id);

  // 获取数组2: 任务IDs
  const obj2TaskIds = (taskList2 ?? []).map(task => task.id);

  // 找出在数组1中存在但在数组2中不存在的任务IDs
  const missingTaskIds = obj1TaskIds.filter(id => !obj2TaskIds.includes(id));

  return missingTaskIds ?? []
}


/**
 * 获取任务扩展字段信息
 * @param taskInfo
 * @returns
 */
export const getExtraInfo = (taskInfo: TaskInfo) => {
  if (Object.prototype.toString.call(taskInfo?.extra) === '[object Object]') {
    return taskInfo?.extra
  }
  const extra = dealTaskExtra(taskInfo.extra)
  let extraObj;
  try {
    extraObj = JSON.parse(extra || '{}');
  } catch (e) {
    console.error('task extra parse err', e);
    extraObj = {}
  }
  return extraObj;
}


/**
 * 处理预加载成功的激励广告任务
 * @param task
 */
export const dealWithPreloadSuccessAdTask = (task: TaskInfo) => {
  const showAdList: TaskInfo[] = mx.store.getStore().task.adTaskPreloadSuccessList;
  const isHas = showAdList?.find((item) => item?.id === task?.id);
  if (!isHas) {
    dispatch.task.set({adTaskPreloadSuccessList: [...showAdList, task]})
  }
}

/**
 * 兜底过滤不需要展示的任务
 * 1. 视频任务未填充 2.调起弹窗任务不在人群
 * @param task
 * @return boolean
 */
export const ifShowTask = (task: TaskInfo) => {
  if (task.event === TASK_EVENT_TYPE.OPEN_MODAL) {
    // 兜底 过滤没有命中人群 但是投放了对应modal_id为需要命中cms才能打开的任务
    const extra = getExtraInfo(task);
    const modal_id = extra?.modal_id || '';
    if (modal_id === MODAL_ID.DOUBLE_CARD_LOTTERY) {
      return checkEnableDoubleCard();
    }
    return true;
  }
  if (isAdVideoTask(task)) {
    const adTaskPreloadSuccessList = mx.store.getStore().task.adTaskPreloadSuccessList;
    const showAdTask = adTaskPreloadSuccessList?.find((item) => item?.id === task?.id);
    const adData = getIncentiveAdSlotData(task);
    return adData?.slotKey && (!adData?.checkAdFill || showAdTask);
  }
  return true;
}

export const getTotalRewardAmount = (prizeList: ResCompleteTask[]) => {
  // 检查 element 是否有 prizes 数组且不为空
  return prizeList.reduce((sum, element) => {
    // 检查 element 是否有 prizes 数组且不为空
    if (element?.prizes?.length > 0) {
      const firstPrize = element.prizes?.[0] || {};
      // 检查 firstPrize 的 win 属性是否为 true，并且 rewardItem 的 amount 大于 0
      if (firstPrize.win && firstPrize.rewardItem?.amount > 0) {
        return sum + firstPrize.rewardItem.amount;
      }
    }
    return sum;
  }, 0);
}

/**
 * 任务列表隐藏下载类任务
 */
export const dealWithAppDownLoadTask = async (taskList: TaskInfo[]) => {
  let list: TaskInfo[] = [];
  const needCheckAppInstall = new Map();
  taskList.forEach(item => {
    const result = getTaskAppInstallMap(item, false);
    result && (needCheckAppInstall[result] = 1);
  });
  const checkAPPInstallResult = await checkMultipleInstallApp(Object.keys(needCheckAppInstall));

  for (const task of taskList) {
    if (task.event !== TASK_EVENT_TYPE.CALL_APP_DOWNLOAD) {
      list.push(task);
      continue;
    }

    if ([TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT].includes(task.event)) {
      // 任务有在做
      if ([TASK_STATUS.TASK_NOT_COMPLETED, TASK_STATUS.TASK_FINISH, TASK_STATUS.TASK_CONFIRMED, TASK_STATUS.TASK_COMPLETED]?.includes(task?.state)) {
        list.push(task);
        continue;
      }
    }

    const pkg = getTaskAppInstallMap(task, false);
    const isInstall = !!checkAPPInstallResult[pkg]?.install;
    if (!isInstall) {
      list.push(task);
      continue;
    }
  }
  return list;
}

/**
* 下载类任务App
*/
export const checkAppDownload = async (taskInfo: TaskInfo) => {
  if (!taskInfo?.extra) {
    return true
  }
  if (taskInfo.state === TASK_STATUS.TASK_PRE_TASK_NOT_FINISH) {
    return true;
  }
  const extraObj = getExtraInfo(taskInfo)
  const { scheme, pkgName } = extraObj;

  if (scheme || pkgName) {
    const schemePrefix = scheme.replace(/[^:]+$/, '//');
    const [isInstall] = await checkInstallApp(schemePrefix, pkgName);
    // 未安装不展示补充打点
    if (!isInstall) {
      stat.custom('task_hide_installed', {
        task_id: taskInfo?.id,
        task_name: taskInfo?.name,
        taskclassify: taskInfo?.taskClassify,
        groupcode: taskInfo?.groupCode,
      })
    }
    return !!isInstall
  }
  return true
}

export const checkUcLoginTask = (taskInfo: TaskInfo) => {
  // 任务完成 false
  if (checkTaskFinished(taskInfo)) {
    return {
      hasUcLoginTask: false
    }
  }
  // 任务为登录任务
  if (taskInfo.event === TASK_EVENT_TYPE.UC_LOGIN) {
    return {
      hasUcLoginTask: true,
      taskInfo: taskInfo
    }
  }

  // 子任务包含登录任务
  if (taskInfo?.preTaskList?.length) {
    const task = taskInfo.preTaskList.filter(item => item.event === TASK_EVENT_TYPE.UC_LOGIN && !checkTaskFinished(taskInfo));
    if (task.length) {
      return {
        hasUcLoginTask: true,
        taskInfo: task[0]
      }
    }
  }
  return {
    hasUcLoginTask: false,
  }
}

// 隐藏任务列表中关于高价值的任务，除用户当前需要做的任务除外
export const getHighValueTaskList = (needCurrentTask = false) => {
  const highValueTask: IHighValueTaskState = mx.store.getStore().highValueTask;
  let taskId: string[] = [];
  if (highValueTask?.resourceTaskList.length) {
    highValueTask.resourceTaskList.forEach(task => {
      taskId.push(String(task.id));
      if (task.preTaskList?.length) {
        task?.preTaskList.forEach(item => {
          taskId.push(String(item.id));
        })
      }
    })
  }
  if (highValueTask.hiddenTaskIdList.length) {
    highValueTask.hiddenTaskIdList.forEach(item => {
      taskId.push(String(item));
    });
  }
  if (highValueTask.currentTaskInfo?.id && !needCurrentTask) {
    taskId = taskId.filter(item => item !== String(highValueTask.currentTaskInfo?.id))
  }
  return taskId;
}

// 任务定时上下线 配置了时间段内的任务 在当天这个时间段内可做
export const hiddenTimePeriodTask = (taskList, curTime = new Date().getTime()) => {
  let resultTaskList = taskList?.filter(item => {
    if (item?.timeLimit?.length) {
      return isTimestampInTimeRanges(curTime, item?.timeLimit)
    }
    return true
  })
  return resultTaskList?.length ? resultTaskList : []
}

// 检测任务是否已经超过时间段
export const checkTaskTimeout = (task, curTime = new Date().getTime()) => {
  if (!task?.timeLimit?.length) {
    return false
  }
  return !isTimestampInTimeRanges(curTime, task?.timeLimit)
}

// 判断时间戳是否在多个时间范围内
const isTimestampInTimeRanges = (timestamp, timeRanges) => {
  const date = new Date(timestamp);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  for (const range of timeRanges) {
    if (timeString > range.left && timeString < range.right) {
      return true;
    }
  }
  return false;
}

/**
 * 处理 app检测安装 需要的参数
 * @param taskInfo
 * @param needConditions 是否需要检测条件
 */
export const getTaskAppInstallMap = (taskInfo: TaskInfo, needConditions = true) => {
  const extraObj = getExtraInfo(taskInfo)
  const { scheme, pkgName, andCheckInstall, iosCheckInstall, showUninstallApp } = extraObj;
  const conditions = (isIOS ? iosCheckInstall : andCheckInstall) || showUninstallApp;

  // 不需需要开启检测条件，即: iosCheckInstall : andCheckInstall;
  if (!needConditions) {
    return isIOS ? scheme : pkgName;
  }

  // 需要开启检测条件
  if (conditions && (scheme || pkgName)) {
    return isIOS ? scheme : pkgName;
  }

  return null;
}

const getAppInstallMap = () => {
  const currentAppInstallMap = mx.store.get('app.appInstallMap');
  return currentAppInstallMap;
}
// 检测多个app安装
export const checkMultipleInstallApp = async (pkgs: string[]) => {
  const currentAppInstallMap = getAppInstallMap();
  const installAppMonitor = tracker.Monitor(140, { sampleRate: 0.5 });

  const needCheckList: string[] = [];
  const resultTaskList: Record<string, {
    install: boolean;
  }> = {};

  // 已经安装的检测，没有安装的再次检测
  pkgs.forEach(pkg => {
    if (currentAppInstallMap[pkg]?.install) {
      resultTaskList[pkg] = {
        install: true
      }
      // 补充打点
      stat.custom('installed_app_detect', {
        is_installed: 1,
        app_name: pkg,
        is_ios: isIOS ? 1 : 0,
        event_id: '19999'
      });
    } else {
      needCheckList.push(pkg);
    }
  });

  const checkPromise = (appPkg: string[]) => {
    return new Promise<Array<{
      appPkg: string;
      error: null;
      res: any;
      installed: boolean;
    // eslint-disable-next-line no-async-promise-executor
    }>>(async (resolve) => {
      try {
        const res = await queryApp({
          cache_first: '0',
          pkgs: appPkg,
        });
        const appInstallInfo = (res || {});
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const result = Object.keys(appInstallInfo).map((appPkg) => {
          const installed = isIOS ? Boolean(appInstallInfo[appPkg].appName) : appInstallInfo[appPkg]?.appSize > 0 || appInstallInfo[appPkg]?.canOpen;
          // 更新数据
          if (installed) {
            currentAppInstallMap.set(appPkg, {
              installed,
              res
            });
          }

          // 补充打点
          stat.custom('installed_app_detect', {
            is_installed: installed ? 1 : 0,
            app_name: appPkg,
            is_ios: isIOS ? 1 : 0,
            event_id: '19999'
          });

          return {
            appPkg,
            error: null,
            res: res[appPkg],
            installed
          }
        });

        installAppMonitor.success({
          msg: `批量查询结果`,
          c2: JSON.stringify(appPkg),
          bl1: JSON.stringify(appInstallInfo)
        });

        resolve(result);
      } catch (error) {
        installAppMonitor.success({
          msg: `批量查询失败`,
          c2: JSON.stringify(appPkg),
          bl1: new Error(error)?.message ?? error
        });
        resolve([]);
      }
    });
  };

  const chunkSize = 10;
  try {
    // 分片处理, 批量查询默认不超过十个,超过十个端侧抛异常
    for (let i = 0; i < needCheckList.length; i += chunkSize) {
      const batch = needCheckList.slice(i, i + chunkSize);
      // eslint-disable-next-line no-await-in-loop
      const result = await checkPromise(batch);
      result.forEach(item => {
        resultTaskList[item.appPkg] = {
          install: item.installed
        }
      });
    }
    mx.store.update('app.appInstallMap', currentAppInstallMap);
    return resultTaskList;
  } catch (error) {
    return resultTaskList;
  }
}

/**
 * 隐藏 未开始的 并且 有配置自定义完成间隔的 激励广告任务
 * @param taskList
 * @param curTime
 * @returns
 */
export const hiddenNotStartTask = (taskList, curTime = new Date().getTime()) => {
  let resultTaskList = taskList?.filter((item: TaskInfo) => {
    const { beginTime, dayTimeIntervalMap = {}} = item;
    if (beginTime > curTime && Object.keys(dayTimeIntervalMap)?.length && isAdVideoTask(item)) {
      return false
    }
    return true
  })
  return resultTaskList?.length ? resultTaskList : []
}
/**
 * 处理汇川效果广告任务
 * @param taskCorpAd 汇川广告信息
 * @param taskCorpAdStoreAccountId 汇川广告缓存信息
 * @param taskList 任务列表
 * @param taskListHasAd 任务列表中包含汇川广告
 */
export const handleCombineCorpAdTask = (params: {
  taskCorpAd: { [k: string]: ISlotAd };
  taskCorpAdStoreAccountId: Map<string, { accountId: string }>;
  taskList: TaskInfo[];
  taskListHasAd: TaskInfo[];
}) => {
  const { taskCorpAd, taskCorpAdStoreAccountId = new Map(), taskList, taskListHasAd } = params;
  const clientType = mx.store.getStore()?.app?.pr;
  let result: TaskInfo[] = [];
  for (let i = 0; i < taskListHasAd.length; i++) {
    const task = taskListHasAd[i];
    // 在做的，默认在列表中
    if (task.state !== TASK_STATUS.TASK_DOING) {
      result.push(task);
      continue;
    }

    if (task.token && task.extra && task.event === TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT) {
      // 如果任务列表已经存在，避免做任务回来汇川广告信息更新
      const hasTaskCorp = taskList.find((t) => t.id === task.id);
      if (hasTaskCorp && hasTaskCorp.sid && hasTaskCorp.slotId && hasTaskCorp.accountId) {
        result.push(hasTaskCorp);
        continue;
      }
      const isLite = clientType === 'UCLite';
      const { ucLite = {}, uc = {} } = getExtraInfo(task);
      const { huichuanAccountIdList = [], huichuanAccountId } = isLite ? ucLite : uc;
      const currenthuichuanAccountIdList = [huichuanAccountId, ...huichuanAccountIdList];
      const huichuanMap: { [k: string]: { ad: IAd; payload: ISlotAd } } = {};

      Object.keys(taskCorpAd).forEach((slotId) => {
        const payload = taskCorpAd[slotId];
        if (payload.ad && Array.isArray(payload.ad)) {
          // eslint-disable-next-line array-callback-return
          payload.ad?.find((ad) => {
            const adAccountId = ad.ad_content?.account_id?.toString() || '';
            if (adAccountId && currenthuichuanAccountIdList.includes(adAccountId) && !huichuanMap[adAccountId]) {
              huichuanMap[adAccountId] = { ad, payload };
            }
          });
        }
      });

      // 扩展字段配置的没有匹配到汇川返回的则不展示任务该条任务
      if (!Object.keys(huichuanMap).length) {
        continue;
      }

      // 匹配到缓存的逻辑
      const adStoreInfo = taskCorpAdStoreAccountId.get(String(task.id));

      if (adStoreInfo?.accountId && Object.keys(huichuanMap).includes(`${adStoreInfo?.accountId}`)) {
        const storeAccountInfo = huichuanMap[adStoreInfo?.accountId];
        result.push({
          ...task,
          title: storeAccountInfo.ad.ad_content.title,
          // eslint-disable-next-line @iceworks/best-practices/no-http-url
          icon: (storeAccountInfo.ad.ad_content?.logo_url ?? task.icon).replace('http://', 'https://'),
          sid: storeAccountInfo.payload.sid,
          adId: storeAccountInfo.ad.ad_id,
          slotId: storeAccountInfo.payload.slot_id,
          fromCache: storeAccountInfo?.payload.from_cache,
          packageName: storeAccountInfo?.ad?.ad_content?.package_name,
          accountId: adStoreInfo?.accountId,
          desc: task.desc ?? '本次可得',
        });
        continue;
      }
      // 没有命中缓存,随机数取
      const firstAccountInfo = huichuanMap[Object.keys(huichuanMap)?.[0]];
      result.push({
        ...task,
        title: firstAccountInfo.ad.ad_content.title,
        // eslint-disable-next-line @iceworks/best-practices/no-http-url
        icon: (firstAccountInfo.ad.ad_content?.logo_url ?? task.icon).replace('http://', 'https://'),
        sid: firstAccountInfo.payload.sid,
        adId: firstAccountInfo.ad.ad_id,
        slotId: firstAccountInfo.payload.slot_id,
        fromCache: firstAccountInfo?.payload.from_cache,
        packageName: firstAccountInfo?.ad?.ad_content?.package_name,
        accountId: firstAccountInfo?.ad?.ad_content?.account_id,
        desc: task.desc ?? '本次可得',
      });
      continue;
    }
    if (task.event === TASK_EVENT_TYPE.AD_HUICHUAN_BRAND) {
      const hasAdBrand = taskList.find((t) => t.id === task.id);
      if (hasAdBrand) {
        result.push(hasAdBrand);
      }
      continue;
    }
    result.push(task);
  }
  return result;
}

/**
 * 处理汇川品牌广告任务
 * @param taskBrandAd 汇川广告信息
 * @param taskList 任务列表
 * @param taskListHasAd 任务列表中包含汇川广告
 */
export const handleCombineBrandAdTask = (params: {
  taskBrandAd: ISlotAd ;
  taskList: TaskInfo[];
  taskListHasAd: TaskInfo[];
}) => {
  const { taskBrandAd, taskList, taskListHasAd } = params;
  let result: TaskInfo[] = [];
  for (let i = 0; i < taskListHasAd.length; i++) {
    const task = taskListHasAd[i];
    // 在做的，默认在列表中
    if (task.state !== TASK_STATUS.TASK_DOING) {
      result.push(task);
      continue;
    }

    // 品牌广告处理
    if (
      task.event === TASK_EVENT_TYPE.AD_HUICHUAN_BRAND &&
      task.token &&
      taskBrandAd.ad?.length &&
      taskBrandAd.ad[0].ad_content?.title &&
      taskBrandAd.ad[0].ad_content?.img_1
    ) {
      // 如果任务列表已经存在，避免做任务回来汇川广告信息更新
      const hasTaskBrand = taskList.find((t) => t.id === task.id);

      const hcAdTaskParams = {
        name: taskBrandAd.ad[0].ad_content.title,
        title: taskBrandAd.ad[0].ad_content.title,
        // eslint-disable-next-line @iceworks/best-practices/no-http-url
        icon: taskBrandAd.ad[0].ad_content.img_1.replace('http://', 'https://'),
        sid: taskBrandAd.sid,
        adId: taskBrandAd.ad[0].ad_id,
        slotId: taskBrandAd.slot_id,
        accountId: taskBrandAd?.ad[0]?.ad_content?.account_id,
      }
      if (hasTaskBrand) {
        result.push({
          ...hasTaskBrand,
          ...hcAdTaskParams,
          desc: task.desc ?? '本次可得',
        });
        continue;
      }
      result.push({
        ...task,
        ...hcAdTaskParams,
      });
      continue;
    }
    // 效果广告处理
    if (task.event === TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT) {
      const hasADEffect = taskList.find((t) => t.id === task.id);
      if (hasADEffect) {
        result.push(hasADEffect);
      }
      continue;
    }

    result.push(task);
  }
  return result;
}

/**
 * 任务列表处理汇川renew
 */
export const dealWithHcAdTask = (apiTaskList: TaskInfo[]) => {
  const { taskCorpAd, taskCorpAdStoreAccountId, taskBrandAd } = mx.store.get('hcAd');
  const newTaskList: TaskInfo[] = apiTaskList.filter((item) => {
    if (![TASK_EVENT_TYPE.AD_HUICHUAN_BRAND, TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT].includes(item.event)) {
      return true;
    }
    if (item.state !== TASK_STATUS.TASK_DOING) {
      return true;
    }
    return false;
  });
  let result: TaskInfo[] = newTaskList;
  // 有效果广告
  if (taskCorpAd && Object.keys(taskCorpAd).length) {
    result = handleCombineCorpAdTask({
      taskCorpAd,
      taskCorpAdStoreAccountId,
      taskList: newTaskList,
      taskListHasAd: apiTaskList
    });
  }
  // 有品牌广告信息
  if (taskBrandAd) {
    result = handleCombineBrandAdTask({
      taskBrandAd,
      taskList: [...result],
      taskListHasAd: apiTaskList
    });
  }
  return result;
}

export const taskHiddenReport = (type: 'install' | 'video' | 'high', taskIds: number[] | string[]) => {
  if (!taskIds || taskIds.length === 0) {
    return;
  }
  const taskIdStr = taskIds.join(',');
  let report = {};
  switch (type) {
    case 'install':
      report['c1'] = taskIdStr;
      break;
    case 'video':
      report['c2'] = taskIdStr
      break;
    case 'high':
      report['c3'] = taskIdStr;
      break;
    default:
      break;
  }
  const taskReportMonitor = tracker.Monitor(182);
  taskReportMonitor.success({
    msg: '任务列表隐藏',
    ...report
  })
}

interface IShareConfig {
  apiPath: string;
  dialogSuccessModalId: string;
  dialogFailModalId: string;
  type: 'normal' | 'double' | 'bangbangzhong';
}
export const getShareActivityConfig = (): IShareConfig => {
  const isBangBangZhongActiviy = getIsBangBangMainActivity();
  const isDoubleActivity = getIsDoubleActivity();

  let finishShareConfig: IShareConfig = {
    apiPath: 'task/finishShareTask',
    dialogSuccessModalId: MODAL_ID.HELP_SUCCESS,
    dialogFailModalId: MODAL_ID.TOAST,
    type: 'normal'
  };

  if (isBangBangZhongActiviy) {
    finishShareConfig = {
      apiPath: 'help/submitAssist',
      dialogSuccessModalId: MODAL_ID.BBZ_HELP_SUCCESS,
      dialogFailModalId: MODAL_ID.BBZ_HELP_FAIL,
      type: 'bangbangzhong'
    };
  } else if (isDoubleActivity) {
    finishShareConfig = {
      apiPath: 'card/submitCardAssist',
      dialogSuccessModalId: MODAL_ID.TOAST,
      dialogFailModalId: MODAL_ID.TOAST,
      type: 'double'
    };
  }
  return finishShareConfig;
}
