import dispatch from "@/logic/store";
import mx from '@ali/pcom-mx';
import stat from "@/lib/stat";
import { getSearchParamObject } from "@/lib/utils/url";
import { FactConfig } from "@/config/fact";

const DEFAULT_DELAY_SECONDS = 2;

const getDelaySeconds = (from: string) => {
  const bbnc_delayed_callback = mx.store.get('cms.bbnc_delayed_callback');
  const callBackData = bbnc_delayed_callback?.items?.[0] || { defaultDelaySeconds: DEFAULT_DELAY_SECONDS, entryDelayList: []};
  const delaySeconds = callBackData?.entryDelayList?.find((item) => item?.entry === from)?.delaySeconds || callBackData?.defaultDelaySeconds;
  return Number(delaySeconds);
}

// 二三方换量回调
export const preThirdTokenTask = async () => {
  const { taskFrom, openId, deliveryId, implId, sceneId, entry, sceneCode } = getSearchParamObject(location.href)
  const from = taskFrom ?? entry;
  
  const { mainInfo } = mx.store.getStore().app;
  const paramsToken = mainInfo?.frontData?.paramsToken
  const token = getParamsToken(paramsToken)
  
  if (!token || !from) {
    return;
  }
  const delaySeconds = getDelaySeconds(from);
  setTimeout(() => {
    stat.custom('call_third_task', {
      from: taskFrom,
    });
    stat.pv(FactConfig.indexConfig.page);
    dispatch.task.noticeByToken({
      token,
      from,
      openId,
      deliveryId,
      implId,
      sceneId,
      convertTag: sceneCode
    })
  }, delaySeconds * 1000);
};

function getParamsToken(tokenList: string[]) {
  const urlParams = getSearchParamObject(location.href)
  let paramsToken = '';
  if (!Array.isArray(tokenList)) {
    tokenList = ["taskToken", "fromToken", "token", "userToken"];
  }
  for (const token of tokenList) {
    if (Object.prototype.hasOwnProperty.call(urlParams, token)) {
      paramsToken = urlParams[token];
      break;
    }
  }
  return paramsToken
}