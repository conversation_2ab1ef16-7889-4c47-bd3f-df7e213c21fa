import mx from '@ali/pcom-mx';
import {
  ITaskState,
  IFinishShareTaskRes,
  INoticeByTokenParam,
  IReceiveTaskQuery,
  IFinishTaskQuery,
  ResCompleteTask,
  IBatchRewardParams,
  IBatchRewardRes,
  SignType,
} from './typing';
import config from '@/config';
import { spamSign, ucparams, queryRewards, onNativeAdShow, clickNativeAdAndNotify, getUserInfo } from '@/lib/ucapi';
import network from '@/lib/network';
import Toast from '@/lib/universal-toast';
import baseModal from '@/lib/modal';
import modals from "@/components/modals";
import { MODAL_ID } from '@/components/modals/types';
import {
  getErrorInfo,
  geneTaskRequestId,
  getPublishVersion,
  getIsBangBangMainActivity,
  getIsDoubleActivity,
} from '../utils';
import tracker from '@/lib/tracker';
import toast from '@/lib/universal-toast/component/toast';
import { HELP_CODE_MAP } from '@/pages/index/utils';
import dispatch from '@/logic/store';
import {
  TASK_STATUS,
  TASK_EVENT_TYPE,
  TaskInfo,
  TanxRewardType,
} from '@/pages/index/components/TaskPop/TaskList/types';
import { getParam } from '@/lib/qs';
import { isUc, isAndroid, isIOS } from '@/lib/universal-ua';
import MxModel from '@/logic/store/models';
import { addUrlParameter, removeUrlParams } from '@/lib/utils/url';
import stat from '@/lib/stat';
import { notifyAdAwardSuccess, getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import {
  geneSecondTokenParams,
  dealwidthUnistallAppTask,
  getExtraInfo,
  checkAppDownload,
  dealWithAppDownLoadTask,
  getHighValueTaskList,
  hiddenTimePeriodTask,
  getTotalRewardAmount,
  handleCombineCorpAdTask,
  handleCombineBrandAdTask,
  dealWithHcAdTask,
  getShareActivityConfig,
  findMissingTaskIds,
  taskHiddenReport,
} from './helper';
import { checkInstallApp } from '@/lib/utils/app';
import { IHighValueTaskState } from '../highTask/types';
import { IRewardInfoItem } from '@/lib/adPlayer/typings';
import { setHighDialogCookie } from '../highTask/utils';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import adPlayerCache from '@/pages/index/components/TaskPop/TaskList/adPlayerCache';
import { isReadTypeSearchWordsTask, tanxAdvancedTaskMonitor } from '@/pages/index/components/TaskPop/TaskList/util';
import { skipCompleteOrderTask } from '@/pages/index/components/TaskPop/TaskList/help';
import { IAccountUnusualEntry } from '@/logic/type/user';
import { checkEnableDoubleCard } from '../app/new_year_time';
import { EDoubleFactFromParam } from '@/components/modals/modal_double_card';
import toastElement from '@/components/toast/toast';
import { ToastElementType } from '@/components/toast';
import { RES_CODES } from '../cms/typings';

const defaultState = {
  curTime: Date.now(), // 当前毫秒时间戳
  todayCompleted: false, // 今天是否已完成签到
  totalCompletedDay: 0, // 总签到天数
  cycleTotalCompletedDay: 0, // 周期内签到总天数--循环周期会重置
  continuousCompletedDay: 0, // 连续签到天数，断签从1开始
  incrementAssistNum: 0, // 收到助力人数
  signTask: [],
  taskList: [],
  taskListHasAd: [],
  inviteCode: '', // 分享任务邀请码
  showTaskToast: false,
  curHandleTask: undefined,
  recommendTaskList: [],
  adTaskPreloadSuccessList: [],
  bannerTaskList: [],
  preloadAdTaskMap: new Map(),
  progressiveIncentiveOrderTask: null,
  hasSignList: false,
};

class Task extends MxModel {
  path: string;
  state: ITaskState;

  constructor() {
    super();
    this.state = defaultState;
    this.path = 'task';
    this.init();
  }

  init(initData: Partial<ITaskState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }

  set(payload: Partial<ITaskState>) {
    Object.keys(payload).forEach((key) => {
      mx.store.update(`${this.path}.${key}`, payload[key]);
    });
  }

  reset() {
    mx.store.set({
      [this.path]: this.state,
    });
  }

  /**
   * 签到任务和任务列表初始化
   */
  async taskInit() {
    const { hasSignList } = mx.store.getStore().task;
    // 签到列表已经有数据了，不请求，签到成功后会更新接口
    if (hasSignList) {
      return await dispatch.task.queryTaskList();
    }
    return await Promise.all([this.querySignInTaskInfo(), this.queryTaskList()]);
  }

  /**
   * 查询签到任务信息
   */
  async querySignInTaskInfo() {
    if (!isUc) return;
    const { kps } = mx.store.getStore().user;
    const signMonitor = tracker.Monitor(103);
    const requestId = geneTaskRequestId();
    try {
      const signTaskRes = await network.get(`${config.farmHost}/task/querySignIn`, {
        appId: config.appId,
        kps,
        requestId,
      });
      if (signTaskRes?.itemList?.length) {
        signMonitor.success({
          msg: '查询签到任务列表-成功',
          bl1: JSON.stringify(signTaskRes),
          w_trace_reqid: signTaskRes['x_wpk_reqid'] ?? ''
        });
        // 服务端当前时间不从签到接口拿, 需要从任务列表接口拿
        delete signTaskRes?.curTime;
        this.set({
          ...signTaskRes,
          signTask: signTaskRes?.itemList,
          hasSignList: true
        });
      } else {
        signMonitor.fail({
          msg: '查询签到任务列表-失败',
          bl1: JSON.stringify(signTaskRes),
          w_trace_reqid: signTaskRes['x_wpk_reqid'] ?? ''
        });
      }
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      signMonitor.fail({
        msg: `查询签到任务列表接口异常-${msg}`,
        c1: errCode,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      console.log(error);
    }
  }

  /**
   * 完成签到任务
   * @param requestId string
   * @returns
   */
  async finishSignTask(requestId: string, signType: SignType) {
    const { kps } = mx.store.getStore().user;
    const signMonitor = tracker.Monitor(104);
    try {
      const ut = await dispatch.app.getAppUtRes();
      const signOriText = `${kps || ''}${ut}${config.appId}${requestId}`;
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      const sign = await spamSign({ text: signOriText, salt });
      const finishRes = await network.post(`${config.farmHost}/task/triggerSignIn`, {
        appId: config.appId,
        kps,
        requestId,
        sign,
      });
      console.log('finishRes===签到', finishRes);
      // 完成签到更新首页和签到列表&任务列表
      dispatch.app.updateHomeData();
      this.querySignInTaskInfo();

      // 触发限时任务,签到后更新任务
      const hasSignTask = mx.store.get('timeLimitTask').hasSignTask;
      if (hasSignTask) {
        this.queryTaskList();
      }

      const isWin = finishRes?.prizes[0]?.win;
      const rewardAmount = finishRes?.prizes[0]?.rewardItem?.amount;
      // 连续签到3、7天S
      const showSignModal = finishRes?.continuousCompletedDay === 3 || finishRes?.continuousCompletedDay === 7;

      const { open_task_dialog_entry } = mx.store.get('cms');
      const entryList = open_task_dialog_entry?.items?.[0]?.entryList || [];
      const showRecommendTask = entryList?.includes(getParam('entry'));
      console.log('open_task_dialog_entry,', showRecommendTask, entryList);

      if (isWin && rewardAmount) {
        signMonitor.success({
          msg: '签到发奖-成功',
          c1: finishRes?.continuousCompletedDay,
          c3: `${showRecommendTask}`,
          c4: signType || '',
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(entryList),
          w_trace_reqid: finishRes['x_wpk_reqid'] ?? ''
        });
        if (showRecommendTask || showSignModal) {
          baseModal.open(MODAL_ID.CONTINUOUS_SIGN, {
            prizeInfo: { rewardItem: finishRes?.prizes[0]?.rewardItem },
            signDay: finishRes?.continuousCompletedDay,
            showRecommendTask,
            signType,
          });
          return;
        }
        Toast.show('签到成功！每天签到，更快1分钱领水果~');
      } else {
        signMonitor.fail({
          msg: '签到发奖-失败',
          c1: finishRes?.continuousCompletedDay,
          c3: `${showRecommendTask}`,
          c4: signType || '',
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(entryList),
          w_trace_reqid: finishRes['x_wpk_reqid'] ?? ''
        });
        Toast.show('签到成功！每天签到，更快1分钱领水果~');
      }
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      signMonitor.fail({
        msg: `签到发奖接口异常-${msg}`,
        c2: errCode,
        c4: signType || '',
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      if (dispatch.user.handleAccountUnusualUser(errCode, IAccountUnusualEntry.sign)) {
        return;
      }
      this.querySignInTaskInfo();
      Toast.show('当前参与人数较多,请稍后再试');
    }
  }

  /**
   * 查询任务列表信息
   * @param isAutoAward 是否触发自动领奖
   * @returns
   */
  async queryTaskList(isAutoAward = true) {
    if (!isUc) return;
    const { kps } = mx.store.getStore().user;
    const taskMonitor = tracker.Monitor(105);
    const requestId = geneTaskRequestId();
    try {
      const taskListRes = await network.get(`${config.farmHost}/task/queryTaskList`, {
        appId: config.appId,
        fve: getPublishVersion(),
        kps,
        requestId,
        activeUser: 1,
      });
      // 接口数据请求打点
      stat.custom('task-api-query', {
        c: 'pop',
        d: 'award',
      });

      // 更新设备等级字段
      dispatch.user.set({
        deviceInfo: {
          deviceLevel: taskListRes.deviceLevel ?? '1.0',
        },
      });
      if (taskListRes?.values?.length) {
        taskMonitor.success({
          msg: '查询任务列表-成功',
          bl1: JSON.stringify(taskListRes),
          w_trace_reqid: requestId
        });
      } else {
        taskMonitor.fail({
          msg: '查询任务列表-失败',
          bl1: JSON.stringify(taskListRes),
          w_trace_reqid: requestId
        });
      }

      const taobaoRtaInfo = mx.store.get('app').taobaoRtaInfo;

      console.log('[AD] taobaoRtaInfo task', taobaoRtaInfo);
      const frontData = mx.store.get('app.mainInfo.frontData') || mx.store.get('app.frontData');
      const taobaoRtaConfig = frontData?.taobaoRtaConfig || {};
      const { highPriorityTaskId, lowPriorityTaskId } = taobaoRtaConfig;

      let taskList = taskListRes?.values || [];
      // 过滤底部banner任务
      const bannerTaskList = taskList.filter((item) => item.event === TASK_EVENT_TYPE.BANNER_CALL_APP_TOKEN);
      if (bannerTaskList?.length) {
        dispatch.task.set({ bannerTaskList });
      }
      // 任务列表过滤逻辑
      const filterTaskTypes = [
        TASK_EVENT_TYPE.BANNER_CALL_APP_TOKEN, // 底部banner
        TASK_EVENT_TYPE.CLL_APP_NO_AWARD, // 返回标签
        TASK_EVENT_TYPE.PROGRESSIVE_INCENTIVE_ORDER, // 进阶下单
      ];
      // 限时任务
      const timeLimitTask = taskList.filter((item) => item.event.includes(TASK_EVENT_TYPE.TIME_LIMIT_TASK.toString()));
      if (timeLimitTask.length) {
        dispatch.timeLimitTask.set({
          timeLimitTask,
        });
      }
      taskList = taskList.filter((item) => {
        if (item.event === TASK_EVENT_TYPE.PROGRESSIVE_INCENTIVE_ORDER) {
          dispatch.task.set({
            progressiveIncentiveOrderTask: item,
          });
        }
        return !filterTaskTypes.includes(item.event) && !item.event.includes(TASK_EVENT_TYPE.TIME_LIMIT_TASK);
      });

      let renderTaskList: TaskInfo[] = [];

      // 是否有未完成的RTA商业任务
      const hasRTATaskNotFinish = taskList.some((task: TaskInfo) => {
        let taskExtra;
        try {
          taskExtra = JSON.parse(task?.extra || '{}');
        } catch (e) {
          console.error('task extra parse err', e);
        }
        return (
          highPriorityTaskId?.includes(`${task.id}`) &&
          task.state !== TASK_STATUS.TASK_CONFIRMED &&
          taobaoRtaInfo &&
          taskExtra?.category === taobaoRtaInfo?.category
        );
      });
      renderTaskList = taskList.filter((task: TaskInfo) => {
        const taskId = `${task.id}`;
        let taskExtra;
        try {
          taskExtra = JSON.parse(task?.extra || '{}');
        } catch (e) {
          console.error('task extra parse err', e);
        }
        // // 新手引导没有home接口配置
        if (!highPriorityTaskId && taskExtra?.defaultHidden === '1') {
          return false;
        }
        // 过滤和当前RTA用户身份不匹配的任务
        if (
          highPriorityTaskId?.includes(taskId) &&
          (!taobaoRtaInfo || taskExtra?.category !== taobaoRtaInfo?.category)
        ) {
          return false;
        }
        // 有未完成的商业化任务，淘宝换量任务先不展示
        if (hasRTATaskNotFinish && lowPriorityTaskId?.includes(taskId)) {
          return false;
        }
        return true;
      });

      // 收到助力人数
      const shareTaskObj = taskList?.find((task) => task?.event === TASK_EVENT_TYPE.SHARE);

      // 存储未过滤 可做小时 & 安装检测的任务
      const notDealWithTaskList = [...renderTaskList];

      // 过滤掉定时上下限的任务
      renderTaskList = hiddenTimePeriodTask(renderTaskList, taskListRes.timestamp);

      // 处理下载类的任务
      renderTaskList = await dealWithAppDownLoadTask(renderTaskList);

      // 处理换量任务未安装app是否展示任务
      const handleResultTask = await dealwidthUnistallAppTask(renderTaskList);

      // 处理汇川相关任务: 在做的任务不过滤
      const handleAdResultTask = dealWithHcAdTask(handleResultTask.taskList);

      this.set({
        taskList: handleAdResultTask,
        taskListHasAd: handleResultTask.taskList,
        recommendTaskList: handleResultTask.recommendTaskList,
        incrementAssistNum: shareTaskObj?.ext?.incrementAssistNum || 0,
        curTime: taskListRes?.timestamp ?? Date.now(),
      });

      const hiddenTaskIdList = findMissingTaskIds(notDealWithTaskList, handleResultTask.taskList ?? []);
      taskHiddenReport('install', hiddenTaskIdList);

      const showTaskToast = mx.store.get('task').showTaskToast;
      let highValueTaskList = getHighValueTaskList(true);
      // 自动领奖逻辑
      if (isAutoAward) {
        const autofinishTask = taskList?.find((task) => task?.state === TASK_STATUS.TASK_COMPLETED);
        // 高价值任务的ID不需要触发
        if (autofinishTask?.id && !highValueTaskList.includes(String(autofinishTask?.id))) {
          dispatch.task.finishTask({
            taskId: autofinishTask?.id,
            type: 'award',
            traceId: '',
            showToast: highValueTaskList.includes(String(autofinishTask?.id)) ? false : showTaskToast,
            useUtCompleteTask: !!autofinishTask?.useUtCompleteTask,
            publishId: autofinishTask.publishId,
          });
          return;
        }
      }
      // 未完成任务处理
      const curHandleTask: TaskInfo = mx.store.get('task').curHandleTask;
      if (curHandleTask?.id && showTaskToast) {
        dealWithToTaskFinsh(taskList, curHandleTask);
      }
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      taskMonitor.fail({
        msg: `查询任务列表接口异常-${msg}`,
        c1: errCode,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      console.log('taskListRes==err', error);
    }
  }

  /**
   * 完成任务
   * @param taskId 任务id
   * @param type   完成类型 award | complete
   * @param traceId requestId
   * @param showToast 是否展示toast
   * @returns
   */
  async finishTask(data: IFinishTaskQuery) {
    const { taskId, type, traceId = '', useUtCompleteTask = false, publishId } = data;
    const { kps, bindTaobao } = mx.store.getStore().user;
    const { needFinishShowRewardTaskList, resourceTaskList } = mx.store.getStore().highValueTask;
    const { preExtraTaskList } = mx.store.getStore().timeLimitTask;
    const cmsStore = mx.store.getStore()?.cms;
    const taskList = mx.store.getStore().task.taskList;
    const requestId = traceId || geneTaskRequestId();
    const doubleCardEnable = checkEnableDoubleCard()
    const taskFromType = localStorage.getItem(LocalStorageKey.FINISH_TASK_FROM);
    const showListMatrioska = dispatch.cms.getCmsShowDialogValue(taskFromType ?? '');

    /**
     * 展示套娃条件
     * 1、任务来自taskBubble
     * 2、任务来自list 并且cms开启套娃 (showListMatrioska)
     * 3、任务来自首页常驻激励广告资源位 并且cms开启套娃 (showListMatrioska)
     */
    const idNeedMatrioska = ['list', 'permanent_ad'].includes(taskFromType || '');
    const openMatrioska = taskFromType === 'taskBubble' || (idNeedMatrioska && showListMatrioska);

    // note: 提前先获取资源位弹窗类型
    const modalType = localStorage.getItem(LocalStorageKey.RESOURCE_NICHE) || taskFromType || 'taskBubble';
    if (localStorage.getItem(LocalStorageKey.RESOURCE_NICHE)) {
      localStorage.removeItem(LocalStorageKey.RESOURCE_NICHE);
    }

    let showToast = data.showToast;
    // 判断当前任务是否是高价值任务
    if (this.currentTaskIsHighTask(data.taskId) || openMatrioska) {
      showToast = false;
    }
    // 高价值: 不需要绑定淘宝，也能完成任务 时间: 10.22
    if (type === 'award' && !bindTaobao) {
      return;
    }

    const finishTaskMonitor = tracker.Monitor(111);
    // 短时间两个共存，调整任务上报日志，用于完善任务告警
    const newTaskMonitor = tracker.Monitor(176);
    const extParams = {
      preExtraTaskRealNum: preExtraTaskList?.length,
    };
    const ext = Object.keys(extParams)
      ?.map((key) => `${key}:${extParams[key]}`)
      ?.join(',');
    try {
      const ut = await dispatch.app.getAppUtRes();
      const signOriText = `${kps || ''}${ut}${config.appId}${taskId}${type}${requestId}`;
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      const sign = await spamSign({ text: signOriText, salt });
      const fve = getPublishVersion();
      const finishRes = await network.post(`${config.farmHost}/task/triggerTask?fve=${fve}`, {
        appId: config.appId,
        kps,
        requestId,
        taskId,
        type,
        sign,
        useUtCompleteTask,
        publishId,
        ext,
      });
      console.log('finishRes==完成任务', finishRes);
      dispatch.task.set({ curHandleTask: undefined });
      const reqId = localStorage.getItem('requestId');
      if (reqId) {
        localStorage.removeItem('requestId');
      }
      const curTask = finishRes?.curTask;
      const isWin = finishRes?.prizes[0]?.win;
      const rewardAmount = finishRes?.prizes[0]?.rewardItem?.amount;
      const rewardMark = finishRes?.prizes[0]?.rewardItem?.mark;

      // 更新汇川广告, 在列表更新之前
      if ([TASK_EVENT_TYPE.AD_HUICHUAN_BRAND, TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT].includes(curTask.event ?? '')) {
        const adTask = taskList.find((item) => item.id === taskId);
        adTask && dispatch.hcAd.updateHcAdDetail(adTask);
      }

      if (
        needFinishShowRewardTaskList.includes(String(taskId)) &&
        finishRes?.prizes?.length &&
        finishRes.prizes[0].win &&
        type === 'award'
      ) {
        // 高价值弹窗
        baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
        baseModal.open(MODAL_ID.HIGH_VALUE_AWARD, {
          // 奖励信息
          ...finishRes.prizes?.[0]?.rewardItem,
          // 任务名称
          taskName: finishRes.curTask.name,
          taskId,
          bindTaobao: true,
        });
        const awardTask = resourceTaskList.filter((item) => String(item.id) === String(taskId));
        if (awardTask[0]) {
          setHighDialogCookie(awardTask[0], bindTaobao);
        }
        showToast = false;
      }

      if (openMatrioska) {
        await dispatch.resource.queryResource({});
      } else {
        dispatch.resource.queryResource({});
      }
      if (finishRes.state === TASK_STATUS.TASK_COMPLETED) {
        finishTaskMonitor.success({
          msg: `完成任务-待领取奖励`,
          c2: curTask?.id,
          c3: type,
          c4: curTask?.event,
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(curTask),
        });
        newTaskMonitor.success({
          msg: `${curTask?.id}-${curTask?.name}`,
          c2: '完成任务-待领取奖励',
          c3: type,
          c4: curTask?.event,
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(curTask),
          w_trace_reqid: requestId
        });
      } else if (finishRes.state === TASK_STATUS.TASK_CONFIRMED && isWin && rewardAmount) {
        finishTaskMonitor.success({
          msg: '发放奖励-成功',
          c2: curTask?.id,
          c3: type,
          c4: curTask?.event,
          c5: `${openMatrioska}`,
          c6: taskFromType || '',
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(curTask),
          bl3: JSON.stringify(cmsStore),
        });
        newTaskMonitor.success({
          msg: `${curTask?.id}-${curTask?.name}`,
          c2: '发放奖励-成功',
          c3: type,
          c4: curTask?.event,
          c5: `${openMatrioska}`,
          c6: taskFromType || '',
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(curTask),
          bl3: JSON.stringify(cmsStore),
          w_trace_reqid: requestId
        });
        if (showToast) {
          Toast.show(curTask.toast?.content ?? '任务完成, 奖励已发放');
        } else if (openMatrioska) {
          baseModal.open(MODAL_ID.PROGRESS_BUBBLE, {
            isReward: true,
            pointAmount: rewardAmount,
            rewardMark,
            modalType,
            curTask,
          });
        }

        // 领取奖励后在清除缓存
        if (taskFromType) {
          localStorage.removeItem(LocalStorageKey.FINISH_TASK_FROM);
        }
      } else if (finishRes.state === TASK_STATUS.TASK_CONFIRMED && (!isWin || !rewardAmount)) {
        // 无奖励任务完成
        if (finishRes?.curTask?.event === TASK_EVENT_TYPE.CALL_TAOBAO) {
          if (showToast) {
            Toast.show(curTask.toast?.content ?? '任务完成, 奖励已发放');
          }
          finishTaskMonitor.success({
            msg: '发放奖励-成功',
            c2: curTask?.id,
            c3: type,
            c4: curTask?.event,
            c7: preExtraTaskList?.length,
            bl1: JSON.stringify(finishRes),
            bl2: JSON.stringify(curTask),
          });
          newTaskMonitor.success({
            msg: String(curTask?.id),
            c2: '发放奖励-成功',
            c3: type,
            c4: curTask?.event,
            c7: preExtraTaskList?.length,
            bl1: JSON.stringify(finishRes),
            bl2: JSON.stringify(curTask),
            w_trace_reqid: requestId
          });
          // 更新首页和任务列表、高价值列表
          dispatch.app.updateHomeData();
          this.queryTaskList(false);
          return;
        }

        finishTaskMonitor.fail({
          msg: '发放奖励-失败',
          c2: `${taskId}`,
          c3: type,
          c4: finishRes?.curTask?.event,
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(finishRes?.curTask),
        });
        newTaskMonitor.fail({
          msg: `${curTask?.id}-${curTask?.name}`,
          c2: '发放奖励-失败',
          c3: type,
          c4: finishRes?.curTask?.event,
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(finishRes?.curTask),
          w_trace_reqid: requestId
        });
        if (showToast) {
          Toast.show(curTask.toast?.content ?? '奖励发放失败，为您补发中');
        }
      } else {
        finishTaskMonitor.fail({
          msg: '完成任务-失败',
          c2: curTask?.id,
          c3: type,
          c4: curTask?.event,
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(curTask),
        });
        newTaskMonitor.fail({
          msg: `${curTask?.id}-${curTask?.name}`,
          c2: '完成任务-失败',
          c3: type,
          c4: curTask?.event,
          c7: preExtraTaskList?.length,
          bl1: JSON.stringify(finishRes),
          bl2: JSON.stringify(curTask),
          w_trace_reqid: requestId
        });
      }
      // 更新首页和任务列表、高价值列表
      dispatch.app.updateHomeData();
      dispatch.task.queryTaskList(false);
      // 接口数据要等会才会更新
      doubleCardEnable && setTimeout(() => {
        dispatch.doublePointsCard.queryCardInfo()
      }, 2500);
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      finishTaskMonitor.fail({
        msg: `完成任务接口异常-${msg}`,
        c1: errCode,
        c2: `${taskId}`,
        c3: `${type}`,
        c7: preExtraTaskList?.length,
        bl1: JSON.stringify(error),
      });
      newTaskMonitor.fail({
        msg: String(taskId),
        c1: errCode,
        c2: `完成任务接口异常-${msg}`,
        c3: type,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      dispatch.user.handleAccountUnusualUser(errCode, IAccountUnusualEntry.task);
    }
  }
  /*
   * 完成tanx进阶浏览下单任务, 分别有只浏览、只下单、浏览+下单
   * @params task: TaskInfo
   * @params rewardType: TanxRewardType
   */
  async completeTanxProgressiveTask(params: { task: TaskInfo; rewardType: TanxRewardType }) {
    const task = params.task;
    const orderTask = mx.store.get('task.progressiveIncentiveOrderTask');
    if (params.rewardType === TanxRewardType.BROWSE) {
      return dispatch.task.finishTask({
        taskId: task.id,
        useUtCompleteTask: task.useUtCompleteTask,
        publishId: task.publishId,
        type: 'complete',
        showToast: true,
      });
    }
    if (params.rewardType === TanxRewardType.ORDER && orderTask) {
      return dispatch.task.finishTask({
        taskId: orderTask.id,
        useUtCompleteTask: orderTask.useUtCompleteTask,
        publishId: orderTask.publishId,
        type: 'complete',
        showToast: true,
      });
    }
    if (params.rewardType === TanxRewardType.BROWSE_ORDER) {
      const completeTasks = orderTask ? [task, orderTask] : [task];
      const resDataList: ResCompleteTask[] = await dispatch.task?.serialTaskTrigger(completeTasks);
      const rewardAmount = getTotalRewardAmount(resDataList);
      if (rewardAmount) {
        Toast.show('任务完成, 奖励已发放');
      }
      // 更新首页和任务列表、高价值列表
      dispatch.app.updateHomeData();
      // dispatch.highValueTask.queryHighValueTask();
      dispatch.resource.queryResource({});
      dispatch.task.queryTaskList(false);
    }
  }
  /**
   * 完成全部任务 串行接口请求
   * @param taskList 任务列表
   * @returns
   */
  async serialTaskTrigger(taskList: TaskInfo[]) {
    const completeTaskApi = async (task: TaskInfo) => {
      let completeRes: ResCompleteTask | null = null;
      const finishTaskMonitor = tracker.Monitor(111);
      // 短时间两个共存，调整任务上报日志，用于完善任务告警
      const newTaskMonitor = tracker.Monitor(176);
      let traceId = '';
      if ([TASK_EVENT_TYPE.ADD_WIDGET, TASK_EVENT_TYPE?.UC_DESKTOP_ADD]?.includes(task?.event)) {
        traceId = localStorage.getItem(LocalStorageKey?.requestId) || '';
        localStorage.removeItem(LocalStorageKey?.requestId);
      }
      try {
        completeRes = await dispatch?.task?.completeTask({
          taskId: task?.id,
          type: 'complete',
          traceId,
          useUtCompleteTask: !!task?.useUtCompleteTask,
          publishId: task.publishId,
        });
        const curTask = completeRes?.curTask;
        const isWin = completeRes?.prizes[0]?.win;
        const rewardAmount = completeRes?.prizes[0]?.rewardItem?.amount;
        if (completeRes?.state === TASK_STATUS.TASK_COMPLETED) {
          finishTaskMonitor.success({
            msg: `完成任务-待领取奖励`,
            c2: `${task?.id}`,
            c3: 'complete',
            c4: completeRes?.curTask?.event,
            bl1: JSON.stringify(completeRes),
          });
          newTaskMonitor.success({
            msg: `${curTask?.id ?? task.id}-${curTask?.name ?? task.name}`,
            c2: `完成任务-待领取奖励`,
            c3: 'complete',
            c4: completeRes?.curTask?.event,
            bl1: JSON.stringify(completeRes),
            w_trace_reqid: completeRes['x_wpk_reqid'] ?? traceId
          });
        } else if (completeRes?.state === TASK_STATUS.TASK_CONFIRMED && isWin && rewardAmount) {
          finishTaskMonitor.success({
            msg: '发放奖励-成功',
            c2: `${task?.id}`,
            c3: 'complete',
            c4: completeRes?.curTask?.event,
            bl1: JSON.stringify(completeRes),
          });
          newTaskMonitor.success({
            msg: `${curTask?.id ?? task.id}-${curTask?.name ?? task.name}`,
            c2: `发放奖励-成功`,
            c3: 'complete',
            c4: completeRes?.curTask?.event,
            bl1: JSON.stringify(completeRes),
            w_trace_reqid: completeRes['x_wpk_reqid'] ?? traceId
          });
        } else {
          finishTaskMonitor.fail({
            msg: '发放奖励-失败',
            c2: `${task?.id}`,
            c3: 'complete',
            c4: completeRes?.curTask?.event,
            bl1: JSON.stringify(completeRes),
            bl2: JSON.stringify(completeRes?.curTask),
          });
          newTaskMonitor.fail({
            msg: `${curTask?.id ?? task.id}-${curTask?.name ?? task.name}`,
            c2: '发放奖励-失败',
            c3: 'complete',
            c4: completeRes?.curTask?.event,
            bl1: JSON.stringify(completeRes),
            bl2: JSON.stringify(completeRes?.curTask),
            w_trace_reqid: completeRes ? completeRes['x_wpk_reqid'] ?? traceId : traceId
          });
        }
      } catch (error) {
        const { errCode, msg } = getErrorInfo(error);
        finishTaskMonitor.fail({
          msg: `完成任务接口异常-${msg}`,
          c1: errCode,
          c2: `${task?.id}`,
          bl1: JSON.stringify(error),
        });
        newTaskMonitor.fail({
          msg: `${task?.id}-${task?.name}`,
          c1: errCode,
          c2: `完成任务接口异常-${msg}`,
          bl1: JSON.stringify(error),
          w_trace_reqid: traceId
        });
        dispatch.user.handleAccountUnusualUser(errCode, IAccountUnusualEntry.other);
      }
      return completeRes;
    };
    const results: any[] = [];
    for (const task of taskList) {
      const res = await completeTaskApi(task);
      results.push(res);
    }
    return results;
  }
  /**
   * 完成任务
   * @param taskId 任务id
   * @param type   完成类型 award | complete
   * @param traceId requestId
   * @returns
   */
  async completeTask(data: IFinishTaskQuery) {
    const { taskId, type, traceId = '', useUtCompleteTask = false, publishId } = data;
    const { kps } = mx.store.getStore().user;
    const requestId = traceId || geneTaskRequestId();
    const ut = await dispatch.app.getAppUtRes();
    const signOriText = `${kps || ''}${ut}${config.appId}${taskId}${type}${requestId}`;
    const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
    const sign = await spamSign({ text: signOriText, salt });
    const fve = getPublishVersion();
    return network.post(`${config.farmHost}/task/triggerTask?fve=${fve}`, {
      appId: config.appId,
      kps,
      requestId,
      taskId,
      type,
      sign,
      useUtCompleteTask,
      publishId,
    });
  }
  /**
   * 判断当前任务是否是高价值任务，用户toast区分
   * @param id 任务ID
   * @returns
   */
  currentTaskIsHighTask(id: number) {
    const highValueTaskList = getHighValueTaskList(true);
    return highValueTaskList.includes(String(id));
  }

  /**
   * 领取任务
   * @param taskId 任务id
   * @param type   完成类型 award | complete
   * @param traceId requestId
   * @param showToast 是否展示toast
   * @returns
   */
  async receiveTask(data: IReceiveTaskQuery) {
    const { taskId, traceId = '', showToast = false, publishId, useUtCompleteTask = false } = data;
    const { kps, bindTaobao } = mx.store.getStore().user;

    const highValueTask: IHighValueTaskState = mx.store.getStore().highValueTask;
    // 高价值任务需求变更，任务领取不需要绑定淘宝
    // if (!bindTaobao) return;
    const receiveMonitor = tracker.Monitor(139);
    const requestId = traceId || geneTaskRequestId();
    const ut = await dispatch.app.getAppUtRes();
    const signOriText = `${kps || ''}${ut}${config.appId}${taskId}${requestId}`;
    const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
    const sign = await spamSign({ text: signOriText, salt });
    const fve = getPublishVersion();
    // 任务列表没有投放ID，需要添加一下
    const params = {
      appId: config.appId,
      kps,
      requestId,
      taskId,
      sign,
      publishId:
        highValueTask.currentTaskInfo?.id === taskId
          ? highValueTask.currentTaskInfo?.publishId || publishId
          : publishId,
      useUtCompleteTask,
    };
    try {
      const receiveRes = await network.post(`${config.farmHost}/task/receiveTask?fve=${fve}`, params);
      console.log('finishRes==领取任务', receiveRes);
      dispatch.task.set({ curHandleTask: undefined });
      const reqId = localStorage.getItem('requestId');
      if (reqId) {
        localStorage.removeItem('requestId');
      }
      if (showToast) {
        Toast.show('任务已领取');
      }
      receiveMonitor.success({
        msg: `领取任务接口成功`,
        c1: `${taskId}`,
        bl1: JSON.stringify(params),
        bl2: JSON.stringify(receiveRes || {}),
        w_trace_reqid: requestId
      });

      // 更新首页和任务列表
      dispatch.app.updateHomeData();
      this.queryTaskList(false);
      // 更新资源位
      // dispatch.highValueTask.queryHighValueTask();
      // 更新资源位
      dispatch.resource.queryResource({});
    } catch (error) {
      console.log(error);
      receiveMonitor.fail({
        msg: `领取任务接口异常`,
        c1: `${taskId}`,
        bl1: JSON.stringify(params),
        bl2: JSON.stringify(error),
        w_trace_reqid: requestId
      });
    }
  }

  /**
   * 查询分享任务信息
   */
  async queryShareTaskInfo() {
    const { kps, bindTaobao } = mx.store.getStore().user;
    if (!bindTaobao) return;
    const queryShareTaskMonitor = tracker.Monitor(110);
    const requestId = geneTaskRequestId();
    try {
      const shareTaskRes = await network.get(`${config.farmHost}/task/queryShareInfo`, {
        appId: config.appId,
        kps,
        requestId,
      });
      console.log('查询分享任务信息===res', shareTaskRes);
      this.set({ inviteCode: shareTaskRes?.inviteCode });

      // 更新设备等级字段
      dispatch.user.set({
        deviceInfo: {
          deviceLevel: shareTaskRes.deviceLevel ?? '1.0',
        },
      });
      if (shareTaskRes?.inviteCode) {
        queryShareTaskMonitor.success({
          msg: '查询助力信息-成功',
          bl1: JSON.stringify(shareTaskRes),
          w_trace_reqid: requestId
        });
      } else {
        queryShareTaskMonitor.fail({
          msg: `查询助力信息-失败`,
          bl1: JSON.stringify(shareTaskRes),
          w_trace_reqid: requestId
        });
      }
    } catch (error) {
      console.log('查询分享任务信息===err', error);
      const { errCode, msg } = getErrorInfo(error);
      queryShareTaskMonitor.fail({
        msg: `查询助力信息接口异常-${msg}`,
        c1: errCode,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
    }
  }

  /**
   * RTA广告曝光
   */
  async ratTaskExposure() {
    const addShowMonitor = tracker.Monitor(136);
    const taobaoRtaInfo = mx.store.get('app').taobaoRtaInfo;
    const frontData = mx.store.get('app.frontData');
    const taobaoRtaConfig = frontData?.taobaoRtaConfig;
    const { androidSlotId, iosSlotId, type } = taobaoRtaConfig;
    const params = {
      sid: taobaoRtaInfo.adInfo?.sid ?? '',
      aid: isAndroid ? androidSlotId : iosSlotId,
      type,
    };
    try {
      const showRet = await onNativeAdShow(params);
      addShowMonitor.success({
        msg: '广告曝光成功',
        c1: taobaoRtaInfo.category,
        c2: taobaoRtaInfo.sceneId,
        c3: params.aid,
        bl1: JSON.stringify(showRet || {}),
        bl2: JSON.stringify(params),
      });
      console.log('[AD] onNativeAdShow result', showRet);
    } catch (err) {
      addShowMonitor.fail({
        msg: '广告曝光异常',
        c1: taobaoRtaInfo.category,
        c2: taobaoRtaInfo.sceneId,
        c3: params.aid,
        bl1: JSON.stringify(err),
        bl2: JSON.stringify(params),
      });
      console.error('[AD] onNativeAdShow err', err);
    }
  }
  async rtaAdClick(task) {
    const clickMonitor = tracker.Monitor(137);
    const taobaoRtaInfo = mx.store.get('app').taobaoRtaInfo;
    const frontData = mx.store.get('app.frontData');
    const taobaoRtaConfig = frontData?.taobaoRtaConfig;
    const { androidSlotId, iosSlotId, type } = taobaoRtaConfig;
    const params = {
      sid: taobaoRtaInfo?.adInfo?.sid ?? '',
      aid: isAndroid ? androidSlotId : iosSlotId,
      thirdid: `${task.token}` || `${task.id}`,
      channel: 2,
      type: type,
    };
    try {
      const clickRet = await clickNativeAdAndNotify(params);
      clickMonitor.success({
        msg: '广告点击正常',
        c1: taobaoRtaInfo.category,
        c2: taobaoRtaInfo.sceneId,
        c3: params.aid,
        bl1: JSON.stringify(clickRet),
        bl2: JSON.stringify(params),
      });
      console.log('[AD] clickNativeAdAndNotify result', params, clickRet);
    } catch (err) {
      console.error('[AD] clickNativeAdAndNotify err', err);
      clickMonitor.fail({
        msg: '广告点击异常',
        c1: taobaoRtaInfo.category,
        c2: taobaoRtaInfo.sceneId,
        c3: params.aid,
        bl1: JSON.stringify(err),
        bl2: JSON.stringify(params),
      });
    }
  }
  /**
   * 完成分享任务
   */
  async finishShareTask(inviteCode: string) {
    const { kps } = mx.store.getStore().user;
    const finishShareTaskMonitor = tracker.Monitor(109);
    const entry = getParam('entry');
    const isBangBangZhongActiviy = getIsBangBangMainActivity();
    const isDoubleActivity = getIsDoubleActivity();
    const finishShareConfig = getShareActivityConfig();
    const ShareType = finishShareConfig.type
    const { helpPlantHome } = mx.store.getStore().farmHelpPlant;
    const doublePointsCard = mx.store.getStore().doublePointsCard;
    const doubleCardEnable = checkEnableDoubleCard() && !helpPlantHome?.accountUnusual
    const requestId = geneTaskRequestId();
    const cmsStore = mx.store.getStore().cms
    // 翻倍卡 是否必中
    const isMustDouble = doubleCardEnable && cmsStore[RES_CODES.BBNC_GUARANTEED_FDCARD]?.items?.[0]?.open;
    const hasDraw = !!doublePointsCard?.drawInfo?.totalDrawTimes
    try {
      const ut = await dispatch.app.getAppUtRes();
      const signOriText = `${kps || ''}${ut}${config.appId}${inviteCode}${requestId}`;
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      const sign = await spamSign({ text: signOriText, salt });
      const shareTaskRes: IFinishShareTaskRes = await network.post(`${config.farmHost}/${finishShareConfig.apiPath}`, {
        appId: config.appId,
        kps,
        requestId,
        sign,
        inviteCode,
      });
      const isAwardedSuccess =
        (ShareType === 'normal' && shareTaskRes?.pointAmount) ||
        (ShareType === 'bangbangzhong' && Object.keys(shareTaskRes).length) ||
        (ShareType === 'double' && Object.keys(shareTaskRes).length);

      // 发放奖励成功
      if (isAwardedSuccess) {
        switch (ShareType) {
          case 'double': {
            toastElement.show({
              content: null,
              options: {
                type: ToastElementType.SHARE,
                props: {
                  id: finishShareConfig.dialogFailModalId,
                  toastData: {
                    type: 'success',
                    title: '助力成功！',
                    subTitle: doubleCardEnable ? '抽肥料膨胀卡，果树加速成熟' : '和支付宝、淘宝共种一棵树',
                    shareType: 'double',
                  },
                  errCode: ''
                },
              }
            });
            doubleCardEnable && modals.openDoubleCardModal({
              from: EDoubleFactFromParam.SHARED_POP,
              isMustDouble: isMustDouble,
              delayLottery: !hasDraw,
            });
            break;
          }
          default: {
            baseModal.open(finishShareConfig.dialogSuccessModalId, { data: { ...shareTaskRes } });
          }
        }
        finishShareTaskMonitor.success({
          msg: inviteCode,
          c2: inviteCode,
          c3: shareTaskRes?.assistUserType,
          c4: entry,
          c5: '助力成功-发放奖励成功',
          bl1: JSON.stringify(shareTaskRes),
          w_trace_reqid: requestId
        });
        // 助力成功用户
        stat.custom('assist_success_user', {
          assistUserType: shareTaskRes?.assistUserType,
          inviteCode,
          entry,
        });
      } else {
        finishShareTaskMonitor.fail({
          msg: inviteCode,
          c2: inviteCode,
          c3: shareTaskRes?.assistUserType,
          c4: entry,
          c5: '助力成功-发放奖励失败',
          bl1: JSON.stringify(shareTaskRes),
          w_trace_reqid: requestId
        });
        const toastProps = {
          id: finishShareConfig.dialogFailModalId,
          toastData: {
            type: 'fail' as const,
            title: HELP_CODE_MAP['HELP_SEND_FAIL'].title ?? '助力失败',
            subTitle: HELP_CODE_MAP['HELP_SEND_FAIL'].subTitle,
            shareType: 'double' as const,
          },
          errCode: 'HELP_SEND_FAIL',
        }
        switch (ShareType) {
          case 'double': {
            toastElement.show({
              content: null,
              options: {
                type: ToastElementType.SHARE,
                props: toastProps,
              }
            });
            doubleCardEnable && modals.openDoubleCardModal({
              from: EDoubleFactFromParam.SHARED_POP,
              isMustDouble: isMustDouble,
              delayLottery: !hasDraw,
            });
            break;
          }
          default: {
            baseModal.open(finishShareConfig.dialogFailModalId, toastProps);
          }
        }
      }
      // 助力后移除url中的inviteCode参数,防止页面刷新重复助力
      removeUrlParams('inviteCode');
      // 更新首页
      dispatch.app.updateHomeData();
      // 更新活动信息
      if (isBangBangZhongActiviy) {
        dispatch.farmHelpPlant.queryHelpPlantHome();
      }
      // 更新翻倍卡信息 @TODO: 唐本达
      if (isDoubleActivity) {
        dispatch.doublePointsCard.queryCardInfo();
      }
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      console.log(error);
      const toastProps = {
        id: finishShareConfig.dialogFailModalId,
        toastData: {
          type: 'fail' as const,
          title: '助力失败',
          subTitle: HELP_CODE_MAP[errCode]?.subTitle || '网络太繁忙，请重新试试吧',
          shareType: 'double' as const,
        },
        errCode,
      }
      switch (ShareType) {
        case 'double': {
          toastElement.show({
            content: null,
            options: {
              type: ToastElementType.SHARE,
              props: toastProps
            }
          });
          doubleCardEnable && modals.openDoubleCardModal({
            from: EDoubleFactFromParam.SHARED_POP,
            isMustDouble: isMustDouble,
            delayLottery: !hasDraw,
          });
          break;
        }
        default: {
          baseModal.open(finishShareConfig.dialogFailModalId, toastProps);
        }
      }

      finishShareTaskMonitor.fail({
        msg: inviteCode,
        c1: errCode,
        c2: inviteCode,
        c4: entry,
        c5: `助力失败-${msg}`,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      // 助力后移除url中的inviteCode参数,防止页面刷新重复助力
      removeUrlParams('inviteCode');
    }
  }

  // 定制任务领奖，如明日肥料，新手礼包
  async drawCustomTaskAward(taskType: 'NEW_USER_GIFT' | 'TOMORROW_GIFT' | 'MULTI_OPEN_GIFT') {
    if (!taskType) return;
    const customTaskMonitor = tracker.Monitor(118);
    const kps = mx.store.get('user').kps;
    const ut = await dispatch.app.getAppUtRes();
    const requestId = geneTaskRequestId();
    const signOriText = `${kps || ''}${ut}${config.appId}${requestId}${taskType}`;
    const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
    const sign = await spamSign({ text: signOriText, salt });
    try {
      const drawRes: { pointAmount: number } = await network.post(`${config.farmHost}/task/drawCustomTaskAward`, {
        appId: config.appId,
        kps,
        requestId,
        taskType,
        sign,
      });
      if (drawRes) {
        customTaskMonitor.success({
          msg: '领奖成功',
          c1: taskType,
          c2: String(drawRes.pointAmount),
          bl1: JSON.stringify(drawRes),
          w_trace_reqid: requestId
        });
        return drawRes;
      } else {
        return null;
      }
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      customTaskMonitor.fail({
        msg: '领奖失败',
        c1: taskType,
        c2: error?.code,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
      if (dispatch.user.handleAccountUnusualUser(errCode, IAccountUnusualEntry.task)) {
        return null;
      }
      toast.show('肥料收取失败，请稍后重试');
      // 更新数据，以防实际领取成功的情况
      await dispatch.app.updateHomeData();
      return null;
    }
  }

  /*
   * 查询tanx视频任务是否需要发奖，需要时触发领取视频奖励
   * @param task: 任务信息
   * @param isInit: 是否是页面初始化
   */
  async queryAndGetAdAward(task: TaskInfo, isInit: boolean) {
    const queryMonitor = tracker.Monitor(127);
    const adData = getIncentiveAdSlotData(task);
    const adParams = {
      slotKey: adData?.slotKey,
      appId: adData?.appId,
      requestId: `${Date.now()}`,
    };
    let hasAdAward = false;
    try {
      const res = await queryRewards(adParams?.slotKey, adParams?.appId, adParams?.requestId);
      const errMsg = res && (res.error || (res.ext || {}).error || res.errMsg || !!res.errCode || !!res.errorCode);
      if (res?.completeTime || res?.code?.toString() === '0') {
        hasAdAward = true;
        const rewardInfoList: IRewardInfoItem[] = res?.reward_data?.reward_info_list || [];
        queryMonitor.success({
          msg: `${adParams?.slotKey}-查询到奖励`,
          c1: `${task?.id}`,
          c2: adParams?.slotKey,
          c4: adParams?.appId,
          c5: `${task?.name}`,
          c6: rewardInfoList?.[0]?.adn_id || '',
          c7: res?.reward_data?.reward_success_id ? '1' : '0',
          c8: `${isInit}`,
          c9: res?.reward_data?.reward_type,
          bl1: JSON.stringify(res),
          bl2: JSON.stringify(adParams),
          bl3: JSON.stringify(task),
        });
        stat.custom('incentive_ad_query_award', {
          task_id: task?.id,
          task_name: task?.name,
          taskclassify: task?.taskClassify || '',
          groupcode: task?.groupCode || '',
          slot_id: rewardInfoList?.[0]?.slot_id || adParams?.slotKey,
          adapp_id: rewardInfoList?.[0]?.app_id || adParams?.appId,
          adn_id: rewardInfoList?.[0]?.adn_id || '',
          sid: rewardInfoList?.[0]?.sid || '',
          price: rewardInfoList?.[0]?.price || '',
          pid: rewardInfoList?.[0]?.pid || '',
          award_amount: task?.rewardItems[0]?.amount || '',
          task_progress: task?.dayTimes?.progress || '',
        });
        const tanxRewardTypes = [TanxRewardType.BROWSE, TanxRewardType.ORDER, TanxRewardType.BROWSE_ORDER];
        const resRewardType = res?.reward_data.reward_type;
        if (!(await skipCompleteOrderTask(task)) && tanxRewardTypes.includes(resRewardType)) {
          tanxAdvancedTaskMonitor('tanx进阶广告发放奖励', task, adData?.slotKey, resRewardType);
          dispatch.task.completeTanxProgressiveTask({
            task,
            rewardType: resRewardType,
          });
        } else {
          dispatch.task.finishTask({
            taskId: task.id,
            type: 'complete',
            useUtCompleteTask: !!task?.useUtCompleteTask,
            publishId: task.publishId,
            showToast: true,
          });
        }
        if (res?.reward_data?.reward_success_id) {
          notifyAdAwardSuccess(
            task,
            {
              slotKey: adParams?.slotKey,
              rewardId: res?.reward_data?.reward_success_id,
              requestId: `${adParams?.requestId}`,
              appId: adParams?.appId,
            },
            'queryRewards',
          );
        }
      } else {
        queryMonitor.fail({
          msg: `${adParams?.slotKey}-无奖励`,
          c1: `${task?.id}`,
          c2: adParams?.slotKey,
          c3: errMsg,
          c4: adParams?.appId,
          c5: `${task?.name}`,
          c8: `${isInit}`,
          bl1: JSON.stringify(res),
          bl2: JSON.stringify(adParams),
          bl3: JSON.stringify(task),
        });
      }
    } catch (e) {
      queryMonitor.fail({
        msg: `${adParams?.slotKey}-无奖励-catch`,
        c1: `${task?.id}`,
        c2: adParams?.slotKey,
        c4: adParams?.appId,
        c5: `${task?.name}`,
        c8: `${isInit}`,
        bl1: JSON.stringify(e),
        bl2: JSON.stringify(adParams),
        bl3: JSON.stringify(task),
      });
    }
    if (!isIOS && !isInit && !hasAdAward) {
      // 未回调奖励 + 异步查询无奖励， toast提示, ios暂走原来逻辑
      Toast.show('任务未完成');
    }
    adPlayerCache.removeInvokeTaskIds(task.id);
  }

  async noticeByToken(p: INoticeByTokenParam) {
    const { token, from } = p;
    const kps = mx.store.get('user').kps;
    const noticeByToken = getParam('noticeByToken');
    if (!isUc || noticeByToken) {
      return;
    }
    const tokenTaskMonitor = tracker.Monitor(132);
    const params = await geneSecondTokenParams(p);
    try {
      const res = await network.get(`${config.taskHost}/task/noticeByToken`, {
        kps,
        appId: config.appId,
        ...params,
      });
      if (res) {
        tokenTaskMonitor.success({
          msg: '任务完成',
          c1: from,
          c2: token,
          w_trace_reqid: res['x_wpk_reqid'] ?? ''
        });
        // 给当前页面url加给参数,有这个参数就不重新发请求了
        addUrlParameter('noticeByToken', 1);
      } else {
        tokenTaskMonitor.fail({
          msg: '任务失败',
          c1: from,
          c2: token,
          w_trace_reqid: res['x_wpk_reqid'] ?? ''
        });
      }
    } catch (err) {
      const { errCode, msg } = getErrorInfo(err);
      tokenTaskMonitor.fail({
        msg: `任务失败-${msg}`,
        c1: from,
        c2: token,
        c3: errCode,
        bl1: JSON.stringify(err),
      });
    }
  }

  /**
   * 添加实时标签
   */
  async addRealTimeTag(task: TaskInfo) {
    const kps = mx.store.get('user').kps;
    const tokenTaskMonitor = tracker.Monitor(160);
    try {
      const tagRes = await network.get(`${config.taskHost}/task/deeplinkFailTag`, {
        kps,
        taskId: task?.id,
      });
      tokenTaskMonitor.success({
        msg: '添加实时标签-成功',
        c1: `${tagRes?.__meta?.code}`,
        c2: `${task?.id}`,
        c3: `${task?.event}`,
        bl1: JSON.stringify(tagRes),
        bl2: JSON.stringify(task),
        w_trace_reqid: tagRes['x_wpk_reqid'] ?? ''
      });
    } catch (err) {
      const { errCode, msg } = getErrorInfo(err);
      tokenTaskMonitor.fail({
        msg: `添加实时标签-失败-${msg}`,
        c1: `${errCode}`,
        c2: `${task?.id}`,
        c3: `${task?.event}`,
        bl1: JSON.stringify(err),
        bl2: JSON.stringify(task),
      });
    }
  }

  // 判断下载类任务是否完成
  checkAppDownloadFinish = async (taskInfo: TaskInfo, showToast = false) => {
    const { event, state } = taskInfo;
    if (state !== TASK_STATUS.TASK_NOT_COMPLETED) {
      return false;
    }

    // 不走页面http通知，按照第三方回传通知为准
    const extraObj = getExtraInfo(taskInfo);
    if (extraObj?.isHttpFinish !== true) {
      return false;
    }

    let appIsInstalled = false;
    switch (event) {
      case TASK_EVENT_TYPE.CALL_APP_DOWNLOAD:
      case TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT:
        appIsInstalled = await checkAppDownload(taskInfo);
        break;
      case TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU:
      case TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD:
        [appIsInstalled] = await checkInstallApp('tbopen://', 'com.taobao.taobao');
        // 未安装不展示补充打点
        if (!appIsInstalled) {
          stat.custom('task_hide_installed', {
            task_id: taskInfo?.id,
            task_name: taskInfo?.name,
            taskclassify: taskInfo?.taskClassify,
            groupcode: taskInfo?.groupCode,
          });
        }
        break;
      default:
        break;
    }
    if (appIsInstalled) {
      const requestId = geneTaskRequestId();
      try {
        await dispatch.task.finishTask({
          taskId: taskInfo?.id,
          type: 'complete',
          traceId: requestId,
          showToast,
          useUtCompleteTask: !!taskInfo?.useUtCompleteTask,
          publishId: taskInfo.publishId,
        });
        return true;
      } catch (error) {
        return null;
      }
    }
    return null;
  };

  /*
   * 批量领奖接口
   * @params IBatchRewardParams
   * return 总奖励金额
   */
  async batchReward(params: IBatchRewardParams): Promise<number> {
    // 解构参数
    const { taskIdList, publishList, requestId = geneTaskRequestId() } = params;
    // 获取用户信息
    let { kps } = mx.store.get('user');
    if (!kps) {
      const userInfo: any = await getUserInfo();
      kps = userInfo?.kps_wg;
    }
    // 获取appUtRes
    const ut = await dispatch.app.getAppUtRes();
    const appId = config.appId;

    // 创建批量奖励监控
    const batchRewardMonitor = tracker.Monitor(167);
    try {
      // 创建原始签名文本
      const signOriText = `${kps || ''}${ut}${appId}${requestId}`;
      // 创建盐
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      // 创建签名
      const sign = await spamSign({ text: signOriText, salt });
      // 发送请求
      const res: IBatchRewardRes = await network.post(`${config.farmHost}/task/batchReward`, {
        kps,
        appId,
        taskIdList,
        publishList,
        requestId,
        sign,
      });
      // 解构响应
      const { state, succTids } = res;
      // 如果成功
      if (res.succTids?.length > 0) {
        const totalRewardAmount = res?.prizes?.reduce((sum, prize) => {
          return prize.win ? sum + prize.rewardItem.amount : sum;
        }, 0);
        if (totalRewardAmount > 0) {
          batchRewardMonitor.success({
            msg: '批量领奖-成功',
            c1: String(state),
            c2: JSON.stringify(succTids),
            c3: `${totalRewardAmount}`,
            c4: JSON.stringify(taskIdList),
            bl1: JSON.stringify(res),
            w_trace_reqid: requestId
          })
        } else {
          batchRewardMonitor.success({
            msg: '批量领奖--无奖励',
            c1: String(state),
            c2: JSON.stringify(succTids),
            c4: JSON.stringify(taskIdList),
            bl1: JSON.stringify(res),
            w_trace_reqid: requestId
          });
        }
        return totalRewardAmount;
      }
      return 0;
    } catch (err) {
      const { errCode, msg } = getErrorInfo(err);
      batchRewardMonitor.fail({
        msg: `批量奖励-失败-${msg}`,
        c4: JSON.stringify(taskIdList),
        c5: `${errCode}`,
        bl1: JSON.stringify(err),
        w_trace_reqid: requestId
      });
      dispatch.user.handleAccountUnusualUser(errCode, IAccountUnusualEntry.batch);
      return 0;
    }
  }

  /**
   * 品牌广告任务处理
   */
  combineBrandAdTask() {
    const { taskBrandAd } = mx.store.get('hcAd');
    const { taskList, taskListHasAd } = mx.store.get('task');
    let result: TaskInfo[] = handleCombineBrandAdTask({
      taskBrandAd,
      taskList,
      taskListHasAd,
    });
    dispatch.task.set({
      taskList: result,
    });
  }

  /**
   * 效果广告任务处理
   */
  combineCorpAdTask() {
    const { taskCorpAd, taskCorpAdStoreAccountId } = mx.store.get('hcAd');
    const { taskList, taskListHasAd } = mx.store.get('task');
    let result = handleCombineCorpAdTask({
      taskCorpAd,
      taskCorpAdStoreAccountId,
      taskList,
      taskListHasAd,
    });
    dispatch.task.set({
      taskList: result,
    });
  }
}

const dealWithToTaskFinsh = (newTaskList: TaskInfo[], oldTask: TaskInfo) => {
  const newTask = newTaskList?.find((task) => task?.id === oldTask?.id);
  if (!newTask || newTask?.state === TASK_STATUS.TASK_COMPLETED) return;
  dispatch.task.set({ curHandleTask: undefined });
  const isFinish = newTask?.state !== oldTask?.state || newTask?.dayTimes?.progress !== oldTask?.dayTimes?.progress;

  if (isFinish) {
    // 完成接口 激励视频有提示了,这里不需要重复提示了;
    // 搜索词完成有回调通知, 不需要在这里提示
    if (newTask?.event === TASK_EVENT_TYPE.VIDEO_AD || isReadTypeSearchWordsTask(newTask)) {
      return;
    }
    Toast.show(newTask.toast?.content ?? '任务完成, 奖励已发放');
  } else {
    if (newTask?.event === TASK_EVENT_TYPE.CALL_TAOBAO) {
      Toast.show('任务失败，流程未走完或登录淘宝账号不同');
      return;
    }
    if (newTask?.event === TASK_EVENT_TYPE?.SEARCH_READ_ONCE) {
      Toast.show('搜索浏览时长不够，任务未完成');
      return;
    }
    if (newTask?.event === TASK_EVENT_TYPE?.SEARCH_READ_CLICK) {
      Toast.show('未点击搜索结果，任务未完成');
      return;
    }
    Toast.show('任务未完成');
  }
};

const taskStore = new Task();
export default taskStore;
