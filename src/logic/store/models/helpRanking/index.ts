import mx from '@ali/pcom-mx';
import MxModel from '@/logic/store/models';
import dispatch from '@/logic/store';
import { IInviteScore } from '@/api/helpRanking/typings';
import { IFarmHelpPlantHomeResponse } from '../farmHelpPlant/type';
import dayjs from 'dayjs';
import { queryHelpRankingHome, queryRankHistory, queryScoreList } from '@/api/helpRanking';
import { IQueryRankHistory } from '@/mockData/queryRankHistory';
import { getPrevious30DaysTimestamp } from '@/pages/helpRanking/utils';
import { getUserInfo } from '@/lib/ucapi';
import { userInfoHandler } from '@/pages/index/utils';
import tracker from '@/lib/tracker';
import { getErrorInfo } from '../utils';
// UCMobile = uc主端; UCLite = uc极速版
export type IPr = 'UCMobile' | 'UCLite';
interface IHelpRankingState {
  scoreList: IInviteScore[];
  appVersionDetail: {
    pr: IPr | null;
    defaultPr: string;
    appVersion: string;
    appSubVersion: string;
    utRes: string;
  } | null;
  rankingHome: IFarmHelpPlantHomeResponse | null;
  rankingHistory: IQueryRankHistory | null;
  rankings: IQueryRankHistory['rankingInfo']['rankings'];
  displayRankNum: number;
}

const defaultState = {
  scoreList: [],
  rankingHome: null,
  appVersionDetail: null,
  rankingHistory: null,
  rankings: [],
  displayRankNum: 100,
};

const ONE_DAY_MS = 24 * 60 * 60 * 1000;

class HelpRanking extends MxModel {
  path: string;
  state: IHelpRankingState;

  constructor() {
    super();
    this.state = defaultState;
    this.path = 'helpRanking';
    this.init({});
  }

  init(initData) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<IHelpRankingState>) {
    console.log('set payload:', payload);
    Object.keys(payload).forEach((key) => {
      mx.store.update(`${this.path}.${key}`, payload[key]);
    });
  }
  reset() {
    mx.store.set({
      [this.path]: this.state,
    });
  }
  async regetData() {
    const userInfo: any = await getUserInfo();
    const kps = userInfo?.kps_wg || '';
    userInfo && dispatch.user.set(userInfoHandler(userInfo));
    const homeData = await queryHelpRankingHome({ kps });
    homeData &&
      dispatch.helpRanking.set({
        rankingHome: {
          ...homeData,
          curTime: homeData.curTime || Date.now(),
        },
      });
    dispatch.helpRanking.initRankingHistory((homeData?.curTime || Date.now()) - ONE_DAY_MS);
    dispatch.helpRanking.initScoreList(homeData?.curTime);
  }

  async initRankingHistory(curTime?: number) {
    const queryRankHistoryMonitor = tracker.Monitor(173);
    try {
      const queryDate = dayjs(curTime || Date.now()).format('YYYYMMDD');      
      const { kps } = mx.store.getStore().user;
      const rankingHistory = await queryRankHistory({ kps, pageNum: 1, queryDate: queryDate });
      dispatch.helpRanking.set({
        rankingHistory,
        rankings: rankingHistory?.rankingInfo?.rankings || [],
        displayRankNum: rankingHistory?.rankingInfo?.displayRankNum || 100,
      });
      queryRankHistoryMonitor.success({
        msg: '查询成功',
        bl1: JSON.stringify(rankingHistory),
      });
    } catch (e) {
      const { errCode } = getErrorInfo(e);
      queryRankHistoryMonitor.fail({
        msg: '查询失败',
        c1: errCode,
        bl1: JSON.stringify(e),
      });
    }
  }
  async initScoreList(curTime?: number) {
    const queryScoreListMonitor = tracker.Monitor(172);
    try {
      const queryCutOffTime = getPrevious30DaysTimestamp(curTime || Date.now());
      const { kps } = mx.store.getStore().user;
      const scoreList = await queryScoreList({ kps, pageNum: 1, queryCutOffTime: queryCutOffTime });
      dispatch.helpRanking.set({
        scoreList,
      });
      queryScoreListMonitor.success({
        msg: '查询成功',
        bl1: JSON.stringify(scoreList),
      });
    } catch (e) {
      const { errCode } = getErrorInfo(e);
      queryScoreListMonitor.fail({
        msg: '查询失败',
        c1: errCode,
        bl1: JSON.stringify(e),
      });
    }
  }
}

const helpRankingStore = new HelpRanking();
export default helpRankingStore;
