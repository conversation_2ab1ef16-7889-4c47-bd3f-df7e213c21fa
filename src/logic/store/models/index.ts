import mx from '@ali/pcom-mx';
import mergeWith from 'lodash.mergewith';

function isObject(value: any) {
  return value && Object.prototype.toString.call(value) === '[object Object]';
}
function isBlankObject(value: any) {
  return value && isObject(value) && Object.keys(value).length === 0;
}
function isArray(value: any) {
  return value && Object.prototype.toString.call(value) === '[object Array]';
}
function isNull(value: any) {
  return (value ?? false) === false;
}
function getType(value: any) {
  return Object.prototype.toString.call(value);
}

export default class MxModel {
  /**
   * 兜底更新值会和旧值类型可能不一致的问题，比如字符串覆盖了对象值
   * @param path mx更新的对象路径
   * @param payload 更新值
   */
  safeUpdate(path: string, payload: any) {
    function customizer(objValue: object | any[], srcValue: any) {
      // console.log(objValue, srcValue)
      if (isArray(objValue) && isArray(srcValue)) {
        // 同为数组类型，数组值直接覆盖旧值
        return srcValue;
      } else if (isObject(objValue) && isObject(srcValue) && isBlankObject(srcValue)) {
        // 同为对象类型，空对象值直接覆盖旧值
        return srcValue;
      } else if (getType(objValue) === getType(srcValue)) {
        // 其他相同类型，暂时不做自定义
        return undefined;
      } else if ((isObject(objValue) || isArray(objValue)) && isNull(srcValue)) {
        return isObject(objValue) ? {} : [];
      } else if (isNull(objValue) && srcValue) {
        return srcValue;
      }
      // 其他类型则不更新
      return objValue;
    }

    const currentData = mx.store.get(path);
    let updateValue = payload;
    if (isObject(currentData) || isArray(currentData)) {
      updateValue = mergeWith(isObject(currentData) ? { ...currentData } : [...currentData], payload, customizer);
    }
    mx.store.update(path, updateValue);
  }
}
