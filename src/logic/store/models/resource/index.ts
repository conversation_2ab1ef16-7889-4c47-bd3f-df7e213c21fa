import MxModel from '@/logic/store/models';
import { IResourceState } from './typings';
import mx from '@ali/pcom-mx';
import { queryByMultiResource } from '@/api/resource';
import config from '@/config';
import tracker from '@/lib/tracker';
import { getErrorInfo } from '@/logic/store/models/utils';
import { hiddenTimePeriodTask, hiddenNotStartTask, ifShowTask } from './../task/helper';
import { getUserInfo } from '@/lib/ucapi';
import dispatch from '@/logic/store';
import { isAdVideoTask, checkTaskFinished } from "@/pages/index/components/TaskPop/TaskList/util";
import { IQueryByMultiResource } from "@/api/typings";
import { getAllResourceTaskList } from "@/logic/store/models/resource/utils";

const defaultState = {
  multiResource: {},
  resourceAdTaskList: [],
};

//  资源位投放数据
class Resource extends MxModel {
  path: string;
  state: IResourceState;

  constructor() {
    super();
    this.state = defaultState;
    this.path = 'resource';
  }

  init(initData: Partial<IResourceState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }

  set(payload: Partial<IResourceState>) {
    Object.keys(payload).forEach((key) => {
      mx.store.update(`${this.path}.${key}`, payload[key]);
    });
  }

  reset() {
    mx.store.set({
      [this.path]: this.state,
    });
  }
  getWateringPopConfig(wateringTimes: number) {
    const { wateringPopResourceCode } = config;
    const multiResource = mx.store.get(`${this.path}.multiResource`);
    const wateringPopResourceData = multiResource?.[wateringPopResourceCode];
    if (!wateringPopResourceData) {
      return null;
    }
    const popConfig = wateringPopResourceData.attributes?.popConfig;
    let taskList = wateringPopResourceData.taskList;
    // 筛选出可以进入弹窗的任务, 未完成/有填充的视频任务
    taskList = taskList.filter(task => {
      return !checkTaskFinished(task) && ifShowTask(task);
    })
    const currentWateringTimesPopConfig = popConfig?.find((item) => {
      return Number(item.wateringTimes) === wateringTimes;
    });
    if (!currentWateringTimesPopConfig) {
      return null;
    }
    const task = taskList?.find(item => item.id === Number(currentWateringTimesPopConfig.taskId));
    if (!task) {
      return null;
    }
    return {
      ...currentWateringTimesPopConfig,
      task
    }
  }

  async queryResource(params: { firstInit?: boolean; resData?: IQueryByMultiResource | null }) {
    let { kps } = mx.store.getStore().user;
    const multiResourceMonitor = tracker.Monitor(156);
    const {
      taskWidgetResourceCode,
      getBubbleTaskCode,
      getBackInterceptCode,
      taskResourceCode,
      bbzResourceCode,
      getMatryoshkaTaskCode,
      backTagResourceCode,
      homeAdResourceCode,
      wateringPopResourceCode,
    } = config;
    const multiResourceCode = [
      taskWidgetResourceCode,
      getBubbleTaskCode,
      getBackInterceptCode,
      taskResourceCode,
      bbzResourceCode,
      getMatryoshkaTaskCode,
      backTagResourceCode,
      homeAdResourceCode,
      wateringPopResourceCode,
    ];
    try {
      let multiResource: IQueryByMultiResource;
      if (params?.firstInit && params?.resData) {
        multiResource = params.resData;
      } else {
        if (!kps) {
          const userInfo: any = await getUserInfo();
          kps = userInfo?.kps_wg;
        }
        multiResource = await queryByMultiResource(multiResourceCode.join(','), kps);
      }

      // 服务端时间戳
      let { curTime } = mx.store.getStore().task;
      if (multiResource && multiResource.__meta?.timestamp) {
        curTime = multiResource.__meta?.timestamp;
      }

      if (multiResource) {
        for (const key in multiResource) {
          if (Object.prototype.hasOwnProperty.call(multiResource, key)) {
            switch (key) {
              // 首页激励广告资源位, 不需要过滤倒计时状态的数据
              case homeAdResourceCode: {
                multiResource[key].taskList = hiddenTimePeriodTask(multiResource[key]?.taskList, curTime);
                break;
              }
              // 高价值任务
              case taskResourceCode: {
                multiResource[key].taskList = hiddenTimePeriodTask(multiResource[key]?.taskList, curTime);
                multiResource[key].taskList = hiddenNotStartTask(multiResource[key].taskList, curTime);
                dispatch.highValueTask.handleHighValueTask(multiResource[key]);
                break;
              }
              // 帮帮种活动
              case bbzResourceCode: {
                dispatch.farmHelpPlant.queryAccumulatedReward(multiResource[key]);
                break;
              }
              // 任务定时上下线
              // note: 首页气泡任务定时上下线
              // note: 挽留弹窗任务定时上下线
              // 套娃任务
              case taskWidgetResourceCode:
              case getBubbleTaskCode:
              case getBackInterceptCode:
              case getMatryoshkaTaskCode: {
                multiResource[key].taskList = hiddenTimePeriodTask(multiResource[key]?.taskList, curTime);
                multiResource[key].taskList = hiddenNotStartTask(multiResource[key]?.taskList, curTime);
                break;
              }
              default: {
                break;
              }
            }
          }
        }
        // 获取资源位的所有任务
        const allResourceTasks = getAllResourceTaskList(multiResource)
        // 过滤出激励广告任务
        const adTaskList = allResourceTasks?.filter((item) => isAdVideoTask(item))
        this.set({
          multiResource,
          resourceAdTaskList: adTaskList,
        });

        multiResourceMonitor?.success({
          msg: '请求成功',
          c1: multiResourceCode.join(','),
          bl1: JSON.stringify(multiResource),
          w_trace_reqid: multiResource['x_wpk_reqid'] ?? ''
        });
      }
    } catch (e) {
      const { errCode, msg } = getErrorInfo(e);
      multiResourceMonitor?.fail({
        msg: `请求失败-${msg}`,
        c1: multiResourceCode.join(','),
        c2: `${errCode}_${msg}`,
      });
    }
  }
}

const ResourceStore = new Resource();
export default ResourceStore;
