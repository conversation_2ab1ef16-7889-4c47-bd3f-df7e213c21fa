import { IQueryByMultiResource } from "@/api/typings";

/* 获取各个资源位投放的所有任务, 根据任务id去重
 * @param resourceData: IQueryByMultiResource
 * return: TaskInfo[]
 */
export const getAllResourceTaskList = (resourceData: IQueryByMultiResource) => {
  if (typeof resourceData !== 'object') {
    return [];
  }
  const taskMap = new Map();
  Object.keys(resourceData).forEach(resourceKey => {
    const resource = resourceData[resourceKey];
    if (resource?.taskList?.length) {
      resource.taskList.forEach(task => {
        taskMap.set(task.id, task);
      });
    }
  });
  return Array.from(taskMap.values());
}
