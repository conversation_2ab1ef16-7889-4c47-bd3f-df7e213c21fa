/* eslint-disable no-await-in-loop */
import mx from '@ali/pcom-mx';
import { IHighValueTaskState, IQueryResourceInfo } from './types';
import MxModel from '../index';
import tracker from '@/lib/tracker';
import { queryResourceInfo } from '@/api/resource';
import dispatch from '@/logic/store';
import { TASK_EVENT_TYPE, TASK_LOCATION, TASK_STATUS, TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { checkTaskFinished, logToFinishTask } from '@/pages/index/components/TaskPop/TaskList/util';
import { MODAL_ID } from '@/components/modals/types';
import baseModal from '@/lib/modal';
import { getHighDialogCookie, isRTAHighTask, needAutoShowReward, setHighDialogCookie, sortPreTaskList } from './utils';
import {
  checkAppDownload,
  checkUcLoginTask,
  getExtraInfo,
  showUninstallAppTask
} from '../task/helper';
import { geneTaskRequestId } from '../utils';
import network from '@/lib/network';
import config from '@/config';
import { taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import stat from '@/lib/stat';
import { isWeb } from '@ali/compass-env';
import { preloadImg } from '@/lib/utils/preloadImg';
import Toast from '@/lib/universal-toast';

const PRE_ONLOAD_HIGH_RESOURCE_KEY = 'preOnloadLimitResource';
const defaultState = {
  resourceConfig: {
    awardShowLimit: '3',
  },
  resourceTaskList: [],
  hiddenTaskIdList: [],
  currentTaskInfo: null,
  taskPanelNeedHidden: new Map(),
  hasTaobaoRtaTask: false,
  hasShowHighValueDialog: false,
  needFinishShowRewardTaskList: [],
} satisfies IHighValueTaskState;

class HighValueTask extends MxModel {
  path: string;
  state: IHighValueTaskState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'highValueTask';
    this.init();
  }
  init(initData: Partial<IHighValueTaskState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<IHighValueTaskState>) {
    Object.keys(payload).forEach((key) => {
      this.safeUpdate(`${this.path}.${key}`, payload[key]);
    });
  }

  /**
   * 处理高价值数据
   */
  async handleHighValueTask(resourceData: IQueryResourceInfo | null) {
    if (!resourceData) {
      return;
    }
    const bindTaobao = mx.store.get('user.bindTaobao');
    // 任务定时上下线隐藏
    dispatch.highValueTask.set({
      resourceConfig: resourceData.attributes,
      resourceTaskList: resourceData.taskList,
      hiddenTaskIdList: resourceData.hiddenTaskIdList ?? [],
      hasShowHighValueDialog: resourceData.taskList?.length ? !bindTaobao : false, // 没有绑定淘宝的时候，默认有登录任务
    });
    if (isWeb) {
      dispatch.highValueTask.handleTaskVisible(
        resourceData.taskList || [],
        resourceData.hiddenTaskIdList ?? [],
        false,
        false,
      );
      dispatch.highValueTask.preOnloadResources(resourceData.attributes || {});
    }
  }
  /**
   * 查询资源位任务列表
   */
  async queryHighValueTask() {
    const { kps } = mx.store.getStore().user;
    const resourceCode = config.taskResourceCode;
    const resourceMonitor = tracker.Monitor(149);
    try {
      const response = await queryResourceInfo(resourceCode, kps);
      if (!response.taskList.length) {
        resourceMonitor.fail({
          msg: '获取高价值任务失败',
          c1: resourceCode,
          bl1: JSON.stringify(response),
          w_trace_reqid: response['x_wpk_reqid'] ?? ''
        });
        return;
      }
      await dispatch.highValueTask.handleHighValueTask(response);
      resourceMonitor.success({
        msg: '获取高价值任务成功',
        c1: resourceCode,
        bl1: JSON.stringify(response),
        w_trace_reqid: response['x_wpk_reqid'] ?? ''
      });
    } catch (error) {
      resourceMonitor.fail({
        msg: '获取高价值任务失败',
        c1: resourceCode,
        bl1: JSON.stringify(error),
      });
    }
  }

  // 处理弹窗当前展示的任务
  async handleTaskVisible(
    resourceTaskList: TaskInfo[],
    hiddenTaskIdList: number[],
    rtaEmitEvent = false,
    showTaskDialog = true,
  ) {
    if (!resourceTaskList.length) {
      dispatch.highValueTask.set({
        hasShowHighValueDialog: false,
      });
      tracker.log({
        category: 152, // 系统自动生成，请勿修改
        msg: '高价值-没有拿到数据返回', // 将根据msg字段聚合展示在平台的top上报内容中
      });
      return;
    }
    // 接口请求UV
    stat.custom('high-value-task', {
      c: 'task',
      d: 'award',
      status: 'task-query',
    });

    const bindTaobao = mx.store.get('user.bindTaobao');
    const taskCompletedList = resourceTaskList.filter((item) => item.state === TASK_STATUS.TASK_COMPLETED);
    // 登录绑定成功了，然后领取奖励
    if (bindTaobao && taskCompletedList.length > 0) {
      dispatch.highValueTask.getAwardForHighTask(taskCompletedList || []);
      return;
    }

    let dialogTaskInfo: TaskInfo | null = null;
    let hasTaobaoRtaTask = false; // 是否有淘宝RTA任务

    // 需要检查是否已经完成任务的
    const hasTaskFinished = await dispatch.highValueTask.showAutoHighValueDialog(resourceTaskList);
    if (hasTaskFinished) {
      // 任务完成接口
      stat.custom('high-value-task', {
        c: 'task',
        d: 'award',
        status: 'task-finish',
      });
      return;
    }

    let needFinishList = resourceTaskList.filter(
      (item) =>
        !checkTaskFinished(item) && !hiddenTaskIdList.includes(item.id) && item.state !== TASK_STATUS.TASK_COMPLETED,
    );
    if (!needFinishList.length) {
      tracker.log({
        category: 152, // 系统自动生成，请勿修改
        msg: '高价值没有需要完成的任务', // 将根据msg字段聚合展示在平台的top上报内容中
        c1: '', // 自定义字段c1 对应 任务ID
        c2: '', // 自定义字段c2 对应 任务名称
        c3: '', // 自定义字段c3 对应 投放ID
        c4: '', // 自定义字段c4 对应 任务状态
        bl1: JSON.stringify(resourceTaskList), // 自定义长文本bl1 对应 任务详情
      });
      console.warn('highValueTask 高价值没有需要完成的任务');
      dispatch.highValueTask.set({
        hasShowHighValueDialog: false,
      });
      return;
    }

    // 需要在任务完成时候，领取奖励
    const needFinishShowRewardTaskList = needFinishList.filter((item) => !needAutoShowReward(item));
    dispatch.highValueTask.set({
      needFinishShowRewardTaskList: needFinishShowRewardTaskList.map((item) => String(item.id)),
    });

    for (const task of needFinishList) {
      let showTask = true; // 是否展示任务
      // 下载类： 领取过的任务，已经完成了触发任务完成
      if (task.state === TASK_STATUS.TASK_NOT_COMPLETED) {
        const result = await dispatch.task.checkAppDownloadFinish(task);
        if (result) {
          tracker.log({
            category: 152, // 系统自动生成，请勿修改
            msg: '下载任务完成-领取奖励', // 将根据msg字段聚合展示在平台的top上报内容中
            c1: String(task.id), // 自定义字段c1 对应 任务ID
            c2: String(task.name), // 自定义字段c2 对应 任务名称
            c3: String(task.publishId), // 自定义字段c3 对应 投放ID
            c4: '', // 自定义字段c4 对应 任务状态
            bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
          });
          return;
        }
      }

      // RTA 任务
      if (isRTAHighTask(task)) {
        showTask = await dispatch.highValueTask.checkTaobaoRtaTask(task);
        tracker.log({
          category: 152, // 系统自动生成，请勿修改
          msg: showTask ? 'RTA任务满足身份' : 'RTA任务不满足身份', // 将根据msg字段聚合展示在平台的top上报内容中
          c1: String(task.id), // 自定义字段c1 对应 任务ID
          c2: String(task.name), // 自定义字段c2 对应 任务名称
          c3: String(task.publishId), // 自定义字段c3 对应 投放ID
          c4: String(task.state), // 自定义字段c4 对应 任务状态
          bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
        });
        if (showTask) {
          dialogTaskInfo = task;
          hasTaobaoRtaTask = true;
          dispatch.highValueTask.set({
            hasTaobaoRtaTask: true,
          });
          break;
        }
        continue;
        // 下载任务
      } else if (task.event === TASK_EVENT_TYPE.CALL_APP_DOWNLOAD) {
        showTask = await checkAppDownload(task);
        tracker.log({
          category: 152, // 系统自动生成，请勿修改
          msg: showTask ? '下载任务满足条件' : '下载任务不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
          c1: String(task.id), // 自定义字段c1 对应 任务ID
          c2: String(task.name), // 自定义字段c2 对应 任务名称
          c3: String(task.publishId), // 自定义字段c3 对应 投放ID
          c4: String(task.state), // 自定义字段c4 对应 任务状态
          bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
        });
        if (!showTask) {
          dialogTaskInfo = task;
          dispatch.highValueTask.set({
            hasTaobaoRtaTask: false,
          });
          break;
        }
        continue;
        // 多步骤任务
      } else if (task.event === TASK_EVENT_TYPE.HIGH_VALUE_TASK) {
        const newTask = sortPreTaskList(task);
        const { result, hasTaobaoRta } = await dispatch.highValueTask.checkPreTask(newTask);
        showTask = result;
        hasTaobaoRtaTask = hasTaobaoRta;

        tracker.log({
          category: 152, // 系统自动生成，请勿修改
          msg: showTask ? '多步骤任务满足条件' : '多步骤任务不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
          c1: String(task.id), // 自定义字段c1 对应 任务ID
          c2: String(task.name), // 自定义字段c2 对应 任务名称
          c3: String(task.publishId), // 自定义字段c3 对应 投放ID
          c4: String(task.state), // 自定义字段c4 对应 任务状态
          bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
        });
        if (showTask) {
          // 子任务排序
          dialogTaskInfo = newTask;
          break;
        }
        continue;
      } else {
        // 其他任务
        showTask = await showUninstallAppTask(task);
        tracker.log({
          category: 152, // 系统自动生成，请勿修改
          msg: showTask ? '任务满足条件' : '任务不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
          c1: String(task.id), // 自定义字段c1 对应 任务ID
          c2: String(task.name), // 自定义字段c2 对应 任务名称
          c3: String(task.publishId), // 自定义字段c3 对应 投放ID
          c4: String(task.state), // 自定义字段c4 对应 任务状态
          bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
        });
        if (showTask) {
          dialogTaskInfo = task;
          break;
        }
        break;
      }
    }

    if (dialogTaskInfo && Object.keys(dialogTaskInfo).length) {
      dispatch.highValueTask.set({
        currentTaskInfo: dialogTaskInfo,
        hasShowHighValueDialog: true,
      });

      // 判断是否有登录任务
      const ucLoginTask = checkUcLoginTask(dialogTaskInfo);
      if (ucLoginTask.hasUcLoginTask) {
        dispatch.app.set({
          ucLoginTask,
        });
      }

      // 如果有高价值任务,等手淘RTA 数据返回, 再打开弹窗
      if (hasTaobaoRtaTask && rtaEmitEvent) {
        dispatch.highValueTask.set({
          currentTaskInfo: dialogTaskInfo,
          hasShowHighValueDialog: true,
        });
        if (showTaskDialog) {
          baseModal.open(MODAL_ID.HIGH_VALUE_TASK);
          stat.custom('high-value-task', {
            c: 'task',
            d: 'award',
            status: 'task-doing',
            task_id: dialogTaskInfo.id,
            task_name: dialogTaskInfo.name,
          });
          console.log('showTaskDialog', dialogTaskInfo);
        }
        return;
      }
      // 打开弹窗
      if (showTaskDialog) {
        baseModal.open(MODAL_ID.HIGH_VALUE_TASK);
        stat.custom('high-value-task', {
          c: 'task',
          d: 'award',
          status: 'task-doing',
          task_id: dialogTaskInfo.id,
          task_name: dialogTaskInfo.name,
        });
        console.log('showTaskDialog', dialogTaskInfo);
      }
      return;
    }
    stat.custom('high-value-task', {
      c: 'task',
      d: 'award',
      status: 'task-not-have',
    });
    dispatch.highValueTask.set({
      hasShowHighValueDialog: false,
    });
  }

  /**
   * 判断子任务逻辑
   */
  async checkPreTask(taskInfo: TaskInfo) {
    const bindTaobao = mx.store.get('user.bindTaobao');
    const { preTaskList = [] } = taskInfo;
    if (!preTaskList.length) {
      console.error('任务配置错误');
      return {
        result: false,
        hasTaobaoRta: false,
      };
    }

    // 手淘RTA
    const isRTATask = preTaskList.filter((item) => isRTAHighTask(item));
    if (isRTATask.length > 0) {
      const result = dispatch.highValueTask.checkTaobaoRtaTask(isRTATask[0]);
      dispatch.highValueTask.set({
        hasTaobaoRtaTask: true,
      });

      // RTA里面有下载任务
      const downloadTask = preTaskList.filter(
        (item) =>
          item.event === TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU || item.event === TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD,
      );
      if (downloadTask.length > 0) {
        await dispatch.task.checkAppDownloadFinish(downloadTask[0]);
      }

      // RTA里面有下载任务只有登录任务没有完成
      const needDoPreTaskList = preTaskList.filter((item) => !checkTaskFinished(item));
      if (needDoPreTaskList.length === 1) {
        const preTask = needDoPreTaskList[0];
        if (preTask.event === TASK_EVENT_TYPE.UC_LOGIN && bindTaobao) {
          await dispatch.task.finishTask({
            taskId: preTask.id,
            type: 'complete',
            useUtCompleteTask: !!preTask?.useUtCompleteTask,
            publishId: preTask.publishId,
            showToast: false,
          });
        }
      }

      tracker.log({
        category: 152, // 系统自动生成，请勿修改
        msg: result ? '多步骤任务-RTA满足条件' : '多步骤任务-RTA不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
        bl1: JSON.stringify(preTaskList), // 自定义长文本bl1 对应 任务详情
      });
      return {
        result,
        hasTaobaoRta: true,
      };
    }

    dispatch.highValueTask.set({
      hasTaobaoRtaTask: false,
    });

    // 下载类
    const isAppDownload = preTaskList.filter((item) => item.event === TASK_EVENT_TYPE.CALL_APP_DOWNLOAD);
    if (isAppDownload.length > 0) {
      const result = checkAppDownload(isAppDownload[0]);
      return {
        result: !result,
        hasTaobaoRta: false,
      };
    }
    return {
      result: true,
      hasTaobaoRta: false,
    };
  }
  /**
   * 手淘RTA相关任务处理
   */
  checkTaobaoRtaTask(taskInfo: TaskInfo) {
    const taobaoRtaInfo = mx.store.get('app').taobaoRtaInfo;
    const frontData = mx.store.get('app.mainInfo.frontData') || mx.store.get('app.frontData');
    const taobaoRtaConfig = frontData?.taobaoRtaConfig || {};
    const { highPriorityTaskId = [] } = taobaoRtaConfig;
    console.log('high taobaoRtaInfo', taobaoRtaConfig, taobaoRtaInfo);
    const checkShowRtaTask = (task: TaskInfo) => {
      const taskId = `${task?.id || ''}`;
      if (!task?.extra) {
        return false;
      }
      const taskExtra = getExtraInfo(task);
      if (!highPriorityTaskId && taskExtra?.defaultHidden === '1') {
        return false;
      }
      if (!taskExtra?.category || !taobaoRtaInfo?.category) {
        return false;
      }
      // 过滤和当前RTA用户身份匹配的任务
      if (highPriorityTaskId?.includes(taskId) && taskExtra?.category === taobaoRtaInfo?.category) {
        return true;
      }
      return false;
    };
    const result = checkShowRtaTask(taskInfo);
    tracker.log({
      category: 152, // 系统自动生成，请勿修改
      msg: `RTA身份校验结果-${result}`, // 将根据msg字段聚合展示在平台的top上报内容中
      c1: String(taskInfo.id || ''),
      c5: String(result),
      bl1: JSON.stringify(taskInfo || {}),
      bl2: JSON.stringify(taobaoRtaInfo || {}),
    });
    stat.custom('high-value-task', {
      c: 'task',
      d: 'award',
      status: 'task-check-rta',
      category: result,
    });
    return result;
  }

  async resourceExposure(taskInfo: TaskInfo, actionType: 'EXPOSURE' | 'CLICK', code = config?.taskResourceCode || '') {
    const { farmHost, appId } = config;
    const { kps } = mx.store.getStore().user;
    const exposureMonitor = tracker.Monitor(151);
    const query = {
      code,
      appId,
      kps,
      taskId: taskInfo?.id,
      actionType,
      publishId: taskInfo?.publishId,
    };
    try {
      const result = await network.get(`${farmHost}/task/resourceExposure`, query);
      if (result.code === 'OK' || result.__meta.code === 'OK') {
        exposureMonitor.success({
          msg: '上报成功',
          c1: String(taskInfo?.id),
          c2: taskInfo?.name,
          c3: actionType,
          c4: String(taskInfo?.publishId),
          c5: code,
          bl1: JSON.stringify(taskInfo),
          w_trace_reqid: result['x_wpk_reqid'] ?? ''
        });
      }
    } catch (error) {
      exposureMonitor.fail({
        msg: '上报失败',
        c1: String(taskInfo?.id),
        c2: taskInfo?.name,
        c3: actionType,
        c4: String(taskInfo?.publishId),
        c5: code,
        bl1: JSON.stringify(taskInfo),
        bl2: error,
      });
    }
  }

  // 弹窗确认做任务操作
  dialogConfirmEvent() {
    const currentTaskInfo = mx.store.get('highValueTask').currentTaskInfo;
    const bindTaobao = mx.store.get('user.bindTaobao');

    let taskInfo = currentTaskInfo;
    if (currentTaskInfo?.preTaskList?.length) {
      const firstTask = currentTaskInfo?.preTaskList?.filter((item) => {
        return !checkTaskFinished(item);
      });

      // 更新任务列表
      if (firstTask.length === 0) {
        dispatch.highValueTask.queryHighValueTask();
        return;
      }
      taskInfo = firstTask[0];
    }
    const requestId = geneTaskRequestId();
    // 去完成任务监控
    logToFinishTask(taskInfo, TASK_LOCATION.HIGH_VALUE_POP);
    taskActionHandler(taskInfo, requestId);
    stat.click('task_click', {
      c: 'home',
      d: 'pop',
      resource_location: TASK_LOCATION.HIGH_VALUE_POP,
      task_id: taskInfo.id,
      task_name: taskInfo.name,
      taskclassify: taskInfo?.taskClassify,
      groupcode: taskInfo?.groupCode,
    });

    if (bindTaobao) {
      // 限时弹窗校验关闭
      dispatch.timeLimitTask.lockLimitDialog();
      // 跳转后，关闭任务弹窗
      if (currentTaskInfo.event !== TASK_EVENT_TYPE.HIGH_VALUE_TASK) {
        baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
      }
    }
  }

  // 弹窗关闭逻辑
  dialogCancelEvent() {
    const bindTaobao = mx.store.get('user.bindTaobao');
    // 没有登录or绑定淘宝，然后不愿意做高价值任务的，打开任务面板
    if (bindTaobao) {
      // 异步 让其他弹窗打开逻辑先执行防止同时出现任务列表和其他弹窗
      setTimeout(() => {
        dispatch.app.openTaskPop();
      }, 50);
      return;
    }
    dispatch.app.uccLoginBind();
  }

  /**
   * 展示领奖弹窗
   */
  showAutoHighValueDialog(resourceTaskList: TaskInfo[]) {
    const bindTaobao = mx.store.get('user.bindTaobao');
    const awardShowLimit = mx.store.get('highValueTask.resourceConfig.awardShowLimit');
    const baseCurrentOpenModal = baseModal.getCurrentOpenModalObj();
    return new Promise((resolve) => {
      const hasFinishedTaskList = resourceTaskList.filter(
        (item) => checkTaskFinished(item) || item.state === TASK_STATUS.TASK_COMPLETED,
      );
      if (!hasFinishedTaskList.length) {
        resolve(false);
        return;
      }
      if (!bindTaobao && baseCurrentOpenModal[MODAL_ID.HIGH_VALUE_AWARD]) {
        resolve(true);
        return;
      }

      let showAwardDialog = false;
      for (const task of hasFinishedTaskList) {
        const { rewardItems, prizes = [], state } = task;
        const prizeInfo =
          state === TASK_STATUS.TASK_COMPLETED && !bindTaobao ? rewardItems[0] : prizes[0]?.rewardItem || {};

        // 移除自动领奖的部分，避免刷接口
        if (
          bindTaobao &&
          !needAutoShowReward(task) &&
          checkTaskFinished(task) &&
          !getHighDialogCookie(task, awardShowLimit, bindTaobao)
        ) {
          // 登录情况下，二次校验，同时延迟展示
          setTimeout(() => {
            if (!getHighDialogCookie(task, awardShowLimit, bindTaobao)) {
              Toast.show('任务完成，奖励已发放');
              stat.exposure('reward_exposure', {
                c: 'home',
                d: 'pop',
                resource_location: 'high_value_pop',
                task_id: task.id,
                task_name: task.name,
              });
              setHighDialogCookie(task, bindTaobao);
            }
          }, 1000);
          continue;
        }

        // 解决登录问题导致随机奖励展示不正确
        if (bindTaobao && Object.keys(prizeInfo).length === 0) {
          continue;
        }

        if (!getHighDialogCookie(task, awardShowLimit, bindTaobao)) {
          baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
          baseModal.open(MODAL_ID.HIGH_VALUE_AWARD, {
            ...prizeInfo,
            // 任务名称
            taskName: task.name,
            taskId: task.id,
            task,
            bindTaobao,
          });
          setHighDialogCookie(task, bindTaobao);
          tracker.log({
            category: 152, // 系统自动生成，请勿修改
            msg: `任务完成-展示弹窗-${bindTaobao}`, // 将根据msg字段聚合展示在平台的top上报内容中
            c1: String(task.id), // 自定义字段c1 对应 任务ID
            c2: String(task.name), // 自定义字段c2 对应 任务名称
            c3: String(task.publishId), // 自定义字段c3 对应 投放ID
            c4: String(task.state), // 自定义字段c4 对应 任务状态
            bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
          });
          showAwardDialog = true;
          // 非登录态展示一个就行了
          if (!bindTaobao) {
            resolve(showAwardDialog);
            return;
          }
        }
      }
      resolve(showAwardDialog);
    });
  }

  /**
   * 锁定限时任务弹窗, 奖励弹窗不需要
   */
  lockHighValueDialog() {
    const baseCurrentOpenModal = baseModal.getCurrentOpenModalObj();
    if (Object.keys(baseCurrentOpenModal).length) {
      Object.keys(baseCurrentOpenModal).forEach((item: MODAL_ID) => {
        switch (item) {
          case MODAL_ID.HIGH_VALUE_TASK:
            baseModal.removeCacheModal(MODAL_ID.HIGH_VALUE_TASK);
            break;
          default:
            break;
        }
      });
    }
  }

  /**
   * 获取任务奖励
   */
  getAwardForHighTask(taskCompletedList: TaskInfo[]) {
    taskCompletedList.forEach(async (item) => {
      const requestId = geneTaskRequestId();
      await dispatch.task.finishTask({
        taskId: item?.id,
        type: 'award',
        traceId: requestId,
        showToast: false,
        useUtCompleteTask: !!item?.useUtCompleteTask,
        publishId: item.publishId,
      });
    });
    stat.custom('high-value-task', {
      c: 'task',
      d: 'award',
      status: 'task-complete',
    });
    dispatch.highValueTask.queryHighValueTask();
  }

  /**
   * 弹窗资源预加载
   */
  preOnloadResources(resourceConfig: Record<string, any>) {
    const key = PRE_ONLOAD_HIGH_RESOURCE_KEY;
    if (localStorage.getItem(key)) {
      return;
    }

    let imgList: string[] = [];
    if (Object.keys(resourceConfig?.dialogStyle || {}).length) {
      Object.keys(resourceConfig?.dialogStyle || {}).forEach((style: string) => {
        if (['topBgImg', 'successTopBgImg', 'bottomBgImg'].includes(style)) {
          const value: string = resourceConfig?.dialogStyle[style];
          value && imgList.push(value);
        }
      });
    }
    try {
      preloadImg(imgList);
      localStorage.setItem(PRE_ONLOAD_HIGH_RESOURCE_KEY, 'true');
    } catch (error) {
      // 捕获一下error
      console.error(error);
    }
  }

  /**
   * rta 任务曝光
   */
  rtaTaskExposure() {
    const currentTaskInfo = mx.store.get('highValueTask').currentTaskInfo;
    const taobaoRtaInfo = mx.store.get('app').taobaoRtaInfo;
    if (!currentTaskInfo || !taobaoRtaInfo) {
      return;
    }
    // 列表有任务，曝光上报
    if (isRTAHighTask(currentTaskInfo)) {
      dispatch.task.ratTaskExposure();
      return;
    }

    if (currentTaskInfo.event !== TASK_EVENT_TYPE.HIGH_VALUE_TASK) {
      return;
    }
    if (!currentTaskInfo.preTaskList?.length) {
      return;
    }

    // 子任务，曝光上报
    const isRTATask = currentTaskInfo.preTaskList.filter((item) => isRTAHighTask(item));
    if (isRTATask.length > 0) {
      dispatch.task.ratTaskExposure();
    }
  }
}

const appStore = new HighValueTask();
export default appStore;
