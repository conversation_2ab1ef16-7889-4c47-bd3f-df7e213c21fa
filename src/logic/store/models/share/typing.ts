import { TaskInfo, RewardItem } from '@/pages/index/components/TaskPop/TaskList/types';

const AssistUserType = ['ALL_USER', 'BIZ_NEW', 'CLIENT_NEW', 'RECALL_USER', 'BIZ_OLD', 'CLIENT_OLD', 'NON_RECALL_USER'] as const;
export type IAssistUserType = (typeof AssistUserType)[number]
export interface IInviteInfoListItem {
  /**
   * 是否允许非目标用户助力
   */
  allowNonTargetUser: boolean;
  inviteCode: string;
  assistUserType: IAssistUserType;
  targetTask: TaskInfo;
  codeAssistList: ICodeAssistListItem[];
  /**
   * 关联子任务
   */
  subTaskIdList: number[];
}

export interface ITargetTask extends TaskInfo {
  inviteCode: string;
}

export interface ICodeAssistListItem {
  nickname: string;
  avatar: string;
  assistTime: number;
  assistUserType: IAssistUserType;
  inviterPointAmount: number;
  needPopUp: boolean;
  inviterTaskId: number;
  inviterPrizes: Array<{
    win: boolean;
    rewardItem: RewardItem;
  }>;
}

export interface ICodeAssistListClass {
  userType: IAssistUserType;
  list: ICodeAssistListItem[];
  taskId: number;
}

export const USER_TYPE_TEXT_MAP = {
  ALL_USER: '农场用户',
  BIZ_NEW: '新用户',
  CLIENT_NEW: '新用户',
  RECALL_USER: '回归老用户',
  CLIENT_OLD: '老用户',
  BIZ_OLD: '老用户',
  NON_RECALL_USER: '农场用户'
};

export interface QueryShareInfoListResponseResult {
  data: {
    inviteInfoList: IInviteInfoListItem[];
  };
}

export type IShareChannelType = 'WechatFriends' | 'QQ' | 'WechatTimeline' | 'Qzone' | 'SinaWeibo' | 'DingDing' | 'CopyLink';
