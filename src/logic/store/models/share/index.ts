import mx from '@ali/pcom-mx';
import tracker from '@/lib/tracker';
import { IShareConfig, SHARE_TARGET_TYPE } from '@ali/act-base-share/lib/types';
import MxModel from '..';
import { getQueryInviteInfoList, getShareDetail } from '@/api/share';
import { defaultConfig } from './utils';
import {
  IInviteInfoListItem,
  ITargetTask,
  QueryShareInfoListResponseResult,
  IAssistUserType,
  ICodeAssistListItem,
  IShareChannelType
} from './typing';
import { isUc } from '@/lib/universal-ua';
import { getErrorInfo } from '../utils';
import { MODAL_ID } from '@/components/modals/types';
import baseModal from '@/lib/modal';
import { ShareBase } from '@ali/act-base-share';
import Toast from '@/lib/universal-toast';
import dispatch from '@/logic/store';
import { BangBangZhongShareEntry, DoubleShareEntry } from '@/constants';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';

// 分享配置链接:
// https://yes.alibaba-inc.com/web/scene/share_dashboard/menu/share-plan/share_dashboard/share-plan/edit/6
type IPr = 'UCMobile' | 'UCLite';
const defaultState = {
  shareConfig: defaultConfig,
  ucLiteShareModuleId: 'lrkpy7iuvl0a',
  ucShareModuleId: 'tryov3qmbfwf',
  plantUcLiteShareModuleId: 'pcyki4ur3twa',
  plantUcShareModuleId: 's9njy04wsz7t',
  doubleUcLiteShareModuleId: 'mocsb9nr1n6o',
  doubleUcShareModuleId: 'qg3ajf3v2f1f',
  plantInviteCode: '',
  inviteInfoList: [],
  inviteTaskIdMap: new Map(),
  inviteCodeMap: new Map(),
  appVersionDetail: null,
};

export interface IShareState {
  shareConfig: IShareConfig;
  /** 极速版分享模块ID */
  ucLiteShareModuleId: string;
  /** 主端分享模块ID */
  ucShareModuleId: string;
  inviteInfoList: IInviteInfoListItem[];
  /** 根据任务ID取数据 */
  inviteTaskIdMap: Map<number, ITargetTask>;
  /** 根据邀请码取数据 */
  inviteCodeMap: Map<string, IInviteInfoListItem>;
  /** 帮帮种极速版分享模块ID */
  plantUcLiteShareModuleId: string;
  /** 帮帮种主端分享模块ID */
  plantUcShareModuleId: string;
  /** 帮帮种极速版分享模块ID */
  doubleUcLiteShareModuleId: string;
  /** 帮帮种主端分享模块ID */
  doubleUcShareModuleId: string;
  appVersionDetail: {
    pr: IPr | null;
    defaultPr: string;
    appVersion: string;
    appSubVersion: string;
    utRes: string;
  } | null;
}

class Share extends MxModel {
  path: string;
  state: IShareState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'share';
    this.init();
  }
  init(initData: Partial<IShareState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<IShareState>) {
    Object.keys(payload).forEach((key) => {
      this.safeUpdate(`${this.path}.${key}`, payload[key]);
    });
  }

  /**
   * 获取分享的配置
   */
  async queryShareConfig() {
    const shareMonitor = tracker.Monitor(155);
    try {
      const resp = await getShareDetail();

      if (resp.shareUnits.length) {
        this.set({
          shareConfig: {
            ...resp,
          },
        });
        shareMonitor.success({
          msg: '获取分享配置成功',
          bl1: JSON.stringify(resp),
        });
        return;
      }
      this.set({
        shareConfig: defaultConfig,
      });
      shareMonitor.fail({
        msg: '获取分享配置失败，走默认配置',
        c1: JSON.stringify(resp),
        bl1: JSON.stringify(defaultConfig),
      });
    } catch (error) {
      this.set({
        shareConfig: defaultConfig,
      });
      shareMonitor.fail({
        msg: '获取分享配置失败，走默认配置',
        c1: JSON.stringify(error),
        bl1: JSON.stringify(defaultConfig),
      });
    }
  }
  /** 查询分享信息列表 */
  async queryInviteInfoList() {
    if (!isUc) {
      return;
    }
    const inviteHelpMonitor = tracker.Monitor(163);
    const { kps } = mx.store.getStore().user;
    try {
      const inviteListRes: QueryShareInfoListResponseResult['data'] = await getQueryInviteInfoList(kps);
      if (inviteListRes?.inviteInfoList?.length) {
        const inviteTaskIdMap = new Map<number, ITargetTask>();
        const inviteCodeMap = new Map<string, IInviteInfoListItem>();
        inviteListRes?.inviteInfoList?.forEach((item) => {
          item.inviteCode && inviteCodeMap.set(item.inviteCode, item as IInviteInfoListItem);
          if (item.targetTask) {
            inviteTaskIdMap.set(item.targetTask.id, {
              inviteCode: item.inviteCode,
              ...item.targetTask,
            });
          }
          // 关联的子任务
          if (item.subTaskIdList && item.subTaskIdList.length) {
            item.subTaskIdList.forEach((subTaskId) => {
              inviteTaskIdMap.set(subTaskId, {
                inviteCode: item.inviteCode,
                ...item.targetTask,
              } as unknown as ITargetTask);
            });
          }
        });
        this.set({
          inviteInfoList: inviteListRes?.inviteInfoList,
          inviteTaskIdMap,
          inviteCodeMap,
        });

        inviteHelpMonitor.success({
          msg: '查询分享信息列表-成功',
          bl1: JSON.stringify(inviteListRes),
          w_trace_reqid: inviteListRes['x_wpk_reqid'] ?? ''
        });
      } else {
        inviteHelpMonitor.fail({
          msg: '查询分享信息列表-失败-空数据',
          bl1: JSON.stringify(inviteListRes),
          w_trace_reqid: inviteListRes['x_wpk_reqid'] ?? ''
        });
      }
    } catch (error) {
      const err = getErrorInfo(error);
      inviteHelpMonitor.fail({
        msg: '查询分享信息列表-失败-catch',
        c1: err?.errCode,
        bl1: JSON.stringify(err),
      });
    }
  }

  async dealWithReceiveInviteModal() {
    const inviteInfoList: IInviteInfoListItem[] = mx.store.getStore().share?.inviteInfoList;
    const assistList = [...inviteInfoList]?.map((item) => item?.codeAssistList)?.flat();
    if (!assistList?.length) {
      return;
    }
    const needPopList = assistList?.filter((assist) => assist?.needPopUp && assist?.inviterPrizes?.[0]?.win);

    if (!needPopList.length) {
      return;
    }

    const assistUserTypeList: Map<IAssistUserType, ICodeAssistListItem[]> = new Map();
    needPopList.forEach((list) => {
      let currentList: ICodeAssistListItem[];
      const type = list?.assistUserType;
      switch (type) {
        case 'BIZ_NEW':
        case 'CLIENT_NEW':
          currentList = assistUserTypeList.get('BIZ_NEW') ?? [];
          currentList.push(list);
          assistUserTypeList.set('BIZ_NEW', currentList);
          break;
        case 'BIZ_OLD':
        case 'CLIENT_OLD':
        case 'NON_RECALL_USER':
          currentList = assistUserTypeList.get('BIZ_OLD') ?? [];
          currentList.push(list);
          assistUserTypeList.set('BIZ_OLD', currentList);
          break;
        default:
          currentList = assistUserTypeList.get(type) ?? [];
          currentList.push(list);
          assistUserTypeList.set(type, currentList);
          break;
      }
    });
    baseModal.open(MODAL_ID.INVITE_SUCCESS, {
      assistUserTypeList,
      allNeedPopList: needPopList,
    });
  }

  /**
   * 活动主页，打开分享面板
   * @@param source 'list_card'(列表入口) | 'share_activity'(底部分享活动) | 'invite_button'(悬浮按钮) | 'pop'(弹窗) | 'reward_pop(奖励领取弹窗)'
   * @param isFarmHome 是否是农场主页
   */
  plantOpenSharePanel(source: 'list_card' | 'share_activity' | 'invite_button' | 'pop' | 'reward_pop', isFarmHome: boolean) {
    if (!dispatch.share.isUcFarmHasAuth(isFarmHome)) {
      return;
    }
    const { helpPlant, share, farmHelpPlant, app } = mx.store.getStore();
    let { inviteInfo = {} } = helpPlant.homeData ?? {};
    let { appVersionDetail = null } = helpPlant;
    let inviteCode = inviteInfo.inviteCode ?? '';
    // 农场主页
    if (isFarmHome) {
      inviteCode = farmHelpPlant.helpPlantHome?.inviteCode;
      appVersionDetail = {
        pr: app.pr,
      };
    }

    if (!inviteCode) {
      isFarmHome ? dispatch.farmHelpPlant.queryHelpPlantHome() : dispatch.helpPlant.queryHelpPlantHome();
      Toast.show('当前参与人数较多，请稍后再试');
      return;
    }
    const { plantUcLiteShareModuleId, plantUcShareModuleId } = share;
    const shareModuleId = appVersionDetail?.pr === 'UCLite' ? plantUcLiteShareModuleId : plantUcShareModuleId;
    ShareBase.share({
      shareModuleId,
      urlAppendSearchParam: {
        inviteCode,
        source: 'uc',
        entry: BangBangZhongShareEntry,
      },
      statOptions: {
        source,
        click_source: 'panel',
      },
    });
  }
  isUcFarmHasAuth(isFarmHome: boolean) {
    const { homeData } = mx.store.getStore().helpPlant;
    const { ucFarmHasAuth } = homeData;
    if (!isFarmHome && !ucFarmHasAuth) {
      Toast.show('请登录授权农场后参与活动');
    }
    return isFarmHome ? true : ucFarmHasAuth;
  }

  /**
   * 活动主页，底部打开分享
   * @@param target WechatFriends' | 'QQ' | 'WechatTimeline' | 'SinaWeibo' | 'CopyLink'
   */
  openAppointTarget(target: 'WechatFriends' | 'QQ' | 'WechatTimeline' | 'Qzone' | 'SinaWeibo' | 'DingDing' | 'CopyLink') {
    if (!dispatch.share.isUcFarmHasAuth(false)) {
      return;
    }
    const { helpPlant, share } = mx.store.getStore();
    let { inviteInfo = {} } = helpPlant.homeData ?? {};
    let { appVersionDetail = null } = helpPlant;
    let inviteCode = inviteInfo.inviteCode ?? '';
    if (!inviteCode) {
      dispatch.helpPlant.queryHelpPlantHome();
      Toast.show('当前参与人数较多，请稍后再试');
      return;
    }

    const { plantUcLiteShareModuleId, plantUcShareModuleId } = share;
    const shareModuleId = appVersionDetail?.pr === 'UCLite' ? plantUcLiteShareModuleId : plantUcShareModuleId;

    ShareBase.openAppointTarget({
      target,
      shareModuleId,
      urlAppendSearchParam: {
        inviteCode,
        source: 'uc',
        entry: BangBangZhongShareEntry,
      },
      statOption: {
        click_source: 'home',
      },
    });
  }

  openAppointTargetByFarm(target: IShareChannelType, shareModuleId: string, activity: 'bbzhong' | string, entry: string, task?: TaskInfo) {
    if (!dispatch.share.isUcFarmHasAuth(true)) {
      return;
    }
    const { farmHelpPlant, share } = mx.store.getStore();
    let { helpPlantHome = {} } = farmHelpPlant ?? {};
    let inviteCode = helpPlantHome?.inviteCode;
    
    if (activity !== 'bbzhong') {
      let inviteCodeTaskId = task?.id;
      inviteCode = share.inviteTaskIdMap.get(inviteCodeTaskId)?.inviteCode
    }

    if (!inviteCode) {
      activity === 'bbzhong' ? dispatch.farmHelpPlant.queryHelpPlantHome() : dispatch.share.queryInviteInfoList();
      Toast.show('当前参与人数较多，请稍后再试');
      return;
    }

    ShareBase.openAppointTarget({
      target,
      shareModuleId,
      urlAppendSearchParam: {
        inviteCode,
        ...(activity === 'bbzhong' ? { source: 'uc' } : {}),
        entry: activity === 'bbzhong' ? BangBangZhongShareEntry : entry,
      },
      statOption: {
        click_source: 'home',
      },
    });
  }
  /**
   * 翻倍卡 打开面板
   */
  doubleOpenSharePanel() {
    const { doublePointsCard, app, share } = mx.store.getStore();
    let { inviteInfo = {} } = doublePointsCard ?? {};
    let { pr } = app;
    let inviteCode = inviteInfo?.inviteCode ?? '';

    if (!inviteCode) {
      dispatch.doublePointsCard.queryCardInfo();
      Toast.show('当前参与人数较多，请稍后再试');
      return;
    }
    const { doubleUcLiteShareModuleId, doubleUcShareModuleId } = share;
    const shareModuleId = pr === 'UCLite' ? doubleUcLiteShareModuleId : doubleUcShareModuleId;
    ShareBase.share({
      shareModuleId,
      urlAppendSearchParam: {
        inviteCode,
        source: 'fd',
        entry: DoubleShareEntry,
      },
      statOptions: {
        click_source: 'panel',
      },
    });
  }
}

const appStore = new Share();
export default appStore;
