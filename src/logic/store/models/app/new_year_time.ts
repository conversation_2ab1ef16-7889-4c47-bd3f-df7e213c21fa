import { mx } from '@ali/pcom-iz-use';
import dispatch from '@/logic/store';
import { IAppState } from './typings';
import { getParam } from '@/lib/qs';
import { RES_CODES } from '../cms/typings';
import { DefaultAssistEntryList } from '@/constants';

// 是否在春节时间内
export const checkCurActPeriod = async () => {
  const app = mx.store.get('app');
  const task = mx.store.get('task');
  let startTime = app?.frontData?.springStartTime;
  let endTime = app?.frontData?.springEndTime;
  const isSpringPeriod = inPeriod(startTime, endTime, task?.curTime || new Date().getTime());
  dispatch?.app?.set({
    isSpringPeriod,
  });
};
export const checkIsShowBBZEnter = () => {
  const cmsStore = mx.store.getStore()?.cms;
  const isOpenCmsHidden = cmsStore[RES_CODES.BBNC_BBZ_TASKCARD_HIDE]?.items?.[0]?.open === '1';
  const isActivityActive = checkPlantActivityCurActPeriod()
  if (!isActivityActive) {
    return false;
  }
  return !isOpenCmsHidden
}

// 是否在活动时间内
export const checkPlantActivityCurActPeriod = () => {
  const app: IAppState = mx.store.get('app');
  const task = mx.store.get('task');
  let startTime = app?.frontData?.bangbangzhongConfig?.activityStartTime;
  let endTime = app?.frontData?.bangbangzhongConfig?.activityEndTime;
  const isBangBangZhongPeriod = inPeriod(
    startTime ?? 0,
    endTime ?? 0,
    app?.mainInfo?.__meta?.timestamp || task?.curTime || new Date().getTime(),
  );
  // 未到活动开放时间，提供测试方式验证
  const isTest = getParam('test') === 'true';
  return isBangBangZhongPeriod || isTest;
};

// 是否在时间范围内
export function inPeriod(startTime, endTime, currTime = new Date().getTime()) {
  return currTime >= startTime && currTime <= endTime;
}

export const checkEnableDoubleCard = () => {
  const cmsData = mx.store.get('cms');
  const enable = cmsData[RES_CODES.ENABLE_FDCARD]?.items?.[0]?.open;
  const { actStartTime, actEndTime, curTime, drawInfo } = mx.store.get('doublePointsCard');
  return (enable || drawInfo?.totalDrawTimes > 0) && inPeriod(actStartTime, actEndTime, curTime);
}
// 是否是助力entry
export const checkAssistEntry = () => {
  const urlEntry = getParam('entry');
  const app: IAppState = mx.store.get('app');
  const assistEntryList = app?.frontData?.assistEntryList ?? DefaultAssistEntryList;
  return assistEntryList.some((entry) => urlEntry.includes(entry));
};
// 获取背景图片
export const getBgImg = () => {
  const app = mx.store.get('app');
  if (app?.isSpringPeriod) {
    if (new Date().getHours() < 6 || new Date().getHours() >= 18) {
      document.documentElement.setAttribute('farm-type', 'night');
    } else {
      document.documentElement.removeAttribute('farm-type');
    }
    return 'new-year-bg';
  }
  return '';
};

// 获取背景颜色
export const getBgColor = () => {
  const app = mx.store.get('app');
  if (app?.isSpringPeriod) {
    if (new Date().getHours() < 6 || new Date().getHours() >= 18) {
      return { backgroundColor: '#1D6045' };
    } else {
      return { backgroundColor: '#689335' };
    }
  }
  return {};
};

// 淘宝登录绑定弹窗相关
export const newYearBingtaobaoImg =
  'https://img.alicdn.com/imgextra/i2/O1CN01YQJrt925t4OewKdsJ_!!6000000007583-2-tps-1378-1128.png';
export const newYearLoginBubbleImg =
  'https://img.alicdn.com/imgextra/i2/O1CN01rVzCKO1HTfdw0g2SL_!!6000000000759-2-tps-852-263.png';
export const newYearBindingBubbleImg =
  'https://img.alicdn.com/imgextra/i2/O1CN01haiEf01KEVNMQhQEQ_!!6000000001132-2-tps-852-263.png';

// 游客态相关
export const newYearTouristimg =
  'https://img.alicdn.com/imgextra/i4/O1CN01v40N4N1c9cEPtVJBE_!!6000000003558-2-tps-1008-1146.png';
export const newYearTouristmini =
  'https://img.alicdn.com/imgextra/i4/O1CN01YNkVDb1aAPgr0cOVA_!!6000000003289-2-tps-900-1023.png';

// logo
export const logoImg = 'https://img.alicdn.com/imgextra/i4/O1CN01PbkM2d1CdgDkrru8F_!!6000000000104-2-tps-759-198.png';
