import mx from '@ali/pcom-mx';
import baseModal from '@/lib/modal';
import { IAppState, IMainInfo } from './typings';
import { getParam } from '@/lib/qs';
import { queryDiamondConf, queryHomeData, queryAssetGatherInfo } from '@/api/home';
import dispatch from '@/logic/store';
import { getAppVersion, isLatestVersion, isAndroid, isIOS, isUc } from '@/lib/universal-ua';
import { StoreName } from '@/logic/type/store';
import toast from '@/lib/universal-toast/component/toast';
import MxModel from '@/logic/store/models';
import { execWithLock } from '@/lib/utils/lock';
import { TASK_STATUS, TaskInfo, TASK_EVENT_TYPE } from '@/pages/index/components/TaskPop/TaskList/types';
import stat from '@/lib/stat';
import { loadNativeAd, getNoahRtaTag, openTaobaoLoginWindow, openPage, ucparams } from '@/lib/ucapi';
import { preThirdTokenTask } from '@/logic/store/models/task/tokenTask';
import tracker from '@/lib/tracker';
import { checkInstallApp } from '@/lib/utils/app';
import { MainAPI } from '@/logic/type/event';
import { localStorageGet, localStorageSet, getSecondsSinceMidnight } from '@/lib/utils/localStorage';
import { rtaAppVersionCheck, isOpenQueryAward } from '@/logic/store/models/utils';
import { isAdVideoTask } from '@/pages/index/components/TaskPop/TaskList/util';
import { widgetTaskHandle } from '@/pages/index/components/TaskPop/TaskList/help';
import adPlayerCache from '@/pages/index/components/TaskPop/TaskList/adPlayerCache';
import { checkAssistEntry, checkCurActPeriod, checkPlantActivityCurActPeriod, inPeriod } from './new_year_time';
import { TAOBA_DOWNLOAD_URL } from '@/constants';
import workerBridge from '@/logic/worker';
import { storageDailyVisit, getDailyVisitCount } from '@/logic/store/models/app/utils';
import { homeBusinessErrCode } from '@/lib/network/businessErrCode';
import { MODAL_ID } from '@/components/modals/types';

// 春节时间
const springStartTime = new Date('2025/01/22 10:00:00').getTime();
const springEndTime = new Date('2025/02/13 10:00:00').getTime();
const isSpringPeriod = inPeriod(springStartTime, springEndTime, new Date().getTime());
const isBangBangZhongPeriod = checkPlantActivityCurActPeriod();

const defaultState = {
  isHomeDataFail: false,
  isHomeDataLoading: false,
  isPageActive: true,
  mainInfo: null,
  frontData: null,
  finishInitTask: false,
  pr: null,
  defaultPr: '',
  detailInfo: null,
  appVersionDetail: {
    appVersion: '',
    appSubVersion: '',
    utRes: '',
  },
  taobaoRtaInfo: {
    sceneId: '',
    category: '', // RTA 用户等级
    adInfo: null,
    price: '',
  },
  uc20ExitFlag: false,
  assetGatherInfo: null, // 昨日收肥数
  ucLoginTask: {
    hasUcLoginTask: false,
  },
  appInstallMap: new Map(),
  shareConfig: {
    sharePlanId: 'nbvl8ls3fxix',
    shareModules: [],
    shareUnits: [],
  },
  videoAdData: {
    appId: '',
    slotKey: '',
  },
  // 是否在春节期限内
  isSpringPeriod: isSpringPeriod,
  // 是否在帮帮种活动时间内
  isBangBangZhongPeriod: isBangBangZhongPeriod,
  // 需要在页面离开的时候，更新数据
  needPageVisibleUpdate: true,
};

class App extends MxModel {
  path: string;
  state: IAppState;
  hasShowModals: boolean;
  showHelpModal: boolean;
  showLoginModal: boolean;
  isAppInit: boolean;

  constructor() {
    super();
    this.state = defaultState;
    this.path = 'app';
    this.hasShowModals = false;
    this.showHelpModal = true;
    this.showLoginModal = true;
    this.isAppInit = false;
    this.init();
  }

  init(initData: Partial<IAppState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }

  set(payload: Partial<IAppState>) {
    Object.keys(payload).forEach((key) => {
      mx.store.update(`${this.path}.${key}`, payload[key]);
    });
  }

  pageActive() {
    const path = `${this.path}.isPageActive`;

    const get = (): IAppState['isPageActive'] => {
      return mx.store.get(path);
    };

    const set = (payload: IAppState['isPageActive']) => {
      mx.store.update(path, payload);
    };

    return {
      get,
      set,
    };
  }

  async setIsPageActive(active: boolean) {
    this.pageActive().set(active);
  }

  async onPageVisible() {
    console.log('onPageVisible');
  }

  async onLoginSuccess() {
    console.log('onLoginSuccess');
  }
  /**
   * 用户没有登录的时候，获取diamond拿配置
   * 之前是放在getTaoBaoRTAAdInfo函数中
   * 未登录下，需要获取部分配置，例如活动有效期
   */
  async queryHomeFrontData() {
    let frontData = mx.store.get('app.mainInfo.frontData') || mx.store.get('app.frontData');
    if (!frontData) {
      // 游客态未请求首页接口，单独从diamond拿配置
      frontData = await queryDiamondConf();
    }
    // 游客态下mainInfo是null，frontData另外存下来。
    this.set({
      frontData,
    });
  }

  async appInit() {
    console.log('[appInit==========]');
    const checkAppInit = Date.now();
    const isAssistEntry = checkAssistEntry();
    await dispatch.user.initUserData();
    const bindTaobao = mx.store.get('user.bindTaobao');
    const ucFarmHasAuth = mx.store.get('app.mainInfo.userInfo.ucFarmHasAuth');
    const highValueTask = mx.store.get('highValueTask');
    // 2025/4/18 RTA相关下线，先注释避免后面有新的淘宝合作
    // try {
    //   let rtaNow = Date.now();
    //   // 获取用户RTA身份标识,提到前面
    //   const taobaoRtaInfo = await this.getTaoBaoRTAAdInfo();
    //   console.log('taobaoRtaInfo', taobaoRtaInfo);
    //   this.set({ taobaoRtaInfo});
    //   tracker.log({
    //     category: 157,
    //     msg: '获取淘宝RTA耗时',
    //     wl_avgv1: Date.now() - rtaNow
    //   });
    // } catch (e) {
    //   console.error('getTaoBaoRTAAdInfo', e);
    // }
    // 用户没有登录的时候，单独获取diamond拿配置，之前是放在getTaoBaoRTAAdInfo
    await this.queryHomeFrontData();

    checkCurActPeriod();
    // 助力记录第二次访问
    !isAssistEntry && storageDailyVisit();

    // 等待RTA身份回来，去做高价值任务识别
    await dispatch.highValueTask.handleTaskVisible(
      highValueTask.resourceTaskList,
      highValueTask.hiddenTaskIdList,
      true,
      true,
    );
    // 高价值弹窗打开时 不展示ucc登录绑定弹窗
    if (!highValueTask?.hasShowHighValueDialog) {
      // 登录绑定弹窗
      this.uccLoginBind();
    }
    // 绑定了但是没认证的用户，走认证后更新数据流程
    if (bindTaobao && !ucFarmHasAuth) {
      dispatch.user.checkAuth();
    } else {
      // 初始化只需要请求一次的接口
      this.getInitDataByFirst();

      await this.getSubScreenData();
      this.showInitModals();
      dispatch.app.openTaskPop();
    }
    workerBridge.init();
    this.isAppInit = true;
    tracker.log({
      category: 157,
      msg: '页面初始化耗时',
      wl_avgv1: Date.now() - checkAppInit,
    });
  }

  // 初始化只需要请求一次的接口
  async getInitDataByFirst() {
    // 获取分享数据
    dispatch.share.queryShareConfig();
    // 查询分享信息列表
    dispatch.share.queryInviteInfoList();
  }

  // 获取非首屏数据
  async getSubScreenData() {
    const checkSubScreen = Date.now();

    // 请求汇川相关广告数据
    dispatch.hcAd.fetchAllTaskCorpAd({ forceUpdate: false});
    dispatch.hcAd.fetchTaskBrandAd({ forceUpdate: false });

    // 查询任务 & 帮帮种活动主页初始化
    await Promise.all([
      dispatch.task.taskInit(),
      dispatch.farmHelpPlant.queryHelpPlantHome(),
    ]);
    const finishInitTask = mx.store.get('app.finishInitTask');

    if (!finishInitTask) {
      await dispatch.cms.initRes();
      // 调用二方换量任务完成
      preThirdTokenTask();
    }
    await dispatch.doublePointsCard.initDoubleCardInf();
    this.set({ finishInitTask: true });
    tracker.log({
      category: 157,
      msg: '获取非首屏数据耗时',
      wl_avgv1: Date.now() - checkSubScreen,
    });
  }

  async getTaoBaoRTAAdInfo(): Promise<IAppState['taobaoRtaInfo']> {
    const app: IAppState = mx.store.get('app');
    let frontData = mx.store.get('app.mainInfo.frontData') || mx.store.get('app.frontData');
    if (!frontData) {
      // 游客态未请求首页接口，单独从diamond拿配置
      frontData = await queryDiamondConf();
    }
    // 游客态下mainInfo是null，frontData另外存下来。
    mx.store.update('app.frontData', frontData);
    let taobaoRtaConfig = frontData?.taobaoRtaConfig;
    const taobaoRtaForUCLite = frontData?.taobaoRtaForUCLite;
    console.log('====pr===', app.pr);
    if (!taobaoRtaConfig) {
      return null;
    }
    if (app.pr === 'UCLite') {
      // 极速版把taobaoRtaConfig更新为极速版配置
      taobaoRtaConfig = Object.assign(taobaoRtaConfig, taobaoRtaForUCLite);
    }
    console.log('[AD]getTaoBaoRTAAdInfo taobaoRtaConfig:', mx.store.get('app.frontData.taobaoRtaConfig'));
    const {
      androidAppKey,
      iosAppKey,
      androidSlotId,
      iosSlotId,
      type,
      sceneId,
      NuLevel,
      activeLevel,
      pkgName,
      openSchema,
      androidOpen,
      iosOpen,
      nuTaobaoInstallCheck,
    } = taobaoRtaConfig;
    if (isAndroid && !androidOpen) {
      return null;
    }
    if (isIOS && !iosOpen) {
      return null;
    }

    const isSupportAppVersion = await rtaAppVersionCheck(app.pr === 'UCLite' ? 'UCLite' : 'UC');
    if (!isSupportAppVersion) {
      return null;
    }

    const rtaAdMonitor = tracker.Monitor(134);
    const rtaTagMonitor = tracker.Monitor(135);
    const rtaShowMonitor = tracker.Monitor(138);
    const nuUserLevel = NuLevel?.split(',');
    const activeUserLevel = activeLevel?.split(',');
    let rtaAdResult;
    const aid = getParam('rta_aid') || isAndroid ? androidSlotId : iosSlotId;
    const adParams = {
      appKey: isAndroid ? androidAppKey : iosAppKey,
      aid,
      type: type,
    };
    const adStartTime = Math.round(window?.performance?.now());
    try {
      stat.custom('taobao_ad_request', {
        event_id: '19999',
        scene: sceneId,
      });
      // 请求淘宝RTA广告
      rtaAdResult = await loadNativeAd(adParams);
      console.log('[AD] rtaAdResult', rtaAdResult);
      if (rtaAdResult?.sid) {
        rtaAdMonitor.success({
          msg: '淘宝RTA广告请求成功',
          wl_avgv1: Math.round(window?.performance?.now()) - adStartTime,
          c2: adParams.aid,
          bl1: JSON.stringify(rtaAdResult || {}),
        });
      } else {
        rtaAdMonitor.fail({
          msg: '淘宝RTA广告请求失败-未返回sid',
          wl_avgv1: Math.round(window?.performance?.now()) - adStartTime,
          c2: adParams.aid,
          bl1: JSON.stringify(rtaAdResult || {}),
          bl2: JSON.stringify(adParams),
        });
      }
      stat.custom('taobao_ad_response', {
        event_id: '19999',
        scene: sceneId,
        response_result: 'success',
        sid: rtaAdResult?.sid,
      });
    } catch (err) {
      stat.custom('taobao_ad_response', {
        event_id: '19999',
        scene: sceneId,
        response_result: 'overtime',
      });
      console.log('[AD] 淘宝RTA广告请求失败', err);
      rtaAdMonitor.fail({
        msg: '淘宝RTA广告请求失败',
        wl_avgv1: Math.round(window?.performance?.now()) - adStartTime,
        c1: `${err.errCode}_${err.ext?.code}`,
        c2: adParams.aid,
        bl1: JSON.stringify(err),
        bl2: JSON.stringify(adParams),
      });
    }
    // 返回了广告ID
    if (rtaAdResult?.sid) {
      const [isInstall] = await checkInstallApp(openSchema, pkgName);
      const tagStartTime = Math.round(window?.performance?.now());
      try {
        stat.custom('taobao_rta_request', {
          event_id: '19999',
          scene: sceneId,
          sid: rtaAdResult?.sid,
        });
        // 获取用户的淘宝分类
        const rtaTagResult = await getNoahRtaTag({
          scene: sceneId,
        });
        console.log('[AD] rtaTagResult', rtaTagResult);
        const rtaTagPrice = rtaTagResult?.price || '0';
        const userLevels = nuUserLevel.concat(activeUserLevel);
        // 由于没办法配置用户的标签，提供Mock的方式验证
        const mockRtaCategory = getParam('rta_category');
        const category = mockRtaCategory || rtaTagResult?.category;
        rtaTagMonitor.success({
          msg: '淘宝RTA身份请求成功',
          c1: category,
          c2: sceneId,
          wl_avgv1: Math.round(window?.performance?.now()) - tagStartTime,
          bl1: JSON.stringify(rtaTagResult ?? {}),
        });
        stat.custom('taobao_rta_response', {
          event_id: '19999',
          scene: sceneId,
          taobao_rta_type: category,
          is_installed_taobao: isInstall ? '1' : '0',
          is_success: '1',
          sid: rtaAdResult?.sid,
          rta_price: rtaTagPrice,
        });
        // 不在用户等级中
        if (!userLevels.includes(category)) {
          console.log('[AD] not in userLevels', category);
          stat.custom('taobao_rta_show', {
            event_id: '19999',
            scene: sceneId,
            taobao_rta_type: category,
            is_installed_taobao: isInstall ? '1' : '0',
            is_show: 'fail',
            show_failed_reason: 'no_target_users',
            sid: rtaAdResult?.sid,
            rta_price: rtaTagPrice,
          });
          rtaShowMonitor.fail({
            msg: '淘宝RTA用户不在目标用户中',
            c1: category,
            c2: sceneId,
            bl1: JSON.stringify(rtaTagResult ?? {}),
            bl2: JSON.stringify({
              scene: sceneId,
            }),
          });
          return null;
        }

        // 命中用户标签
        if (mockRtaCategory || (rtaTagResult?.target === '1' && category)) {
          console.log('[AD] isInstall', isInstall);
          // 拉活用户先检测是否安装淘宝
          if (activeUserLevel.includes(category) && !isInstall) {
            stat.custom('taobao_rta_show', {
              event_id: '19999',
              scene: sceneId,
              taobao_rta_type: category,
              is_installed_taobao: isInstall ? '1' : '0',
              is_show: 'fail',
              show_failed_reason: 'no_taobao',
              sid: rtaAdResult?.sid,
              rta_price: rtaTagPrice,
            });
            rtaShowMonitor.fail({
              msg: '淘宝RTA拉活用户未安装淘宝',
              c1: category,
              c2: sceneId,
              bl1: JSON.stringify(rtaTagResult ?? {}),
              bl2: JSON.stringify({
                scene: sceneId,
              }),
            });
            return null;
          }
          if (nuTaobaoInstallCheck && nuUserLevel.includes(category) && isInstall) {
            stat.custom('taobao_rta_show', {
              event_id: '19999',
              scene: sceneId,
              taobao_rta_type: category,
              is_installed_taobao: isInstall ? '1' : '0',
              is_show: 'fail',
              show_failed_reason: 'has_taobao',
              sid: rtaAdResult?.sid,
              rta_price: rtaTagPrice,
            });
            rtaShowMonitor.fail({
              msg: '淘宝RTA拉新用户已安装淘宝',
              c1: category,
              c2: sceneId,
              bl1: JSON.stringify(rtaTagResult ?? {}),
              bl2: JSON.stringify({
                scene: sceneId,
              }),
            });

            return null;
          }
          stat.custom('taobao_rta_show', {
            event_id: '19999',
            scene: sceneId,
            taobao_rta_type: category,
            is_installed_taobao: isInstall ? '1' : '0',
            is_show: 'success',
            sid: rtaAdResult?.sid,
            rta_price: rtaTagPrice,
          });
          rtaShowMonitor.success({
            msg: '淘宝RTA用户符合展示规则',
            c1: category,
            c2: sceneId,
            bl1: JSON.stringify(rtaTagResult ?? {}),
            bl2: JSON.stringify({
              scene: sceneId,
            }),
          });
          return {
            sceneId,
            category,
            adInfo: {
              title: rtaAdResult.title,
              sid: rtaAdResult.sid,
              price: rtaTagPrice,
            },
          };
        }
        rtaTagMonitor.fail({
          msg: '淘宝RTA身份不在目标用户',
          wl_avgv1: Math.round(window?.performance?.now()) - tagStartTime,
          c1: category,
          c2: sceneId,
          bl1: JSON.stringify(rtaTagResult ?? {}),
          bl2: JSON.stringify({
            scene: sceneId,
          }),
        });
        stat.custom('taobao_rta_show', {
          event_id: '19999',
          scene: sceneId,
          taobao_rta_type: category,
          is_installed_taobao: isInstall ? '1' : '0',
          is_show: 'fail',
          show_failed_reason: 'no_target_users',
          sid: rtaAdResult?.sid,
          rta_price: rtaTagPrice,
        });
      } catch (err) {
        stat.custom('taobao_rta_response', {
          event_id: '19999',
          scene: sceneId,
          sid: rtaAdResult?.sid,
          taobao_rta_type: '',
          is_installed_taobao: '',
          is_success: '0',
        });
        console.error('[AD] 淘宝RTA身份请求失败', err);
        rtaTagMonitor.fail({
          msg: '淘宝RTA身份请求失败',
          wl_avgv1: Math.round(window?.performance?.now()) - tagStartTime,
          c1: '',
          c2: sceneId,
          bl1: JSON.stringify(err),
          bl2: JSON.stringify({
            scene: sceneId,
          }),
        });
      }
    }
    return null;
  }

  /**
   * 更新所有数据
   */
  async updateAllData(isLogin = false, showModal = true, needUpdateAll = true) {
    console.log('[正在更新首页数据]');
    console.log('是否登录回调:', isLogin);
    await dispatch.user.updateUserState(isLogin);
    // 更新资源位数据
    // await dispatch.highValueTask.queryHighValueTask();
    needUpdateAll && (await Promise.all([dispatch.resource.queryResource({}), dispatch.app.updateHomeData()]));
    execWithLock('updateSubScreenData', async (unlock) => {
      needUpdateAll && (await this.getSubScreenData());
      if (showModal) {
        this.showInitModals(isLogin);
      }
      unlock();
    });
  }

  /**
   * 弹窗处理逻辑
   * @param isLoginCallback 未登陆->登陆回调
   */
  async showInitModals(isLoginCallback?: boolean) {
    const app = mx.store.get('app');
    const user = mx.store.get('user');
    const stageLevel = mx.store.get(StoreName.StageLevel) // 树等级
    if (this.hasShowModals) {
      return;
    }
    console.log('==== showInitModals ====');
    // 1、助力弹框
    const inviteCode = getParam('inviteCode') || '';
    // 帮帮种助力回流 但没有种树 弹窗种树后完成助力流程
    const assistShowSeedDialog = !(stageLevel >= 1) && inviteCode;
    // 端外回流, 没登录绑定,先弹登录绑定弹框
    // if (inviteCode && !user?.bindTaobao && isUc && this.showLoginModal) {
    //   dispatch.user.checkLoginAndBind();
    //   this.showLoginModal = false;
    // }
    // 没有绑定淘宝 不处理弹框
    if (!user?.bindTaobao) {
      return;
    }

    // 确保登录后弹的弹窗 只弹一次
    this.hasShowModals = true;

    // 昨日肥料收益数据弹窗
    this.queryAssetGatherInfo();

    // 树的等级>=1 助力
    if (inviteCode && stageLevel >= 1) {
      dispatch.task.finishShareTask(inviteCode);
    }

    // 2、收到助力提醒弹框
    dispatch.share.dealWithReceiveInviteModal();
    // 新人领取礼物弹框
    if (app?.mainInfo?.activityData?.newUserGift?.status === 'CAN_RECEIVE') {
      mx.event.emit('new_gift', { newUserGitData: app?.mainInfo?.activityData?.newUserGift });
    }
    // 选择种子弹框: 农场新人 ｜ 助力果树未选种
    if (mx.store.get(StoreName.Fresher) || assistShowSeedDialog) {
      this.showSelectSeedModal({
        isAssist: assistShowSeedDialog,
        status: assistShowSeedDialog ? 'assist_user' : 'newuser',
        needToast: assistShowSeedDialog,
      })
    }
    // 翻倍卡相关弹窗
    dispatch.doublePointsCard.handleDialogQueue(isLoginCallback);
    // 帮帮种活动相关弹窗
    dispatch.farmHelpPlant.handleDialogQueue();
    // 限时福利弹窗
    dispatch.timeLimitTask.getTaskDetail();

    // 小组件任务访问and小组件安装
    widgetTaskHandle();
  }

  /**
   * 展示选种弹框
   */
  showSelectSeedModal = (params: { isAssist: boolean; status: string; needToast?: boolean }) => {
    const { isAssist = false, status, needToast = false } = params
    const inviteCode = getParam('inviteCode') || '';
    stat.custom('select_seed_exposure_emit', {
      selection_status: status,
    });
    mx.event.emit('select_seed', {
      flag: mx.store.get(StoreName.Fresher),
      onSuccess: () => {
        isAssist && dispatch.task.finishShareTask(inviteCode);
      },
      btnTitle: isAssist ? '种下果树后进行助力' : undefined,
    });
    needToast && toast.show('种下果树后可进行助力');
  }

  // 针对极速版UCC登录优化
  fixInUcLiteBinding = async () => {
    const app: IAppState = mx.store.get('app');
    const bindTaobao = mx.store.get('user.bindTaobao');
    const appVersion = await dispatch.app.getAppVersion();
    // 判断是否安装淘宝
    const [appIsInstalled] = await checkInstallApp('tbopen://', 'com.taobao.taobao');
    const callBack = () => {
      toast.show('请先下载淘宝后再绑定登录～', {
        duration: 2000,
      });
      setTimeout(() => {
        openPage(TAOBA_DOWNLOAD_URL);
      }, 2000);
    };

    // 极速版IOS，没有安装淘宝，没有绑定淘宝, 且低于版本号17.4.3
    if (!bindTaobao && !appIsInstalled && app?.pr === 'UCLite' && isIOS && !isLatestVersion(appVersion, '17.4.3')) {
      callBack();
      return {
        checkIosInUcLite: false,
        appIsInstalled,
        appVersion,
      };
    } else if (!bindTaobao && !appIsInstalled && app?.pr === 'UCLite' && !isIOS) {
      // 极速版安卓没有安装淘宝
      callBack();
      return {
        checkIosInUcLite: false,
        appIsInstalled,
        appVersion,
      };
    }

    return {
      checkIosInUcLite: true,
      appIsInstalled,
      appVersion,
    };
  };

  /**
   *  获取当前app版本号
   */
  async getAppVersion(): Promise<string> {
    const appVersionDetail = mx.store.get('app.appVersionDetail');
    let appVersion: string = appVersionDetail.appVersion;
    if (!appVersion) {
      appVersion = await getAppVersion();
      dispatch.app.set({
        appVersionDetail: {
          ...appVersionDetail,
          appVersion,
        },
      });
    }
    return appVersion;
  }
  /**
   *  获取当前用户的ut
   */
  async getAppUtRes(): Promise<string> {
    const appVersionDetail = mx.store.get('app.appVersionDetail');
    let utRes: string = appVersionDetail.utRes;
    if (!utRes) {
      const ucparamsRes = await ucparams({ params: 'ut' });
      dispatch.app.set({
        appVersionDetail: {
          ...appVersionDetail,
          utRes: decodeURIComponent(ucparamsRes.ut || ''),
        },
      });
      return decodeURIComponent(ucparamsRes.ut || '');
    }
    return utRes;
  }

  // ucc登录绑定
  async uccLoginBind() {
    if (!isUc) {
      return
    }
    const app = mx.store.get('app');
    const isLogin = mx.store.get('user.isLogin');
    const bindTaobao = mx.store.get('user.bindTaobao');
    const { checkIosInUcLite, appIsInstalled, appVersion } = await this.fixInUcLiteBinding();
    console.log(`我是版本号`, appVersion);
    // 登录绑定
    if (!isLogin) {
      // 兼容极速版 以及低版本 直接调用弹窗
      if (app?.pr === 'UCLite' || !isLatestVersion(appVersion, '17.0.8.0000')) {
        checkIosInUcLite && mx.event.emit('modal_binding_taobao', { modalName: 'login' });
        return;
      }
      if (appIsInstalled) {
        mx.event.emit('modal_binding_taobao', { modalName: 'login' });
        return;
      }
      // 未安装淘宝用手机号登录
      stat.custom('phone_login');
      try {
        await openTaobaoLoginWindow({ loginType: 'phone', orderPlatformIds: '1010', agreeLicense: 1, bottomLoginText: '' });
      } catch (e) {
        console.log('打开登录面板失败')
      }
      return
    }
    if (!bindTaobao) {
      // 未绑定去绑定淘宝
      checkIosInUcLite && mx.event.emit('modal_binding_taobao', { modalName: 'bindTaobao', from: 'init' });
    }
  }
  async updateHomeData() {
    let kps = mx.store.get('user.kps');
    if (!kps) {
      const userInfo = await dispatch.user.initUserData();
      kps = userInfo.kps;
    }
    return await queryHomeData(kps)
      .then((res: IMainInfo & { code: string }) => {
        const isApiSuccess = res?.userInfo;
        const auth = res?.userInfo?.ucFarmHasAuth;
        const bindTaobao = mx.store.get('user.bindTaobao');
        dispatch.app.set({
          mainInfo: res,
        });
        if (isApiSuccess && !auth && bindTaobao) {
          dispatch.user.checkAuth();
        }

        if (!isApiSuccess && !homeBusinessErrCode.includes(res?.code)) {
          toast.show('网络不佳，请检查网络状况');
        }
      })
      .catch((err) => {
        const { code } = err;
        if (!code) {
          console.error('home接口请求失败');
          toast.show('网络不佳，请检查网络状况');
        }
      });
  }
  /*
   * 异步查询视频广告奖励
   * @param isInit 是否首次打开页面，首次打开查询所有的slot
   * */
  async dealwithVideoAward(isInit: boolean) {
    const bindTaobao = mx.store.get('user.bindTaobao');
    if (!bindTaobao) {
      return;
    }
    const invokeVideoTaskIds = adPlayerCache.getInvokeTaskIds(isInit);
    const taskList: TaskInfo[] = mx.store.get('task.taskList');
    const resourceAdTaskList = mx.store.get('resource.resourceAdTaskList');
    const uniqueTaskList = Array.from(new Map([...taskList, ...resourceAdTaskList].map(task => [task.id, task])).values());
    let adTaskList: TaskInfo[] = [];
    if (invokeVideoTaskIds?.length) {
      // 有调起但未收到领奖回调的任务
      adTaskList = uniqueTaskList?.filter((item) => invokeVideoTaskIds.includes(item.id) && item.state === TASK_STATUS.TASK_DOING);
    } else if (isInit) {
      // 首次打开页面，查所有未完成的激励视频任务
      adTaskList = uniqueTaskList?.filter((item) => isAdVideoTask(item) && item.state === TASK_STATUS.TASK_DOING);
    }
    const appVersion = await dispatch.app.getAppVersion();
    try {
      const isLite = mx.store.get('app.pr') === 'UCLite';
      if (adTaskList?.length && isOpenQueryAward(appVersion, isLite)) {
        adTaskList.sort((a, b) => {
          // 多个slot同时查时，先查询激励浏览类的任务，避免浏览任务奖励查询时间过长
          const aPriority = a.event === TASK_EVENT_TYPE.VIDEO_AD_BROWSE ? -1 : 1;
          const bPriority = b.event === TASK_EVENT_TYPE.VIDEO_AD_BROWSE ? -1 : 1;
          return aPriority - bPriority;
        });
        for (const itemAd of adTaskList) {
          dispatch.task.queryAndGetAdAward(itemAd, isInit);
          // tanx限制，每个slot查询必须间隔2s，否则会查不到
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }
      }
    } catch (error) {
      console.log('error', error);
    }
  }
  // 是否一开始就打开任务面板
  async openTaskPop() {
    // @ts-ignore
    const user = mx.store.get('user');
    const hasLimitTaskDialog = mx.store.get('timeLimitTask.hasLimitTaskDialog');
    const urlEntry = getParam('entry');

    // 有高价值或者限时弹窗的时候，暂时不打开
    const baseCurrentOpenModal = baseModal.getCurrentOpenModalObj();
    const isShare = checkAssistEntry();

    // 检查当前是否只有翻倍卡弹窗打开
    const currentModalKeys = Object.keys(baseCurrentOpenModal);
    const isOnlyDoubleCardModal = currentModalKeys.length === 1 &&
      currentModalKeys[0] === MODAL_ID.DOUBLE_CARD_LOTTERY;
    // 如果只有翻倍卡弹窗打开，监听其关闭事件并在关闭后打开任务列表
    if (isOnlyDoubleCardModal) {
      const handleCloseDoubleCard = () => {
        mx.event.off(MainAPI.CLOSE_DOUBLE_CARD_POP, handleCloseDoubleCard);
        // 延迟一下确保弹窗完全关闭
        setTimeout(() => {
          mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: 'auto' });
        }, 100);
      };
      mx.event.on(MainAPI.CLOSE_DOUBLE_CARD_POP, handleCloseDoubleCard);
      return;
    }

    // 有其他弹窗或者限时弹窗的时候，暂时不打开
    if (Object.keys(baseCurrentOpenModal).length || hasLimitTaskDialog || isShare) {
      return;
    }
    // 获取上一次任务面板的状态
    const lastTimeTaskPopStatus = localStorage.getItem('taskPopStatus');

    /**
     * 1、有entry 或则 上一次任务面板时打开状态
     * 2、已经登陆
     * 3、已经绑定淘宝
     * 以上条件都满足才走下面的是否自动展开任务面板的逻辑
     */
    if (!((urlEntry || lastTimeTaskPopStatus === 'open') && user?.isLogin && user?.bindTaobao)) {
      return;
    }
    const { ucfarm_task_window } = mx.store.get('cms');
    const resultObj = ucfarm_task_window?.items?.[0]?.option?.find((item) => {
      return item?.entry && item?.entry?.includes(urlEntry);
    });
    console.log(`ucfarm_task_window:`, ucfarm_task_window);
    console.log(`entryresult:`, resultObj);
    // 上一次任务面板是打开状态、则刷新页面或则重新进入页面时也要打开
    if (lastTimeTaskPopStatus === 'open') {
      mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: 'auto' });
      return;
    }
    // 是否打开任务面板
    if (resultObj?.showtaskPopFlag !== 'true') {
      return false;
    }
    // 是否当日只展示一次
    if (resultObj?.onceFlag === 'true') {
      const initTaskPop: any = localStorageGet('inittaskpop');
      if (initTaskPop?.urlEntry !== urlEntry) {
        mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: 'auto' });
        localStorageSet(
          'inittaskpop',
          { urlEntry: initTaskPop?.urlEntry ? `${initTaskPop?.urlEntry},${urlEntry}` : `${urlEntry}` },
          getSecondsSinceMidnight(),
        );
      }
    } else {
      mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: 'auto' });
    }
  }
  async checkAppInstall() {
    const appInstallCheck = mx.store.getStore()?.cms?.app_install_check?.items ?? [];
    const { checkOpen = '0', appList = [] } = appInstallCheck[0] || {};
    try {
      // console.log('cms appCheckInfo:', checkOpen, appList);
      if (checkOpen === '1') {
        const checkList = appList.map((item) => {
          return checkInstallApp(item.scheme, item.pkgName);
        });
        await Promise.all(checkList);
      }
    } catch (err) {
      console.info(err);
    }
  }
  async queryAssetGatherInfo() {
    let kps = mx.store.get('user.kps');
    if (!kps) {
      return
    }
    const dailyVisitCount = getDailyVisitCount();
    // 仅当天第二次访问时出弹窗
    if (dailyVisitCount !== 2) {
      return
    }
    try {
      const res = await queryAssetGatherInfo(kps);
      dispatch.app.set({
        assetGatherInfo: res
      });
    } catch (e) {
      console.log('e:', e);
    }
  }
}

const appStore = new App();
export default appStore;
