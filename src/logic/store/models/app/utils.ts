import { LocalStorageKey } from "@/lib/utils/localStorage_constant";

const dailyVisitKey = LocalStorageKey.DAILY_VISITE_COUNT; // 固定的 key

// 存储当天访问次数信息
export const storageDailyVisit = () => {
  const today = new Date().toLocaleDateString();
  try {
    const data = localStorage.getItem(dailyVisitKey);
    // 如果没有数据，初始化为今天第一次访问
    if (!data) {
      localStorage.setItem(dailyVisitKey, JSON.stringify({ date: today, count: 1 }));
      return;
    }
    // 解析数据
    const { date: storedDate, count: storedCount } = JSON.parse(data);
    // 比较日期
    if (storedDate === today) {
      // 同一天，增加访问次数
      const newCount = storedCount + 1;
      localStorage.setItem(dailyVisitKey, JSON.stringify({ date: today, count: newCount }));
    } else {
      // 不是同一天，重置为第一次访问
      localStorage.setItem(dailyVisitKey, JSON.stringify({ date: today, count: 1 }));
    }
  } catch (e) {
    console.log('storageDailyVisit error', e);
  }
}
// 获取当天访问次数
export const getDailyVisitCount = () => {
  const today = new Date().toLocaleDateString();
  try {
    const data = localStorage.getItem(dailyVisitKey);
    if (!data) {
      return 0;
    }
    const { date: storedDate, count: storedCount } = JSON.parse(data);
    if (storedDate !== today) {
      return 0
    }
    return storedCount;
  } catch (e) {
    return 0
  }
}