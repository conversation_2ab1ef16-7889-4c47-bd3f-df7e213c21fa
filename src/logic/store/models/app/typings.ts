import { IGameInfo, IAssetsGatherInfo } from '@/api/typings';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { IShareConfig } from '@ali/act-base-share';

export interface IAppState {
  isHomeDataFail: boolean;
  isHomeDataLoading: boolean;
  isPageActive: boolean;
  mainInfo: IMainInfo | null;
  finishInitTask: boolean; // 是否完成任务初始化
  pr: IPr | null;
  defaultPr: string;
  detailInfo: IDetailInfo | null;
  appVersionDetail: {
    appVersion: string;
    appSubVersion: string;
    utRes: string;
  };
  taobaoRtaInfo: {
    sceneId: string;
    category: string;
    adInfo: {
      title: string;
      sid: string;
      price: string;
    } | null;
  } | null;
  uc20ExitFlag: boolean;
  assetGatherInfo: IAssetsGatherInfo;
  ucLoginTask?: {
    // 是否有UC 登录任务
    hasUcLoginTask: boolean;
    taskInfo?: TaskInfo;
  };
  // app 是否安装
  appInstallMap: Map<string, {
    installed: boolean;
    res: Record<any, any>;
  }>;
  /**
   * 分享相关配置
   */
  shareConfig: IShareConfig;
  /** 激励视频slot数据 */
  videoAdData: {
    appId: string;
    slotKey: string;
  };
  isSpringPeriod: boolean;
  isBangBangZhongPeriod: boolean;
  frontData: IFrontData | null; // 前端diamond数据
  needPageVisibleUpdate: boolean;
}

// UCMobile = uc主端; UCLite = uc极速版
export type IPr = 'UCMobile' | 'UCLite';

export interface IMainInfo {
  userInfo: IUserInfo;
  activityData: IActivityData;
  gameInfo: IGameInfo;
  __meta?: {
    timestamp: number; // 服务端时间
  };
  frontData: IFrontData; // 前端diamond数据
  deviceLevel?: string; // 设备等级字段
}

export interface IFrontData {
  adVideoConfig: IAdVidoeConfig;
  ucLiteAdVideoConfig: IAdVidoeConfig;
  welfareLink: string;
  detailLink: string;
  ucLiteWelfareLink: string;
  shareConfig: IShareInfo;
  ucLiteShareConfig: IShareInfo;
  // 二方换量任务token参数列表
  paramsToken: string[];
  paramsEntry: string[];
  downloadTaobaoUrl: string;
  taobaoRtaConfig: TaobaoRtaConfig;
  taobaoRtaForUCLite: TaobaoRtaConfig;
  userAgreement: string;
  privacyPolicy: string;
  redPacketLink: string;
  /** ios支持异步查奖客户端版本号 */
  iosOpenQueryAwardVersion: string;
  /** ios极速版支持异步查奖客户端版本号 */
  iosLiteOpenQueryAwardVersion: string;
  /** 需要打实时人群包标签的任务ID列表 */
  needAddTagTaskIdList: string[];
  /** 限时额外池任务事件列表 */
  limitTimeExtraPoolEvents: string[];
  /** 帮帮种活动数据 */
  bangbangzhongConfig: {
    activityStartTime: number;
    activityEndTime: number;
    activityLink: string;
  };
  assistEntryList: string[];
  springStartTime: number;
  springEndTime: number;
}

export interface IShareInfo {
  link: string;
  iconUrl: string;
  title: string;
  content: string;
  weiboLink?: string;
}

export type TAllAdType = 'tt' | 'mixed' | 'hc' | 'default';

export interface IAdVidoeConfig {
  androidAppId: string;
  iosAppId: string;
  androidSlotKey: string;
  iosSlotKey: string;
  type: TAllAdType;
}
export interface TaobaoRtaConfig {
  androidAppKey: string;
  iosAppKey: string;
  sceneId: string;
  androidSlotId: string;
  iosSlotId: string;
  type: string;
  pkgName: string;
  openSchema: string;
  NuLevel: string; // NU用户等级
  activeLevel: string; // 拉活用户等级
  nuTaobaoInstallCheck: boolean;
  highPriorityTaskId: string[]; // RTA高优任务ID
  lowPriorityTaskId: string[]; // RTA低优任务ID
}

export type CustomTaskStatus = 'NONE' | 'NOT_START' | 'CAN_RECEIVE' | 'ADVANCE_NOTICE';

export interface IActivityData {
  newUserGift: {
    status: CustomTaskStatus;
    pointAmount: number; // 奖励肥料
    curTask: ICurTask;
  };
  tomorrowGift: ITomorrowGift;
  multiOpenGift: IMultiOpenGift;
}

export interface ITomorrowGift {
  status: CustomTaskStatus; // NONE 没有任务 NOT_START 未到时间领取 CAN_RECEIVE 可以领取 ADVANCE_NOTICE 预告
  receiveStartTime: number; // 领取开始时间，0表示没时间限制
  pointAmount: number; // 奖励肥料
  pointAmountPerTimes: number; // 每次奖励肥料数
  completeTimes: number; // 已完成次数
  maxTimes: number; // 最大次数
  curTask: ICurTask;
}

export interface IMultiOpenGift {
  status: 'NONE' | 'INIT' | 'CAN_RECEIVE'; // NONE 没有任务 INIT 任务已触发未可领 CAN_RECEIVE 可以领取
  completeTimes: number; // 已完成次数
  needTimes: number; // 需要完成次数
  groupKey: string; // ab实验结果
  round: number; // 第几局
  curTask: ICurTask;
}

export interface ICurTask {
  id: number;
  name: string;
  target: string;
  event: string;
  /** 任务分类 */
  taskClassify?: string;
  /** 任务分组 */
  groupCode?: string;
}

export enum EMultiOpenGiftStatus {
  // NONE 没有任务
  NONE = 'NONE',
  // INIT 任务已触发未可领
  INIT = 'INIT',
  // CAN_RECEIVE 可以领取
  CAN_RECEIVE = 'CAN_RECEIVE',
}

export interface IUserInfo {
  ucFarmHasAuth: boolean; // 是否授权 UC/夸克农场
  avatar: string;
  extInfoMap: any;
  firstLogin: boolean;
  fresher: boolean;
  hasAuthorized: boolean;
  hasSubscribed: boolean;
  inviteCode: string;
}

export interface IDetailInfo {
  detailData: IDetail[];
  curTime?: number;
}

export interface IDetail {
  createTime: number;
  detail: string;
  from: string;
  icon: string;
  title: string;
}

export enum IRedPacketStatus {
  /** 收入确认 */
  INCOME_CONFIRM = 'INCOME_CONFIRM',
  /** 收入过期 */
  INCOME_EXPIRE = 'INCOME_EXPIRE',
  /** 预支出 */
  PAYOUT_PRE = 'PAYOUT_PRE',
  /** 支出确认  */
  PAYOUT_CONFIRM = 'PAYOUT_CONFIRM',
  /** 支出取消 */
  PAYOUT_CANCEL = 'PAYOUT_CANCEL',
  /** 收入被提现 */
  INCOME_USED ='INCOME_USED',
  /** 提现中  */
  INCOME_USING = 'INCOME_USING'
}
export interface IRedPacketDetail {
  createTime: number;
  expireTime: number;
  state: IRedPacketStatus;
  amount: number;
  title: string;
  appId: string;
}
