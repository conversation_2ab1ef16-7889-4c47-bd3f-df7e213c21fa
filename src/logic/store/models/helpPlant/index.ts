import mx from '@ali/pcom-mx';
import MxModel from '@/logic/store/models';
import dispatch from '@/logic/store';
import { IHelpPlantHomeRes, IQueryRankingRes } from '@/api/helpPlant/typings';
import config from '@/config';
import network from '@/lib/network';
import { geneTaskRequestId, getErrorInfo } from '@/logic/store/models/utils';
import { formatTimestamp } from '@/lib/utils/date';
import tracker from '@/lib/tracker';
import { IQueryByMultiResource } from '@/api/typings';
import { queryByMultiResource } from '@/api/resource';
import { queryRankingList } from '@/api/helpPlant';
import { queryRankHistory } from '@/api/helpRanking';
import { TASK_EVENT_TYPE, TASK_STATUS } from '@/pages/index/components/TaskPop/TaskList/types';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import { IQueryRankHistory } from '@/mockData/queryRankHistory';
import { IDialogHintType } from '../farmHelpPlant/type';
import Toast from '@/lib/universal-toast';
import { openPage, getUserInfo } from '@/lib/ucapi';
import { updateRankingDisplay } from '@/pages/helpPlant/utils';
import { getParam } from '@/lib/qs';
import { IAccountUnusualEntry } from '@/logic/type/user';
import stat from '@/lib/stat';

// UCMobile = uc主端; UCLite = uc极速版
export type IPr = 'UCMobile' | 'UCLite';
export interface IHelpPlantState {
  pageDataReady: boolean;
  homeData: IHelpPlantHomeRes;
  rankingList: IQueryRankingRes;
  multiResource: IQueryByMultiResource | null;
  yesterdayRanking: IQueryRankHistory | null;
  appVersionDetail: {
    pr: IPr | null;
    defaultPr: string;
    appVersion: string;
    appSubVersion: string;
    utRes: string;
  } | null;
  showHandGuide: boolean;
}

const defaultState = {
  pageDataReady: false, // 是否已拿到首屏数据
  homeData: {
    frontData: {
      ucFarmLink: '',
      ruleLink: '', // 规则
      historyRankingLink: '', // 历史榜单
      detailLink: '', // 肥料明细
      redPacketLink: '', // 红包明细
      shareConfig: {
        ucShareModuleId: 'v9dost6gew0p', // 主端分享模块
        ucLiteShareModuleId: 'kftnkofntv6g', // 极速版分享模块
      },
    },
    inviteInfo: {
      inviteCode: '',
    },
  } as unknown as IHelpPlantHomeRes,
  rankingList: {} as unknown as IQueryRankingRes,
  multiResource: null,
  appVersionDetail: null,
  yesterdayRanking: null,
  showHandGuide: false,
};

class HelpPlant extends MxModel {
  path: string;
  state: IHelpPlantState;

  constructor() {
    super();
    this.state = defaultState;
    this.path = 'helpPlant';
    this.init({});
  }

  init(initData) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<IHelpPlantState>) {
    Object.keys(payload).forEach((key) => {
      mx.store.update(`${this.path}.${key}`, payload[key]);
    });
  }
  reset() {
    mx.store.set({
      [this.path]: this.state,
    });
  }
  getSubTaskList(payload: IQueryByMultiResource) {
    const resourceTaskList = payload?.[config.bbzResourceCode]?.taskList || [];
    return resourceTaskList
      .filter((taskItem) => taskItem.event === TASK_EVENT_TYPE.SUB_INVITE_HELP)
      .sort((a, b) => a.target - b.target);
  }
  // 初始化一些次屏数据，需要等首屏数据加载完成之后再执行
  async initHelpPlant() {
    await Promise.all([dispatch.helpPlant.updateRankingList(), dispatch.helpPlant.queryYesterdayRanking()]);
    dispatch.helpPlant.shareInit();
    // 累计阶梯 访问自动领奖弹框
    dispatch.helpPlant.autoReceiveLadderRewards();
    // 弹窗处理
    dispatch.helpPlant.handleDialogQueue();
    dispatch.helpPlant.set({ pageDataReady: true });
    const { homeData, rankingList } = mx.store.getStore().helpPlant;
    dispatch.helpPlant.logAssistByInviteCode(homeData.inviteInfo?.inviteCode, rankingList.myInfo?.newestScore);
  }

  async updatePageData(firstScreenRetry = false) {
    await Promise.all([dispatch.helpPlant.updateMultiResource(), dispatch.helpPlant.queryHelpPlantHome()]);
    dispatch.helpPlant.initHelpPlant();
  }

  /**
   * 分享相关内容初始化
   */
  shareInit() {
    // 查询分享信息列表
    dispatch.share.queryShareConfig();

    const { homeData } = mx.store.getStore().helpPlant;
    if (homeData?.frontData?.shareConfig) {
      const { shareConfig } = homeData.frontData;
      dispatch.share.set({
        plantUcLiteShareModuleId: shareConfig?.ucLiteShareModuleId,
        plantUcShareModuleId: shareConfig?.ucShareModuleId,
      });
    }
  }
  async queryHelpPlantHome() {
    const { farmHost, appId } = config;
    const requestId = geneTaskRequestId();
    const { kps } = mx.store.getStore().user;
    const queryHelpPlantHomeMonitor = tracker.Monitor(166);
    try {
      const res: IHelpPlantHomeRes = await network.get(`${farmHost}/help/queryHome`, {
        appId,
        kps,
        requestId,
        pageNum: 1,
        pageSize: 100,
      });
      queryHelpPlantHomeMonitor.success({
        msg: '查询成功',
        bl1: JSON.stringify(res),
        w_trace_reqid: requestId
      });
      dispatch.helpPlant.set({
        homeData: res,
      });
      if (res.frontData?.shareConfig) {
        const { shareConfig } = this.state.homeData.frontData;
        dispatch.share.set({
          plantUcLiteShareModuleId: shareConfig?.ucLiteShareModuleId,
          plantUcShareModuleId: shareConfig?.ucShareModuleId,
        });
      }
      return res;
    } catch (e) {
      const { errCode } = getErrorInfo(e);
      queryHelpPlantHomeMonitor.fail({
        msg: '查询失败',
        c1: errCode,
        bl1: JSON.stringify(e),
        w_trace_reqid: requestId
      });
      return null;
    }
  }
  async updateMultiResource() {
    const { kps } = mx.store.getStore().user;
    const multiResourceData = await queryByMultiResource(config.bbzResourceCode, kps);
    if (multiResourceData) {
      dispatch.helpPlant.set({
        multiResource: multiResourceData,
      });
    }
  }
  async updateRankingList() {
    const { kps } = mx.store.getStore().user;
    const { homeData } = mx.store.getStore().helpPlant;
    const maxRankNum = homeData.displayRankNum || 100;
    const rankingList = await queryRankingList(kps, maxRankNum);
    if (rankingList) {
      dispatch.helpPlant.set({
        rankingList: updateRankingDisplay(rankingList),
      });
    }
    // 更新埋点公共参数
    let assist_rank =
      rankingList?.myInfo?.ranking > 0 ? rankingList?.myInfo?.ranking : rankingList?.myInfo?.newestScore > 0 ? 0 : '-1';
    stat.updateParam({
      assist_value: rankingList?.myInfo?.newestScore,
      assist_rank,
    });
  }
  async queryYesterdayRanking() {
    const { kps } = mx.store.getStore().user;
    const curTime = mx.store.get('helpPlant.homeData.curTime');
    const yesterdayRanking = await queryRankHistory({
      kps,
      pageNum: 1,
      pageSize: 100,
      queryDate: formatTimestamp(curTime - 24 * 60 * 60 * 1000, 'YYYYMMDD'),
    });
    dispatch.helpPlant.set({
      yesterdayRanking,
    });
  }
  /**
   * 累计阶梯 访问自动领奖
   */
  async autoReceiveLadderRewards() {
    const { multiResource, rankingList, homeData } = mx.store.getStore().helpPlant;
    const { newestScore = 0 } = rankingList.myInfo ?? {};
    const resourceTaskList = multiResource?.[config.bbzResourceCode]?.taskList || [];
    const sortTaskList = resourceTaskList
      .filter((taskItem) => taskItem.event === TASK_EVENT_TYPE.SUB_INVITE_HELP)
      .sort((a, b) => a.target - b.target);
    const nextDoingTask = sortTaskList?.find((taskItem) => taskItem?.state === 0);
    const taskList = sortTaskList.filter((taskItem) => taskItem?.state === TASK_STATUS.TASK_COMPLETED);
    const topOnePrizes = homeData.rankRewardList?.[0]?.prizeValue;

    if (!taskList.length) {
      return;
    }

    const taskIdList = taskList.map((taskItem) => taskItem.id);
    const publishList = taskList.map((taskItem) => ({
      tid: taskItem.id,
      publishId: taskItem.publishId,
    }));

    try {
      const totalRewardAmount = await dispatch.task.batchReward({
        taskIdList,
        publishList,
        requestId: geneTaskRequestId(),
      });
      if (totalRewardAmount) {
        baseModal.open(MODAL_ID.BBZ_PROGRESS_CHALLENGE_AWARD, {
          dataInfo: {
            totalPoint: totalRewardAmount,
            curHelpValue: newestScore,
            nextHelpValue: nextDoingTask ? nextDoingTask.target - newestScore : 0,
            nextAward: nextDoingTask ? nextDoingTask.rewardItems?.[0]?.amount : 0,
            finishAllTask: !nextDoingTask,
            rankMaxAward: topOnePrizes / 100,
          },
          sourceTaskList: taskList,
        });
      }
      dispatch.helpPlant.updateMultiResource();
    } catch (error) {
      dispatch.helpPlant.updateMultiResource();
    }
  }

  /**
   * 黑产弹窗
   * 引导登录toast
   * 新人教育弹窗
   * 帮帮种排名奖励领取弹窗
   * 累计奖励领取弹窗，需要调接口，可以暂时不关注
   */
  handleDialogQueue() {
    const { homeData } = mx.store.getStore().helpPlant as IHelpPlantState;
    // 如果是风控用户
    if (homeData.accountUnusual) {
      baseModal.open(MODAL_ID.RISK_CONTROL);
      const isTest = getParam('test') === 'true';
      const accountUnusualMonitor = tracker.Monitor(175);
      !isTest &&
        accountUnusualMonitor.success({
          msg: '风控用户',
          c1: IAccountUnusualEntry.bbzhong,
        });
      return;
    }
    // 没有绑定授权
    if (homeData.ucFarmHasAuth === false) {
      Toast.show('请登录授权农场后参与活动');
      setTimeout(() => {
        openPage(homeData.frontData?.ucFarmLink);
      }, 3000);
      return;
    }
    // 新人教育弹窗
    const isNewUser = !localStorage.getItem(MODAL_ID.BBZ_BEGINNER_GUIDANCE);
    if (isNewUser) {
      baseModal.open(MODAL_ID.BBZ_BEGINNER_GUIDANCE);
      setTimeout(() => {
        localStorage.setItem(MODAL_ID.BBZ_BEGINNER_GUIDANCE, `${homeData.curTime || new Date().getTime()}`);
      }, 0);
    }

    const { hintList = [] } = homeData;
    const showhHintList = hintList.filter((item) => item.needInformClose);
    if (!showhHintList.length) {
      return;
    }
    for (const hint of hintList) {
      switch (hint.hintType) {
        case IDialogHintType.RANKING_REWARD: // 上榜发奖
          baseModal.open(MODAL_ID.BBZ_RANKING_AWARD, {
            rankAwardInfo: {
              ...hint.hintInfo,
              timestamp: hint.hintInfo?.rankingStartTime ?? homeData?.curTime,
            },
            curTime: homeData?.curTime,
            isFarmPage: false,
          });
          break;
        case IDialogHintType.NEW_ASSIST_GUIDE: {
          // 当天新人不出公告弹窗
          const newUserTime = Number(localStorage.getItem(MODAL_ID.BBZ_BEGINNER_GUIDANCE));
          const isSameDay = (timeA: number, timeB: number) => {
            const dateA = new Date(timeA);
            const dateB = new Date(timeB);
            return (
              dateA.getFullYear() === dateB.getFullYear() &&
              dateA.getMonth() === dateB.getMonth() &&
              dateA.getDate() === dateB.getDate()
            );
          };
          const needShowNotice = !isNewUser && (isNaN(newUserTime) || !isSameDay(homeData.curTime, newUserTime));
          needShowNotice &&
            homeData?.frontData?.noticeConfig?.title &&
            baseModal.open(MODAL_ID.BBZ_NOTICE, {
              title: homeData?.frontData?.noticeConfig?.title,
              subtitle: homeData?.frontData?.noticeConfig?.subtitle,
              prize: homeData.frontData?.noticeConfig?.prizeConfig,
              curTime: homeData?.curTime || new Date().getTime(),
            });
          break;
        }
        default:
          break;
      }
    }
  }
  /**
   * 上报邀请code关联的助力值
   */
  logAssistByInviteCode(inviteCode: string, score: number) {
    const userAssistScore = tracker.Monitor(174);
    userAssistScore.success({
      msg: inviteCode,
      wl_avgv1: score,
      c1: String(score),
      c2: inviteCode,
      c3: getParam('entry'),
    });
  }
}

const helpPlantStore = new HelpPlant();
export default helpPlantStore;
