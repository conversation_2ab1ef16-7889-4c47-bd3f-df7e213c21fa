import { getParam } from '@/lib/qs';
import { openPage } from '@/lib/ucapi';
import stat from '@/lib/stat';
import MxModel from "@/logic/store/models";
import mx from '@ali/pcom-mx';
import { ICmsResData } from '../cms/typings';
import tracker from '@/lib/tracker';

export interface IRedirectState {
  init: boolean;
  redirectConfigList: Array<{
    filterEntry: string[];
    redirectLink: string;
  }>;
  defaultCmsData?: {
    bbnc_redirect_flz: ICmsResData<any>;
    [key: string]: ICmsResData<any>;
  };
}

const defaultState: IRedirectState = {
  init: false,
  redirectConfigList: [],
};

class Redirect extends MxModel {
  path: string;
  state: IRedirectState;
  
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'redirect';
    this.init();
  }

  init(initData: Partial<IRedirectState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }

  set(payload: Partial<IRedirectState>) {
    Object.keys(payload).forEach(key => {
      mx.store.update(`${this.path}.${key}`, payload[key])
    })
  }

  async handleRedirectEvent(cmsData: {bbnc_redirect_flz: ICmsResData<any>}) {
    const {defaultCmsData, init } = mx.store.getStore().redirect;
    const bbnc_redirect_flz = cmsData?.bbnc_redirect_flz || defaultCmsData?.bbnc_redirect_flz;
    const redirectDataList = bbnc_redirect_flz?.items[0]?.redirectConfigList as IRedirectState['redirectConfigList'] || [];
    const entry = getParam('entry') || 'unknown';

    if (init) {
      return;
    }

    // 根据entry匹配cms对应数据
    const matchConfig = redirectDataList?.find((item) => item?.filterEntry?.includes(entry));

    if (!entry || !matchConfig) {
      tracker.log({
        category: 164, // 系统自动生成，请勿修改
        msg: '没有命中重定向',
        c1: entry,
        bl1: JSON.stringify(bbnc_redirect_flz || {})
      });
      return;
    }

    if (matchConfig?.redirectLink) {
      stat.custom('page_redirect_jump', {
        entry: entry,
      });
      try {
        openPage(matchConfig?.redirectLink);
      } catch (e) {
        window.location.href = matchConfig?.redirectLink;
      }
      // 更新数据
      this.set({
        init: true,
        redirectConfigList: redirectDataList,
      });
    }
    tracker.log({
      category: 164, // 系统自动生成，请勿修改
      msg: matchConfig?.redirectLink ? '命中重定向' : '命中重定向-配置不准确',
      c1: entry,
      bl1: JSON.stringify(bbnc_redirect_flz || {})
    });
  }
}

const redirectStore = new Redirect()

export default redirectStore;
