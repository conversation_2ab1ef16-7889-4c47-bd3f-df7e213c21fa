import mx from '@ali/pcom-mx';
import MxModel from '@/logic/store/models';
import tracker from '@/lib/tracker';
import { geneTaskRequestId, getErrorInfo } from '../utils';
import config from '@/config';
import network from '@/lib/network';
import { getUserInfo } from '@/lib/ucapi';
import { IDialogHintType_ID, IDialogProbityState } from './types';
import { MODAL_ID } from '@/components/modals/types';

// 农场主页帮帮种活动信息汇总
const defaultState: IDialogProbityState = {};

class DialogProbity extends MxModel {
  path: string;
  state: IDialogProbityState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'dialog';
    this.init();
  }
  init(initData: Partial<IDialogProbityState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<IDialogProbityState>) {
    Object.keys(payload).forEach((key) => {
      this.safeUpdate(`${this.path}.${key}`, payload[key]);
    });
  }

  /**
   * 关闭弹窗提示
   */
  async closePopup(popupId: MODAL_ID, curTime: number = +new Date()) {
    const userInfo: any = await getUserInfo();
    const kps = userInfo?.kps_wg;
    const closeMonitor = tracker.Monitor(168, { sampleRate: 1 });
    const hintTypeList = [IDialogHintType_ID[popupId]];
    const requestId = geneTaskRequestId();
    try {
      const { farmHost, appId } = config;
      await network.post(`${farmHost}/help/closeHint`, {
        hintTypeList,
        appId,
        kps,
        hintTime: curTime,
        requestId
      });
      closeMonitor.success({
        msg: `${popupId}-关闭成功`,
        c1: JSON.stringify(popupId),
        w_trace_reqid: requestId
      });
    } catch (error) {
      const { msg, errCode } = getErrorInfo(error);
      closeMonitor.fail({
        msg: `接口异常-${msg}`,
        c1: JSON.stringify(popupId),
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
    }
  }
}

const dialog = new DialogProbity();
export default dialog;
