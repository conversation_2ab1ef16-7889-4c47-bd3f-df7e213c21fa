import { Rankings } from "@/api/helpPlant/typings";
import { TaskInfo } from "@/pages/index/components/TaskPop/TaskList/types";

/** 提示类型枚举 */
export enum IDialogHintType {
  /** 活动参加弹窗 */
  ACTIVITY_JOIN = 'ACTIVITY_JOIN',
  /** 上榜发奖弹窗 */
  RANKING_REWARD = 'RANKING_REWARD',
  /** 公告弹窗 */
  NEW_ASSIST_GUIDE = 'NEW_ASSIST_GUIDE'
}

/** 排行榜提示信息 */
export interface HintInfo {
  /** 不同内容待定 */
  xx: string;
  /** 排行榜奖励-排行榜标签 */
  tag: string;
  /** 排行榜奖励-奖品名 */
  prizeName: number;
  /** 排行榜奖励-奖品值 */
  prizeAmount: number;
  /** 排行榜奖励-排名 */
  ranking: number;
  /** 排行榜奖励-助力值 */
  score: number;
  /** 排行榜奖励-奖品code */
  prizeMark: string;
  /** 排行榜奖励-奖品icon */
  prizeIcon: string;
  /** 排行榜奖励 -榜单开始时间 */
  rankingStartTime: number;
}

/** 排行榜奖励信息 */
export interface RankRewardInfo extends HintInfo {
  type: string;
  typeValue: string;
  prizeIcon: string;
  prizeValue: string;
  prizeActualValue: string;
  rewardSource: string;
}

/** 提示项 */
export interface IHintItem {
  /** 提示信息 */
  hintInfo: HintInfo;
  /** 提示类型 */
  hintType: IDialogHintType;
  /** 是否需要前端通知关闭 */
  needInformClose: boolean;
}

/** 帮帮种/农场主页数据结构 */
export interface IFarmHelpPlantHomeResponse {
  /** 当前毫秒时间戳 */
  curTime: number;
  /** 是否账号异常 */
  accountUnusual: boolean;
  /** 前端数据 */
  frontData: {
    guideList: Array<{
      title: string; // 标题
      subTitle: string; // 副标题
      imgUrl: string; // 图片配置
    }>;
    shareConfig: {
      ucShareModuleId: string; // 主端分享模块
      ucLiteShareModuleId: string; // 极速版分享模块
    };
    [key: string]: any;
  };
  /** 邀请码 */
  inviteCode: string;
  /** 帮帮种是否首次访问 */
  helpFirstVisit: boolean;
  /** 助力值 */
  score: number;
  /** 排名奖励 */
  rankingAward: number;
  /** 排名 */
  ranking: number;
  /** 累计肥料奖励值 */
  totalAward: number;
  /** 待领取肥料奖励值 */
  unReceiveAward: number;
  /** 累计助力值下个阶段助力值 */
  nextStageScore: number;
  /** 累计助力值下个阶段肥料值 */
  nextStageAward: number;
  /** 弹窗提示列表 */
  hintList: IHintItem[];
  /** 排行榜奖励 */
  rankRewardList: RankRewardInfo[];
  /** 排名显示数量 */
  displayRankNum: number;
  /** 下一个阶段排名奖励信息  */
  preRankAwardInfo: Pick<Rankings, 'score' | 'ranking' | 'prizeItems'>;
}

export interface IFarmHelpPlantState {
  helpPlantHome: IFarmHelpPlantHomeResponse;
  topOnePrizeInfo: RankRewardInfo;
  accumulatedTaskList: TaskInfo[];
  calculateRewardAmount: number;
}
