import mx from '@ali/pcom-mx';
import { IDialogHintType, IFarmHelpPlantHomeResponse, IFarmHelpPlantState, IHintItem, RankRewardInfo } from './type';
import MxModel from '@/logic/store/models';
import { isUc } from '@/lib/universal-ua';
import tracker from '@/lib/tracker';
import dispatch from '@/logic/store';
import { geneTaskRequestId, getErrorInfo, calculateTotalAmount } from '../utils';
import config from '@/config';
import network from '@/lib/network';
import { IAppState } from '../app/typings';
import { addParams, getParam } from '@/lib/qs';
import { openPage } from '@/lib/ucapi';
import { IQueryResourceInfo } from '../highTask/types';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import { checkAssistEntry, checkPlantActivityCurActPeriod } from '../app/new_year_time';
import { IAccountUnusualEntry } from '@/logic/type/user';

// 农场主页帮帮种活动信息汇总
const defaultState: IFarmHelpPlantState = {
  helpPlantHome: {} as unknown as IFarmHelpPlantHomeResponse,
  topOnePrizeInfo: {} as unknown as RankRewardInfo,
  accumulatedTaskList: [],
  calculateRewardAmount: 0,
};

class FarmHelpPlant extends MxModel {
  path: string;
  state: IFarmHelpPlantState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'farmHelpPlant';
    this.init();
  }
  init(initData: Partial<IFarmHelpPlantState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<IFarmHelpPlantState>) {
    Object.keys(payload).forEach((key) => {
      this.safeUpdate(`${this.path}.${key}`, payload[key]);
    });
  }

  /**
   * 活动信息初始化
   */
  async queryHelpPlantHome() {
    if (!isUc) {
      return;
    }
    const { kps } = mx.store.getStore().user;
    const queryHelpPlantHomeMonitor = tracker.Monitor(165);
    const userAssistScore = tracker.Monitor(174);
    const requestId = geneTaskRequestId();
    try {
      const res: IFarmHelpPlantHomeResponse = await network.get(`${config.farmHost}/help/queryGatherInfo`, {
        appId: config.appId,
        kps,
        requestId,
      });

      const topOnePrizeInfo = res.rankRewardList[0];
      this.set({
        helpPlantHome: res,
        topOnePrizeInfo,
      });

      const { shareConfig } = res.frontData ?? {};
      dispatch.share.set({
        plantUcLiteShareModuleId: shareConfig?.ucLiteShareModuleId,
        plantUcShareModuleId: shareConfig?.ucShareModuleId,
      });
      queryHelpPlantHomeMonitor.success({
        msg: '信息获取成功',
        bl1: JSON.stringify(res),
        w_trace_reqid: requestId
      });
      userAssistScore.success({
        msg: res.inviteCode,
        wl_avgv1: res.score ?? 0,
        c1: String(res.score),
        c2: res.inviteCode,
        c3: getParam('entry'),
      });
    } catch (error) {
      const { errCode, msg } = getErrorInfo(error);
      queryHelpPlantHomeMonitor.fail({
        msg: `信息获取失败-${msg}`,
        c1: errCode,
        bl1: JSON.stringify(error),
        w_trace_reqid: requestId
      });
    }
  }

  /**
   * 参加活动宣发弹窗
   * 排名奖励弹窗
   */
  handleDialogQueue() {
    const { helpPlantHome = ({} as unknown as IFarmHelpPlantHomeResponse)} = mx.store.getStore().farmHelpPlant;
    // 如果是风控用户
    if (helpPlantHome.accountUnusual) {
      const accountUnusualMonitor = tracker.Monitor(175);
      const isTest = getParam('test') === 'true';
      !isTest && baseModal.open(MODAL_ID.RISK_CONTROL, {
        riskRule: helpPlantHome?.frontData?.riskRule,
        isFarmPage: true
      });
      accountUnusualMonitor.success({
        msg: '风控用户',
        c1: IAccountUnusualEntry.farm
      });
      return;
    }
    // 增加活动未开始不处理弹窗队列
    if (!checkPlantActivityCurActPeriod()) {
      return;
    }
  
    const hintList: IHintItem[] = helpPlantHome.hintList ?? [];
    const showhHintList = hintList.filter(item => item.needInformClose);
    const isAssistEntry = checkAssistEntry();
    if (!showhHintList.length || isAssistEntry) {
      return;
    }
    for (const hint of hintList) {
      switch (hint.hintType) {
        case IDialogHintType.ACTIVITY_JOIN: // 参加活动
          baseModal.open(MODAL_ID.BBZ_ATTEND_ACTIVITY, {
            curTime: helpPlantHome.curTime,
          });
          break;
        case IDialogHintType.RANKING_REWARD: // 上榜发奖
          baseModal.open(MODAL_ID.BBZ_RANKING_AWARD, {
            rankAwardInfo: {
              ...hint.hintInfo,
              timestamp: hint.hintInfo?.rankingStartTime ?? helpPlantHome.curTime,
            },
            curTime: helpPlantHome.curTime,
            isFarmPage: true,
          });
          break;
        default:
          break;
      }
    }
  }

  /**
   * 活动页面打开
   * @param from 'card': 列表入口 | 'bubble': 首页入口 ｜ 'guide_pop': 宣发弹窗 ｜ 'shared_pop': 助力结果弹窗
   */
  openActivityPage(from: 'card' | 'bubble' | 'guide_pop' | 'shared_pop') {
    const app: IAppState = mx.store.get('app');
    let page = app.frontData?.bangbangzhongConfig?.activityLink;
    if (!page) {
      try {
        const url = new URL(window.location.href);
        url.pathname = '/helpPlant';
        page = url.toString();
      } catch (error) {
        console.log('链接兜底处理失败');
      }
    }
    if (!page) {
      console.log('链接为空');
      return;
    }
    page = addParams(page, {
      from,
      entry: 'farmbbz',
    });
    if (!page.includes('$kps_info')) {
      page += '&$kps_info';
    }
    openPage(page);
    console.info('page', page);
  }

  /**
   * 获取累计阶梯奖励值
   */
  queryAccumulatedReward(data: IQueryResourceInfo) {
    const { taskList = [] } = data;
    if (!taskList.length) {
      return;
    }

    const calculateRewardAmount = calculateTotalAmount(taskList);
    this.set({
      accumulatedTaskList: taskList.sort((pre, next) => pre.target - next.target),
      calculateRewardAmount,
    });
  }
}

const farmHelpPlant = new FarmHelpPlant();
export default farmHelpPlant;
