import mx from '@ali/pcom-mx';
import { IRedPacketState } from './typings';
import MxModel from '@/logic/store/models';

// 红包
const defaultState = {
  detailRedPacketInfo: null,
};

class RedPacket extends MxModel {
  path: string;
  state: IRedPacketState;
  constructor() {
    super();
    this.state = defaultState;
    this.path = 'redPacket';
    this.init();
  }
  init(initData: Partial<IRedPacketState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<IRedPacketState>) {
    Object.keys(payload).forEach((key) => {
      this.safeUpdate(`${this.path}.${key}`, payload[key]);
    });
  }
}

const redPacket = new RedPacket();
export default redPacket;
