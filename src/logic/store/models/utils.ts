import config from '@/config'
import { getAppVersion, isAndroid, isIOS, isLatestVersion } from "@/lib/universal-ua";
import mx from '@ali/pcom-mx';
import dispatch from '@/logic/store';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { getParam } from '@/lib/qs';
import { BangBangZhongShareEntry, DoubleShareEntry } from '@/constants';

export const getErrorInfo = (err) => {
  const errCode = err.code || (err.data && err.data.code) || '';
  const msg = err.message || err.msg || '';
  const status = err.status || -1;
  return {
    errCode,
    msg,
    status
  }
}

export function geneTaskRequestId() {
  function s4() {
    // eslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
}

export const getPublishVersion = () => {
  // 测试环境PUBLISH_VERSION 格式为1.20.0-beta.1244142421, 去掉多余的字符
  const version = (PUBLISH_VERSION || '').replace(/-.+/, '')
  if (+version.replace(/\./g, '') === 0) {
    return config.fve
  }
  return version
}

export const rtaAppVersionCheck = async (appType: 'UC' | 'UCLite') => {
  const appVersion = await dispatch.app.getAppVersion();
  if (appType === 'UC') {
    return isLatestVersion(appVersion, '17.0.0.0000')
  }
  if (appType === 'UCLite') {
    // 极速版ios版本暂未确定
    // const isSupportVersion = (appVersion.startsWith('16.5.7') && appSubVersion === 'ucliteplusrelease3') || isLatestVersion(appVersion, '16.5.8.0000')
    return (isAndroid && isLatestVersion(appVersion, '16.5.7.0000')) || (isIOS && isLatestVersion(appVersion, '16.2.8.0000'))
  }
  return false
}

/**
 * 是否开启异步查奖
 * 1、ios需要指定版本以上才可以开启异步查询奖励, 否则会导致广告调起失败;
 * 2、例如: 主端ios需要在版本17.2.0.2476后才开启, 在之前版本开启大概率会导致广告调起失败
 * @param curVer - 当前客户端版本
 * @param isLite - 是否为极速版
 */
export const isOpenQueryAward = (curVer: string, isLite: boolean) => {
  if (isAndroid) {
    return isLatestVersion(curVer, '16.4.0.0000')
  }
  const frontData = mx.store.get('app')?.mainInfo?.frontData;
  const iosSupVer = isLite ? frontData?.iosLiteOpenQueryAwardVersion : frontData?.iosOpenQueryAwardVersion;
  return isLatestVersion(curVer, iosSupVer || '17.2.0.2476');
};

/**
 * 计算任务列表累计奖励
 * @param list 
 * @returns 
 */
export const calculateTotalAmount = (list: TaskInfo[]): number => {
  if (list.length === 0) {
    return 0;
  }
  try {
    return list.reduce<number>((sum, task) => {
      if (!task?.rewardItems?.[0]?.amount) {
        return sum;
      }
      return sum + task.rewardItems[0].amount;
    }, 0);
  } catch (error) {
    console.warn('计算总额失败:', error);
    return 0;
  }
};

/** 
 * 是否是帮帮种活动
 */
export const getIsBangBangMainActivity = () => {
  const entry = getParam('entry');
  const source = (getParam('source') ?? '')?.toLowerCase();
  return entry.includes(BangBangZhongShareEntry) || source.includes('uc');
}

/** 
 * 是否是翻倍卡
 */
export const getIsDoubleActivity = () => {
  const entry = getParam('entry');
  const source = (getParam('source') ?? '')?.toLowerCase();
  return entry.includes(DoubleShareEntry) || source.includes('fb');
}
