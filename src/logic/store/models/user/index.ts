import { isMock } from '@/constants/dev';
import mx from '@ali/pcom-mx';
import stat from '@/lib/stat';
import {getUserInfo, openTaobaoLoginWindow, getBindInfo, bindThirdPartyAccount} from '@/lib/ucapi';
import toast from "@/lib/universal-toast/component/toast";
import { IUserState, IAccountChangeData, EnumAccountLoginStatus, IBindThirdInfo } from './typings';
import { configMaxName } from './help';
import { defaultAvatar, defaultNickname } from '@/constants/default';
import { EventMap } from '@/lib/utils/events';
import dispatch from "@/logic/store";
import {MainAPI} from "@/logic/type/event";
import network from "@/lib/network";
import baseModal from '@/lib/modal';
import tracker from "@/lib/tracker";
import config from "@/config";
import {geneTaskRequestId} from "@/logic/store/models/utils";
import MxModel from "@/logic/store/models";
import { isLatestVersion } from '@/lib/universal-ua';
import { IAccountUnusualEntry } from '@/logic/type/user';
import { MODAL_ID } from '@/components/modals/types';
import { DefaultRiskRule } from '@/constants';

// 绑定淘宝冲突
const BIND_TAOBAO_CONFLICT_CODE = '51003';

const defaultState = {
  vcode: '',
  kps: isMock ? 'test kps' : '',
  isLogin: !!isMock, // 是否登录
  nickname: isMock ? configMaxName(defaultNickname) : '',
  avatar: isMock ? defaultAvatar : '',
  uId: '',
  utdId: '',
  signWg: '',
  isFetched: !!isMock,
  bindTaobao: false, // 是否绑定淘宝账户
  deviceInfo: {
    deviceLevel: 'unknown'
  }
};

class User extends MxModel {
  path: string;
  state: IUserState;

  constructor() {
    super();
    this.state = defaultState;
    this.path = 'user';
    this.init();
  }

  init(initData: Partial<IUserState> = {}) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }

  set(payload: Partial<IUserState>) {
    Object.keys(payload).forEach(key => {
      mx.store.update(`${this.path}.${key}`, payload[key])
    })
  }

  reset() {
    mx.store.set({
      [this.path]: this.state,
    });
  }

  async initUserData() {
    const checkUserState = Date.now();
    const userState: Partial<IUserState> = await this.updateUserState();
    console.info('==userInfo==', userState);
    const { isLogin, bindTaobao } = userState
    let log = 'unbound_unlog';
    if (bindTaobao) {
      log = 'bound'
    } else if (isLogin) {
      log = 'unbound_log'
    }

    stat.custom('login_bind', {
      log
    })

    // 登录状态公参更新
    stat.updateParam({ login: userState.isLogin ? 1 : 0 });

    tracker.log({
      category: 157,
      msg: '获取用户登录信息',
      wl_avgv1: Date.now() - checkUserState
    });

    this.onAccountStateChange();
    if (!userState.isLogin) {
      // openLoginWindow();
      stat.exposure('login_pop', {
        c: 'login',
        d: 'pop',
        type: 'init_data',
      });
    }
    return Promise.resolve(userState);
  }

  async updateUserState(isLoginCallback = false): Promise<Partial<IUserState>> {
    try {
      const vCode = +new Date();
      const checkUserTime = Date.now();
      const userDataRes: any = await getUserInfo();
      tracker.log({
        category: 157,
        msg: '用户信息-account.getUserInfo-耗时',
        wl_avgv1: Date.now() - checkUserTime
      });
      if (userDataRes.result === 'success') {
        const { kps_wg, loginStatus, sign_wg, uId, utdId, nickname, avatar_url } = userDataRes;
        const resUserData = {
          kps: kps_wg,
          isLogin: loginStatus,
          signWg: sign_wg,
          nickname: configMaxName(nickname || defaultNickname),
          avatar: avatar_url ? decodeURIComponent(avatar_url) : defaultAvatar,
          vcode: vCode,
          uId,
          utdId,
        };
        tracker.config({uid: uId});
        this.set(resUserData);
        const checkBindTaobaoTime = Date.now();
        const bindTaobao = await this.getBindTaobaoInfo();

        const userState = {
          ...resUserData,
          bindTaobao
        }
        tracker.log({
          category: 157,
          msg: '用户信息-account.getBindUserInfo-耗时',
          wl_avgv1: Date.now() - checkBindTaobaoTime
        });

        // 产品逻辑：第三方登录回来如果没有绑定淘宝，自动弹出淘宝授权弹窗
        if (!bindTaobao && isLoginCallback) {
          // mx.event.emit(MainAPI.ShowBindTaobaoPop)
          mx.event.emit('modal_binding_taobao', {modalName: 'bindTaobao', from: 'callback'});
        }
        return Promise.resolve(userState);
      }

      const { utdId = '', loginStatus, kps_wg } = userDataRes;
      const initUserData = {
        utdId,
        isLogin: loginStatus,
        kps: kps_wg || '',
      };
      this.set(initUserData);

      return Promise.resolve(initUserData);
    } catch (e) {
      console.error('[getUserData error]:', JSON.stringify(e));
      return Promise.resolve(mx.store.get(this.path));
    }
  }

  // 登录状态变化回调
  async onAccountStateChange() {
    // jsApi: https://jas.alibaba-inc.com/jsapi/5e952609baad8800f8b47fa6?type=jump
    const accountStateChangeCallback = async (event) => {
      const data = event as unknown as IAccountChangeData;
      const { detail } = data || {};
      const { status } = detail || {};
      console.log('[== AccountStateChange data ==]:', detail);

      // 非登录 -> 登录
      if (status === EnumAccountLoginStatus.LOGIN) {
        dispatch.app.updateAllData(true)
        mx.event.emit(EventMap.LoginChange, { isLogin: true });
      }

      // 登录 -> 非登录
      if (status === EnumAccountLoginStatus.LOGOUT) {
        this.reset();
        // 登录状态公参更新
        mx.event.emit(EventMap.LoginChange, { isLogin: false });
      }
    }

    document.removeEventListener('UCEVT_Global_AccountStateChange', accountStateChangeCallback);
    document.addEventListener('UCEVT_Global_AccountStateChange', accountStateChangeCallback);
  }
  async toBindTaobao(from_number) {
    const bindTaobao = mx.store.get('user.bindTaobao');
    const defaultPr = mx.store.get('app.defaultPr');
    tracker.log({
      category: 116,
      msg: `点击去绑定淘宝-${defaultPr}`,
      w_succ: 1,
      c1: bindTaobao,
      c4: from_number,
    })
    if (bindTaobao) {
      mx.event.emit(MainAPI.HideBindTaobaoPop)
      dispatch.app.updateAllData();
      return;
    }
    const appVersion = await dispatch.app.getAppVersion();
    stat.custom('to_bind_taobao', {
      from: from_number
    })
    let uccTrustPhone = 1;
    // 极速版IOS 低于17.4.3, UCC登录关闭
    if (defaultPr.includes('ucliteios') && !isLatestVersion(appVersion, '17.4.3')) {
      uccTrustPhone = 0;
    }
    console.log('uccTrustPhone', uccTrustPhone);
    const res = await bindThirdPartyAccount('taobao', uccTrustPhone);
    if (res.result !== 'success') {
      tracker.log({
        category: 116,
        msg: `调起淘宝失败-${defaultPr}`,
        c4: from_number,
        w_succ: 1,
        bl1: JSON.stringify(res)
      })
      toast.show('调起淘宝失败')
      return
    }
    // 绑定淘宝回调
    const listenBind = async (state: any) => {
      console.log('[toBindTaobao res]', state.detail)
      tracker.log({
        category: 116,
        msg: `收到绑定回调-${defaultPr}`,
        w_succ: 1,
        c4: from_number,
        bl1: JSON.stringify(state.detail)
      })
      if (state && state.detail) {
        const bindRes: IBindThirdInfo = state.detail;
        const bindSuccess = bindRes.result === 'success';
        // ios返回的错误码platform_code，android返回fail_event
        const failCode = String(bindRes?.platform_code || bindRes.fail_event)
        if (bindSuccess) {
          // 绑定淘宝成功
          stat.custom('bind_taobao_success', {
            from: from_number
          })
          tracker.log({
            category: 116,
            msg: `绑定淘宝成功-${defaultPr}`,
            w_succ: 1,
            c4: from_number,
            c3: failCode,
            bl1: JSON.stringify(bindRes)
          })
          await dispatch.user.auth()
          mx.event.emit(MainAPI.HideBindTaobaoPop)
          dispatch.app.updateAllData()
          document.removeEventListener('UCEVT_Global_BindThirdPartyAccountComplete', listenBind);
        } else {
          tracker.log({
            category: 116,
            msg: `绑定淘宝失败-${defaultPr}`,
            w_succ: 1,
            c2: bindRes?.fail_msg,
            c3: failCode,
            c4: from_number,
            bl1: JSON.stringify(bindRes)
          })
          stat.custom('bind_taobao_fail', {
            code: failCode,
            msg: bindRes?.fail_msg,
            from: from_number,
          })
          // 绑定冲突时隐藏绑定弹窗,由客户端接管后续流程
          if (failCode === BIND_TAOBAO_CONFLICT_CODE) {
            mx.event.emit(MainAPI.HideBindTaobaoPop)
          }
        }
      }
    }
    document.addEventListener('UCEVT_Global_BindThirdPartyAccountComplete', listenBind);
  }

  async getBindTaobaoInfo(retry = false) {
    const bindInfoMonitor = tracker.Monitor(108);
    const bindTaobao = mx.store.get('user.bindTaobao');
    if (bindTaobao) {
      return true;
    }
    try {
      let bindInfo = await getBindInfo('taobao', true);
      if ((bindInfo as any).errCode) {
        bindInfoMonitor.fail({
          msg: '获取绑定信息失败',
          c1: 'unknown',
          c2: (bindInfo as any).errCode,
          bl1: JSON.stringify(bindInfo)
        })
        return false;
      }
      // const bindStatus = bindInfo.result === 'success' && !!bindInfo.third_kps;
      if (bindInfo.result === 'success') {
        bindInfoMonitor.success({
          msg: `${retry ? '重试' : ''}已绑定`,
          c1: '1',
          bl1: JSON.stringify(bindInfo)
        })
        stat.updateParam({ page_status: 1 });
        this.set({
          bindTaobao: true
        })
        return true
      } else {
        stat.updateParam({ page_status: 0 });
        this.set({
          bindTaobao: false
        })
        const mainInfo = mx.store.get('app.mainInfo.gameInfo')
        // 未绑定但是有数据，重置首页数据
        if (mainInfo) {
          dispatch.app.set({
            mainInfo: {}
          })
        }
        bindInfoMonitor.success({
          msg: `${retry ? '重试' : ''}未绑定`,
          c1: '0',
          bl1: JSON.stringify(bindInfo)
        })
      }
      return false
    } catch (e) {
      bindInfoMonitor.fail({
        msg: `${retry ? '重试' : ''}获取绑定信息失败`,
        c1: 'unknown',
        c2: `${e.errMsg || ''}`,
        bl1: JSON.stringify(e)
      })
      if (!retry) {
        this.getBindTaobaoInfo(true)
      }
      return false
    }
  }

  // 已登录UC + 已绑定淘宝 返回true，其余返回false
  async checkLoginAndBind(delay = 0, from) {
    if (!dispatch.app.isAppInit) {
      return;
    }
    const isLogin = mx.store.get('user.isLogin')
    const bindTaobao = mx.store.get('user.bindTaobao')
    const {
      checkIosInUcLite,
    } = await dispatch.app.fixInUcLiteBinding();
    if (!isLogin) {
      console.log('需要登录')
      checkIosInUcLite && openTaobaoLoginWindow({})
      return false
    }
    if (!bindTaobao) {
      console.log('需要登录淘宝')
      // setTimeout解决弹窗出来后会自动点击蒙层关闭的问题
      setTimeout(() => {
        checkIosInUcLite && mx.event.emit(MainAPI.ShowBindTaobaoPop, {from})
      }, delay)
      return false
    }
    return true
  }

  // 已绑定但是未授权的用户，自动触发一次授权
  async checkAuth() {
    const ucFarmHasAuth = mx.store.get('app.mainInfo.userInfo.ucFarmHasAuth')
    if (!ucFarmHasAuth) {
      await this.auth()
      dispatch.app.updateAllData()
    }
  }

  async auth(retry = false) {
    try {
      const authRes = await network.post(`${config.farmHost}/auth`, {
        appId: config.appId,
        kps: mx.store.get('user').kps || '',
        requestId: geneTaskRequestId(),
      })
      if (authRes) {
        tracker.log({
          category: 141,
          msg: `授权接口${retry ? '重试' : ''}成功`,
          w_succ: 1,
          bl1: JSON.stringify(authRes)
        })
      }
    } catch (e) {
      tracker.log({
        category: 141,
        msg: `授权接口${retry ? '重试' : ''}失败`,
        w_succ: 0,
        c2: e.msg || '',
        c3: e.code || '',
        bl1: JSON.stringify(e)
      })
      // 超时重试
      if ((e.code || '').includes('timeout') && !retry) {
        return await this.auth(true)
      }
      toast.show('授权失败了，请重新进入页面试试')
    }
  }

  handleAccountUnusualUser(errCode: string, entry: IAccountUnusualEntry) {
    const AccountUnusualUserErrCode = "UNUSUAL_USER"
    const { helpPlantHome = {}} = mx.store.getStore().farmHelpPlant;
    if (errCode && errCode.includes(AccountUnusualUserErrCode)) {
      const accountUnusualMonitor = tracker.Monitor(175);
      baseModal.open(MODAL_ID.RISK_CONTROL, {
        riskRule: helpPlantHome?.frontData?.riskRule ?? DefaultRiskRule
      });
      accountUnusualMonitor.success({
        msg: '风控用户',
        c1: entry
      });
      return true;
    }
    return false;
  }
}

const userStore = new User();
export default userStore;
