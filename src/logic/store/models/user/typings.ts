export interface IUserState {
  kps: string;
  isLogin: boolean;
  uId: string;
  utdId: string;
  signWg: string;
  nickname: string;
  avatar: string;
  vcode: string | number;
  isFetched: boolean;
  bindTaobao: boolean;
  deviceInfo?: {
    /**
     * 0.0 低端机
     * 1.0 中端机
     * 1.5 中端机
     * 2.0 高端机
     * -999 未知设备
     */
    deviceLevel?: string; // 设备等级
  };
}

export interface IBindThirdInfo {
  result: 'success' | 'failed';
  /** nologin 表示当前未登录，unbind 表示没有该第三方账号类型的绑定信息。 */
  fail_msg: 'nologin' | 'unbind';
  platform_code?: string; // ios绑定失败返回
  fail_event?: number; // android绑定失败返回
  /**   "taobao"|"alipay" */
  bindThirdType: 'taobao' | 'alipay';
  /** 第三方账号昵称，可能为空  */
  third_nickname: string;
  /**   第三方账号头像，可能为空 */
  third_avatar_url: string;
  /** 第三方账号信息 third_kps=base6 */
  third_kps: string;
}


export interface IAccountChangeData {
  detail: {
    ucid: string;
    status: EnumAccountLoginStatus; // 当前登录状态
    avatar_url: string; // 头像地址
    uidE: string;
    uidWg: string;
  };
}

export enum EnumAccountLoginStatus {
  LOGOUT,
  LOGIN,
  CANCEL = '2',
}
