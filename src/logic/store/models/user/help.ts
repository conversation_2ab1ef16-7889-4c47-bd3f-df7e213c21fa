// 中间最多展示字符数量 默认：3
const MIDDLE_MAX_STR_LENGTH = 3;

/**
 * 名称脱敏
 * @description 只展示第一个和最后一个字符，中间用 * 代替
 */
export const desensitizeName = (nickname: string) => {
  if (!nickname) return ''
  if (nickname.length < MIDDLE_MAX_STR_LENGTH) return nickname;

  const first = nickname.slice(0, 1);
  const last = nickname.slice(-1);
  const restStr = nickname.slice(1, -1);

  const middle = Array.from(restStr.slice(0, MIDDLE_MAX_STR_LENGTH), () => '*').join('');
  const result = first + middle + last;

  return result;
};

// 最多展示真实字符数量 默认：5
const MAX_SHOW_NAME_LENGTH = 5;
// 最多 * 数量 默认：1
const MAX_STAR_LENGTH = 1;

/**
 * 处理最大字符
 * @description 超过最大最大字符使用 * 代替
 */
export const configMaxName = (nickname: string) => {
  if (nickname.length < MAX_SHOW_NAME_LENGTH) return nickname;

  const leftName = nickname.slice(0, MAX_SHOW_NAME_LENGTH);
  const resetName = nickname.slice(MAX_SHOW_NAME_LENGTH, MAX_SHOW_NAME_LENGTH + MAX_STAR_LENGTH);

  const restName = Array.from(resetName, () => '*').join('');
  const result = leftName + restName;

  return result;
};
