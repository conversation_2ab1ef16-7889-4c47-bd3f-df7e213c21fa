export interface DrawCardRes {
  win: boolean; // 是否中奖 true 中奖， false不中奖
  notWinCode: string; // win=false的不中奖码： LOTTERY:NOT_WIN_PRIZE 不中奖，LOTTERY:NOT_MATCH_SCENE 未匹配到抽奖场次，LOTTERY:PRIZE_SEND_LIMIT 奖品发放限制，LOTTERY:NOT_HIT_WIN_RATE 没有命中抽奖概率，LOTTERY:PRIZE_NO_ENOUGH 奖品库存不足，LOTTERY:BASE_PRIZE_NO_ENOUGH 兜底奖品库存不足
  prize: DrawPrizeItem;
  drawType: DRAW_TYPE;
  maxDrawTimes: boolean; // 是否达到最大抽奖次数
  doubleNum: number;
}

export enum DRAW_TYPE {
  HAS_CARD = 1, // 有卡刷新倍数
  NO_CARD = 0, // 无卡抽奖
}

export interface DrawPrizeItem {
  "id": number;
  "moduleCode": string;
  "prizeId": number;
  "sceneId": number;
  "type": string; // CASH ：现金类型（废弃）  PHONE_FARE ：话费类型（废弃）    ENTITY ：实物奖品（废弃）    VIRTUAL_CODE ：虚拟奖品-code（废弃） VIRTUAL_LINK ：虚拟奖品-链接  （废弃）   VIRTUAL_CURRENCY  : 虚拟货币   VIRTUAL_AWARD_PRIZE : 权益福利社奖品
  "userKey": string;
  "mark": string; // cash_=现金 用开头判断  card_=旺牌  coin_金币   用开头判断
  "name": string;
  "nickName": string;
  "price": number; // 奖品价值
  "costPrice": number;
  "localLuckyTime": number;
  "sendStatus": string;
  "sourceType": string;
  "sourceMark": string;
  "hasReceived": boolean;
  "businessTag": string;
}

export interface ReceiveCardAward {
  doubleNum: number; // 卡倍数，122表示1.22倍
  yesterdayTotalAmount: number; // 昨日可翻倍总肥料
  yesterdayDoubleAmount: number; // 昨日翻倍奖励的肥料，奖励肥料=min(yesterdayTotalAmount*doubleNum/100,doubleAmountLimit)
  doubleAmountLimit: number; // 肥料翻倍限额
}