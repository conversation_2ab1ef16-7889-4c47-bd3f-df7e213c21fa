import mx from '@ali/pcom-mx';
import MxModel from '@/logic/store/models';
import dispatch from '@/logic/store';
import config from '@/config';
import network from '@/lib/network';
import { QueryCardInfoRes } from "@/api/doublePointsCard/typings";
import { geneTaskRequestId, getErrorInfo } from "@/logic/store/models/utils";
import { spamSign } from "@/lib/ucapi";
import { DrawCardRes, ReceiveCardAward, DRAW_TYPE } from "@/logic/store/models/doublePointsCard/typings";
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import tracker from "@/lib/tracker";
import { checkAssistEntry, checkEnableDoubleCard } from "@/logic/store/models/app/new_year_time";
import { MainAPI } from "@/logic/type/event";
import toast from '@/lib/universal-toast/component/toast';
import { isMock } from "@/mockData";
import { getParam } from '@/lib/qs';
import modals from '@/components/modals';
import { removeUrlParams } from '@/lib/utils/url';
import { isSameDay } from '@/lib/utils/date';
import { RES_CODES } from '../cms/typings';
import stat from '@/lib/stat';
import { EDoubleFactFromParam } from '@/components/modals/modal_double_card';

const defaultState: QueryCardInfoRes = {
  curTime: Date.now(),
  actStartTime: 0,
  actEndTime: 0,
  inviteInfo: null,
  yesterdayCardInfo: undefined,
  todayCardInfo: undefined,
  drawInfo: undefined,
  frontData: {},
}

class DoublePointsCard extends MxModel {
  path: string;
  state: QueryCardInfoRes;

  constructor() {
    super();
    this.state = defaultState;
    this.path = 'doublePointsCard';
    this.init({});
  }

  init(initData) {
    mx.store.set({
      [this.path]: {
        ...this.state,
        ...initData,
      },
    });
  }
  set(payload: Partial<QueryCardInfoRes>) {
    Object.keys(payload).forEach((key) => {
      mx.store.update(`${this.path}.${key}`, payload[key]);
    });
  }
  async initDoubleCardInf() {
    await dispatch.doublePointsCard.queryCardInfo({isInit: true});
    const yesterdayCardInfo = mx.store.get('doublePointsCard.yesterdayCardInfo') || {};
    const { canReceiveDoubleAmount = 0, received } = yesterdayCardInfo;
    if (canReceiveDoubleAmount && !received) {
      await dispatch.doublePointsCard.receiveCardAward();
    }
  }

  // 检查并统计翻倍卡资格曝光
  checkAndStatEligibility() {
    if (checkEnableDoubleCard()) {
      stat.exposure('fdcard_eligibility_exposure', {
        c: 'fdcard',
        d: 'gacha',
      });
    }
  }

  // 所有翻倍卡入口统一使用的曝光
  doubleCardExposure() {
    stat.exposure('fdcard_activiy__exposure', {
      c: 'fdcard',
      d: 'gacha',
    });
  }

  // 查询翻倍卡信息
  async queryCardInfo(params?: { isInit?: boolean }) {
    const isInit = params?.isInit
    const { kps } = mx.store.getStore().user;
    const { farmHost, appId} = config;
    const cardMonitor = tracker.Monitor(184);
    const requestId = geneTaskRequestId();
    try {
      const cardInfo: QueryCardInfoRes = await network.get(`${farmHost}/card/queryCardInfo`, {
        appId,
        kps,
        requestId,
      })
      const { todayCardInfo } = cardInfo;
      cardMonitor.success({
        msg: '翻倍卡查询',
        c1: '查询成功',
        c2: String(todayCardInfo?.doubleNum || 0),
        c3: String(todayCardInfo?.canReceiveDoubleAmount || 0),
        c4: String(todayCardInfo?.totalAmount || 0),
        bl1: JSON.stringify(cardInfo),
        w_trace_reqid: requestId
      })
      dispatch.doublePointsCard.set(cardInfo);
      const { shareConfig } = cardInfo.frontData ?? {};
      dispatch.share.set({
        doubleUcLiteShareModuleId: shareConfig?.ucLiteShareModuleId,
        doubleUcShareModuleId: shareConfig?.ucShareModuleId,
      });

      // 翻倍卡资格曝光打点
      isInit && this.checkAndStatEligibility();
    } catch (e) {
      const { msg} = getErrorInfo(e);
      cardMonitor.fail({
        msg: '翻倍卡查询',
        c1: `查询失败-${msg}`,
        bl1: JSON.stringify(e),
        w_trace_reqid: requestId
      })
    }
  }
  // 抽卡或刷新翻倍卡
  async drawCard() {
    const { appId } = config;
    const kps = mx.store.get('user.kps');
    // 是否有卡
    const hasCard = !!mx.store.get('doublePointsCard.todayCardInfo.doubleNum');
    const drawType = hasCard ? DRAW_TYPE.HAS_CARD : DRAW_TYPE.NO_CARD;
    const requestId = geneTaskRequestId();
    const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
    const drawMonitor = tracker.Monitor(183);
    if (isMock()) {
      return {
        win: true,
        doubleNum: 200,
      }
    }
    try {
      const ut = await dispatch.app.getAppUtRes();
      const signOriText = `${kps || ''}${ut}${appId}${drawType}${requestId}`;
      const sign = await spamSign({ text: signOriText, salt });
      const drawRes: DrawCardRes = await network.post(`${config.farmHost}/card/drawCard`, {
        appId: config.appId,
        kps,
        drawType,
        requestId,
        sign,
      })
      if (drawRes.win && drawRes.doubleNum) {
        dispatch.doublePointsCard.queryCardInfo();
        drawMonitor.success({
          msg: '抽奖成功',
          c1: drawRes.doubleNum,
          c2: String(drawType),
          bl1: JSON.stringify(drawRes),
          w_trace_reqid: requestId
        })
        return drawRes;
      } else {
        drawMonitor.success({
          msg: '抽奖失败',
          c2: String(drawType),
          bl1: JSON.stringify(drawRes),
          w_trace_reqid: requestId
        })
        return null;
      }
    } catch (e) {
      const { msg, errCode } = getErrorInfo(e);
      if (errCode === 'NO_PRIZE') {
        drawMonitor.success({
          msg: `未中奖`,
          c2: String(drawType),
          c3: msg,
          bl1: JSON.stringify(e),
        })
      } else {
        toast.show('抽奖失败了，请重新试试')
        drawMonitor.fail({
          msg: `抽奖失败-${errCode}`,
          c2: String(drawType),
          c3: msg,
          bl1: JSON.stringify(e),
          w_trace_reqid: requestId
        })
      }
      await dispatch.doublePointsCard.queryCardInfo();
      return null;
    }
  }
  async receiveCardAward() {
    const { appId } = config;
    const cardMonitor = tracker.Monitor(184);
    const kps = mx.store.get('user.kps');
    const requestId = geneTaskRequestId();
    const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
    try {
      const ut = await dispatch.app.getAppUtRes();
      const signOriText = `${kps || ''}${ut}${appId}${requestId}`;
      const sign = await spamSign({ text: signOriText, salt });
      const receiveRes: ReceiveCardAward = await network.post(`${config.farmHost}/card/receiveCardAward`, {
        appId,
        kps,
        ut,
        requestId,
        sign,
      })
      if (receiveRes) {
        cardMonitor.success({
          msg: '翻倍卡领奖',
          c1: '领奖成功',
          c2: String(receiveRes.doubleNum),
          c3: String(receiveRes.yesterdayDoubleAmount),
          c4: String(receiveRes.yesterdayTotalAmount),
          bl1: JSON.stringify(receiveRes),
          w_trace_reqid: requestId
        })
        mx.event.emit(MainAPI.RECEIVED_DOUBLE_CARD_AWARD);
      }
      dispatch.doublePointsCard.queryCardInfo();
    } catch (e) {
      const { msg } = getErrorInfo(e);
      cardMonitor.fail({
        msg: '翻倍卡领奖',
        c1: `领奖失败-${msg}`,
        bl1: JSON.stringify(e),
        w_trace_reqid: requestId
      })
      dispatch.doublePointsCard.queryCardInfo();
    }
  }

  handleDialogQueue(isLoginCallback?: boolean) {
    const autoOpenDoubleCard = getParam('showFdcardModal');
    const fromAppResource = getParam('from') === 'app_resource';
    const { helpPlantHome } = mx.store.getStore().farmHelpPlant;
    const doublePointsCard = mx.store.getStore().doublePointsCard;
    const { guideDisplayNum = 1, doubleCardDirectActiveDays = 1 } = doublePointsCard?.frontData || {}
    const hasDraw = doublePointsCard?.drawInfo?.totalDrawTimes
    const enableDoubleCard = checkEnableDoubleCard() && !helpPlantHome?.accountUnusual;
    const isAssistEntry = checkAssistEntry();
    const today = doublePointsCard?.curTime || helpPlantHome?.curTime || Date.now()
    const cmsStore = mx.store.getStore().cms
    // 是否必中
    const isMustDouble = cmsStore[RES_CODES.BBNC_GUARANTEED_FDCARD]?.items?.[0]?.open;
    const bindTaobao = mx.store.get('user.bindTaobao');
    let isShowGuideModal = true
    if (!enableDoubleCard) {
      return;
    }
    const localDirectActiveDays = localStorage.getItem(LocalStorageKey.DOUBLE_CARD_DIRECT_SEND);
    const localDirectSendData = localDirectActiveDays
      ? JSON.parse(localDirectActiveDays)
      : null;
    const isReachDaysLimit = localDirectSendData && localDirectSendData?.count >= doubleCardDirectActiveDays
    const isDirectSendTodayShown = localDirectSendData ? isSameDay(localDirectSendData?.date, today) : false;
    if (isMustDouble && bindTaobao && !hasDraw && !isReachDaysLimit && !isDirectSendTodayShown && !isLoginCallback) {
      modals.openDoubleCardModal({
        autoLottery: true,
        from: fromAppResource ? EDoubleFactFromParam.APP_RESOURCE : EDoubleFactFromParam.AUTO_100,
        isMustDouble: true,
        delayLottery: true,
      })
      const newLocalDirectSendData = {
        date: today,
        count: localDirectSendData ? localDirectSendData?.count + 1 : 1
      };
      localStorage.setItem(LocalStorageKey.DOUBLE_CARD_DIRECT_SEND, JSON.stringify(newLocalDirectSendData));
      isShowGuideModal = false
    } else if (!isAssistEntry && autoOpenDoubleCard) {
      modals.openDoubleCardModal({
        autoLottery: true,
        from: fromAppResource ? EDoubleFactFromParam.APP_RESOURCE : EDoubleFactFromParam.AUTO_DRAWCARD,
      })
      removeUrlParams('showFdcardModal');
      isShowGuideModal = false
    }


    const storageValue = localStorage.getItem(LocalStorageKey.DOUBLE_CARD_PUBLICITY);
    const storageData = storageValue
      ? storageValue === '1' // 原本的格式 判定为昨天已弹
        ? { date: today - 24 * 60 * 60 * 1000, count: 1 }
        : JSON.parse(storageValue)
      : null;

    const isTodayShown = storageData ? isSameDay(storageData?.date, today) : false;
    const isReachTotalLimit = storageData ? storageData?.count >= guideDisplayNum : false;
    if (isMustDouble || hasDraw || isReachTotalLimit || isTodayShown || isAssistEntry || autoOpenDoubleCard || !isShowGuideModal) {
      return;
    }
    baseModal.open(MODAL_ID.DOUBLE_CARD_PUBLICITY, {})
    const newDateCount = {
      date: today,
      count: storageData ? storageData?.count + 1 : 1
    };
    localStorage.setItem(LocalStorageKey.DOUBLE_CARD_PUBLICITY, JSON.stringify(newDateCount));
  }
}

const doublePointsCardStore = new DoublePointsCard();
export default doublePointsCardStore;
