import app from './models/app';
import user from './models/user';
import task from './models/task';
import seed from './models/seed';
import cms from './models/cms';
import timeLimitTask from './models/limit/index';
import redPacket from './models/redPacket/index';
import highValueTask from './models/highTask/index';
import share from './models/share';
import resource from './models/resource';
import redirect from './models/redirect';
import helpPlant from './models/helpPlant';
import farmHelpPlant from './models/farmHelpPlant';
import helpRanking from './models/helpRanking';
import dialog from './models/dialog';
import hcAd from './models/ad/index';
import doublePointsCard from '@/logic/store/models/doublePointsCard';

export type StoreState = {
  [K in keyof IDispatch]: IDispatch[K] extends { state: infer S } ? S : never;
};

interface IDispatch {
  app: typeof app;
  user: typeof user;
  seed: typeof seed;
  task: typeof task;
  cms: typeof cms;
  timeLimitTask: typeof timeLimitTask;
  redPacket: typeof redPacket;
  highValueTask: typeof highValueTask;
  share: typeof share;
  resource: typeof resource;
  redirect: typeof redirect;
  helpPlant: typeof helpPlant;
  farmHelpPlant: typeof farmHelpPlant;
  helpRanking: typeof helpRanking;
  dialog: typeof dialog;
  hcAd: typeof hcAd;
  doublePointsCard: typeof doublePointsCard;
}

const dispatch: IDispatch = {
  app,
  user,
  seed,
  task,
  cms,
  timeLimitTask,
  redPacket,
  highValueTask,
  share,
  resource,
  redirect,
  helpPlant,
  farmHelpPlant,
  helpRanking,
  dialog,
  hcAd,
  doublePointsCard
};

export default dispatch;
