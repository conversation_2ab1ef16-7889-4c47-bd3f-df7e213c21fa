import { postmessage } from "@/lib/ucapi";
import { WORKER_EVENT_TYPE } from "@/logic/worker/typings";
import tracker from "@/lib/tracker";
import config from "@/config";
import { getParam } from "@/lib/qs";
import dispatch from '@/logic/store';
import stat from "@/lib/stat";
import mx from '@ali/pcom-mx';
import { TaskInfo } from "@/pages/index/components/TaskPop/TaskList/types";
import { checkTaskFinished } from "@/pages/index/components/TaskPop/TaskList/util";

class TaskWorkerBridge {
  private workerRegisterTaskIds: number[] = [];
  private MAX_QUERY_TIMES = 5;
  
  init() {
    this.addEventListener();
    this.queryWorkerRegisterTaskIds()
  }
  
  addEventListener() {
    const onReceiveMessage = (event) => {
      const data = event?.detail?.data || {};
      const receiveTypes = [WORKER_EVENT_TYPE.UC_TASK_REGISTER, WORKER_EVENT_TYPE.UC_TASK_COMPLETE];
      if (data.businessKey !== config.businessKey || !receiveTypes.includes(data.type)) {
        return;
      }
      const taskIds = data.taskIds;
      switch (data.type) {
        case WORKER_EVENT_TYPE.UC_TASK_REGISTER:
          if (taskIds) {
            tracker.log({
              category: 178,
              msg: `查询到注册的任务`,
              sampleRate: 1,
              w_succ: 1,
              c1: JSON.stringify(taskIds),
              c2: getParam('entry'),
            });
            this.setRegisterTaskIds(taskIds);
          } else {
            tracker.log({
              category: 178,
              msg: `未查询到注册任务`,
              sampleRate: 1,
              w_succ: 1,
              c2: getParam('entry'),
            });
          }
          break;
        case WORKER_EVENT_TYPE.UC_TASK_COMPLETE:
          if (data.taskId) {
            const taskFished = this.taskFinished(Number(data.taskId));
            tracker.log({
              category: 178,
              msg: `worker触发任务完成`,
              sampleRate: 1,
              w_succ: 1,
              c1: `${data.taskId}`,
              c2: getParam('entry'),
              c3: `${taskFished}`
            });
            stat.custom('worker_complete_task', {
              action: 'completetask',
              task_id: data.taskId,
            });
            if (!taskFished) {
              dispatch.task.finishTask({ taskId: data.taskId, type: 'complete' });
            }
          }
          break;
        default:
          break;
      }
    }
    // 监听worker发送的消息，
    // 1.获取注册过监听的任务 2.触发任务完成
    document.addEventListener('UCEVT_OnReceiveMessage', onReceiveMessage);
  }

  getRegisterTaskIds() {
    // console.log('[TaskWorkerBridge]getRegisterTaskIds', this.workerRegisterTaskIds)
    return this.workerRegisterTaskIds;
  }
  
  setRegisterTaskIds(taskIds: string[]) {
    this.workerRegisterTaskIds = taskIds.map((item) => Number(item));
  }
  
  taskFinished(taskId: number) {
    const taskList: TaskInfo[] = mx.store.get('task.taskList');
    const taskInfo = taskList?.find(item => item.id === taskId);
    return !!(taskInfo && checkTaskFinished(taskInfo));
  }
  // 向任务worker查询注册过监听的任务
  queryWorkerRegisterTaskIds() {
    let queryTimes = this.MAX_QUERY_TIMES;
    const postWorkerQueryMsg = (times) => {
      tracker.log({
        category: 178,
        msg: `查询worker注册信息-第${times}次`,
        sampleRate: 1,
        w_succ: 1,
        c1: '',
        c2: getParam('entry')
      })
      postmessage({
        data: {
          secret: config.WORKER_SECRET, businessKey: config.businessKey, type: WORKER_EVENT_TYPE.UC_TASK_WORKER_QUERY
        }
      }).catch(() => {});
    }
    const intervalId = setInterval(() => {
      const workerRegisterTaskIds = this.getRegisterTaskIds();
      if (!workerRegisterTaskIds?.length) {
        postWorkerQueryMsg(this.MAX_QUERY_TIMES + 1 - queryTimes);
        queryTimes--;
        if (queryTimes <= 0) {
          clearInterval(intervalId);
        }
      } else {
        clearInterval(intervalId);
      }
    }, 2000);
  }
}
const workerBridge = new TaskWorkerBridge();
export default workerBridge;
