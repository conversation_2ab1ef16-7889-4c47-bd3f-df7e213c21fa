import mx from '@ali/pcom-mx';
import { MainAPI } from './type/event';
import dispatch from '@/logic/store';
import { startLogicGameWater } from './water';
import { handleUpdateHomeData } from '@/logic/business/home';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import { TaobaoRouseApp } from '@/lib/utils/rouseApp';

let hasBindEvent = false;

export const bindEvents = () => {
  if (hasBindEvent) {
    return;
  }

  // 点击施肥按钮
  mx.event.on(MainAPI.WaterGame, async () => {
    // 升级弹窗测试
    // setTimeout(() => {
    //   const upgradeModalProps = {
    //     prevStageLevel: 5,
    //     prevSeedImg: "https://gw.alicdn.com/tfs/TB1G_0hsxD1gK0jSZFyXXciOVXa-180-220.png",
    //     stageLevel: 6,
    //     upgradeImg: "https://gw.alicdn.com/tfs/TB1PUFhsAT2gK0jSZFkXXcIQFXa-180-220.png"
    //   }
    //   baseModal.open(MODAL_ID.UPGRADE, upgradeModalProps);
    // }, 100)
    // return
    const isLoginAndBind = await dispatch.user.checkLoginAndBind(100, 2);
    if (isLoginAndBind) {
      startLogicGameWater();
    }
  });

  // 更新home接口数据
  mx.event.on('update_home_data', async () => {
    await handleUpdateHomeData();
  });

  // 新人领取礼物弹框 pointAmount 肥量number
  mx.event.on('new_gift', async ({newUserGitData}) => {
    baseModal.open(MODAL_ID.NEWBIE_GIFT_PACK, { newUserGitData });
  });

  // 选择种子弹框 flag 带头部和农夫传true
  mx.event.on('select_seed', async ({ flag, onSuccess, btnTitle }) => {
    await dispatch.seed.querySeedList();
    baseModal.open(MODAL_ID.SELECT_SEED, { flag, onSuccess, btnTitle });
  });
  /**
   * 种成兑换
   * @param isNeedModal boolean 是否弹窗跳转 必须
   * @param exchangeUrl string  跳转的兑换链接 必须
   * @param completeFruit number 种成几次
   * @param seedName string 种子名称
   * @param seedSubTitle string 获取的数量
   */
  mx.event.on('sxchange_gift', async ({ isNeedModal, exchangeUrl, completeFruit, seedName, seedSubTitle }) => {
    if (isNeedModal) {
      baseModal.open(MODAL_ID.PLANT_TREES_SUCCESS, {
        completeFruit,
        seedName,
        seedSubTitle,
        exchangeUrl,
      });
    } else {
      TaobaoRouseApp(exchangeUrl, 'harvest');
    }
  });

  // 挽留弹窗
  // mx.event.on('modal_retention', async () => {
  //   baseModal.open(MODAL_ID.UC20_RETENTION);
  // });

  // 绑定淘宝弹窗
  mx.event.on('modal_binding_taobao', async (params) => {
    baseModal.open(MODAL_ID.BINDING_TAOBAO, {...params});
  });
  hasBindEvent = true;
};
