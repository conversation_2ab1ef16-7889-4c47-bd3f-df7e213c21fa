import { bizDecrypt, bizEncrypt, getIDFA, openPage, queryApp, startApp, ucparams } from '@/lib/ucapi';
import { isIOS } from '@/lib/universal-ua';
import stat from '@/lib/stat';
import {
  byteToString,
  generateCacheKey,
  getAndUpdatePersonalizedAdSwitch,
  hcAdStorageHandler,
  RequiredUcParams,
  WPK_CATEGORY_MAP,
} from './utils';
import { HcAdRequestBaseParams, IAd, IHcAdRes, ITaskRecordRes } from './type';
import tracker from '@/lib/tracker';
import config from '@/config';
import { getParam } from '@/lib/qs';

let promiseCache: HcAdRequestBaseParams | null;
let personalizedAdSwitch: string | null;
const debug = getParam('debug');
export const hcAdHelper = {
  generateBaseParams: async () => {
    if (promiseCache) {
      return promiseCache;
    }
    // @ts-ignore
    // eslint-disable-next-line no-async-promise-executor
    promiseCache = new Promise(async (resolve, reject) => {
      try {
        let ucParams = await ucparams({ params: RequiredUcParams });
        let idfa = '';
        if (isIOS) {
          idfa = await getIDFA();
        }
        await Promise.all(
          ['ut', 'od', 'aa', 'ca', 'me', 'pc', 'bd']
            .filter((paramName) => {
              return !!ucParams[paramName];
            })
            .map(async (paramName) => {
              const result = await bizDecrypt({ text: decodeURIComponent(ucParams[paramName]) });
              // eslint-disable-next-line require-atomic-updates
              ucParams[paramName] = result;
              return result;
            }),
        );

        const ad_device_info = {
          cp: ucParams.pc,
          imei: ucParams.me,
          oaid: ucParams.od,
          aaid: ucParams.aa,
          caid: ucParams.ca,
          os: ucParams.fr === 'iphone' ? 'ios' : ucParams.fr,
          idfa: ucParams.me,
          device: ucParams.mi,
          devid: ucParams.di,
          osv: ucParams.os,
          client_ip: ucParams.ip,
          access: ucParams.nw,
          android_id: ucParams.di,
          brand: ucParams.bd,
          is_jb: '0',
          sh: (ucParams.pi && ucParams.pi.split('x')[1]) ?? '',
          sw: (ucParams.pi && ucParams.pi.split('x')[0]) ?? '',
        };
        const ad_app_info = {
          fr: ucParams.fr,
          utdid: encodeURIComponent(decodeURIComponent(ucParams.ut || '')), // 默认期望是没encode，但部分可能会被encode了，为了避免encode两次,
          dn: ucParams.dn,
          pkg_name: 'com.UCMobile',
          pkg_ver: ucParams.ve,
          app_name: 'UC',
          ua: navigator.userAgent,
          lang: ucParams.la,
        };
        const ad_gps_info = {
          amap_code: '',
          gps_time: '',
          lat: '',
          lng: '',
        };
        let ext_info: Array<{ key: string; value: string }> = [];
        if (personalizedAdSwitch === null) {
          // eslint-disable-next-line require-atomic-updates
          personalizedAdSwitch = await getAndUpdatePersonalizedAdSwitch();
          ext_info.push({
            key: 'personalized_ad',
            value: personalizedAdSwitch === 'true' ? '1' : '0',
          });
        }

        const data: HcAdRequestBaseParams = {
          ad_device_info,
          ad_app_info,
          ad_gps_info,
          ext_info,
          page_info: {},
          res_info: {},
        };
        stat.custom('adid_stat', {
          im: ucParams.me ? 1 : 0,
          id: idfa ? 1 : 0,
          oa: ucParams.od ? 1 : 0,
          ca: ucParams.ca ? 1 : 0,
          aa: ucParams.aa ? 1 : 0,
          ut: ucParams.ut ? 1 : 0,
        });
        debug && console.info('[hc-ad] promiseCache request', data);
        resolve(data);
      } catch (error) {
        // eslint-disable-next-line prefer-promise-reject-errors
        reject(null);
      }
    });
    return promiseCache;
  },

  getAdCache(slotId: string) {
    const currentTs = new Date().getTime();
    const hcAdCache = localStorage.getItem(generateCacheKey(slotId));
    if (hcAdCache) {
      const cacheInfo = JSON.parse(hcAdCache);
      if (cacheInfo.expireTime > currentTs) {
        return cacheInfo.ad;
      }
    }
    return null;
  },

  async reportTracking(params: {
    slotId: string;
    isHttpSuccess: boolean;
    accountId?: string;
    adInfo?: IAd;
    resp: IHcAdRes;
    respError?: any;
    req: HcAdRequestBaseParams;
  }) {
    const { slotId, resp, isHttpSuccess, accountId, adInfo, req, respError } = params;
    let msg;
    if (isHttpSuccess && accountId) {
      msg = `${slotId}-${accountId}-请求成功`;
    } else if (isHttpSuccess) {
      msg = `${slotId}-无广告填充`;
    } else if (respError) {
      msg = `${slotId}-请求失败-其他异常`;
    } else {
      msg = `${slotId}-请求失败`;
    }

    const val = await hcAdStorageHandler.get(msg);
    debug && console.log('[hc-ad] reportTracking', msg, val);
    //  判断当天是否上报过此msg
    if (val) {
      return;
    }
    hcAdStorageHandler.setDaily(msg, true);

    const fetchMonitor = tracker.Monitor(WPK_CATEGORY_MAP.HC_AD_FETCH, { sampleRate: 1 });
    const triceCb = isHttpSuccess && accountId ? fetchMonitor.success : fetchMonitor.fail;
    triceCb({
      msg: String(slotId),
      c1: resp?.code,
      c2: adInfo?.ad_id,
      c3: resp?.sid,
      c4: accountId ?? '',
      c5: msg,
      bl1: JSON.stringify(respError ?? {}),
      bl2: JSON.stringify(req.ad_device_info ?? []),
      bl3: JSON.stringify(resp ?? {}),
    });

    stat.custom('huichuan_ad_fetch', {
      slot_id: slotId,
      account_id: accountId ?? '',
      sid: resp?.sid ?? '',
      ad_id: adInfo?.ad_id ?? '',
      is_fill: accountId ? 1 : 0,
      is_error: isHttpSuccess ? 0 : 1,
    });
  },

  async requestHuichuanAd(params: { slotId: string; forceUpdate: boolean; cacheHourTime: number }) {
    const { slotId, forceUpdate, cacheHourTime = 0 } = params;
    const CACHE_KEY = generateCacheKey(slotId);

    // 判断本地广告缓存标识，缓存时间内直接返回缓存数据
    const currentTime = new Date().getTime();
    const hcAdCache = localStorage.getItem(CACHE_KEY);
    if (hcAdCache) {
      const cacheInfo = JSON.parse(hcAdCache);
      if (cacheInfo.expireTime > currentTime && !forceUpdate) {
        cacheInfo.ad.from_cache = true;

        debug && console.info('[hc-ad] from_cache', slotId, cacheInfo.ad);
        return cacheInfo.ad;
      }
      localStorage.removeItem(CACHE_KEY);
    }

    const reqBaseData = await hcAdHelper.generateBaseParams();
    const requestData = {
      ...reqBaseData,
      ad_pos_info: [
        {
          // 61 大图打开 62 小图打开 63  三图打开 64  大图下载 65  小图下载 66  三图下载。不传则使用服务端配置
          ad_style: [],
          req_cnt: '1',
          slot_id: slotId,
          slot_type: '0',
        },
      ],
    };

    const requestDataStr = JSON.stringify(requestData);
    const encriptedDataStr = await bizEncrypt({ text: requestDataStr });
    const buffer = new Uint8Array(16);
    buffer[0] = 5; // ENV: 0 - 不加密 1 - UC-m9加密 2 - RSA加密 4 -无线保镖 5 - 无线保镖Base64
    buffer[1] = 2; // DATATYPE: 2-json
    buffer[2] = 1; // VER。协议版本号。1表示1.0
    buffer[3] = 1; //  SDK_VER(1 byte) sdk版本号，1表示1.0
    const bodyStr = byteToString(buffer) + encriptedDataStr;

    // debug && console.info('[hc-ad] bodyStr', bodyStr);
    try {
      // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
      const res = await fetch(`${config.HC_AD_API_URL}/${config.HC_AD_API_PATH_NAME}?app=UC`, {
        method: 'POST',
        body: bodyStr,
      }).then((resp) => {
        return resp.text();
      });

      debug && console.info('[hc-ad]fetch post 请求 汇川 返回', res);
      const resStr = res.substr(16);
      const resAdData: IHcAdRes = JSON.parse(resStr);
      if (resAdData.code && resAdData.code === '0') {
        const curAdData = resAdData?.slot_ad ?? [];

        if (
          curAdData.length > 0 &&
          curAdData[0].slot_id === slotId &&
          curAdData[0]?.ad &&
          curAdData[0]?.ad?.length > 0
        ) {
          curAdData[0].ad.forEach(async (adInfo, idx) => {
            const account_id = adInfo?.ad_content.account_id ?? '';
            hcAdHelper.reportTracking({
              slotId,
              isHttpSuccess: true,
              accountId: account_id,
              adInfo: adInfo,
              resp: resAdData,
              req: requestData,
            });
          });
          // 获取当前时间戳
          const ts = new Date().getTime();
          // 计算过期时间戳，如果有配置时间，则使用配置的过期时间，没有则默认当天 24 点过期
          let hcAdCacheEndTs = new Date(
            new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000,
          ).getTime();
          if (cacheHourTime > 0) {
            hcAdCacheEndTs = ts + cacheHourTime * 60 * 60 * 1000;
          }
          curAdData[0].sid = resAdData.sid;
          const cacheValue = {
            ad: curAdData[0],
            expireTime: hcAdCacheEndTs,
          };
          // 缓存广告&过期时间到本地
          localStorage.setItem(CACHE_KEY, JSON.stringify(cacheValue));
          return curAdData[0];
        }

        localStorage.removeItem(CACHE_KEY);
        hcAdHelper.reportTracking({
          slotId,
          isHttpSuccess: true,
          resp: resAdData,
          req: requestData,
        });
        return null;
      }

      hcAdHelper.reportTracking({
        slotId,
        isHttpSuccess: false,
        resp: resAdData,
        req: requestData,
      });
      return null;
    } catch (error) {
      hcAdHelper.reportTracking({
        slotId,
        isHttpSuccess: false,
        req: requestData,
        resp: {} as unknown as IHcAdRes,
        respError: error,
      });
      return null;
    }
  },

  getTimestampInSecond() {
    return Math.floor(Date.now() / 1000);
  },

  getExposeStatUrl(ad: IAd) {
    if (!ad?.vurl || !ad?.vurl.length) {
      return '';
    }
    const timestamp = hcAdHelper.getTimestampInSecond();
    return ad?.vurl[0].replace('{TS}', `${timestamp}`);
  },

  getOtherExposeStatUrl(ad: IAd): string[] {
    if (!ad?.vurl || !ad?.vurl?.length) {
      return [];
    }
    return ad?.vurl?.splice(1, ad.vurl.length);
  },

  // 获取点击打点 url，点击打点的timestamp需要和跳转的timestamp一致
  getClickStatUrl(ad: IAd, timestamp: number) {
    if (!ad.curl || !ad.curl.length) {
      return '';
    }
    let isDownload = false;
    if (ad.turl.length > 1) {
      isDownload = true;
    }
    return ad.curl[0].replace('{TS}', `${timestamp}`) + (isDownload ? '&hc_subid=0' : '');
  },

  getOtherClickStatUrl(ad: IAd): string[] {
    if (!ad.curl || !ad.curl.length) {
      return [];
    }
    return ad.curl.splice(1, ad.curl.length);
  },

  // 获取点击后需要跳转的 url
  getRedirectUrl(ad: IAd, timestamp: number) {
    if (!ad.turl.length) {
      return '';
    }
    let redirectUrl = ad.turl[0];
    // 如果turl[0]里有callback参数，需要urldecode callback参数值后添加stm，再urlencode回去，否则保持下发的turl不变。
    if (redirectUrl.indexOf('callback=') > -1) {
      const exp = /callback=(\S+?)(?=&|$)/;
      redirectUrl = redirectUrl.replace(exp, `callback=$1${timestamp}`);
    }
    return redirectUrl;
  },

  /**
   * 曝光上报汇川
   */
  hcAdExpose(ad: IAd) {
    const urls = [hcAdHelper.getExposeStatUrl(ad), ...hcAdHelper.getOtherExposeStatUrl(ad)];

    debug && console.log('[hc-ad] hcAdExpose', urls, ad);
    urls.forEach((url) => {
      if (url) {
        const rpt = new Image();
        // eslint-disable-next-line @iceworks/best-practices/no-http-url
        rpt.src = url.replace('http://', 'https://');
      }
    });
  },
  /**
   * 点击上报汇川，点击打点的timestamp需要和跳转的timestamp一致
   */
  async hcAdClick(ad: IAd) {
    const timestamp = hcAdHelper.getTimestampInSecond();
    const redirectUrl = hcAdHelper.getRedirectUrl(ad, timestamp);
    debug && console.info('[hc-ad] adClick', redirectUrl);
    if (!redirectUrl) {
      return;
    }
    const statUrls = [hcAdHelper.getClickStatUrl(ad, timestamp), ...hcAdHelper.getOtherClickStatUrl(ad)];
    statUrls.forEach((clickStatUrl) => {
      if (clickStatUrl) {
        const rpt = new Image();
        // eslint-disable-next-line @iceworks/best-practices/no-http-url
        rpt.src = clickStatUrl.replace('http://', 'https://');
      }
    });

    const targetLinks: Array<{ type: string; link: string; scheme?: string }> = [];
    if (isIOS && ad.ad_content.adm_fixed_ulk && ad.ad_content.scheme) {
      targetLinks.push({
        type: 'ulk',
        link: ad.ad_content.adm_fixed_ulk,
        scheme: ad.ad_content.scheme,
      });
    } else if (ad.ad_content.scheme) {
      targetLinks.push({
        type: 'scheme',
        link: ad.ad_content.scheme,
      });
    }
    
    targetLinks.push({
      type: 'url',
      link: redirectUrl
    });

    let curr;
    debug && console.info(`[hc-ad] hcAdClick 处理跳转的顺序列表：${JSON.stringify(targetLinks)}`);
    // eslint-disable-next-line no-cond-assign
    while ((curr = targetLinks.shift())) {
      if (!curr) {
        break;
      }
      let needBreak = false;
      let schemeName;
      let queryAppResp;
      let startAppResp;
      debug && console.info(`[hc-ad] hcAdClick 处理：${curr.type} ${curr.link}`);
      switch (curr.type) {
        case 'ulk':
          console.log('处理ulk');
          schemeName = curr.scheme;
          queryAppResp = queryApp({
            cache_first: '0',
            pkgs: [schemeName],
          });
          debug && console.info('[hc-ad] hcAdClick app安装查询结果：', schemeName, '\n', JSON.stringify(queryAppResp));
          if (queryAppResp[schemeName] === '') {
            console.log('查询ulk对应应用不存在');
            break;
          }
          debug && console.info('[hc-ad] hcAdClick ulk对应应用存在，准备通过startApp打开');
        // eslint-disable-next-line no-fallthrough
        case 'scheme':
          debug && console.info(`[hc-ad]  处理scheme:${curr.type}`);
          startAppResp = await startApp(curr.link);
          debug && console.info('[hc-ad] hcAdClick  startApp:', JSON.stringify(startAppResp));
          if (!startAppResp.result || startAppResp.result === 'false') {
            // 打开失败
            debug && console.info(`[hc-ad] hcAdClick 处理scheme:${curr.type},打开失败，准备通过url跳转`);
            break;
          }
          debug && console.info(`[hc-ad] hcAdClick 处理scheme:${curr.type},打开成功，结束`);
          needBreak = true; // 打开成功，需要结束
          break;
        case 'url':
          debug && console.info(`[hc-ad] hcAdClick 处理url:${curr.type}`);
          openPage(redirectUrl);
          needBreak = true;
          break;
        default:
          break;
      }
      if (needBreak) {
        break;
      }
    }
    debug && console.info(`[hc-ad] hcAdClick 处理结束`);
  },
  /**
   * 提交记录到汇川进行记录 sid、ucutid
   */
  async submitTaskRecord(
    sid: string,
    thirdid: string,
    adInfos: { account_id: string; slot_id: string },
  ): Promise<boolean> {
    const submitMonitor = tracker.Monitor(WPK_CATEGORY_MAP.UT_SID_SUBMIT_HC, { sampleRate: 1 });
    try {
      // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
      const res: ITaskRecordRes = await fetch(`${config.HC_TASK_API}/web/main/reward/taskrecord/add4third`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sid,
          thirdid,
          channel: 2
        })
      }).then(data => data.json()); 

      if (res.status === 0) {
        submitMonitor.success({
          msg: `${adInfos.slot_id}-${adInfos.account_id}-提交成功`,
          c1: JSON.stringify(res.status),
          c2: sid,
          c3: thirdid,
          bl2: JSON.stringify(res)
        });
        return true;
      }
      submitMonitor.fail({
        msg: `${adInfos.slot_id}-${adInfos.account_id}-提交失败`,
        c1: JSON.stringify(res.status),
        bl1: JSON.stringify(res),
        bl2: JSON.stringify(res)
      });
      return false;
    } catch (error) {
      submitMonitor.fail({
        msg: `${adInfos.slot_id}-${adInfos.account_id}-提交失败`,
        bl1: JSON.stringify(error),
      });
      return false;
    }
  },
};
