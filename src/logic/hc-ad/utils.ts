import { getPersonalizedRecommendSwitch } from '@/lib/ucapi';
import Storage from './storage';

/**
 * 汇川单独的Key,不丢进业务一起放置
 */
export const STORAGE_HC_AD_CACHE_KEY = 'hc_ad_cache';
export const STORAGE_HC_AD_CARD_CACHE_KEY = 'hc_ad_cache_last_card_ad';
export const STORAGE_HC_AD_PERSONALIZED_AD_KEY = 'hc_ad_cache_personalized_ad';
export const WPK_CATEGORY_MAP = {
  HC_AD_FETCH: 179,
  UT_SID_SUBMIT_HC: 180
};

export const RequiredUcParams = 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf';

export function byteToString(arr) {
  if (typeof arr === 'string') {
    return arr;
  }
  let str = '';
  for (let i = 0; i < arr.length; i++) {
    const one = arr[i].toString(2);
    const v = one.match(/^1+?(?=0)/);
    if (v && one.length === 8) {
      const bytesLength = v[0].length;
      let store = arr[i].toString(2).slice(7 - bytesLength);
      for (let st = 1; st < bytesLength; st++) {
        store += arr[st + i].toString(2).slice(2);
      }
      str += String.fromCharCode(parseInt(store, 2));
      i += bytesLength - 1;
    } else {
      str += String.fromCharCode(arr[i]);
    }
  }
  return str;
}
/**
 * 获取个性化推荐开关
 */
export async function getAndUpdatePersonalizedAdSwitch() {
  let closePersonalizedAd = localStorage.getItem(STORAGE_HC_AD_PERSONALIZED_AD_KEY);

  const getPersonalizedRecommendSwitchCb = async () => {
    try {
      const data = await getPersonalizedRecommendSwitch();
      let value = data?.personalized_ad === 0;
      localStorage.setItem(STORAGE_HC_AD_PERSONALIZED_AD_KEY, String(value));
      return String(value);
    } catch (error) {
      console.error('getPersonalizedRecommendSwitch err', error);
      return 'false';
    }
  };

  if (!closePersonalizedAd) {
    closePersonalizedAd = await getPersonalizedRecommendSwitchCb();
  }

  return closePersonalizedAd;
}

export const generateCacheKey = (slotId: string) => {
  return `${STORAGE_HC_AD_CACHE_KEY}-${slotId}`;
};

export const hcAdStorageHandler = {
  setNameSpace(nameSpace: string) {
    Storage.setNameSpace(nameSpace);
  },
  get(key: string): Promise<any> {
    return Storage.get(key)
      .then((ret) => {
        if (!ret) return 0;

        const { expire, data } = ret;
        const isExpired = expire && Date.now() > expire;
        return isExpired ? 0 : data;
      })
      .catch(() => {
        return 0;
      });
  },
  set(key: string, value: any, expire?: Date | number): Promise<void> {
    let exp;
    if (!expire) exp = 0;
    else if (typeof expire === 'number') exp = Date.now() + expire;
    else if (expire instanceof Date) exp = expire.getTime();
    else exp = 0;

    return Storage.set(key, { expire: exp, data: value });
  },
  setDaily(key: string, value: any): Promise<void> {
    const todayLast = new Date();
    todayLast.setHours(23, 59, 59, 999);
    return Storage.set(key, { expire: todayLast.getTime(), data: value });
  },
  remove(key: string): Promise<void> {
    return Storage.remove(key);
  },
  getAllKeys(): Promise<string[]> {
    return Storage.getAllKeys();
  }
};
