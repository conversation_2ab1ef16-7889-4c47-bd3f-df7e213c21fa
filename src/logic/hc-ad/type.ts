export interface IHcAdRes {
  code: string;
  reason: string;
  serverTime: string;
  sid: string;
  slot_ad?: ISlotAd[];
}
  
export interface ISlotAd {
  from_cache?: boolean;
  slot_id: string;
  ad?: IAd[];
  sid: string;
}
  
export interface IAd {
  ad_action?: any;
  ad_content: {
    scheme?: string;
    package_name?: string;
    adm_fixed_ulk?: string;
    title: string;
    img_1: string;
    account_id?: string; // 账户id todo 真正字段叫啥？
    logo_url?: string;
  };
  ad_id: string;
  ad_is_effect?: string;
  ad_source_type?: number;
  curl: string[];
  eurl: string;
  furl: string;
  style: string;
  turl: string[];
  vurl: string[];
}
  
export interface ITaskRecordRes {
  status: number;
  message: string;
  extend?: any;
}

export interface HcAdRequestBaseParams {
  ad_device_info?: {
    cp?: string;
    imei?: string;
    oaid?: string;
    aaid?: string;
    caid?: string;
    os?: string;
    idfa?: string;
    device?: string;
    devid?: string;
    osv?: string;
    client_ip?: string;
    access?: string;
    android_id?: string;
    brand?: string;
    is_jb?: string;
    sh?: string;
    sw?: string;
  };
  ad_app_info?: {
    fr?: string;
    utdid?: string;
    dn?: string;
    pkg_name?: string;
    pkg_ver?: string;
    app_name?: string;
    ua?: string;
    lang?: string;
  };
  ad_gps_info?: {
    amap_code?: string;
    gps_time?: string;
    lat?: string;
    lng?: string;
  };
  ext_info?: Array<{
    key: string;
    value: string;
  }>;
  page_info?: Record<string, any>;
  res_info?: Record<string, any>;
}
