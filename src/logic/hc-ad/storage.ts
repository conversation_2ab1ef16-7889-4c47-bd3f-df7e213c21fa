export interface Result {
  result: 'success' | 'fail';
  data: any;
}

const hcAdStorage = {
  getItem(key, callback) {
    const data = localStorage.getItem(key) || 0;
    callback({ data, result: 'success' });
  },
  setItem(key, value, callback) {
    localStorage.setItem(key, value);
    callback({ data: 0, result: 'success' });
  },
  removeItem(key, callback) {
    localStorage.removeItem(key);
    callback({ data: 0, result: 'success' });
  },
  getDefaultAllKeys(callback) {
    const data = Object.keys(localStorage);
    callback({ data, result: 'success' });
  },
  length(callback) {
    const data = Object.keys(localStorage).length;
    callback({ data, result: 'success' });
  },
  p(fn) {
    return (...args): Promise<any> =>
      new Promise((resolve, reject) => {
        const callback = (ret: Result) => (ret.result === 'success' ? resolve(ret.data) : reject(ret.data));
        fn.apply(null, args.concat(callback));
      });
  },
  get(key: string): Promise<any> {
    return hcAdStorage
      .p(hcAdStorage.getItem)(key)
      .then((ret) => {
        if (!ret) return 0;
        return JSON.parse(ret);
      })
      .catch(() => {
        return 0;
      });
  },

  set(key: string, data: any): Promise<void> {
    return hcAdStorage.p(hcAdStorage.setItem)(key, JSON.stringify(data));
  },

  remove(key: string): Promise<void> {
    return hcAdStorage.p(hcAdStorage.removeItem)(key);
  },
  getAllKeys(): Promise<string[]> {
    return hcAdStorage.p(hcAdStorage.getDefaultAllKeys)();
  },
};

let NAMESPACE = 'hc_ad_cache_slot_all_ad';
let cache: any = null;

export default {
  setNameSpace: (nameSpace: string) => {
    NAMESPACE = nameSpace;
  },
  // FIXME: 队列处理
  get(key: string): Promise<any> {
    if (cache) return Promise.resolve(cache[key]);

    return hcAdStorage.get(NAMESPACE).then((ret) => {
      cache = { ...ret, ...cache };
      return cache[key] || 0;
    });
  },
  // FIXME: 队列处理
  set(key: string, value: any): Promise<void> {
    if (cache) {
      cache[key] = value;
      return hcAdStorage.set(NAMESPACE, cache);
    }

    return this.get(key).then(() => {
      cache[key] = value;
      return hcAdStorage.set(NAMESPACE, cache);
    });
  },
  remove(key: string): Promise<void> {
    if (cache) {
      delete cache[key];
      return hcAdStorage.set(NAMESPACE, cache);
    }

    return this.get(key).then(() => {
      delete cache[key];
      return hcAdStorage.set(NAMESPACE, cache);
    });
  },
  getAllKeys(): Promise<string[]> {
    if (cache) return Promise.resolve(Object.keys(cache));
    return hcAdStorage.getAllKeys();
  },
};
