import mx from '@ali/pcom-mx';
import { MainAPI } from './type/event';

export const clickTree = () => {
  const plantInfo = mx.store.get('mainInfo.gameInfo.plantInfo');
  if (!plantInfo) return;

  // const extFuncs = mx.store.get('realExtraData.extFuncs');
  const { seedStage = {}, canExchange } = plantInfo;
  let branch = '';
  if (canExchange) {
    branch = 'exchange';
    // if (Array.isArray(extFuncs)) {
    //   const ext = extFuncs.find((item) => item.code === 'signInWaterPlant');
    //   if (ext?.data?.state === 'finish') {
    //     branch = 'fastPlantExchange';
    //   }
    // }
  } else if (!seedStage.seedCode) {
    branch = 'openSeedSelect';
  }
  switch (branch) {
    case 'exchange': {
      // mx.event.emit('toExchange_show', {
      //   canExchange: 'true',
      //   isCaptain: 'true',
      //   helpMode: mx.store.get('realExtraData.extraData.teamInfo.plantMode') === 'help' ? 'true' : 'false',
      // });
      break;
    }
    case 'openSeedSelect': {
      mx.event.emit('toSeed_show');
      break;
    }
    // case 'fastPlantExchange': {
    //   mx.event.emit('OPEN_WFP_POP');
    //   break;
    // }
    default: {
      // 树抖动动画
      mx.event.emit(MainAPI.PlayClickTreeAnim);
      break;
    }

  }
};
