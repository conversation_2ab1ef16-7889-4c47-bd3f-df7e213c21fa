// import updateGameData from './updateGameData';
import mx from '@ali/pcom-mx';
import { startGameWater } from '@/logic/business/water';
import toast from "@/lib/universal-toast/component/toast";

// 浇水/施肥逻辑
export const startLogicGameWater = () => {
  const canExchange = mx.store.get('mainInfo.gameInfo.plantInfo.canExchange') || false;
  const seedStage = mx.store.get('mainInfo.gameInfo.plantInfo.seedStage') || {};
  const exchangeCount = mx.store.get('mainInfo.gameInfo.plantInfo.exchangeCount')
  if (!seedStage.seedCode) {
    if (!exchangeCount) {
      toast.show('选择水果种子后即可施肥')
    } else {
      toast.show('重新种下一棵果树即可施肥')
    }
    return;
  }
  if (canExchange) {
    toast.show('恭喜种成，快去兑换水果吧')
    return;
  }
  startGameWater();
};
