import wpk from './wpk';
import withUniversal from '@/lib/render-utils/with-universal';
import { ILogParams, ILogErrorParams, WpkReporterOptions } from './typings.d';

let scr: string;

const tracker = {
  /**
   * 初始化啄木鸟
   */
  init(options: Partial<WpkReporterOptions>) {
    // wpk.install(options);
    wpk.init(options as any);
    initGlobalErrorListener();
  },

  /**
   * 更新  Wpk 实例的配置项
   */
  config(options: Partial<WpkReporterOptions>) {
    wpk.setConfig(options);
  },

  /**
   * 上报自定义监控项，不带异常堆栈信息
   */
  log(params: ILogParams) {
    scr = scr || `${window.innerHeight}x${window.innerWidth}`;
    wpk.report({ ...normalize(params), scr });
  },

  /**
   * 上报 JS 异常，不带异常堆栈信息
   */
  logError(err: Error, params: Partial<ILogErrorParams> = {}) {
    wpk.reportJSError(err, { ...normalize(params), scr });
  },

  reportApiError(params: Partial<ILogErrorParams> = {}) {
    wpk.reportApi({ ...normalize(params), scr });
  },

  Monitor(category: number, options: Partial<ILogErrorParams & { timeout: number }> = {}) {
    let isDone = false;
    let timer;

    const t0 = Date.now();
    const log = (w_succ: 0 | 1) => (params?: Omit<ILogParams, 'category'>) => {
      // REFACTOR: 超时阻止多次回调暂时兼容 monitor 奇怪的用法
      if (options.timeout && isDone) return;
      if (timer) clearTimeout(timer);
      timer = null;
      isDone = true;

      this.log({ w_succ, category, wl_avgv1: Date.now() - t0, ...options, ...params });
    };

    if (options.timeout) {
      timer = setTimeout(() => log(0)({ msg: `timeout(${options.timeout}ms)` }), options.timeout);
    }

    return {
      success: log(1),
      fail: log(0),
    };
  },
};

function normalize<T extends ILogParams | ILogErrorParams>(obj: T): T {
  if (!obj) return obj;
  const { c1, c2, c3, c4, c5, c6, c7, ...others } = obj;
  const output = { ...others } as any;

  if (c1) output.c1 = typeof c1 === 'object' ? JSON.stringify(c1) : c1;
  if (c2) output.c2 = typeof c2 === 'object' ? JSON.stringify(c2) : c2;
  if (c3) output.c3 = typeof c3 === 'object' ? JSON.stringify(c3) : c3;
  if (c4) output.c4 = typeof c4 === 'object' ? JSON.stringify(c4) : c4;
  if (c5) output.c5 = typeof c5 === 'object' ? JSON.stringify(c5) : c5;
  if (c6) output.c6 = typeof c6 === 'object' ? JSON.stringify(c6) : c6;
  if (c7) output.c7 = typeof c7 === 'object' ? JSON.stringify(c7) : c7;

  return output;
}

function handleException(msg: string, ev: ErrorEvent) {
  const e = ev && ev.error || ev;

  if (!e || !e.stack || !e.message) return;
  tracker.logError(e, { c1: msg });
}

function initGlobalErrorListener() {
  // const target = IS_WEEX ? global : window;
  const target = window;
  if (!target) return;

  target.addEventListener('error', (ev: ErrorEvent) => {
    handleException('errorHandler', ev);
  }, false);

  target.addEventListener('unhandledrejection', ({ reason }) => {
    handleException('unhandledRejection', reason);
  }, false);
}

export default withUniversal(tracker);
