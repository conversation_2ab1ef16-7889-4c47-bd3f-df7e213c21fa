## tracker

> 封装 wpk-reporter 组件，实现啄木鸟异常监控上报，包括 weex 环境下的全局异常监控



## Getting Started

```js
import tracker from '@ali/weex-toolkit/lib/tracker';
import * as weexConfig from '@ali/weex-toolkit/lib/weex_config';

// 初始化监控，同时启动全局异常捕获
tracker.init({
  bid: 'YOUR_WPK_BID',
  uid: () => weexConfig.uc.ucParams.ut,
  rel: MAIN_VERSION,
  debug: !!DEV,
});

// 配置用户标识，默认为 wpk 生成的 uuid
tracker.config({ uid: 'YOUR_USER_ID' });

// 自定义事件上传
tracker.log({ category: 100, msg: 'ERROR_MESSAGE', c1: 'CUSTOM_C1' });

// 异常上报
tracker.logError(error, { c1: 'CUSTOM_C1' });
```



### 自定义监控项目接入

```js
import tracker from '@ali/weex-toolkit/lib/tracker';

const FIRSTSCREEN_CATEGORY = 100;
const firstscreen = tracker.Monitor(FIRSTSCREEN_CATEGORY);

try {
	// 开始加载首屏...
  firstscreen.success();
} catch (e) {
  const msg = e && e.message || JSON.stringify(e);
  firstscreen.fail({ msg, c1: 'CUSTOM ERROR MESSAGE' });
}
```



## 相关链接

- [啄木鸟接入文档](<https://yuque.antfin-inc.com/wpk/help/web>)