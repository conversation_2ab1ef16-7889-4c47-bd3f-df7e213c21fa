import { SdkOptions } from '@ali/itrace-types';
import { iTraceReporter } from '@ali/itrace-browser';

type TWpkMethod = 'setConfig' | 'report' | 'reportJSError' | 'reportApi';
interface IStackItem {
  method: TWpkMethod;
  args: any;
}

let stack: IStackItem[] | null = [];
let loadChunk = false;

let wpk: iTraceReporter;

const wpkProxy = {
  init,

  setConfig: proxy('setConfig'),

  report: proxy('report'),

  reportJSError: proxy('reportJSError'),

  reportApi: proxy('reportApi'),
};

function proxy(method: TWpkMethod) {
  return (...args) => {
    if (wpk) {
      wpk[method](...args);
      return;
    }

    if (stack) stack.push({ method, args });
  };
}

function init(args: SdkOptions) {
  load(args);
}

function load(args) {
  if (loadChunk) return;
  loadChunk = true;

  const onload = (ret) => {
    const WpkReporter: iTraceReporter = ret && ret.default || ret;
    if (!stack) return;

    wpk = WpkReporter.getItraceSingleton({
      sampleRate: 1,
      bid: '', // 应用标识
      uid: '', // 支持函数，需返回最终的uid字符串
      rel: '', // 支持函数，需返回最终的版本字符串
      spa: true,
      debug: false,
      ...args,
    });


    stack
      .forEach((item) => wpk[item.method](...item.args));

    stack = null;

    // wpk.setReady();
    // itrace 日志延迟1s上报，避免初始化抢占资源，已经确认过不会丢数据
    setTimeout(() => {
      wpk.setReady();
    }, 1000);
  };

  const trigger = () => {
    import(/* webpackChunkName: "@ali/itrace-browser" */ '@ali/itrace-browser').then(onload).catch(() => { loadChunk = false; });
  };

  setTimeout(trigger, 0);
}

export default wpkProxy;
