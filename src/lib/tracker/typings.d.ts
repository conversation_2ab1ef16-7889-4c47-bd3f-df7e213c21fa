export interface WpkReporterOptions {
  bid: string;
  uid: () => string;
  rel: string;
  spa: boolean;
  debug: boolean;
  checkHidden: boolean;
  plugins: any[];
  sampleRate: number;
  /** 是否关闭系统监控项数据上报，只保留自定义监控上报。若应用同时有“内核接入”方式和“JS探针接入”方式时，建议在android系统下设置此项为 true, 兼容性好 */
  onlyCustom?: boolean;
  /** 新参数 和 onlyCustom 一致，不过暂时依赖版本没支持 */
  onlyCustomInUCCore?: boolean;
  beforeSend?: any;
}

export interface ILogErrorParams {
  c1?: string;
  c2?: string;
  c3?: string;
  c4?: string;
  c5?: string;
  c6?: string;
  c7?: string;
  c8?: string;
  c9?: string;
  c10?: string;
  bl1?: string;
  bl2?: string;
  bl3?: string;
  bl4?: string;
  bl5?: string;
  sampleRate?: number;
}

export interface ILogParams extends ILogErrorParams {
  category: number;
  msg?: string;
  w_msg?: string;
  stack?: string;
  w_file?: string;
  w_line?: string;
  w_col?: string;
  w_succ?: 0 | 1;
  wl_avgv1?: number;
  wl_avgv2?: number;
  wl_avgv3?: number;
  wl_avgv4?: number;
  wl_avgv5?: number;
  wl_avgv6?: number;
  wl_avgv7?: number;
  wl_avgv8?: number;
  wl_avgv9?: number;
  wl_avgv10?: number;
  wl_avgv11?: number;
  wl_avgv12?: number;
  sampleRate?: number;
  w_trace_reqid?: string; 
}
