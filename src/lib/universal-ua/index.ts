import ua from '@ali/uc-toolkit/lib/ua';
import { ucparams } from '../ucapi';

export enum APP_NAME {
  UC = 'UC',
  QUARK = 'QUARK',
  DDLEARN = 'BBD',
  UNKNOWN = 'unknown',
}

export const isAndroid = ua.isAndroid();

export const isIOS = ua.isIOS();

export const isWechat = ua.isWechat();

export const appVersion = ua.getUCVersion();

export const bizVersion = MAIN_VERSION;

export const isUc = ua.isUC();

export const osName = isAndroid ? 'Android' : 'iOS';

export const isLatestVersion = ua.isLatestVersion;

export const getAppVersion = async () => {
  let version = ua.getUCVersion()
  if (!version) {
    const ucParams = await ucparams({ params: 've' }, true);
    version = (ucParams || { ve: '' }).ve
  }
  return version
}

export const getAppSubVersion = async () => {
  const ucParams = await ucparams({params: 'sv'});
  return (ucParams || {sv: ''}).sv
}
