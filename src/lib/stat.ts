import idx from 'idx';
import withUniversal from '@/lib/render-utils/with-universal';
import { getParam as getQuery } from '@/lib/qs';
import fact from '@ali/fact-stat';

let baseParam: {
  spma?: string;
  spmb?: string;
} = {};

const statExport = {
  init(obj: any = {}) {
    const statOption = {
      entry: getQuery('entry') || 'default',
      ...obj,
    };
    const { ev_ct, spma, spmb } = obj;
    fact.setup({ ev_ct, a: spma, b: spmb });
    this.updateParam(statOption);
  },
  updateParam(options = {}) {
    fact.baseParam(options);
    baseParam = {...baseParam, ...options};
  },
  pv(page = '', { b = baseParam?.spmb } = {}) {
    fact.pageview(page, { b });
  },
  exposure(arg1, options = {}) {
    fact.exposure(arg1, options);
  },
  click(arg1, options = {}) {
    fact.click(arg1, options);
  },
  custom(arg1, options = {}) {
    fact.event(arg1, options);
  },
  getCustomStatParams() {
    return idx(fact, _ => _.wa.customStatParams) || {};
  },
};

export default withUniversal(statExport);
