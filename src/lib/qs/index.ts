import qs from 'qs';
/**
 * 获取 url 上的 search 参数值
 * @param {string} key - 参数名
 * @param {string} url - 被查找的 url，默认为当前的 location
 */
export function getParam(key, url?) {
  url = url || location.search;
  const hashIndex = url.indexOf('#');
  if (hashIndex > 0) {
    url = url.substr(0, hashIndex);
  }
  const keyMatches = url.match(new RegExp(`[?|&]${encodeURIComponent(key)}=([^&]*)(&|$)`));
  if (keyMatches && keyMatches[1] === '%s') {
    return keyMatches[1];
  } else {
    return keyMatches ? decodeURIComponent(keyMatches[1]) : '';
  }
}
export function getQueryUCParamsObj(ucParamsStr = 'dnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicckpbdmiodca') {
  const qsObj = qs.parse(location.search, { ignoreQueryPrefix: true});
  let ret = Object.keys(qsObj).filter(key => ucParamsStr !== key && ucParamsStr.includes(key)).reduce((acc, key) => {
    acc[key] = qsObj[key];
    return acc;
  }, {})
  console.log('[lib/qs/index] location.search ', location.search, ' ret', ret);
  return ret;
}

export function stringify(data = {}) {
  const stack: string[] = [];
  Object.keys(data).forEach((key) => {
    const value = data[key];
    stack.push(`${encodeURIComponent(key)}=${encodeURIComponent(value === undefined ? '' : value)}`);
  });
  const query = stack.join('&').replace(/%20/g, '+');

  return query;
}
interface IParams {
  [key: string]: any;
}

export function addParams(url: string, params: IParams) {
  const paramsKeys = Object.keys(params)
  if (!url) {
    return ''
  }
  if (!paramsKeys.length) {
    return url
  }
  url += !url ? '' : url.includes('?') ? '&' : '?'
  return url + paramsKeys.map((key) => `${key}=${params[key]}`).join('&')
}


export default {
  getParam,
  stringify,
  getQueryUCParamsObj
};
