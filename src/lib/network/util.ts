import qsUtil from '@/lib/qs';
import { NetworkOption } from './typings.d';
import fetch from './fetch';

export const qs = qsUtil;

/**
 * 发起请求
 * @param params 请求参数
 */
export function request(params: NetworkOption): Promise<any> {
  const defaultOption = {
    type: 'json',
    method: 'GET',
    timeout: 5e3,
    json: true,
  };

  const options = { ...defaultOption, ...params };

  return fetch(options);
}
