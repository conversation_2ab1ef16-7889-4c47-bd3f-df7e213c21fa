import config from "@/config";

interface IParseUrlRes {
  scheme: string | undefined;
  host: string | undefined;
  port: string | undefined;
  path: string | undefined;
  query: string | undefined;
  hash: string | undefined;
}
  
export function parseUrl(url: string): IParseUrlRes {
  const regExp = /^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;
  const result = regExp.exec(url) || []
  return {
    scheme: result[0],
    host: result[3],
    port: result[4],
    path: result[5],
    query: result[6],
    hash: result[7]
  }
}

export function geneTaskRequestId() {
  function s4() {
    // eslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
}
  

// 每个用户一个访问周期内只能有一个page uuid
export let userPageUuid = '';
/**
 * 全链路日志串联，
 */
export const getUserPageUuid = (ssrValue = '') => {
  // SSR 获取的时候，userPageUuid 并未全局唯一，故添加赋值过程
  if (ssrValue) {
    userPageUuid = ssrValue;
    return userPageUuid;
  }
  if (userPageUuid) {
    return userPageUuid;
  }
  function s4() {
    // eslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  userPageUuid = `${s4() + s4()}${s4()}${s4()}${s4()}${s4()}${s4()}${s4()}`;
  return userPageUuid;
};

// itrace 请求头
export const handleItraceHeaderPrams = (requestId: string) => {
  const currentPageUuid = userPageUuid.length > 0 ? userPageUuid : getUserPageUuid();
  return {
    'x-wpk-reqid': requestId,
    x_wpk_reqid: requestId,
    'x-wpk-traceid': currentPageUuid,
    x_wpk_traceid: currentPageUuid,
    'x-wpk-bid': config.WPK_BID,
    x_wpk_bid: config.WPK_BID,
  }
}

// 需要追加itrace headers的api
const needAppendHeaderApiList = [
  '/home',
  '/task/queryAssetGatherInfo',
  '/task/queryByResource',
  '/task/queryByMultiResource',
  '/task/queryShareInfoList',
  '/task/querySignIn',
  '/task/queryTaskList',
  '/task/triggerTask',
  '/task/receiveTask',
  '/task/queryShareInfo',
  '/help/submitAssist',
  '/task/finishShareTask',
  '/task/drawCustomTaskAward',
  '/task/noticeByToken',
  '/task/resourceExposure',
  '/help/queryHome',
  '/task/batchReward',
  '/help/queryGatherInfo',
  '/help/closeHint',
  '/manureHis',
  '/task/triggerSignIn',
  '/help/queryRankingList',
  '/help/queryRankHistory',
];

export const optionsHandler = (url: string, data: Record<string| number, any> = {}) => {
  let appendHeader = {};
  try {
    const path = url.includes('://') ? `/${parseUrl(url).path}` : url;
    if (needAppendHeaderApiList.includes(path)) {
      const requestId = data?.requestId ? data?.requestId : geneTaskRequestId();
      appendHeader = handleItraceHeaderPrams(requestId);
    }
    return {
      headers: appendHeader
    };
  } catch (error) {
    return {
      headers: appendHeader
    };
  }
}
