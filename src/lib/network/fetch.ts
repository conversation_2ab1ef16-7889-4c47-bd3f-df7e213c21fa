import qs from '@/lib/qs';
import { perfMarkPrefetch } from '@/lib/render-utils/perf';
import { getUserPageUuid, userPageUuid } from './itrace-utils';

const debug = qs.getParam('debug');

function request(options) {
  return new Promise((resolve, reject) => {
    let isDone = false;
    const timer: any = null;

    const callback = (ret: any) => {
      if (isDone) return;
      if (timer) clearTimeout(timer);
      isDone = true;

      if (options.type === 'text') {
        resolve(ret);
      } else if (ret && (ret.code === 'OK' || ret.code === 0 || ret.message === 'ok')) {
        let {data, ...rest} = ret;
        data = data || {};
        if (typeof data === 'object') {
          data.__meta = rest;
        }

        resolve(data);
      } else if (options.url.indexOf('wh_page_only=true') > -1) {
        // for 本地并行SSR调试使用。
        resolve(ret);
      } else {
        reject(ret);
      }
    };

    setTimeout(() => {
      if (isDone) return;
      isDone = true;
      reject({ success: false, code: 'network timeout' });
    }, options.timeout);

    const { url, ...args } = options;
    let deviceLevel: string | null = null;
    // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
    fetch(url, normalizeNetworkOptions(args))
      .then((data) => {
        deviceLevel = data?.headers?.get('deviceLevel');
        debug && console.log('[fetch] deviceLevel', deviceLevel);
        if (options.url.indexOf('/ssr/async-fetch') > -1) {
          const isAsyncPrefetch = data?.headers && data?.headers.get('x-pars-from') === 'prefetch'
          perfMarkPrefetch(isAsyncPrefetch);
        }
        return data.json();
      }).then((res) => {
        if (res?.data && deviceLevel) {
          res.data.deviceLevel = deviceLevel;
        }
        if (res?.data && options?.headers && Object.keys(options.headers ?? {}).length) {
          res.data.x_wpk_reqid = options?.headers?.x_wpk_reqid ?? '';
          res.data.x_wpk_traceid = options?.headers?.x_wpk_traceid ?? '';
          // 赋值userPageUuid
          !userPageUuid && getUserPageUuid(options?.headers?.x_wpk_traceid);
        }
        callback(res);
      }, reject);
  });
}

/**
 * 兼容 fetch 数据格式
 */
function normalizeNetworkOptions(options) {
  const { method, headers, body, ...others } = options;
  let bodyData = body;
  const contentType = headers && headers['Content-Type'] || '';
  if (method === 'POST' && body && (contentType === 'application/x-www-form-urlencoded' || contentType === '') && isObject(body)) {
    bodyData = qs.stringify(body);
  }
  return { method, headers, body: bodyData, ...others };
}

function isObject(arg) {
  return Object.prototype.toString.call(arg).indexOf('Object') !== -1;
}

export default request;
