export interface NetworkOption {
  type?: 'json' | 'text';
  method?: 'GET' | 'POST';
  timeout?: number;
  json?: boolean;
  url?: string;
  headers?: object & Record<string, any | undefined | null>; 
  body?: object;

  retry?: boolean;
  closeAppendTimestamp?: boolean;
}

export interface InitOptions {
  baseURL?: string;
  avgCategory?: number;
  errCategory?: number;
  sampleRate?: number;
  timeout?: number;
}
