
const store: {
  appear: unknown[];
  disappear: unknown[];
} = {
  appear: [],
  disappear: [],
};

type EventName = 'appear' | 'disappear';

function on(eventName: EventName, callback: () => void) {
  if (!store[eventName]) throw new TypeError(`not support ${eventName}`);
  if (typeof callback !== 'function') throw new TypeError('callback should be a function');
  if (store[eventName].indexOf(callback) > -1) return;

  store[eventName].push(callback);
}

function off(eventName: EventName, callback: () => void) {
  if (!store[eventName]) throw new TypeError(`not support ${eventName}`);

  const index = store[eventName].indexOf(callback);
  if (index > -1) store[eventName].splice(index, 1);
}

function initial() {
  const callback = eventName => () => {
    const targets = store[eventName];
    if (!targets) return;

    targets.forEach(fn => {
      try {
        fn();
      } catch (e) {
        console.error(e);
      }
    });
  };

  document.addEventListener('visibilitychange', () => {
    if (document.hidden === false) callback('appear')();
    else callback('disappear')();
  });
}

// 修复 Android 页面首次加载自动触发 appear 异常
setTimeout(initial, 0);

export default { on, off };
