import { getParam } from '@/lib/qs';
import { isNode } from 'universal-env';
import jssdk from '@ali/uc-toolkit/lib/jssdk';
import { isUc, isIOS } from "@/lib/universal-ua";
import { IBindThirdInfo } from "@/lib/ucapi/types";
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';

function safeObject(obj) {
  if (Object.prototype.toString.call(obj) === '[object Object]') return obj;
  try {
    obj = JSON.parse(obj);
  } catch (e) { }
  return obj;
}

export function getUserInfo() {
  return new Promise((resolve, reject) => {
    if (isNode) {
      const kps_wg = getParam('kps_wg')
        .replace(/(\<|\>|\&)/g, '');
      resolve({
        kps_wg,
      });
    } else if (window.ucapi) {
      const vCode = +new Date();
      window.ucapi.invoke('account.getUserInfo', {
        vCode,
        success: data => {
          data = safeObject(data);
          resolve({
            ...data,
            vCode,
          });
        },
        fail: data => {
          reject(safeObject(data));
        },
      });
    } else {
      resolve({})
    }
  });
}
export function exit() {
  if (isUc) {
    jssdk.invoke('biz.openPageUrl', { url: 'ext:back' });
    return;
  }
  history.back();
}


export function openPageUrl(options) {
  return jssdk.invoke('biz.openPageUrl', options);
}

/**
 * 打开新页面
 * @param url 链接
 */
export function openPage(url: string) {
  if (!url) {
    console.error('openPage error: params url is empty!');
    return;
  }

  if (isUc) {
    try {
      openPageUrl({ url }).catch(() => {
        window.location.href = url;
      });
    } catch (e) {
      window.location.href = url;
    }
  } else {
    window.location.href = url;
  }
}

export function miniwua(params) {
  return new Promise((resolve, reject) => {
    if (window?.ucapi) {
      window?.ucapi?.invoke('security.miniwua', {
        ...params,
        success: (result) => {
          resolve(result)
        },
        fail: (data) => {
          resolve(data)
        }
      })
    }
  })
  // return jssdk.invoke('security.miniwua', { ...params });
}

export function closeCurrentJSAPIBindWindow() {
  if (window?.ucapi) {
    window?.ucapi?.invoke('biz.closeCurrentJSAPIBindWindow', {
      success: (result) => {
        console.log(result);
      },
      fail: (data) => {
        console.log(data);
      }
    })
  }
}


export function shareEx(options) {
  return jssdk.invoke('biz.shareEx', options);
}

export function login() {
  return jssdk.invoke('account.openLoginWindow', { uiType: 'window' });
}

export function gestureDisabled() {
  return new Promise((resolve, reject) => {
    if (window?.ucapi) {
      window?.ucapi?.invoke('biz.gestureDisabled', {
        disabled: true, // true 代表关闭手势，false 代表默认行为
        success: (result) => {
          // console.log(result);
        },
        fail: (data) => {
          console.log(data);
        }
      })
    }
  });
}


export const spamSign = async (params: { text: string; salt: string }) => {
  const ret = await jssdk.invoke('spam.sign', params);
  return ret?.output_text || '';
};

export const ucparams = (params: { params: string }, isHttps = false) => {
  return jssdk.invoke('biz.ucparams', { ...params, isHttps });
};

export const postmessage = (params = {}) => {
  return jssdk.invoke('base.postmessage', params);
}

export const geneDomEl2Base64 = (nodeId: string, useCore = false, maxDuration = 2000) => {
  let waitTimer;
  return new Promise<{
    imgUrl: string;
    err?: any;
  }>((resolve, reject) => {
    const handleRes = (imgUrl: string, err?: any) => {
      if (waitTimer) {
        clearTimeout(waitTimer);
      }
      console.log('geneDomEl2Base64 >>', imgUrl);
      resolve({
        imgUrl,
        err,
      });
    };

    waitTimer = setTimeout(() => {
      console.log('geneDomEl2Base64 timeout ', maxDuration);
      handleRes('', new Error(`截图超时，设置 ${maxDuration}ms`));
    }, maxDuration);

    const params = {
      id: nodeId,
      common_mode: useCore ? 'false' : 'true',
    };
    // console.log('开始截图, params?', params);
    // 这里不能直接调 jssdk，估计是里面没实现相关方法？
    window.ucapi?.invoke('biz.screenShotAtDom', {
      ...params,
      success: (data) => {
        const imgUrl = data?.img_base64 ?? '';
        handleRes(imgUrl);
      },
      fail: (err) => {
        console.error('geneDomEl2Base64 fail!!', err);
        handleRes('', err);
      },
    });
  });
};

export const saveImg2Album = (params) => {
  const { data, format = 'base64' } = params;
  return new Promise<{
    succ: boolean;
    err?: any;
  }>((resolve) => {
    if (!data) {
      resolve({
        succ: false,
        err: new Error('未指定保存图片url'),
      });
      return;
    }

    window.ucapi?.invoke('biz.saveImageToPhotoAlbum', {
      // data: data.img_base64, // 图片url
      // format: 'base64',
      data,
      format,
      success(res) {
        // toast({ message: '保存相册成功' })
        console.log('保存相册结果 >> res', res);
        resolve({ succ: res.result === 'success', err: res });
      },
      fail(err) {
        console.warn('biz.saveImageToPhotoAlbum fail~!!!', err);
        resolve({ succ: false, err });
      },
    });
  });
};

export const shareDomShot = (options) => {
  console.log('分享截图，入参 >> ', options);
  return jssdk.invoke('biz.shareEx', {
    // type: 'img',
    success: (res) => {
      console.log('分享成功！！', res);
    },
    ...options,
  });
};

// 禁用长按工具栏
export function forbiddenToolbar() {
  return new Promise((resolve, reject) => {
    if (window?.ucapi) {
      window?.ucapi.invoke('webview.setConfig', {
        data: {
          enableLongClick: false
        }
      })
      // 对webCompassApp生效
      window?.ucapi.invoke('biz.webviewDisableLongClickWithoutEditType', {
        success: function (data) {
          console.log(data)
        },
        fail: function (data) {
          console.log(data)
        }
      })
    }
  })
}

// 三方账号相关，绑定淘宝
export const bindThirdPartyAccount = (type: 'taobao' | 'alipay', uccTrustPhone = 1) => {
  return jssdk.invoke('account.bindThirdPartyAccount', {
    type,
    webBusiness: 'farm',
    uccTrustPhone
  })
}
// 获取绑定信息
export const getBindInfo = (bindThirdType: 'taobao' | 'alipay', force = false): Promise<IBindThirdInfo> => {
  return jssdk.invoke('account.getBindUserInfo', {
    vCode: Date.now(),
    /** 同时获取绑定的第三方账号信息 */
    bindThirdType,
    /** 是否强制请求开放平台第三方账号绑定信息 */
    forceUpdate: force,
    doubleRelation: true, // 是否开启ucc双读关系
  });
}

export const openTaobaoLoginWindow = (params) => {
  const orderPlatformIds = isIOS ? '1003,1011,1005,1006,1001,1004,1000,1002' : '1003,1010,1006,1001,1004,1000,1002,1007'
  return jssdk.invoke('account.openLoginWindow', {
    loginCallback: "", // 接口返回的页端方法，等客户端登陆成功后会调用该方法告知页端登录成功
    loginType: isIOS ? "" : "guide_panel_new", // Android 10.9.0版本增加 调起登录窗口的类型： 无该字段：登录面板 或 登录引导窗口; "uc":UC账号; "taobao":淘宝账号; "qq":QQ账号; "sina"：新浪账号; "phone":手机号(手机号是10.9.8新增@wenbo.lwb) @xiaoyh; "wechat":微信账号 guide_panel":登录引导面板、 guide_panel_new: 新的半屏登录引导 panel @12.2.2
    uiType: "panel", // Android 11.0.5.841版本增加，uiType="panel"时打开登录面板，uiType="window"时打开登录引导窗口，默认为uiType="panel"  新增  uiType="detail"时对于已登陆用户打开用户详情页面(要求loginType、loginCallback都为空)",   11.8.8@顾辉;  新增uiType="guide_panel_minigame",用于打开小游戏定制登陆面板
    loginTip: '登录立享1分钱领1箱水果权益', // 顶部提示文案
    accountName: "", // Android 11.4.2 增加,账号，用于填充登录界面的账号(UC账号登录、手机号登录)
    webBusiness: "farm", // 可选：页端的业务标识。gold_hunter 赏金模式金币中心，后续可继续扩展 TODO@梁焯栩 基于11.8.2开发，上线后同步回来
    forceCurrentContext: "", // 可选：是否强制使用当前的Activity作为对应的Context，1： 是。0：否。默认0,
    agreeLicense: 0, // 是否自动勾选用户协议，16.1.9 版本开始支持
    orderPlatformIds, // 指定的登录方式排序，16.3.4 之后支持，Android对应id(第一位作为首推登录方式)： UC:1000; QQ:1001; 新浪:1002; 淘宝：1003; 支付宝:1004; 手机号:1005; 微信:1006; 小米:1007; 手机号一键登录:1010
    nightStyleLogin: 0, // 强制夜间模式，16.3.4 之后支持
    showLoginHistory: 0, // 是否展示登录历史，16.4.0 之后支持
    bottomLoginText: "或登录以下账号 绑定淘宝账号后享同等权益", // 底部登录方式说明文案，不传或空字符串则不显示，16.4.0 之后支持
    success: function (data) {
      console.log(data);
    },
    fail: function (data) {
      console.error(data);
    },
    ...params
  })
}
// 打开淘宝app
export const openTaobaoApp = (params) => {
  return jssdk.invoke('biz.openTaobao', { ...params });
}
// 禁用 iOS WebView 弹性动画
export function disableIOSBounces(nodeIds: string[]) {
  if (!isIOS || !isUc) return;

  const data = nodeIds.map((id) => ({
    nodeId: id, // 填写节点的id，如果是webview的根容器则为 web_root_scroll
    disable_bounces: true,
  }));

  return jssdk.invoke('biz.setWebScroller', {
    data,
  });
}

export const queryApp = (params) => {
  return jssdk.invoke('biz.queryApp', { ...params });
};

export const startApp = (scheme) => {
  return jssdk.invoke('biz.startApp', {
    pkg: scheme,
    data: '',
    appstoreURL: ''
  })
}

export const copyToClipboard = (params: { text: string }) => {
  return jssdk.invoke('base.copyToClipboard', { ...params, toast: '1' });
}

// 无网络判断
export const checkNoNetWork = async () => {
  /* { status: 1}// 取值 0:无网络 1:wifi 2:2G 3:3G 4:4G */
  const networkStatus = await jssdk.invoke('device.getNetworkStatus')
  return Number(networkStatus?.status) === 0
}

// 获取桌面组件安装信息
export function getInstallInfo(addtype, type, widgetReceiverName, extraParams: {taskInfo: TaskInfo | null; resource_location: string}): Promise<{ isInstalled: boolean }> {
  const { taskInfo, resource_location } = extraParams;
  console.log('widget.getInstallInfo', extraParams);
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('widget.getInstallInfo', {
        addType: addtype, // 添加类型，
        channel: 'guide', // 必须，来源，默认是 guide
        typeId: type, // 必须：类型 id，需要跟模板的ID一致
        widgetReceiverName, // 必须，找客户端同学获取，eg：com.uc.business.widget.dynamic.receiver.DynamicWidgetReceiver
        extra: {
          resource_location,
          task_id: taskInfo?.id || '',
          task_name: taskInfo?.name || '',
        },
        success: function (data) {
          resolve(data)
        },
        fail: function (data) {
          console.error(data);
        }
      })
    } else {
      resolve({ isInstalled: false })
    }
  })
}

// 通知客户端更新 桌面组件状态
export function notifyChanged(type, widgetReceiverName) {
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('widget.notifyChanged', {
        typeId: type, // 必须：样式 id，需要跟模板的ID一致
        widgetReceiverName, // 必须，找客户端同学获取，eg：com.uc.business.widget.dynamic.receiver.DynamicWidget1x1Receiver
        success: function (data) {
          resolve(data)
        },
        fail: function (data) {
          console.error(data);
          resolve(data)
        }
      })
    } else {
      resolve({})
    }
  })
}

// 添加桌面小组件
export function installWidget(addType, type, widgetReceiverName, extraParams: {taskInfo: TaskInfo; resource_location: string}): Promise<{data: any;fail?: boolean}> {
  const { taskInfo, resource_location } = extraParams;
  console.log('widget.install', extraParams);
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('widget.install', {
        addType: addType,
        channel: 'guide',
        typeId: type,
        forceNotShowGuide: true,
        widgetReceiverName,
        extra: {
          resource_location,
          task_id: taskInfo?.id || '',
          task_name: taskInfo?.name || '',
        },
        success: function (data) {
          console.info('添加桌面小组件==success', data);
          resolve({data: data})
        },
        fail: function (data) {
          console.error('添加桌面小组件==fail', data);
          resolve({
            fail: true,
            data,
          })
        }
      });
    } else {
      resolve({
        fail: true,
        data: {},
      })
    }
  })
}

// 创建任务
export function crateMission(params: { appId: string; id: string; name: string; event: string; target: number; scene: string; from: string; entry: string; requestId: string }): Promise<{ result: number | string }> {
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      const { appId, id, name, event, target, scene, from, entry, requestId } = params;
      window.ucapi.invoke('mission.createMission', {
        appId, // 业务方id
        id, // 任务id
        name, // 任务名称
        event, // 任务事件
        target, // 当前阶段任务的目标时长（毫秒）
        needSaveToLocal: false, // 是否需要保存到本地
        extra: { // 扩展字段
          scene, // 业务场景(search: 搜索; novel: 小说; iflow: 信息流)
          from, // 来源
          entry, // 入口
          traceID: requestId
        },
        success: function (data) {
          resolve(data)
        },
        fail: function (data) {
          reject(data)
        }
      });
    } else {
      resolve({
        result: ''
      })
    }
  })
}

// 通知客户端销毁 芭芭农场任务
export const notifyVisitBaBaFarm = async () => {
  return new Promise((resolve, reject) => {
    if (window?.ucapi) {
      window?.ucapi?.invoke('biz.notifyVisitBaBaFarm', {
        success: (result) => {
          resolve(result)
        },
        fail: (data) => {
          reject(data)
        }
      })
    }
  });
};

// 通知ios 桌面任务已经为完成
export const finishDesktopTask = async (appId: string) => {
  return new Promise((resolve, reject) => {
    if (window?.ucapi) {
      window?.ucapi?.invoke('widget.finishDesktopTask', {
        appId,
        success: (result) => {
          resolve(result)
        },
        fail: (data) => {
          reject(data)
        }
      })
    }
  });
};

// 获取cd数据
export const getCDParams = (key: string) => {
  return jssdk.invoke('biz.getCDParams', { key });
}

// 获取cms数据
export const getCMSParams = (key: string) => {
  return jssdk.invoke('biz.getCMSResource', { key, array: false, now_and_future: false });
}

export const queryRewards = (slotKey: string, appId: string, requestId: string) => {
  return jssdk.invoke('biz.queryRewards', {
    requestId,
    appId,
    enableAsyncQueryReward: true,
    rewarded_video_slot_key: slotKey
  })
}

// ios锁定横屏
export const lockOrientation = async () => {
  return jssdk.invoke('device.lockOrientation', {
    orientation: 1,
  });
}
/**
 * 获取广告接口
 * @param options
 * @returns
 */
export const loadNativeAd = (options: {
  appKey: string;
  aid: string;
  type: string;
  requestInfo?: Record<string, any>;
}) => {
  return new Promise((resolve: (data: {

    title: string;
    sid: string;
    assetId: string;
  }) => void, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.loadNativeAd', {
        ...options,
        success: (ret) => {
          if (ret.success) {
            resolve(ret);
          } else {
            reject(ret);
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    } else {
      reject(new Error('loadNativeAd window.ucpai not exist'));
    }
  });
}
/**
 *  获取RTA标签信息
 * @param options
 * @returns
 */
export const getNoahRtaTag = (options: {
  scene: string;
  rta_ids?: string;
}) => {
  return new Promise((resolve: (data: {
    success: string;
    show_order: string;
    target: string;
    category: string;
    price: string;
  }) => void, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.getNoahRtaTag', {
        ...options,
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('getNoahRtaTag window.ucapi not exist'));
    }
  });
}
/**
 * 展示广告通知接口
 * @param options
 * @returns
 */
export const onNativeAdShow = (options: {
  sid: string;
  aid: string;
  type: string;
}) => {
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.onNativeAdShow', {
        ...options,
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('onNativeAdShow window.ucapi not exist'));
    }
  });
}
/**
 * 点击并绑定任务通知接口
 * @param options
 * @returns
 */
export const clickNativeAdAndNotify = (options: {
  sid: string;
  type: string;
  aid: string;
  thirdid: string;
  channel: number;
}) => {
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.clickNativeAdAndNotify', {
        ...options,
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('clickNativeAdAndNotify window.ucapi not exist'));
    }
  });
}


/**
 * 东风检测接口
 * url: 检测的链接
 * action: 传expose则是曝光监测，click是点击监测
 */
export function dongfengMonitoring(url: string, action: 'expose' | 'click') {
  if (!url) {
    console.error('dongfeng.request error:  url is empty!');
    return;
  }
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('dongfeng.request', {
        url,
        action,
        type: "dongfeng",
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('dongfeng.request window.ucapi not exist'));
    }
  });
}

/**
 * 激励视频发奖成功回传
 */
export const consumeSuccessRewards = (options: {slotKey: string; rewardId: string; appId: string; requestId: string; businessCode?: string }) => {
  const { slotKey, rewardId, businessCode = 'uc_activity_ad', requestId, appId } = options;
  return jssdk.invoke('biz.consumeSuccessRewards', {
    rewarded_video_slot_key: slotKey, // 可选，如果type 是 noah 的时候需要传
    businessCode: businessCode, // 可选参数， uc_minigame_ad \ uc_novel_ad \ uc_activity_ad 业务类型 uc_minigame_ad ：小游戏 uc_novel_ad ：小说 uc_activity_ad : 通用活动，默认值是 uc_activity_ad
    reward_success_id: rewardId, // 激励视频领奖成功ID，由端发奖接口或者异步查奖接口回传，页端再透传给端
    requestId,
    appId, // 激励广告appId
  });
};

export const getIDFA = async () => {
  const result = await jssdk.invoke('biz.getIDFA', {});
  return result?.idfa ? result.idfa : '';
}

export const bizDecrypt = async (params: { text: string }) => {
  const result = await jssdk.invoke('biz.encryptOrDecrypt', { enc: 1, type: 0, data: { key: params.text } });
  return result?.key || '';
}

export const bizEncrypt = async (params: { text: string }) => {
  if (isIOS) {
    return (await jssdk.invoke('security.encrypt', params)).outputText;
  }
  return (await jssdk.invoke('biz.encryptOrDecrypt', { enc: 1, type: 1, data: { key: params.text } })).key;
}

/**
 * 获取个性化推荐开关
 */
export const getPersonalizedRecommendSwitch = () => {
  return jssdk.invoke('biz.getPersonalizedRecommendSwitch', {});
}
