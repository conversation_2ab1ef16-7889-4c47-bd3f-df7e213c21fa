import { isNode } from 'universal-env';

// 模块通用化：针对模块的一些方法调用，一些在SSR时不需要执行的代码，进行置空处理。（如：埋点/监控等）
export default function withUniversal<T>(
  mod: T,
  options?: {
    instead?: T;
  }
): T {
  if (!isNode) return mod;

  if (typeof options?.instead === typeof mod) return options?.instead as T;

  if (typeof mod === 'function') {
    let tmp: any = () => { }
    Object.keys(mod.prototype).forEach(func => {
      tmp.prototype[func] = () => { }
    })
    return tmp;
  }
  if (typeof mod === 'object') {
    const tmp: any = {};
    if (mod.__proto__) {
      Object.keys(mod.__proto__).forEach(func => {
        tmp[func] = () => { }
      });
    }
    Object.keys(mod).forEach(func => {
      tmp[func] = () => { }
    })
    return tmp;
  }
}
