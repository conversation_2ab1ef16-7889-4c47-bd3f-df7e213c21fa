export const enum renderAction {
  miniJsExecute = 'miniJsExecute',  // miniJS开始执行
  asyncSsrCacheOnScreen = 'asyncSsrCacheOnScreen',  // 并行SSR缓存上屏
  asyncSsrApiStart = 'asyncSsrApiStart',  // 并行SSR接口开始时间
  asyncSsrIsPrefetch = 'asyncSsrIsPrefetch',
  asyncSsrOnScreen = 'asyncSsrOnScreen',  // 并行SSR上屏
  asyncSsrDemote = 'asyncSsrDemote',  // 并行SSR上屏
  nodeFirstData = 'nodeFirstData', // node端首屏接口获取数据耗时
  bootstrapExecute = 'bootstrapExecute',  // bootstrap执行
  csrGotData = 'csrGotData',  // CSR获取到数据
  hydrate = 'hydrate',  // hydrate
  hydrated = 'hydrated',  // hydrate完成，根组件didmount中记录比较合适。
  t2 = 't2',  // t2时间，根据业务情况自行记录
}

interface PerfTime {
  documentType: string;
  documentStart?: number;
  [renderAction.miniJsExecute]?: number;
  [renderAction.asyncSsrCacheOnScreen]?: number;
  [renderAction.asyncSsrApiStart]?: number;
  [renderAction.asyncSsrIsPrefetch]?: '0' | '1';
  [renderAction.asyncSsrOnScreen]?: number;
  [renderAction.asyncSsrDemote]?: '0' | '1';
  [renderAction.nodeFirstData]?: number;
  [renderAction.bootstrapExecute]?: number;
  [renderAction.csrGotData]?: number;
  [renderAction.hydrate]?: number;
  [renderAction.hydrated]?: number;
  [renderAction.t2]?: number;
}

export const perfTime: PerfTime = {
  documentType: window?.__INITIAL_DATA__?.pageInitialProps?.__documentType ?? 'CSR_TPL',
  documentStart: Math.floor(window?.__documentStartTime || window?.performance?.now()),
};

// 标记性能
export function perfMark(key: renderAction, force = false) {
  switch (key) {
    case renderAction.asyncSsrDemote:
    case renderAction.asyncSsrIsPrefetch:
      return;
    default:
      if (!perfTime[key] || force) {
        perfTime[key] = Math.floor(window?.performance?.now());
      }
  }
}

// 标记SSR或并行SSR，Node端调用firstData消耗的时间。
export function perfMarkNodeFirstDataTime(duration = window?.__INITIAL_DATA__?.pageInitialProps?.__duration) {
  if (duration) {
    perfTime[renderAction.nodeFirstData] = duration;
  }
}

// 标记并行SSR是否降级
export function perfMarkAsyncSsrDemote(bool) {
  perfTime[renderAction.asyncSsrDemote] = bool ? '1' : '0';
}

// 标记并行SSR是否为数据预取
export function perfMarkPrefetch(bool) {
  perfTime[renderAction.asyncSsrIsPrefetch] = bool ? '1' : '0';
}

// 获取性能数据
export function getPerf() {
  console.log(perfTime)
  return perfTime;
}

const DocumentTypeMap = {
  CSR_TPL: 'CSR',
  CSR: 'CSR（带骨架）',
  SYNC_SSR: '串行SSR',
  ASYNC_SSR: '并行SSR',
}

// GUIDANCE: 获取啄木鸟上报使用的数据
// 建议业务标记T2后进行上报。
// 啄木鸟配置指引：

// 监控名称：渲染性能监控
// 均值指标：
// wl_avgv1: 主文档开始
// wl_avgv2: 最小JS执行
// wl_avgv3: 并行SSR缓存上屏
// wl_avgv4: 并行SSR请求开始
// wl_avgv5: 并行SSR上屏
// wl_avgv6: node首屏接口耗时
// wl_avgv7: bootstrap
// wl_avgv8: CSR获取到数据
// wl_avgv9: 开始hydrate
// wl_avgv10: hydrate完成
// wl_avgv11: t2
// 自定义指标：
// c1: 命中数据预取
// c2: 并行SSR降级
export function getPerfWpkLogParams() {
  const {
    documentType,
    documentStart: wl_avgv1,
    miniJsExecute: wl_avgv2,
    asyncSsrCacheOnScreen: wl_avgv3,
    asyncSsrApiStart: wl_avgv4,
    asyncSsrOnScreen: wl_avgv5,
    nodeFirstData: wl_avgv6,
    bootstrapExecute: wl_avgv7,
    csrGotData: wl_avgv8,
    hydrate: wl_avgv9,
    hydrated: wl_avgv10,
    t2: wl_avgv11,

    asyncSsrIsPrefetch: c1,
    asyncSsrDemote: c2,
  } = perfTime;

  return {
    msg: DocumentTypeMap[documentType],
    wl_avgv1,
    wl_avgv2,
    wl_avgv3,
    wl_avgv4,
    wl_avgv5,
    wl_avgv6,
    wl_avgv7,
    wl_avgv8,
    wl_avgv9,
    wl_avgv10,
    wl_avgv11,
    c1,
    c2,
  };
}
