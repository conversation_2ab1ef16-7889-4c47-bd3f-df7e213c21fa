import { LONG_DELAY, SHORT_DELAY } from '../utils/index';
import { ToastOption } from '../types';
import Driver from '@ali/pcom-driver';
import CoinIcon from './images/<EMAIL>';
import CashIcon from './images/<EMAIL>';

interface QueueOption {
  message: string;
  duration?: number;
  award: IAward;
  more: IAward;
  style?: {
    [key: string]: string;
  };
}

interface IAward {
  mark: string;
  icon?: string;
  name?: string;
  amount: number;
}

let queue: QueueOption[] = [];
let isProcessing = false;
let toastWin: HTMLElement;
let imgIconEle: HTMLElement;
let messageEle: HTMLElement;
let awardEle: HTMLElement;
let tipEle: HTMLElement;
let moreAwardEle: HTMLElement;
let lastToastType: string;

const styles = {
  container: {
    left: '50%',
    bottom: '50%',
    marginRight: '-50%',
    transform: 'translate(-50%)',
    position: 'fixed',
    zIndex: 9999,
    // display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.75)',
    boxSizing: 'border-box',
    maxWidth: '80%',
    color: '#ffffff',
    paddingTop: '22rpx',
    paddingBottom: '22rpx',
    paddingLeft: '32rpx',
    paddingRight: '32rpx',
    fontSize: '28rpx',
    fontWeight: '600',
    borderRadius: '16rpx',
    textAlign: 'center',
    transition: 'all 0.4s ease-in-out',
    webkitTransition: 'all 0.4s ease-in-out',
    display: 'none',
    backgroundImage: 'linear-gradient(180deg, rgba(0,0,0,0.3), rgba(0,0,0,0.6))',
  },
  icon: {
    width: '64rpx',
    height: '64rpx',
    marginRight: '10rpx',
  },
  tips: {
    color: '#FFF',
    fontSize: '24rpx',
  },
  awardContainer: {
    display: 'flex',
    'flex-direction': 'row',
    'align-items': 'center',
    marginTop: '28rpx',
    'padding-left': '32rpx',
    'padding-right': '32rpx',
    marginBottom: '8rpx',
  },
  awardMessage: {
    'font-size': '36rpx',
    color: '#FFBF00',
    'letter-spacing': '0',
  },
  award: {
    display: 'flex',
    'flex-direction': 'row',
  }
};

// function showToastWindow(message: string): void {
function showToastWindow(info: QueueOption): void {
  if (!toastWin) {
    toastWin = document.createElement('div');
    toastWin.setAttribute('role', 'alert');
    // support for ARIA, add tabindex for focus
    // https://developer.mozilla.org/zh-CN/docs/Web/HTML/Global_attributes/tabindex
    toastWin.setAttribute('tabindex', '-1');
    Driver.setStyle(toastWin, {
      ...styles.container,
      ...(info.style || {}),
    });
    document.body.appendChild(toastWin);
  }

  if (info.award) {
    Driver.setStyle(toastWin, { display: 'none' });

    if (lastToastType === '') {
      toastWin.textContent = '';
    }

    const isCash = info.award ? info.award?.mark?.indexOf('cash') > -1 : false;
    let awardIcon = isCash ? CashIcon : CoinIcon;
    // toastWin.innerHTML = `
    //   <img src="${awardIcon}" style="width: 190px; margin-top: -24px;" />
    //   <div style="margin-top: -10px;">${info.message}</div>
    //   <div style="color: #FF726B; margin-bottom: 10px;">+${awardAmount}${awardName}</div>
    // `;

    if (info.more) {
      awardIcon = CashIcon;
    }

    if (!awardEle) {
      awardEle = document.createElement('div');
      Driver.setStyle(awardEle, styles.awardContainer);
      imgIconEle = document.createElement('img');
      Driver.setStyle(imgIconEle, styles.icon);
      imgIconEle.setAttribute('src', awardIcon);
      messageEle = document.createElement('div');
      Driver.setStyle(messageEle, styles.awardMessage);
      messageEle.textContent = info.message;
      awardEle.appendChild(imgIconEle)
      awardEle.appendChild(messageEle)
    }
    if (!tipEle) {
      tipEle = document.createElement('div');
      Driver.setStyle(tipEle, styles.tips);
      tipEle.textContent = '竞猜金币仅用于投注不参与排行';
    }

    toastWin.appendChild(awardEle);
    toastWin.appendChild(tipEle);

    if (info.more) {
      if (!moreAwardEle) {
        moreAwardEle = document.createElement('div');
        Driver.setStyle(moreAwardEle, styles.award);
      }
      const isCash = info.more ? info.more?.mark?.indexOf('cash') > -1 : false;
      const moreAwardAmount = isCash ? info.more?.amount / 100 : info.more?.amount || 0;
      const moreAwardName = isCash ? '元' : '元宝';
      moreAwardEle.textContent = moreAwardAmount ? `+${moreAwardAmount}${moreAwardName}` : '';
      toastWin.appendChild(moreAwardEle);
    }

    lastToastType = 'award';
    Driver.setStyle(toastWin, {
      display: 'block',
      borderRadius: '40rpx',
      padding: '0 0 40rpx 0',
      boxShadow: '0 40rpx 80rpx 0 rgba(0,0,0,0.25)',
      transform: 'translate(-50%,-50%)',
      webkitTransform: 'translate(-50%,-50%)'
    });
  } else {
    if (lastToastType === 'award') {
      toastWin.innerHTML = '';
    }

    toastWin.textContent = info.message;
    lastToastType = '';
    Driver.setStyle(toastWin, { display: 'block', borderRadius: '16rpx', padding: '22rpx 32rpx', transform: 'translate(-50%,-50%)', webkitTransform: 'translate(-50%,-50%)' });
  }
  // toastWin.textContent = message;
  // Driver.setStyle(toastWin, { transform: 'translate(-50%,-50%)', webkitTransform: 'translate(-50%,-50%)' });
}

function hideToastWindow(): void {
  setTimeout((): void => {
    if (toastWin && toastWin.style) {
      Driver.setStyle(toastWin, {
        transform: 'translate(-50%,-50%) scale(0.8)',
        webkitTransform: 'translate(-50%,-50%) scale(0.8)',
      });
    }
  }, 0);
}

const innerToast = {
  hideTimer: null,
  show() {
    // All messages had been toasted already, so remove the toast window,
    if (!queue.length) {
      if (toastWin) {
        // eslint-disable-next-line
        (toastWin as any).parentNode.removeChild(toastWin);
      }
      (toastWin as any) = null;
      return;
    }

    // the previous toast is not ended yet.
    if (isProcessing) {
      // @ts-ignore
      clearTimeout(innerToast.hideTimer);
    }
    isProcessing = true;

    const toastInfo: QueueOption = queue.shift() as QueueOption;
    // showToastWindow(toastInfo.message);
    showToastWindow(toastInfo);
    // @ts-ignore
    innerToast.hideTimer = setTimeout(() => innerToast.switchToNext(), toastInfo.duration);
  },
  // push(message: string, duration: number): void {
  //   queue.push({
  //     message,
  //     duration,
  //   });
  //   innerToast.show();
  // },
  push(message: string, options: any): void {
    queue.push({
      message,
      ...options,
    });
    innerToast.show();
  },
  // Switch to next message
  // This function will hide current, and call `show()` to display next
  // If queue is empty, DOM will be clear in `show()`
  switchToNext() {
    hideToastWindow();
    isProcessing = false;
    setTimeout(() => innerToast.show(), 500);
    if (innerToast.hideTimer) {
      clearTimeout(innerToast.hideTimer);
      innerToast.hideTimer = null;
    }
  },
};


const Toast: ToastOption = {
  SHORT: SHORT_DELAY,
  LONG: LONG_DELAY,

  /*
   * @param message {String}
   * @param duration {Number}
   * @param userStyle {Object} user defined style
   */
  // show(message: string, duration: number = SHORT_DELAY): void {
  //   innerToast.push(message, duration);
  // },
  show(message: string, options: QueueOption): void {
    options = { duration: this.SHORT, ...options };
    innerToast.push(message, options);
  },

  hide() {
    // remove all queued messages
    queue = [];
    innerToast.switchToNext();
  },
};

export default Toast;
