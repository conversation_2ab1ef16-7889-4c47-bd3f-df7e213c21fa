interface StyleCss {
  [key: string]: React.CSSProperties;
}

const ModalStyle: StyleCss = {
  modal_wrap: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  modal_mask: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    opacity: 0,
  },
  modal_body: {
    transform: 'scale(0.8)',
    opacity: 0,
  },
};

export default ModalStyle;
