import React, { cloneElement, Component, MouseEvent, TouchEvent } from 'react';
import isValidElement from './helpers/isValidElement';
import transition from 'universal-transition';
import Lottie from 'lottie-web';
import modal from './index';
import './index.scss'
import {
  DEFAULT_ACTIVE_TIME_OPTION,
  MASK_ENTER_ANIMATION_STYLE,
  MASK_LEAVE_ANIMATION_STYLE,
  BODY_LEAVE_ANIMATION_STYLE,
  WEB_BODY_ANIMATION_OPTIONS,
} from './constants';
import ModalStyle from './style';
import { BaseModalProps, BaseModalState, ModalDataInterface } from './typings';

let closeDelay = 0;

class BaseModal extends Component<BaseModalProps, BaseModalState> {
  static defaultProps: Partial<BaseModalProps> = {
    isShowMask: true,
    isMaskCanClose: true,
    maskClose: async () => {},
    bodyEnterAnimaitonOptions: [],
    bodyLeaveAnimationOptions: [],
    disableEnterAnimation: false,
    showBodyLeaveAnimation: false,
    isShowStar: false,
    disableContainerPointerEvent: false,
  };

  private maskDom: HTMLDivElement | null = null;
  private bodyDom: HTMLDivElement | null = null;
  private resetTimer: NodeJS.Timeout | null = null;

  constructor(props) {
    super(props);
    this.state = {
      data: props,
      show: false,
    };
  }

  componentDidMount() {
    this.bindEvent();
  }

  componentWillUnmount() {
    this.unbindEvent();
    if (this.resetTimer) clearTimeout(this.resetTimer);
  }

  bindEvent() {
    (this.context.modal || modal).eventEmitter.on('open', this.handleOpen);
    (this.context.modal || modal).eventEmitter.on('close', this.handleClose);
  }

  unbindEvent() {
    (this.context.modal || modal).eventEmitter.off('open', this.handleOpen);
    (this.context.modal || modal).eventEmitter.off('close', this.handleClose);
  }

  async maskEnterAnimation() {
    const { maskDom } = this;
    await transition(maskDom, MASK_ENTER_ANIMATION_STYLE, DEFAULT_ACTIVE_TIME_OPTION);
  }

  handleOpen = async (id: string, data?: ModalDataInterface) => {
    const modalDatas = { ...this.props, ...(data || {}) };
    const { bodyEnterAnimaitonOptions } = this.state.data;
    if (id === this.props.id && !this.state.show) {
      this.setState({ show: true, data: modalDatas }, async () => {
        if (
          this.state.data.disableEnterAnimation
        ) {
          this.setOpenStyle(modalDatas);
        } else {
          this.maskEnterAnimation();
          await this.bodyEnterAnimation();
          if (this.props.isShowStar) this.isStar()
          // 避免动画失效，弹窗无法显示
          let timeWaitting = 500;
          if (bodyEnterAnimaitonOptions && bodyEnterAnimaitonOptions.length) {
            timeWaitting = bodyEnterAnimaitonOptions.reduce((preTime, option) => {
              const { delay = 0, duration = 0 } = option.options;
              return preTime + delay + duration;
            }, 0);
          }
          this.resetTimer = setTimeout(() => {
            this.setOpenStyle(modalDatas);
          }, timeWaitting);
        }
      });
    }
  };

  setOpenStyle(modalDatas) {
    this.setState({
      data: {
        maskStyle: { ...ModalStyle.modal_mask, opacity: 1 },
        bodyStyle: {
          transform: 'scale(1) translate3d(0, 0, 0) rotate(0deg)',
          opacity: 1,
        },
        ...modalDatas,
      },
    });
  }
  // 星星动画
  isStar = () => {
    Lottie.loadAnimation({
      name: 'star',
      container: document.getElementById('star-lottie') as HTMLElement,
      renderer: 'canvas',
      loop: false,
      autoplay: true,
      animationData: require('@/lib/animation/star.json'),
      assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202404/c98e12/images/',
    });
  }
  async bodyEnterAnimation() {
    const { bodyEnterAnimaitonOptions } = this.state.data;
    const { bodyDom } = this;

    if (bodyEnterAnimaitonOptions && bodyEnterAnimaitonOptions.length) {
      for (let i = 0; i < bodyEnterAnimaitonOptions.length; i++) {
        const { style, options } = bodyEnterAnimaitonOptions[i];
        closeDelay = options.duration || 0;
        await transition(bodyDom, style, options);
      }
      return;
    }

    const BodyEnterAnimation = WEB_BODY_ANIMATION_OPTIONS;

    for (let i = 0; i < BodyEnterAnimation.length; i++) {
      const { style, options } = BodyEnterAnimation[0];
      closeDelay = options.duration;
      await transition(bodyDom, style, options);
    }
  }
  // 关闭蒙层
  closeShowMask = () => {
    const data = this.state.data
    this.setState({show: true, data: {...data, isShowMask: false}})
  }
  handleClose = async (id: string, closeCb: () => void) => {
    if (this.resetTimer) clearTimeout(this.resetTimer);
    if (id === this.props.id) {
      if (this.state.data.showBodyLeaveAnimation) {
        await this.bodyLeaveAnimation(this.state.data);
      }
      const delay = this.state.data.showBodyLeaveAnimation ? closeDelay : 0;
      setTimeout(() => {
        this.setState({ show: false }, () => {
          closeCb();
          if (this.state.data.afterClose) this.state.data.afterClose();
        });
      }, delay);
    }
  };

  async bodyLeaveAnimation(data: ModalDataInterface) {
    // const { maskDom } = this;
    const { bodyDom } = this;
    const { bodyLeaveAnimationOptions } = data;

    // transition(maskDom, MASK_LEAVE_ANIMATION_STYLE, DEFAULT_ACTIVE_TIME_OPTION);
    if (bodyLeaveAnimationOptions && bodyLeaveAnimationOptions.length) {
      for (let i = 0; i < bodyLeaveAnimationOptions.length; i++) {
        const { style, options } = bodyLeaveAnimationOptions[i];
        await transition(bodyDom, style, options);
      }
    }
    // await transition(bodyDom, BODY_LEAVE_ANIMATION_STYLE, DEFAULT_ACTIVE_TIME_OPTION);
  }

  render() {
    if (!this.state.show) return null;

    const { isShowMask, isShowStar, disableContainerPointerEvent } = this.state.data;
    return (
      <div style={{...this.getStyle('container'), pointerEvents: disableContainerPointerEvent ? 'none' : 'auto'}} key="base-modal">
        {isShowMask && (
          <div
            key="mask"
            className="wrc-modal-mask"
            onClick={this.maskClick}
            style={{...this.getStyle('mask'), pointerEvents: 'inherit'}}
            ref={(ref) => {
              this.maskDom = ref;
            }}
            onTouchMove={this.onMaskTouchMove}
          />
        )}
        <div id="color-bar-lottie" />
        {/* <div id="star-lottie" /> */}
         {isShowStar && <div id="star-lottie" />}
        <div id="clap-lottie" style={this.ifShowClapLottie()} />
        <div
          key="modal-body"
          className="wrc-modal-body"
          style={this.getStyle('body')}
          ref={(ref) => {
            this.bodyDom = ref;
          }}
          onClick={this.handleStopEv}
        >
          {React.Children.map(this.props.children, (child, idx) => {
            if (isValidElement(child)) {
              return cloneElement(child as any, { id: this.props.id, key: idx, ...this.state.data, closeShowMask: this.closeShowMask });
            }
          })}
        </div>
      </div>
    );
  }
  ifShowClapLottie = () => {
    const phoneHeight = document.documentElement.clientHeight || document.body.clientHeight;
    const isShow = phoneHeight > 690 ? 'bolck' : 'none';
    return {
      display: isShow
    };
  };

  maskClick = async (event) => {
    const { isMaskCanClose, maskClose } = this.state.data;
    if (!isMaskCanClose) return;
    if (maskClose) await maskClose();
    (this.context.modal || modal).close(this.props.id, this.state.data);
  };

  getStyle(mode: 'container' | 'mask' | 'body') {
    const { modalContainerStyle, maskStyle, bodyStyle } = this.state.data;
    let style = {};

    switch (mode) {
      case 'container':
        style = ModalStyle.modal_wrap;
        if (modalContainerStyle && Object.keys(modalContainerStyle).length) {
          console.log('modalContainerStyle', modalContainerStyle);
          style = { ...ModalStyle.modal_wrap, ...modalContainerStyle};
        }
        break;
      case 'mask':
        style = ModalStyle.modal_mask;
        if (maskStyle && Object.keys(maskStyle).length) style = { ...ModalStyle.modal_mask, ...maskStyle };
        break;
      case 'body':
        style = ModalStyle.modal_body;
        if (bodyStyle && Object.keys(bodyStyle).length) style = { ...ModalStyle.modal_body, ...bodyStyle };
        break;
      default:
        break;
    }

    return style;
  }

  handleStopEv = (event: MouseEvent<any>) => {
    event.stopPropagation();
  };

  onMaskTouchMove = (e: TouchEvent<HTMLDivElement>) => {
    if (this.props.preventMaskMove) {
      e.preventDefault();
    }
  };
}

export default BaseModal;
