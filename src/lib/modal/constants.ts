const MASK_ENTER_ANIMATION_STYLE = { opacity: 1 };
const MASK_LEAVE_ANIMATION_STYLE = { opacity: 0 };
const DEFAULT_ACTIVE_TIME_OPTION = { timingFunction: 'ease', duration: 200 };
const BODY_ANIMATION_OPTIONS = [
  { style: { transform: 'scale(1.07)', opacity: 1 }, options: DEFAULT_ACTIVE_TIME_OPTION },
  { style: { transform: 'scale(1)', opacity: 1 }, options: DEFAULT_ACTIVE_TIME_OPTION },
];

// Web需要一次完成动画，否则可能出现transition不生效问题
const WEB_DEFAULT_ACTIVE_TIME_OPTION = { timingFunction: 'cubic-bezier(.25,.1,.36,1.95)', duration: 400 };
const WEB_BODY_ANIMATION_OPTIONS = [
  { style: { transform: 'scale(1)', opacity: 1 }, options: WEB_DEFAULT_ACTIVE_TIME_OPTION },
];

const BODY_LEAVE_ANIMATION_STYLE = { transform: 'scale(0.8)', opacity: 0 };

export {
  MASK_ENTER_ANIMATION_STYLE,
  MASK_LEAVE_ANIMATION_STYLE,
  DEFAULT_ACTIVE_TIME_OPTION,
  BODY_ANIMATION_OPTIONS,
  BODY_LEAVE_ANIMATION_STYLE,
  WEB_DEFAULT_ACTIVE_TIME_OPTION,
  WEB_BODY_ANIMATION_OPTIONS,
};
