interface TimeOption {
  timingFunction: string;
  duration: number;
  delay: number;
}

interface AnimationOptions {
  style: React.CSSProperties;
  options: Partial<TimeOption>;
}

export interface BaseModalProps {
  id: string;
  afterClose?: () => void;
  modalContainerStyle?: React.CSSProperties;
  maskStyle?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
  isShowMask?: boolean;
  isMaskCanClose?: boolean;
  preventMaskMove?: boolean;
  maskClose?: Function;
  bodyEnterAnimaitonOptions?: AnimationOptions[];
  bodyLeaveAnimationOptions?: AnimationOptions[];
  disableEnterAnimation?: boolean;
  showBodyLeaveAnimation?: boolean;
  disableContainerPointerEvent?: boolean;
  children?: any;
  isShowStar?: boolean;
}

type BaseModalPropsExcludeId = Pick<BaseModalProps, Exclude<keyof BaseModalProps, 'id'>>;

export interface ModalDataInterface extends BaseModalPropsExcludeId {
  [k: string]: any;
}

export interface BaseModalState {
  data: ModalDataInterface;
  show: boolean;
}
