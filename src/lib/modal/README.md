# 模态对话框。

## 如何使用

先在页面声明一个modal，然后通过调用open来打开弹窗；

假设自动弹出一系列的弹窗的过程中，不希望被其他弹窗中断过程，可以通过加锁来实现；

### 普通使用

``` js
  import View from 'rax-view';
  import BaseModal from '@ali/weex-rax-components/lib/base_modal/modal';
  import Modal from '@ali/weex-rax-components/lib/base_modal/index';

  componentDidMount() {
    Modal.open('modal', {
      afterClose: () => console.log('close')
    });
  }

  <BaseModal id="modal">
    <View>这是个弹窗</View>
  </BaseModal>
```

### 加锁的例子
``` js
  import View from 'rax-view';
  import BaseModal from '@ali/weex-rax-components/lib/base_modal/modal';
  import Modal from '@ali/weex-rax-components/lib/base_modal/index';

  componentDidMount() {
    const MODAL_LOCK_KEY = 'modal_lock_key';

    Modal.lock(MODAL_LOCK_KEY).then(() => {
      Modal.open('modal', {
        afterClose: () => Modal.release(MODAL_LOCK_KEY)
      }, MODAL_LOCK_KEY);
    })
  }

  <BaseModal id="modal">
    <View>这是个弹窗</View>
  </BaseModal>

```

## Component API

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| id | 弹窗的唯一ID | string | '' |
| modalContainerStyle | 弹窗容器的样式 | object | {} |
| maskStyle | 蒙层的样式 | object | {} |
| bodyStyle | Modal body 的样式 | object | {} |
| isShowMask | 是否展示蒙层 | boolean | true |
| isMaskCanClose | 点击蒙层是否触发关闭 | boolean | true |
| bodyEnterAnimaitonOptions | Modal body的进场动画配置 | array | [] |
| bodyLeaveAnimationOptions | Modal body的退场动画配置 | array | [] |
| afterClose | Modal 完全关闭后的回调 | function | 无 |

### Modal body 进退场动画的具体配置

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| style | 动画样式 | object | {} |
| options | transition的相关配置 [transition](http://rax.alibaba-inc.com/docs/api/transition) | object | {} |

注：基础弹窗带有默认的动画样式以及进退场过渡效果

## Method API

### Modal.open（打开弹窗）

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| id | 弹窗唯一ID | string | 无 |
| data | 需要透传给弹窗子组件的参数 | object | {} |
| key | 加锁key | string | 无 |

注：可以将 component api 作为data参数下传到子组件中

### Modal.close（关闭弹窗）

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| id | 弹窗唯一ID | string | 无 |

### Modal.lock（加锁）

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| key | 锁的唯一值 | string | 无 |

### Modal.release（解锁）

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| key | 需要解锁的唯一值 | string | 无 |
