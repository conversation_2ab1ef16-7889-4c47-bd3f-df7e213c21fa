import { ModalDataInterface } from './typings';
import { Eventer } from './helpers/eventer';
import { DialogProbity, MODAL_ID } from '@/components/modals/types';


interface ModalControlInterface {
  id: MODAL_ID;
  data: any;
  key?: MODAL_ID;
}
class ModalControl {
  eventEmitter;

  id: string;

  protected currentOpenModalObj: Map<MODAL_ID, ModalControlInterface> = new Map();
  private cacheOpenModalArr: ModalControlInterface[] = [];
  private lockQueue: any[] = [];

  private currentLock: any = null;
  private lockToRelease: any = null;

  constructor(id = 'unknown') {
    this.id = id;
    this.eventEmitter = new Eventer();
  }

  // 弹窗优先级处理
  pushCacheModal(data: ModalControlInterface) {
    if (!data) {
      return;
    }
    // 弹窗存在的时候，不再添加
    if (this.cacheOpenModalArr.find(item => item.id === data.id)) {
      return;
    }
    const newModalPriority = DialogProbity[data.id] || 2;
    // 找到第一个优先级小于新弹窗的位置，这样可以保证高优先级在前面
    const insertIndex = this.cacheOpenModalArr.findIndex(existingModal => {
      const existingPriority = DialogProbity[existingModal.id] || 2;
      return existingPriority < newModalPriority;
    });
    if (insertIndex === -1) {
      // 没有找到优先级小于新弹窗的，说明新弹窗优先级最低或相等，放到最后
      this.cacheOpenModalArr.push(data);
    } else {
      // 在找到的位置插入新弹窗，保证高优先级在前面
      this.cacheOpenModalArr.splice(insertIndex, 0, data);
    }
  }

  open(id: MODAL_ID | string, data?: ModalDataInterface, key?: MODAL_ID) {
    if (document) {
      document.body.style.overflow = 'hidden';
    }
    const isLock = this.currentLock && this.currentLock?.key !== key;
    const isOpen = Object.keys(this.currentOpenModalObj).length > 0;

    if (isLock || isOpen) {
      // this.cacheOpenModalArr.push({ id, data, key });
      this.pushCacheModal({
        id: id as MODAL_ID,
        data: data ?? {},
        key: (key ?? id) as MODAL_ID
      });
    } else {
      this.currentOpenModalObj[id] = { id, data, key };
      this.eventEmitter.emit('open', id, data);
    }
  }

  removeCacheModal(id?: string) {
    if (id) {
      const targetModalIndex = this.cacheOpenModalArr.findIndex((modal) => modal.id === id);
      if (targetModalIndex > -1) {
        this.cacheOpenModalArr.splice(targetModalIndex, 1);
      }
    } else {
      this.cacheOpenModalArr = [];
    }
  }

  close(id: string) {
    if (document) {
      document.body.style.overflow = 'auto';
    }
    if (!this.currentOpenModalObj[id]) return;

    const openParam = this.currentOpenModalObj[id];
    delete this.currentOpenModalObj[id];

    this.eventEmitter.emit('close', id, () => {
      if (this.lockToRelease && this.lockToRelease.key === openParam.key && this.shouldRelease(openParam.key)) {
        this.lockToRelease = null;
        this.doRelease();
      } else {
        this.openNextModal();
      }
    });
  }

  lock(key: string) {
    const promise = new Promise((resolve) => this.lockQueue.push({ key, __resolve: resolve }));
    setTimeout(() => this.nextLock(), 0);

    return promise;
  }

  release(key: string) {
    if (this.currentLock && this.currentLock.key === key) {
      if (this.shouldRelease(key)) {
        this.doRelease();
      } else {
        this.lockToRelease = this.currentLock;
      }
    } else {
      console.error(`窗口解锁错误：当前锁key:${this.currentLock ? this.currentLock.key : '无'}，调用解锁的key：${key}`);
    }
  }

  private openNextModal() {
    if (!this.cacheOpenModalArr.length) {
      return;
    }
    // 优先寻找当前锁的弹窗
    const modalData = this.currentLock
      ? this.findNextModalByKey(this.currentLock.key, true)
      : this.cacheOpenModalArr.shift();

    if (modalData) {
      this.open(modalData.id, modalData.data, modalData.key);
    }
  }

  private nextLock() {
    if (!this.currentLock && this.lockQueue.length) {
      this.currentLock = this.lockQueue.shift() as any;
      this.currentLock.__resolve();
      return true;
    }
    return false;
  }

  private shouldRelease(key) {
    let modalNotClosed = false;
    Object.keys(this.currentOpenModalObj).forEach((modalId) => {
      const tmp = this.currentOpenModalObj[modalId];
      if (tmp.key === key) modalNotClosed = true;
    });

    const modalNotOpenYet = !!this.findNextModalByKey(key);
    return !(modalNotClosed || modalNotOpenYet);
  }

  private findNextModalByKey(key: MODAL_ID, toDelete = false) {
    if (!key) return false;

    const next = this.cacheOpenModalArr.filter((item) => item.key === key)[0];
    if (!next) return null;

    if (toDelete) {
      const index = this.cacheOpenModalArr.indexOf(next);
      this.cacheOpenModalArr.splice(index, 1);
    }

    return next;
  }

  private doRelease() {
    this.currentLock = null;
    const hasNext = this.nextLock();
    if (!hasNext && Object.keys(this.currentOpenModalObj).length === 0) {
      this.openNextModal();
    }
  }

  // eslint-disable-next-line @typescript-eslint/member-ordering
  getCurrentOpenModalObj() {
    return this.currentOpenModalObj;
  }
}

const Modal = new ModalControl();

export default Modal;
export { ModalControl };
