export enum LocalStorageKey {
  /** 激励广告类型任务开始离开页面的时间 */
  InvokeStartTime = 'invokeStartTime',
  requestId = 'requestId',

  DAILY_VISITE_COUNT = 'dailyVisitCount',

  /** 任务完成出套完弹窗KEY */
  PROGRESS_BUBBLE = 'from_task_widget',

  /** 气泡任务完成弹窗KEY */
  RESOURCE_NICHE = 'resource_niche',

  /* 调起广告但未领奖的任务id */
  INVOKED_TASK_ID = 'invoked_task_id',

  /** 限时任务额外池列表KEY */
  LIMIT_EXTRA_TASK_LIST = 'limit_extra_task_list',

  /**
   * 完成任务来源
   * list --- 任务列表
   * taskBubble --- 气泡组件
   * permanent_ad --- 首页常驻激励广告资源位
   */
  FINISH_TASK_FROM = 'finish_task_from',

  /**
   * 存储当前任务ID匹配的account ID
   */
  TASK_BRAND_BY_ACCOUNT_ID = 'task_brand_by_account_id',


  /**
   * 限时任务素材请求
   */
  LIMIT_RESOURCE_REQUEST = 'limit_resource_request',
  /**
   * 翻倍卡宣传弹窗出现 次数&时间
   */
  DOUBLE_CARD_PUBLICITY = 'double_card_publicity',
  /**
   * 翻倍卡 当日未抽卡（前一日有奖励）状态 出现次数
   */
  DOUBLE_CARD_NOT_DRAW_COUNT = 'double_card_not_draw_count',
  /**
   * 领取翻倍卡奖励提示
   */
  DOUBLE_CARD_AWARD_TIP = 'double_card_award_tip',
  /**
   * 膨胀卡直送 次数&时间
   */
  DOUBLE_CARD_DIRECT_SEND = 'double_card_direct_send',
  /**
   * 膨胀卡次日领奖感知展示时间
   */
  DOUBLE_CARD_YESTERDAY_AWARD = 'double_card_yesterday_award',

}
