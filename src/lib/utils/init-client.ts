import config from '@/config';
import tracker from '@/lib/tracker';
import withUniversal from '@/lib/render-utils/with-universal';
import stat from '@/lib/stat';
import { getParam } from '@/lib/qs';
import { IFactConfig } from '@/config/fact';
import ItraceFluency from '@ali/itrace-fluency';
import ItraceInterface from '@ali/itrace-interface'
import { isIOS } from '@/lib/universal-ua';

// TODO: 配置啄木鸟 & 配置埋点基本信息
export const inittialUserId = () => {
  const KEY = '__wpkuid__';
  const n = localStorage.getItem(KEY);
  if (n) {
    return n;
  }
  function s4() {
    // tslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  const mockUid = `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
  localStorage.setItem(KEY, mockUid);
  return mockUid;
}

function initClient(factConfig: IFactConfig) {
  console.log('PUBLISH_VERSION', PUBLISH_VERSION)
  tracker.init({
    bid: config.WPK_BID,
    sampleRate: 1,
    rel: PUBLISH_VERSION,
    uid: () => inittialUserId(),
    debug: false,
    plugins: [
      'flow', // 没有特殊配置时，直接指定插件id即可
      'perf',
      'resource',
      {
        id: 'interface',
        plugin: ItraceInterface,
        enableMtop: false,
        forceEnable: true,
        withReqHeader: true,
        withRespHeader: true,
        enableCorsTrace(url) {
          // 跨域的API请求，仅对开启农场服务开启
          const APIHost = [config.farmHost, config.taskHost, config.coralHost];
          return APIHost.some((host) => url.includes(host));
        }
      },
      {
        id: 'fluency',
        plugin: ItraceFluency,
        sampleRate: 1, // 采样率，默认为 1，同时控制响应延迟和长任务,
        inputDelayOpt: { // 响应延迟的配置
          enable: true, // 是否启用，默认为 true
          sampleRate: 1, // 响应延迟采样率，不设置时，用上面一个
          minThreshold: 100, // 响应延迟时间的最小阈值，即：超过此阈值才上报，默认 100ms
          maxThreshold: 60000 // 响应延迟时间的最大阈值，即：低于此阈值才上报，默认 60000ms
        },
        longTaskOpt: {// 长任务的配置
          enable: true, // 是否启用，默认为 true
          sampleRate: 1, // 响应延迟采样率，不设置时，用上面一个

          minThreshold: 150, // 长任务时间的最小阈值，即：超过此阈值才上报，默认 50ms
          maxThreshold: 60000 // 长任务时间的最大阈值，即：低于此阈值才上报，默认 60000ms
        },
        scrollBlockOpt: {// 滚动、滑动卡顿监控配置
          enable: false, // 是否启用
        },
        zoomBlockOpt: {
          enable: false, // 是否启用
        }
      },
      'blank',
    ],
    beforeSend: (logData: {type: string; w_res: string; w_type: number }) => {
      if (['inputdelay', 'h5block', 'longtask', 'perf'].includes(logData.type)) {
        logData['c1'] = window?.__lp_disable;
      }

      if (logData?.type === 'api') {
        // IOS 存在一个API请求重复上报:fetch/xhr各一条, xhr 不上报
        if (isIOS && logData?.w_type === 16 && logData?.w_res) {
          const resUrl = logData?.w_res ?? '';
          const disAllowedHosts = [
            config.farmHost,
            config.taskHost,
            config.cmsUrl,
            config.coralHost,
            config.shareHost,
            config.HC_AD_API_URL,
            config.HC_TASK_API
          ];
          return !disAllowedHosts.some((host) => resUrl.includes(host));
        }

        // 树相关的素材资源忽略日志上报, 这部分应该属于资源不属于API
        if (logData?.w_res) {
          const resUrl = logData?.w_res ?? '';
          const disAllowedURL = ['https://g.alicdn.com', 'https://image.uc.cn', 'https://huichuan.sm.cn', 'https://broccoli-static.uc.cn'];
          return !disAllowedURL.some((host) => resUrl.includes(host));
        }

        return true;
      }
      return true;
    }
  });


  const statOption = {
    entry: getParam('entry') || '',
    page: factConfig.page,
    page_h5: factConfig.page_h5,
    from: getParam('from') ?? 'unknown',
  };


  Object.assign(statOption, {
    spma: factConfig.a,
    spmb: factConfig.b,
    ev_ct: factConfig.ev_ct,
    ev_sub: factConfig.ev_sub,
    page_status: 0,
  });

  stat.init(statOption);
}

export default withUniversal(initClient);
