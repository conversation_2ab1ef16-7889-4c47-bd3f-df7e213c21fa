/**
 * 自定义分隔位数格式化
 * @param num 
 * @param groupSize 
 * @returns 
 */
export const customSeparator = (num, groupSize = 3) => {
  const numStr = Number(num).toString();
  const [integer, decimal] = numStr.split('.');
  const sign = integer.startsWith('-') ? '-' : '';
  const cleanInt = integer.replace('-', '');
  
  const regex = new RegExp(`\\B(?=(\\d{${groupSize}})+(?!\\d))`, 'g');
  return sign + cleanInt.replace(regex, ',') + (decimal ? `.${decimal}` : '');
}


export const formatRewardNum = (rewardAmount: number) => {
  const units = [
    {value: 10000, unit: '万'},
    {value: 1000, unit: '千'},
  ]
  const formatAmount = rewardAmount;
  for (const {value, unit} of units) {
    if (formatAmount >= value) {
      const result = (formatAmount / value).toFixed(1).replace(/\.0$/, '');
      return result + unit
    }
  }
  // 返回原数据
  return formatAmount;
}

/**
 * 获取数字的长度
 * @param num 
 * @returns 
 */
export const getNumberLength = (num: number) => {
  if (!num) {
    return 0
  }    
  const str = String(num / 100);  
  const len = str.replace(/[^0-9]/g, '').length;  
  return len;
}

// 单位换算: 分 -> 元
export function convertCentsToYuan(cents: number) {
  const yuan = cents / 100;
  return yuan % 1 === 0 ? `${yuan.toFixed(0)}` : `${yuan.toFixed(1)}`;
}

// 单位换算: 肥料
export function convertCentsToPoint(cents: number, takeUnit = true) {
  const wan = cents / 10000;
  return wan % 1 === 0 ? `${wan.toFixed(0)}${takeUnit ? '万' : ''}` : `${wan.toFixed(1)}${takeUnit ? '万' : ''}`;
}

export function convertRanking(cents: number) {
  if (cents >= 10000) {
    return '1万+'
  }
  return cents;
}

/** 单位换算: 肥料(千单位) */
export function convertToPoint(cents: number, unit = 1000) {
  const thousand = cents / unit;
  return thousand % 1 === 0 ? thousand.toFixed(0) : thousand.toFixed(1);
}
