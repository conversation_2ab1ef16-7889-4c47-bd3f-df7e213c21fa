/**
 * 激励广告相关处理函数文件
 */

import { TaskInfo } from "@/pages/index/components/TaskPop/TaskList/types";
import { consumeSuccessRewards } from "@/lib/ucapi";
import tracker from "@/lib/tracker";
import { isIOS } from "@/lib/universal-ua";
import { getExtraInfo } from "@/logic/store/models/task/helper";
import mx from '@ali/pcom-mx';

/**
 * 通知客户端激励广告发奖成功
 * @param task
 * @param options
 */
export const notifyAdAwardSuccess = async (task: TaskInfo, options: {slotKey: string; rewardId: string; appId: string; requestId: string; businessCode?: string }, source: string, requestId = '',) => {
  try {
    const res = await consumeSuccessRewards({...options});
    console.log('res======consumeSuccessRewards', res);
    if (res?.success?.toString() === '1' || res?.success?.toString() === 'true') {
      tracker.log({
        category: 154,
        sampleRate: 1,
        msg: `${options?.slotKey}-成功`,
        w_succ: 1,
        c1: `${task.id}`,
        c2: options?.slotKey,
        c3: options?.rewardId,
        c4: `${task?.name}`,
        c5: `${source}`,
        bl1: JSON.stringify(task),
        bl2: JSON.stringify(options),
        bl3: JSON.stringify(res)
      })
      // dispatch.task.finishTask(task?.id, 'complete', requestId, true);
    } else {
      tracker.log({
        category: 154,
        sampleRate: 1,
        msg: `${options?.slotKey}-失败`,
        w_succ: 0,
        c1: `${task.id}`,
        c2: options?.slotKey,
        c3: options?.rewardId,
        c4: `${task?.name}`,
        c5: `${source}`,
        bl1: JSON.stringify(task),
        bl2: JSON.stringify(options),
        bl3: JSON.stringify(res)
      })
    }
  } catch (error) {
    console.log('error======consumeSuccessRewards', error);
    tracker.log({
      category: 154,
      sampleRate: 1,
      msg: `${options?.slotKey}-失败-catch`,
      w_succ: 0,
      c1: `${task.id}`,
      c2: options?.slotKey,
      c3: options?.rewardId,
      c4: `${task?.name}`,
      c5: `${source}`,
      bl1: JSON.stringify(task),
      bl2: JSON.stringify(options),
      bl3: JSON.stringify(error)
    })
  }
};

/**
 * 获取激励广告slot数据
 * @param task 
 * @returns 
 */
export const getIncentiveAdSlotData = (task: TaskInfo) => {
  const clientType = mx.store.getStore()?.app?.pr; 
  const isLite = clientType === 'UCLite';
  const {
    iosSlotKey = '',
    androidSlotKey = '',
    androidAppId = '',
    iosAppId = '',
    androidCheckAdFill = false, 
    iosCheckAdFill = false,
  } = isLite ? getExtraInfo(task)?.ucLiteAdConfig || {} : getExtraInfo(task)?.ucAdConfig || {};

  return {
    slotKey: isIOS ? iosSlotKey : androidSlotKey,
    appId: isIOS ? iosAppId : androidAppId,
    checkAdFill: isIOS ? iosCheckAdFill : androidCheckAdFill,
  }
}
