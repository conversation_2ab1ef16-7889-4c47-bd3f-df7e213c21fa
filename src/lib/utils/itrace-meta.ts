import { getUserPageUuid, userPageUuid } from '@/lib/network/itrace-utils';


/**
 * 设置itrace 相关能力字段
 */
export const setItraceDocumentMeta = (key: '__lp_disable' | '__itracecfg__', value: string) => {
  let metaViewport;
  switch (key) {
    case '__lp_disable':
      window['__lp_disable'] = value;
      metaViewport = document.querySelector('meta[name="wpk-c2"]');
      if (metaViewport) {
        metaViewport.setAttribute('content', value ?? 'unknown');
      }
      break;
    case '__itracecfg__':
      window['__itracecfg__'] = { wv_traceid: value};
      metaViewport = document.querySelector('meta[name="wpk-wv_traceid"]');
      if (metaViewport) {
        metaViewport.setAttribute('content', value);
      }
      break;
    default:
      break;
  }
}

/**
 * 设置页面pageUuid
 */
export const setItracePageUuid = (value: string | null | undefined) => {
  const currentUserPageUuid = userPageUuid.length > 0 ? userPageUuid : (value ?? getUserPageUuid());
  !userPageUuid && getUserPageUuid(currentUserPageUuid);
  setItraceDocumentMeta('__itracecfg__', currentUserPageUuid);
}
