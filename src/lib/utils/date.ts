export function isSameDay(st1, st2) {
  const d1 = new Date();
  const d2 = new Date();
  d1.setTime(st1);
  d2.setTime(st2);

  function getFullDate(d: Date) {
    return [d.getFullYear(), d.getMonth(), d.getDate()].join('');
  }

  return getFullDate(d1) === getFullDate(d2);
}

/**
 * 格式化毫秒时间戳
 *
 * @param {String, Number} timestamp (单位为豪秒)
 * @param {String} format (格式)
 *    format='YYYY-MM-DD'
 *    format='MM/DD hh:mm'
 * @param {boolean} buling 日期不足两位，是否需要前置补零
 *    默认 true
 * @returns {String} default return YYYY/MM/DD hh:mm:ss
 */
export function formatTimestamp(timestamp, format = 'YYYY/MM/DD hh:mm:ss', buling = true): string {
  const time = Number.parseInt(timestamp, 10);
  const date = new Date(time);

  const year: any = date.getFullYear();
  let month: any = date.getMonth() + 1;
  let day: any = date.getDate();
  let hour: any = date.getHours();
  let minute: any = date.getMinutes();
  let second: any = date.getSeconds();

  month = month > 9 ? month : `${buling ? '0' : ''}${month}`;
  day = day > 9 ? day : `${buling ? '0' : ''}${day}`;
  hour = hour > 9 ? hour : `${buling ? '0' : ''}${hour}`;
  minute = minute > 9 ? minute : `${buling ? '0' : ''}${minute}`;
  second = second > 9 ? second : `${buling ? '0' : ''}${second}`;

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('hh', hour)
    .replace('mm', minute)
    .replace('ss', second);
}


export function calculateDaysBetweenDates(beginTime: number, endTime: number) {
  // 确保beginTime和endTime都是Date对象
  let oneDay = 24 * 60 * 60 * 1000; // 一天的时间毫秒数
  let start = new Date(beginTime).getTime(); // 将开始时间转换为毫秒
  let end = new Date(endTime).getTime(); // 将结束时间转换为毫秒

  // 计算时间差并转换为天数
  let days = Math.ceil(Math.abs((end - start) / oneDay));
  return days;
}

/**
 * @param timestamp 时间戳
 * @returns 月.日的格式数据
 */
export function uniqueDateFormat(timestamp: number) {
  const dateObj = new Date(timestamp);
  const month = dateObj.getMonth() + 1; // 月份从0开始，需加1
  const day = dateObj.getDate();
  // 直接返回"月份.日"的字符串表示，或按你的要求转换为浮点数（尽管不推荐）
  return `${month}.${day}`
}

export function formatLeftTime(leftTime: number) {
  const leftHour = Math.floor(leftTime / (3600 * 1000))
  const leftMin = Math.floor((leftTime - (leftHour * 3600 * 1000)) / (60 * 1000))
  const leftSecond = Math.floor(leftTime % (60 * 1000) / 1000)
  return {
    hour: leftHour > 9 ? leftHour : `0${leftHour}`,
    min: leftMin > 9 ? leftMin : `0${leftMin}`,
    second: leftSecond > 9 ? leftSecond : `0${leftSecond}`
  }
}
