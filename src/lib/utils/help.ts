type ElementPlus = Element & {
  $currentObserver?: IntersectionObserver;
};

export const unbindObserver = (element: ElementPlus) => {
  element.$currentObserver?.unobserve(element);
  element.$currentObserver?.disconnect();
};

export const bindObserver = (
  element: ElementPlus,
  inCallback: () => void,
  outCallback?: () => void,
  options?: IntersectionObserverInit,
) => {
  const observer = new IntersectionObserver((entries: IntersectionObserverEntry[]) => {
    if (entries.length > 0) {
      const entry = entries[entries.length - 1];
      if (entry) {
        const isInElement = entry.isIntersecting;
        isInElement ? inCallback() : outCallback?.();
      }
    }
  }, options);

  observer.observe(element);
  const ele = element;
  ele.$currentObserver = observer;
};
