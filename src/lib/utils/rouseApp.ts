import config from '@/config'
import wormholeData from "@/lib/wormhole-data";
import mx from '@ali/pcom-mx';
import { isIOS } from '../universal-ua';
import { handleCallApp } from '@/pages/index/components/TaskPop/TaskList/help';

const link = wormholeData?.page?.['10010']?.link || {};

export const taobaoScheme = 'tbopen://m.taobao.com/tbopen/index.html?action=ali.open.nav&module=h5&bootImage=0&source=dp&bc_fl_src=uc_nc_zsrw'

const CALL_TAOBAO_CONFIG_MAP = {
  UCMobile: {
    ios: {
      pgkName: 'com.ucweb.iphone.lowversion',
      backURL: 'uclink://www.uc.cn/f11f7cb5d16fa0969d670c1eccc6053d'
    },
    android: {
      pgkName: 'com.UCMobile',
      backURL: 'uclink://www.uc.cn/cc77796ca7c25dff9607d31b29effc07'
    },
  },
  UCLite: {
    ios: {
      pgkName: 'com.ucweb.iphone.pro',
      backURL: 'ucliteioslink://www.uc.cn/8c09be3332aa631fcbad0c5834ef68fd'
    },
    android: {
      pgkName: 'com.ucmobile.lite',
      backURL: 'uclink://www.uc.cn/19b64348381e629f44f43b8506f24e92'
    },
  },
}

export const getTaobaoUrlParams = () => {
  const pr = mx.store.getStore().app?.pr || 'UCMobile';
  const fr = isIOS ? 'ios' : 'android';
  const urlObj = CALL_TAOBAO_CONFIG_MAP[pr][fr];
  return {
    packageName: urlObj?.pgkName,
    backURL: urlObj?.backURL,
  }
}

/**
 * 跳端是否需要追加backURL&packageName
 */
export const handleJumpAppURL = (data: {
  scheme: string;
  packageName: string;
}) => {
  const { scheme, packageName} = data;
  let jumpAppScheme = scheme;
  // 淘宝默认需要,对应scheme | pkg:  tbopen:// | taobao:// | com.taobao.taobao
  if (scheme.includes('tbopen') || scheme.includes('taobao') || packageName.includes('taobao')) {
    const urlParamsObj = getTaobaoUrlParams();
    jumpAppScheme = `${jumpAppScheme}&packageName=${packageName}&backURL=${encodeURIComponent(
      urlParamsObj?.backURL,
    )}`
  }
  return jumpAppScheme;
}

// 换端淘宝
export async function TaobaoRouseApp(link_url, position: string) {
  const frontData = mx.store.get('app.mainInfo.frontData');
  const downloadTaobaoUrl = frontData?.downloadTaobaoUrl || config?.downloadTaobaoUrl;
  const tbScheme = `${taobaoScheme}&h5Url=${encodeURIComponent(link_url)}`;
  const params = {
    url: downloadTaobaoUrl,
    extra: JSON.stringify({pkgName: 'com.taobao.taobao', scheme: tbScheme})
  }
  handleCallApp(params, position);
}

