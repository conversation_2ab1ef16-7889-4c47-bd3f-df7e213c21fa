// 存储localStorage
export const localStorageSet = (name, data, time) => {
  const obj = {
    data,
    expire: new Date().getTime() + time
  };
  localStorage.setItem(name, JSON.stringify(obj));
}

// 获取localStorage
export const localStorageGet = (name) => {
  const storage = localStorage.getItem(name);
  const time = new Date().getTime();
  let result = null;
  if (storage) {
    const obj = JSON.parse(storage);
    if (time < obj.expire) {
      result = obj.data;
    } else {
      localStorage.removeItem(name);
    }
  }
  return result;
}

// 获取现在到今晚12点的时间
export const getSecondsSinceMidnight = () => {
  // 当前日期
  let now: any = new Date();
  // 当前日期的晚上0点
  let midnight: any = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
  // 晚上0点减去当前时间，得到时间差
  let diff = midnight - now;
  return Math.floor(diff);
}


export function setLocalStorageWithExpiry(key, value, timestamp?: number) {
  const now = new Date(timestamp || new Date());
  // 设置当天23:59:59的时间戳
  const expiryTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59).getTime();
  const item = {
    value,
    expiry: expiryTime,
  };
  localStorage.setItem(key, JSON.stringify(item)); // 存储数据和过期时间
}

export function getLocalStorageWithExpiry(key, timestamp?: number) {
  const itemStr = localStorage.getItem(key);
  if (!itemStr) {
    return null;
  }
  const item = JSON.parse(itemStr);
  const now = timestamp || new Date().getTime();

  // 检查当前时间是否已超过过期时间
  if (now > item.expiry) {
    localStorage.removeItem(key); // 如果已过期，从localStorage中移除
    return null;
  }
  return item.value;
}
