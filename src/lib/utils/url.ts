/**
 * @file 实现几个可能常用的方法
 */

export function completeURL(url: string) {
  try {
    return new URL(url);
  } catch (error) {
    throw new Error('Invalid URL');
  }
}

/**
 * 获取链接参数对象
 * @param url url
 * @returns Record<string, any>
 */
export const getSearchParamObject = <T = Record<string, any>>(url: string) => {
  const u = completeURL(url);
  const searchParams = new URLSearchParams(u.search);
  const queryParamsObj = {};

  searchParams.forEach((value, key) => {
    queryParamsObj[key] = value;
  });
  return queryParamsObj as T;
};

/**
 * 获取链接某个参数
 * @param key 
 * @param url 
 * @returns string
 */
export const getSearchParam = <T = string>(key: string, url: string) => {
  const queryParamsObj = getSearchParamObject(url);
  return queryParamsObj.get(key) as T | null;
};

/**
 * 追加链接参数
 * @param params 
 * @param url 
 * @returns string
 */
export const appendSearchParam = (params: Record<string, any>, url: string = location.href) => {
  const u = completeURL(url);
  const searchParams = u.searchParams;
  Object.keys(params).forEach((key: string) => {
    searchParams.set(key, params[key]);
  });
  return u.toString();
};

export const getSearchParamBatch = <T extends string>(
  paramList: readonly T[],
  url: string = location.href,
): Record<T, string> => {
  const u = completeURL(url);
  const searchParams = u.searchParams;
  const obj = {};
  paramList.forEach((key) => {
    Object.assign(obj, { key: searchParams.get(key) });
  });
  return obj as Record<T, string>;
};

/**
 * 给当前页面url追加参数
 * @param paramName 
 * @param paramValue 
 */

export const addUrlParameter = (paramName, paramValue) => {
  let url = completeURL(window.location.href);
  url.searchParams.append(paramName, paramValue);
  window.history.replaceState({}, '', url);
}

/**
 * 移除当前页面url的指定参数
 * @param params
 */
export const removeUrlParams = (params) => {
  const url = completeURL(window.location.href);  
  if (url.searchParams.has(params)) {
    url.searchParams.delete(params);    
    window.history.replaceState({}, '', url);
  }
};
