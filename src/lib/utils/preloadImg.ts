/**
 * @title 预加载图片
 * @param imgs 需要进行预加载的图片
 * @param onLoaded 全部图片加载完成的回调（可选）
 */
export const preloadImg = (imgs: string | string[], onLoaded?: () => void) => {
  const imgList = typeof imgs === 'string' ? [imgs] : imgs;

  if (!imgs.length) return;

  for (let index = 0; index < imgList.length; index++) {
    const img = new Image();
    img.src = imgList[index];

    if (index === imgList.length - 1) {
      if (onLoaded && typeof onLoaded === 'function') {
        img.onload = onLoaded;
      }
    }
  }
};


/**
 * @title 预加载资源
 * @param url 资源地址
 * 
 */
export const preloadFileWithFetch = (url: string) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response);
      } else {
        reject(new Error('Failed to load file'));
      }
    };
    xhr.onerror = reject;
    xhr.send();
  });
}
