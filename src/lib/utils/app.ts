import {isIOS} from "@/lib/universal-ua";
import { queryApp} from "@/lib/ucapi";
import tracker from "../tracker";
import mx from '@ali/pcom-mx';
import fact from '@/lib/stat';

// 检查是否安装 app
export const checkInstallApp = async (scheme, pkgName) => {
  const installMonitor = tracker.Monitor(140);
  const appPkg = isIOS ? scheme : pkgName;

  const currentAppInstallMap = mx.store.get('app.appInstallMap');
  const currentResult = currentAppInstallMap.get(appPkg);
  // 已经检测安装过了,安装过的没必要二次检测安装，未安装需要二次
  if ((currentResult !== undefined || currentResult !== null) && currentResult?.installed) {
    fact.custom('installed_app_detect', {
      is_installed: currentResult?.installed ? '1' : '0',
      app_name: appPkg,
      is_ios: isIOS ? '1' : '0',
      event_id: '19999'
    });
    installMonitor.success({
      msg: `【${appPkg}】查询成功-缓存`,
      c1: currentResult?.installed ? '已安装' : '未安装',
      c2: appPkg,
      bl1: JSON.stringify(currentResult?.res)
    })
    // console.log('store【checkInstallApp】scheme: ', currentResult?.installed, currentResult?.res)
    return [currentResult?.installed, currentResult?.res];
  }

  try {
    const res = await queryApp({
      cache_first: '0',
      pkgs: [appPkg],
    });
    const appInstallInfo = (res || {})[appPkg];
    const isInstall = isIOS ? Boolean(appInstallInfo.appName) : (appInstallInfo?.appSize > 0 || appInstallInfo?.canOpen);

    fact.custom('installed_app_detect', {
      is_installed: isInstall ? '1' : '0',
      app_name: appPkg,
      is_ios: isIOS ? '1' : '0',
      event_id: '19999'
    });
    installMonitor && installMonitor.success({
      msg: `【${pkgName}】查询成功`,
      c1: isInstall ? '已安装' : '未安装',
      c2: `${pkgName}`,
      bl1: JSON.stringify(res),
      bl2: `${scheme}`
    })

    currentAppInstallMap.set(appPkg, {
      installed: isInstall,
      res
    });
    mx.store.update('app.appInstallMap', currentAppInstallMap);

    return [isInstall, res];
  } catch (err) {
    // console.error('[AD] queryApp fail', err, installMonitor);
    installMonitor && installMonitor.fail({
      msg: '查询失败-jsapi异常',
      c2: `${pkgName}`,
      bl1: JSON.stringify(err),
      bl2: `${scheme}`
    })
  }
  return [false, {}]
};
