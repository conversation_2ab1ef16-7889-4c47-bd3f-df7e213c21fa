export const disabledPinchToZoom = () => {
  // 禁用双指放大
  document.documentElement.addEventListener(
    'touchstart', (event) => {
      if (event.touches?.length > 1) {
        console.log('event.touches:', event.touches)
        event.preventDefault();
      }
    }, {
      passive: false,
    }
  );
};

export const landscapeBan = () => {
  const banLandscapeOrientationStyle = document.createElement('style');
  const banLandscapeOrientationMask = document.createElement('div');
  const style = `
      .banLandscapeOrientationMask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #1a1a1a;
        display: none;
        z-index: 999;
      }
      .banLandscapeOrientationMask .landscape-box {
        position:absolute;
        top:50%;
        left:50%;
        color: #9aa29b;
        font-size: 15px;
        white-space: nowrap;
        -webkit-transform:translate(-50%,-50%);
      }
      .banLandscapeOrientationMask .landscape-icon {
        display:inline-block;
        width:49px;
        height:46px;
        background:url("//image.uc.cn/s/uae/g/01/fit/icon_rotate.png") no-repeat;
        background-size:49px 46px;
      }
      .banLandscapeOrientationMask p {
        margin-left:10px;
        display:inline-block;
        vertical-align: 76%;
      }
      @media all and (orientation: landscape) {
        .banLandscapeOrientationMask {
          -webkit-box-orient: horizontal;
          -webkit-box-pack: center;
          -webkit-box-align: center;
        }
      }
    `;

  banLandscapeOrientationStyle.innerHTML = style;
  banLandscapeOrientationMask.className = 'banLandscapeOrientationMask';
  banLandscapeOrientationMask.innerHTML = '<div class="landscape-box"><div class="landscape-icon"></div><p>竖屏观看获得最佳体验！</p></div>'; 

  document.body.appendChild(banLandscapeOrientationStyle);
  document.body.appendChild(banLandscapeOrientationMask);

  window.addEventListener('orientationchange', () => {
    banLandscapeOrientationMask.style.display = window.orientation === 90 || window.orientation === -90
      ? '-webkit-box'
      : 'none';
  });
}
