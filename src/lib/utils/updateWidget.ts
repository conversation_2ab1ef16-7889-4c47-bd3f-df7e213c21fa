import { getInstallInfo, notifyChanged, installWidget } from '@/lib/ucapi/index';
import { appVersion, isLatestVersion, isUc} from '@/lib/universal-ua';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';

export const widget = {
  typeId: 'widget_babafarm_2x2',
  addtype: 'widget_babafarm_2x2_add',
  widgetReceiverName: 'com.uc.business.widget.babafarm.DeskBaBaFarmReceiver2x2',
};

// 更新小组件状态
export async function updateWidget({ typeId, addtype, widgetReceiverName }) {
  if (isUc && isLatestVersion(appVersion, '16.1.8')) {
    getInstallInfo(addtype, typeId, widgetReceiverName, {taskInfo: null, resource_location: 'refresh' }).then((res: { isInstalled: boolean }) => {
      if (res?.isInstalled) notifyChanged(typeId, widgetReceiverName);
    });
  }
}

// 判断小组件是否安装
export async function whetherWidget({ typeId, addtype, widgetReceiverName }, extraParams: {taskInfo: TaskInfo; resource_location: string }) {
  return await getInstallInfo(addtype, typeId, widgetReceiverName, extraParams);
}

// 安装小组件
export async function installDesktopWidget({ typeId, addtype, widgetReceiverName }, extraParams: {taskInfo: TaskInfo; resource_location: string }) {
  return await installWidget(addtype, typeId, widgetReceiverName, extraParams);
}
