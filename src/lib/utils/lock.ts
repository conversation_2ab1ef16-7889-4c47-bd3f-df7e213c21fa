const lockMap = {};
const lockTimer = {};

export function getLock(key: string): boolean {
  return lockMap[key] !== undefined ? lockMap[key] : false;
}

export function lock(key: string, time?: number) {
  lockMap[key] = true;
  if (time && time > 0 && !lockTimer[key]) {
    lockTimer[key] = setTimeout(() => {
      unlock(key);
    }, time);
  }
}

export function unlock(key: string) {
  lockMap[key] = false;
  clearTimeout(lockTimer[key]);
  lockTimer[key] = false;
}

export async function execWithLock(key: string, func: (unlock) => void, time?: number) {
  if (getLock(key)) {
    return;
  }
  lock(key, time);
  try {
    await func(() => { unlock(key); });
  } finally {
    if (!time) {
      unlock(key);
    }
  }
}

export enum LOCK_KEY {
  // USER_REGISTER = 'USER_REGISTER',
}
