# 激励视频广告处理

> 相关jsapi

[预加载广告 biz.loadRewardVideoAd](https://jas.alibaba-inc.com/jsapi/5e01f7314c572c0117202ed4)

[播放广告 biz.showRewardVideoAd](https://jas.alibaba-inc.com/jsapi/5e01ee73ecb2070105813f20)

## Getting Started

1. 封装了啄木鸟自定义监控，需要定义三个监控项———预加载、视频调起播放、播放结果
2. 必须提供兜底的穿山甲广告 aid
3. 支持聚合 sdk 调用的客户端，如有配置聚合 sdk aid，默认走聚合方式，否则使用旧方式

```ts
import adPlayer from '@ali/weex-toolkit/lib/adPlayer';

/** 初始化设置 */
adPlayer.setup({
  /** 非聚合方式广告 aid ，必填 */
  backupAid: {
    ios: '',
    android: '',
  },
  /** 非聚合方式用的类型, 默认 tt(穿山甲)，可选 hc（汇川） */
  backupTypeMap: {
    ios: 'tt', // tt | hc
    android: 'tt', // tt | hc
  },
  /** 聚合sdk 的aid，建议传入 */
  mixedAidMap: {
    ios: '',
    android: '',
  },
  // 具体的监控项id，必填，切页面需使用相同的 tracker 实例  
  wpkCategory: {
    preload: 101,
    invoke: 102,
    play: 103,
  },
  /** 可选参数，对应声浪活动 */
  businessCode: 'uc_activity_ad',
  // 调用间隔，默认3000，可选
  invokeInterval: 3000,
});

/** 可重复调用，会针对key完整替换 */
adPlayer.setup({
  // 内置了聚合 sdk 可支持使用的版本，android: '13.1.2' ios 13.1.6
  mixedAidMap: {
    android: '13.1.2',
    ios: '13.1.4',
  }
})

adPlayer.preload();

interface ICallBackParams {
  success: boolean;
  msg: string;
  params?: Record<string, string>;
}

adPlayer.play(playCallBack: (playRes: ICallBackParams) => {
  // 处理播放完成的场景，一般会执行任务的完成接口
  if (playRes.success) {
    consooe.log('视频播放完成~')
  }

}, invokeCallBack?: (invokeRes: ICallBackParams) => {
  // 处理视频调起失败场景，可判断处理 toast
  if (!invokeRes.success) {
    console.log('调用失败');
  }
})
```

## 版本支持

视频广告播放方式分两种
1. 聚合sdk 模式，对接声浪策略系统，支持多个广告源。传入参数 type='mixed'
2. 单一广告源模式，只需要传入 type 和 aid，仅支持指定的广告源。
### 穿山甲广告

Android>= 12.8.0

iOS >= 13.0.7
> ios 在 13.0.7 版本有bug，直接调用 biz.showRewardVideoAd 没有响应，需要先执行 `minigame.loadRewardVideoAd` 打开开关后才可执行;
> 此 bug在 13.0.8 版本修复

### 汇川广告

由于历史原因，汇川广告入参有一些特殊，需要传入一个 json 字符串，包含 key(slot_id, wid, model_name)
其中 slot_id 即广告id
wid 会在播放完成后以参数形式传入回调方法，adPlayer 做了校验逻辑
model_name 可传入一个不为空的字符串即可

目前已经做了兼容处理，传入的 bakcupType 为 hc 时自动判断补齐 json 字符串
**使用方仅需要传入广告id（一串数字）即可。**

### 聚合sdk(对接声浪系统)

Android >= 13.1.2

IOS >= 13.1.4

> 安卓的 13.1.1 有部分子版本执行有 bug 调用无响应

> IOS 13.1.4 之前的版本有回调数据不正常问题；另 13.1.4 没有对 cd 开关做兼容，需要业务侧处理

## 客户端已知问题

* IOS@13.1.5 版本对聚合 sdk cd开关关闭的情况下，会直接读取参数的 `backupType` 和 `backupAid`，需要业务侧配合;
* IOS@13.1.6 之前版本存在对视频播放状态处理不正确问题————可以理解为没正确释放锁，导致有偏高的 `requesting already` 报错
  * 此问题在 13.1.5 加剧，会导致正常调用也失败
* 截止20201110，安卓和IOS端存在同一个问题【广点通超过30秒广告，30秒倒计时提示可领奖励，实际上需要看完才能领取】
  * 此问题在 13.1.6 版本同步修复，20201116 正式发布
