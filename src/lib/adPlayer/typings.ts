export interface IRewardInfoItem {
  /** 广告来源id */
  adn_id: string;
  /** 广告产品ID */
  app_id: string;
  /** 广告pid */
  pid: string;
  /** 广告实际价格 */
  price: string;
  /** 广告返回的广告Search ID */
  sid: string;
  /** 广告位ID */
  slot_id: string;
}

export interface IRewardVideoPlayRes {
  /** 广告位id */
  aid: string;
  /** 广告类型 */
  ad_type: string;
  /** 广告源 */
  ad_sdk: string;
  /** 播放是否完成 */
  is_ended: boolean;
  ad_action: number;
  error?: string;
  reward_type?: number; // tanx奖励类型，0: 浏览  1: 下单  2: 浏览+下单
  reward_success_id?: string; // 奖成功标识符，需要媒体在用户发奖成功后回传给sdk
  reward_info_list?: IRewardInfoItem[];
}