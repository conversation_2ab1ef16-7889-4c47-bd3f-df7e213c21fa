import tracker from '../tracker';
import { isAndroid, isIOS, isLatestVersion, appVersion } from '../universal-ua';
import { isWeex } from 'universal-env';
import jsbridge from '@ali/uc-toolkit/lib/jssdk';
import { getSubVersion } from './utils';
import { IRewardInfoItem, IRewardVideoPlayRes } from "@/lib/adPlayer/typings";

import viewappear from '../viewappear';
import { ILogParams } from '../tracker/typings';

export type CLIENT_EVENT_NAME = 'appStateChange'
| 'sceneAction'
| 'hardwareBackPress'
| 'keyboardDidShow'
| 'keyboardDidHide'
| 'UCEVT_Global_AccountStateChange'
| 'UCEVT_Global_AccountSendSmsCode'
| 'UCEVT_Global_AccountThirdPartyToken'
| 'weex.initParamsEvent'
| 'UCEVT_IAP_OrderStateChange'
| 'UCEVT_IAP_PaymentStateChange'
| 'UCEVT_PAY_ResultNotify'
| 'UCEVT_Biz_AdActionNotify';

interface IAdAllParams {
  /** 兜底类型，默认 tt=穿山甲 */
  backupTypeMap: {
    ios: TAllAdType;
    android: TAllAdType;
  };
  backupAid: {
    ios: string;
    android: string;
  };
  businessCode: string;
  wpkCategory: Record<'preload' | 'invoke' | 'play', number>;
  /** 聚合 sdk 的aid */
  mixedAidMap: {
    ios: string;
    android: string;
  };
  invokeInterval?: number;
  slotKey?: string; // Noah sdk id
  rewardedVideoSlotKey?: string;
  appId: string;
  type: TAllAdType;
  coralAppId?: string;
  moduleCode?: string;
  enableAsyncQueryReward: boolean; // 是否开启异步查奖功能
}

interface ICallBackParams {
  success: boolean;
  msg: string;
  params?: IAdParams;
}

export type TAllAdType = 'tt' | 'mixed' | 'hc' | 'default';

interface IAdParams {
  aid?: string;
  type: TAllAdType;
  backupType?: string;
  backupAid?: string;
  businessCode?: string;
  slot_key?: string; // Noah广告id
  rewarded_video_slot_key?: string; // 激励视频
  /** 请求id  */
  requestId?: string;
  appId?: string;
  coralAppId?: string;
  moduleCode?: string;
  enableAsyncQueryReward?: boolean; // 是否开启异步查奖功能
}

export type IMonitorFn = (params?: Omit<ILogParams, 'category'>) => void;

interface IMonitor {
  success: IMonitorFn;
  fail: IMonitorFn;
}

function geneRequestId(preFix: string) {
  const second = Date.now() / 1000 >> 0;
  return `${preFix}_${second}`
}

export class AdPlayer {
  /** 调起超时的 timer */
  invokeTimer?: ReturnType<typeof setTimeout>;
  /** 阻止频繁调起的 timer  */
  invokeLockTimer?: ReturnType<typeof setTimeout>;
  invoking = false;
  invokeInterval = 3000;
  /** 是否已经调起视频 */
  invoked = false;
  lastCallBackTime?: number;
  inited = false;
  businessCode = 'uc_activity_ad';
  backupTypeMap: IAdAllParams['backupTypeMap'] = {
    ios: 'tt',
    android: 'tt',
  };
  backupAid: IAdAllParams['backupAid'] = {
    ios: '',
    android: '',
  };
  wpkCategory: IAdAllParams['wpkCategory'] = {
    preload: -1,
    invoke: -1,
    play: -1,
  };
  mixedAidMap: IAdAllParams['mixedAidMap'] = {
    ios: '',
    android: '',
  };
  type: IAdAllParams['type'] = 'default';
  appId: string;
  slotKey = ''; // Noah id
  rewardedVideoSlotKey = '';
  requestId = '';
  coralAppId = '';
  moduleCode = '';
  enableAsyncQueryReward = false;
  get allAid() {
    return [
      this.backupAid.ios,
      this.backupAid.android,
      this.mixedAidMap.android,
      this.mixedAidMap.ios,
      this.slotKey,
      this.rewardedVideoSlotKey
    ].filter(aid => !!aid);
  }

  /** 客户端回调事件名 */
  cbEventName: CLIENT_EVENT_NAME = 'UCEVT_Biz_AdActionNotify';
  // needListenPageHide = isIOS && !isLatestVersion(appVersion || '13.6.0', '13.1.6');
  /** 仅在需要处理页面前后台切换时设 false */
  isPageBack = true;
  playCallBack?: (params: ICallBackParams) => void;
  /** 播放回调参数，需要结束广告播放后才有值，含 播完/未播完 两种状态 */
  playCallBackParams?: ICallBackParams;

  inVokeCb?: (params: ICallBackParams) => void;
  invokeMonitor?: IMonitor;

  playMonitor?: IMonitor;

  /** 初始化参数，可多次调用 */
  setup(options: Partial<IAdAllParams>) {
    if (options.backupAid) {
      this.backupAid = {
        ...this.backupAid,
        ...options.backupAid,
      };
    }
    if (options.wpkCategory) {
      this.wpkCategory = {
        ...this.wpkCategory,
        ...options.wpkCategory,
      };
    }
    if (options.mixedAidMap) {
      this.mixedAidMap = {
        ...this.mixedAidMap,
        ...options.mixedAidMap,
      };
    }
    if (options.backupTypeMap) {
      this.backupTypeMap = {
        ...this.backupTypeMap,
        ...options.backupTypeMap,
      }
    }
    if (options.invokeInterval !== undefined) {
      this.invokeInterval = options.invokeInterval;
    }
    if (options.businessCode) {
      this.businessCode = options.businessCode;
    }
    if (options.appId) {
      this.appId = options.appId
    }
    if (options.coralAppId) {
      this.coralAppId = options.coralAppId
    }
    if (options.moduleCode) {
      this.moduleCode = options.moduleCode
    }
    this.type = options.type || this.type;
    this.slotKey = options.slotKey || this.slotKey;
    this.rewardedVideoSlotKey = options.rewardedVideoSlotKey || this.rewardedVideoSlotKey;
    this.enableAsyncQueryReward = options.enableAsyncQueryReward || this.enableAsyncQueryReward;
    if (!this.inited) {
      this.bindAndUnbindPlayEvent(true);
    }
    this.inited = true;
  }

  /**
   * 仅在低版本处理，按页面隐藏判断为调起成功
   */
  listenPageHide = () => {
    setTimeout(() => {
      // 处理因为跳过 requesting already 的处理, 不过时机无法保证
      this.reportInvokeResult(true, '检测页面隐藏上报');
    }, 3000);
    this.cleartInvokeTimeoutTimer();
    viewappear.off('disappear', this.listenPageHide);
    viewappear.on('appear', this.handlePageReVisible);
  }

  handlePageReVisible = () => {
    viewappear.off('appear', this.handlePageReVisible);
    this.isPageBack = true;
    this.triggerCbIfPageAndPlayerDone();
  }

  reportInvokeResult = async (success: boolean, c1: string, message?: string) => {
    if (success) {
      this.invoked = true;
    }

    const messageText = JSON.stringify(c1).replace(/\s|\+/g, '').toLowerCase().includes('nofill') ? '广告未填充' : '';
    if (this.invokeMonitor) {
      const baseWpkParams = await this.getBaseWpkParams();
      const adParams = await this.getAdParams();
      const msg = message || (success ? '调起成功' : `调起失败-${messageText}`);
      const logParams: Omit<ILogParams, 'category'> = {
        msg,
        c1,
        c5: JSON.stringify(c1).replace(/\s|\+/g, '').toLowerCase().includes('nofill') ? '否' : '是',
        ...baseWpkParams,
        bl1: JSON.stringify(adParams),
        /** IOS 的消息可能很长，放不下 */
        bl2: c1,
      };
      if (this.invokeMonitor) {
        if (success) {
          this.invokeMonitor.success(logParams);
        } else {
          this.invokeMonitor.fail(logParams);
        }
      } else {
        tracker.logError(new Error('[ad] monitor undefined'), logParams);
      }
      if (this.inVokeCb) {
        this.inVokeCb({ success, msg: c1, params: adParams });
        this.inVokeCb = undefined;
      }
      // 确保只上报一次
      this.invokeMonitor = undefined;
    }
  }

  bindAndUnbindPlayEvent = (isBind = true) => {
    if (isBind) {
      document.addEventListener(this.cbEventName, this.handlePlaySuccess);
    } else {
      document.removeEventListener(this.cbEventName, this.handlePlaySuccess);
    }
  }

  /**
   * 统一处理超时上报的延时器
   */
  cleartInvokeTimeoutTimer = () => {
    if (this.invokeTimer) {
      clearTimeout(this.invokeTimer);
    }
  }

  /**
   * 内部带锁
   * enableAsyncQueryReward {number} --- 是否开启异步查奖功能
   */
  play = async (playCb: (params: ICallBackParams) => void, inVokeCb?: (params: ICallBackParams) => void) => {
    this.requestId = geneRequestId('play');
    const adParams = await this.getAdParams();
    console.log('adParams===play', adParams);

    // if (!adParams.aid) {
    //   throw new Error(`缺少参数 aid，请检查，当前参数 -> ${JSON.stringify(adParams)}`);
    // }
    if (this.invoking) return;
    this.invoking = true;
    this.delayUnlockInvoke(this.invokeInterval);

    this.invokeMonitor = tracker.Monitor(this.wpkCategory.invoke, { sampleRate: 1 });
    this.playCallBack = playCb;
    this.playCallBackParams = undefined;
    this.inVokeCb = inVokeCb;
    this.playMonitor = undefined;
    if (isIOS && !isLatestVersion(appVersion, '13.1.6')) {
      this.isPageBack = false;
      viewappear.on('disappear', this.listenPageHide);
    } else {
      this.isPageBack = true;
    }

    this.cleartInvokeTimeoutTimer();
    this.invokeTimer = setTimeout(() => {
      this.reportInvokeResult(false, '超时上报', '调起失败-超时');
    }, 15000);
    try {
      const res = await jsbridge.invoke('biz.showRewardVideoAd', adParams);
      console.log('[play-------res]', res);

      // 因为 {"errCode":4,"ext":""} 返回很快，如果直接释放锁的话，用户可以再次触发，改成1秒后再释放
      this.delayUnlockInvoke();
      this.cleartInvokeTimeoutTimer();
      this.setHandleProcessStatus(false);
      viewappear.on('appear', this.listenPageReVisible);
      // {"errCode":4,"ext":""} 判断为失败
      const errMsg = res && (res.error || (res.ext || {}).error || res.errMsg || !!res.errCode);

      // ios调起失败也会进入 success 逻辑
      const isInvokeSuccess = !res || !errMsg;
      const resStr = JSON.stringify(res || {});
      // XXX: 13.1.5 版本直接点击播放也会出现这个报错，但是可能会播放成功，当成正常情况处理
      const maybeMistakeErr = isIOS && resStr.indexOf('is requesting already...') > -1;
      if (!isInvokeSuccess) {
        console.warn('[ad]调起失败，params >> ', JSON.stringify(adParams), ' res > ', JSON.stringify(res || {}));
        if (!maybeMistakeErr) {
          viewappear.off('disappear', this.listenPageHide);
          this.reportInvokeResult(false, JSON.stringify(res || { error: 'empty-res' }));
        }
      } else {
        this.reportInvokeResult(true, JSON.stringify(res || {}));
      }
      this.playMonitor = tracker.Monitor(this.wpkCategory.play, { sampleRate: 1 });
    } catch (err) {
      this.cleartInvokeTimeoutTimer();
      this.delayUnlockInvoke();
      this.playMonitor = undefined;
      console.warn('[ad]视频调起失败', err);
      this.reportInvokeResult(false, JSON.stringify(err || {}));
    }
  }

  preload = async () => {
    const preLoadMonitor = tracker.Monitor(this.wpkCategory.preload, { sampleRate: 1 });
    const adParams = await this.getAdParams();
    const baseParams = await this.getBaseWpkParams();
    try {
      const res = await jsbridge.invoke('biz.loadRewardVideoAd', adParams);
      const isPreloadFailed = res?.error || res?.errCode || res?.ext?.error;
      const errMsg = decodeURIComponent(res?.error || res.ext?.error_msg || '').replace(/\+/g, ' ');
      // 移除返回值中的空格和加号，判断未填充
      const isNoFill = JSON.stringify(res).replace(/\s|\+/g, '').toLowerCase().includes('nofill');
      if (isPreloadFailed) {
        preLoadMonitor.fail({
          msg: '预加载失败',
          c1: errMsg,
          ...baseParams,
          c9: res.errCode || '',
          c10: isNoFill ? '未填充' : '有填充',
          bl1: JSON.stringify(res || {})
        });
        return null
      }
      preLoadMonitor.success({
        msg: '预加载成功',
        c1: JSON.stringify(res || {}),
        ...baseParams,
        c4: res?.is_tanx_advanced ? '1' : '0',
        c7: res?.reward_info_list?.[0]?.pid || '',
        c9: res.errCode || '',
        c10: isNoFill ? '未填充' : '有填充',
        bl1: JSON.stringify(res)});
      return res
    } catch (err) {
      preLoadMonitor.fail({
        msg: '预加载失败-catch',
        c1: JSON.stringify(err || {}),
        ...baseParams,
        bl1: JSON.stringify(err || {})
      });
      console.warn('预加载失败~~ 参数', adParams);
      return null
    }
  }

  /**
   *  因为 {"errCode":4,"ext":""} 返回很快，如果直接释放锁的话，用户可以再次触发，改成默认1秒后再释放
   * @param delay
   */
  delayUnlockInvoke = (delay = 1000) => {
    if (this.invokeLockTimer) {
      clearTimeout(this.invokeLockTimer);
    }
    this.invokeLockTimer = setTimeout(this.unlockInvokeStatus, delay)
  }

  private unlockInvokeStatus = () => {
    if (this.invokeLockTimer) {
      clearTimeout(this.invokeLockTimer);
    }
    this.invoking = false;
  }

  private async getBaseWpkParams() {
    const ver = appVersion;
    const subVer = await getSubVersion();
    const adParams = await this.getAdParams();
    const baseWpkParams = {
      c2: ver,
      c3: subVer,
      c6: adParams.slot_key || '',
      c8: adParams.rewarded_video_slot_key || '',
      bl3: JSON.stringify(adParams || {}),
    };
    return baseWpkParams;
  }

  /**
   * 标识视频是否已经处理完回调，兜底
   * @param isUsed 做
   */
  setHandleProcessStatus = (isUsed = false) => {
    if (!isWeex) {
      if (isUsed) {
        if (window._ucEvent && window._ucEvent.type === this.cbEventName) {
          window._ucEvent.used = true;
        }
      } else if (window._ucEvent && window._ucEvent.type === this.cbEventName) {
        window._ucEvent = undefined;
      }
    }
  }

  listenPageReVisible = () => {
    if (!isWeex) {
      // XXX: 因为不能保证正常监听回调和 appear 哪个先执行，延后一点
      setTimeout(() => {
        if (window._ucEvent && window._ucEvent.type === this.cbEventName && !window._ucEvent.used) {
          if (document.dispatchEvent) {
            Object.assign(window._ucEvent.detail, {
              from_fe: true,
            })
            document.dispatchEvent(window._ucEvent)
          }
        }
        viewappear.off('appear', this.listenPageReVisible);
      }, 100);
    }
  }

  /** 汇川aid 格式 */
  autoConvertAid = (oriAid: string, type: TAllAdType, mixedAid: string) => {
    if (type === 'hc' && Number.isInteger(+oriAid)) {
      // {"slot_id": "1000355", "wid": "uc_activity_ad_springpigvideo", "model_name": "uc_activity"}
      return JSON.stringify({ slot_id: `${oriAid}`, wid: mixedAid || oriAid, model_name: 'uc_activity' })
    }
    return oriAid;
  }

  async getAdParams() {
    const appId = this.appId;
    let params: IAdParams = {
      type: this.type,
    };
    if (this.slotKey) {
      params.slot_key = this.slotKey;
    }
    if (this.rewardedVideoSlotKey) {
      params.rewarded_video_slot_key = this.rewardedVideoSlotKey
    }
    if (appId) {
      params.appId = appId
    }
    if (this.moduleCode) {
      params.moduleCode = this.moduleCode
    }
    if (this.coralAppId) {
      params.coralAppId = this.coralAppId
    }
    params.requestId = this.requestId || '';
    params.enableAsyncQueryReward = this.enableAsyncQueryReward;
    return params;
  }

  checkValidPlayDoneRes = (res: IRewardVideoPlayRes) => {
    let flag = true;
    const { aid = '' } = res;
    const aidList = this.allAid;
    if (aidList.indexOf(aid) < 0) {
      flag = false;
    }
    if (!this.invoked) {
      flag = false;
    }
    const now = Date.now();
    const MIN_REPORT_INTERVAL = 2000;
    if (this.lastCallBackTime && now - this.lastCallBackTime < MIN_REPORT_INTERVAL) {
      flag = false;
    }
    return flag;
  }

  private handlePlaySuccess = async (originRes) => {
    const res: IRewardVideoPlayRes = isWeex ? originRes : originRes.detail;
    const shouldReport = this.checkValidPlayDoneRes(res);
    if (!shouldReport) return;
    this.lastCallBackTime = Date.now();
    this.invoked = false;

    this.setHandleProcessStatus(true);
    const baseWpkParams = await this.getBaseWpkParams();
    const playMonitor = this.playMonitor || tracker.Monitor(this.wpkCategory.play, { sampleRate: 1 });
    const watchFinish = res.is_ended.toString() !== 'false';
    const rewardInfoList: IRewardInfoItem[] = res?.reward_info_list || [];
    this.playCallBackParams = {
      success: watchFinish,
      msg: JSON.stringify(res),
    };
    if (watchFinish) {
      playMonitor.success({
        msg: '播放完成',
        ...baseWpkParams,
        c9: res.reward_type,
        c10: rewardInfoList?.[0]?.adn_id || '',
        bl2: JSON.stringify(res || {}),
      });
    } else {
      playMonitor.fail({
        msg: '播放未完成',
        bl2: JSON.stringify(res || {}),
        c10: rewardInfoList?.[0]?.adn_id || '',
        ...baseWpkParams
      });
    }
    this.triggerCbIfPageAndPlayerDone();
  }

  private triggerCbIfPageAndPlayerDone() {
    if (!this.playCallBackParams || !this.isPageBack) return;
    this.isPageBack = false;
    if (this.playCallBack) {
      this.playCallBack(this.playCallBackParams);
    }
    this.playCallBackParams = undefined;
  }
}

const adPlayer = new AdPlayer();
export default adPlayer;
