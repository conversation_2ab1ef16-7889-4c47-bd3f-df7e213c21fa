import { ELDER_APP_MIN_VERSION, MIN_VERSION, QUARK_MIN_VERSION } from "./contans";
import { ucparams } from "../ucapi";

const APP_TYPE = {
  /** 大字版 */
  ELDER: 'UCElder',
}

const getAppAndSubVersionParams = (() => {
  let result: { pr: string; sv: string };
  return async () => {
    if (result) return result;
    try {
      result = await ucparams({ params: 'svpr' });
    } catch {
      // result = { pr: '', sv: '' }
    }
    return result;
  };
})();

export const getSubVersion = async () => {
  const ucParams = await getAppAndSubVersionParams();
  return ucParams.sv;
}

/** 是否大字版 */
export const isElder = async () => {
  const ucParams = await getAppAndSubVersionParams();
  return ucParams.pr === APP_TYPE.ELDER;
};

export const getVideoMinVersion = async (osType: "android" | "ios" = "android") => {
  const isElderApp = await isElder();
  // 判断系统版本
  let VERSION_CONFIG = MIN_VERSION;
  // 大字版
  if (isElderApp) VERSION_CONFIG = ELDER_APP_MIN_VERSION;
  // 夸克
  // if (isQuark()) VERSION_CONFIG = QUARK_MIN_VERSION;
  return VERSION_CONFIG.mixed[osType];
};
