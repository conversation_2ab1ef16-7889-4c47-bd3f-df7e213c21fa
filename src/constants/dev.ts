import qs from '@/lib/qs';
import config from '@/config';

const devHostName = 'local.uc.cn';

const isDevUrl = location.hostname === devHostName;

const isProxy = isDevUrl && qs.getParam('proxy');

const proxyUrl = isProxy ? '' : null;

// GUIDANCE: 本地 mock 端口
const mockPort = 9999;

export const isDev = config.env === 'local';

export const isMock = isDevUrl && !!qs.getParam('mock');

export const isDebug = isDev && !!qs.getParam('debug');

// eslint-disable-next-line @iceworks/best-practices/no-http-url
export const devUrl = isMock ? `http://${devHostName}:${mockPort}` : proxyUrl;
