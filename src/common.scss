@import './components/font-compress/FZLanTingYuanS/FZLanTingYuanS-Bold.css';
@import './components/font-compress/<PERSON><PERSON>/<PERSON><PERSON>-Bold.css';
@import './components/font-compress/FZLanTingYuanS-EB/FZLanTingYuanS-Extra-Bold.css';
@import './components/font-compress/<PERSON><PERSON>-ExtraBoldItalic/<PERSON><PERSON>-ExtraBoldItalic.css';


* {
  margin: 0;
  // padding: 0;
}

body{
  background: #eee;
}

html{
  touch-action: none;
  touch-action: pan-y;
  -webkit-touch-callout: none; //系统默认菜单被禁用;可以实现页面因为长按弹出各种操作窗口
  -webkit-user-select: none; //webkit浏览器
  -khtml-user-select: none; //早期浏览器
  -moz-user-select: none; //火狐
  -ms-user-select: none; //IE10
  user-select: none;
  touch-callout: none;
  -webkit-touch-callout: none;
}
.share-panel-comp{
 .share-operation-content{
 .title-wrap {
    .panel-uu{
      width: 226rpx!important;
      height: 194rpx!important;
      left: 6rpx!important;
      top: -78rpx!important;
    }
    .panel-title{
      color: #005210 !important;
      font-size: 36rpx !important;
      font-weight: 600 !important;
    }
    .panel-sub-title {
      top: 82rpx !important;
      color: #005210 !important;
      font-weight: 400 !important;
      font-size: 24rpx !important;
    }
    .share-panel-subTitle{
      display: flex;
      align-items: center;
      span {
        display: inline-block;
      }
      .task-reward {
        color: #CE9E70;
        font-weight: 600;
        font-size: 28rpx !important;
        display: flex;
        align-items: center;
        .manure-icon {
          display: inline-block;
          width: 30rpx;
          height: 30rpx;
        }
      }
    }
 }
}
}
.wrc-modal-body, .moda-toast-card-comp {
  img {
    uc-perf-stat-ignore: image;
  }
}

// 适应折叠屏
// 宽度大于等于 500px 的屏幕
@media screen and (min-width: 500px){
  // 不展示分享面板的图片dom元素
  .share-panel-comp{
    .share-panel-code{
      visibility: hidden;
    }
  }
  // 弹框缩小
  .modal-continuous-sign,
  .modal-help-success,
  .newbie-gift-pack,
  .plant-trees-success,
  .modal-selectSeed .content-wrap,
  .moda-toast-card-comp,
  .modal-upgrade,
  .task-modal-comp,
  .progress-bubble,
  .modal-retention,
  .modal-receive-fertilier,
  .modal-limited-time-benefits-task,
  .modal-limited-time-benefits-award,
  .modal-bbz-help-fail-comp,
  .modal-bbz-help-success-comp,
  .modal-bbz-attend-activity-comp,
  .modal-risk-control-comp,
  .modal-bbz-progress-challenge-award-comp,
  .modal-binding-taobao,
  .modal-ranking-award-comp,
  .modal-notice-award-comp,
  .modal-double-card-publicity,
  .modal-double-card-wrapper,
  .modal-bbz-beginner-guidance .beginner-guidance-content {
    transform: scale(0.5);
  }
  .modal-double-card-wrapper {
    height: auto !important;
    .modal-double-card {
      border-radius: 48rpx !important;
    }
  }
  .modal-selectSeed .select-seed-head{
    transform: scale(0.7);
  }
  .modal-bbz-beginner-guidance .animation{
    height: 100% !important;
    top: 0 !important;
    transform: scale(0.7) !important;
  }
  // 任务浮层
  .task-pop-comp{
    .content-task{
      height: 80% !important;
    }
  }
  .help-ranking-page-ranking-list{
    height: 100vh !important;
    .list-wrap {
      height: 100vh !important;
    }
  }
}

@font-face {
  font-family: D-DIN-Bold;
  // src: url("https://image.uc.cn/s/uae/g/01/yuanbaosign2020/DIN-Bold.ttf");
  src: url("https://image.uc.cn/s/uae/g/42/uc-interactive/D-DIN-Bold.otf");
}

.din-num {
  font-family: D-DIN-Bold !important;
}

.gilroy-num {
  font-family: Gilroy-Bold !important;
}
