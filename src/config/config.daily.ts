export default {
  env: 'daily',
  appId: 'uc_baba_farm_task',
  bbzAppId: 'uc_baba_farm_bang', // 帮帮种appid
  // farmHost: 'https://farm-server.alibaba.net',
  farmHost: 'https://farm-server.uc.alibaba-inc.com',
  /** 任务服务 */
  taskHost: 'https://task12.uc.alibaba-inc.com',
  coralHost: 'https://coralnew.uc.alibaba-inc.com',
  harvestUrl: 'https://pages.tmall.com/wow/hdwk/act/2020nhj-my-fruits',
  strategyUrl: 'https://broccoli.uc.cn/apps/nt81RWJDj/routes/rXjOTYUetK?uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt',
  downloadTaobaoUrl: 'https://market.m.taobao.com/app/starlink/wakeup-transit/pages/download?star_id=5582&slk_force_set_request=true&hd_from=ucfarm&bc_fl_src=uc_nc_zsrw',
  selectTaobaoAddressUrl: 'https://pre-wormhole.tmall.com/wow/z/hdwk/2020nhj/JX2L2HD7R5?disableNav=YES&force_no_smb=true',
  sharePlanId: 'nbvl8ls3fxix',
  shareHost: 'https://coral-wechat.uc.alibaba-inc.com',
  redPacketmoduleCode: '765e3a92f3e04603968c6d76a9f8be8c',
  walletUrl: 'https://broccoli.uc.cn/apps/UCwallet/routes/uW8XQJxn2?walletType=ucfarm&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401',
  taskResourceCode: 'uc_baba_farm_high_value',
  taskWidgetResourceCode: 'uc_baba_farm_task_widget',
  getMatryoshkaTaskCode: 'uc_baba_farm_task_nest',
  getBackInterceptCode: "uc_baba_farm_back_intercept",
  getBubbleTaskCode: 'uc_baba_farm_bubble',
  backTagResourceCode: 'uc_baba_farm_back_tag',
  bbzResourceCode: 'uc_baba_farm_bangbangzhong',
  homeAdResourceCode: 'bbnc_permanent_video',
  doubleTaskResourceCode: 'bbnc_double_win_ad_task',
  drawChanceTaskResourceCode: 'bbnc_double_draw_chance_task',
  HC_AD_API_URL: 'https://test.huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativeadnb',
  HC_TASK_API: 'https://ad-qatest1.sm.cn/reward',
}
