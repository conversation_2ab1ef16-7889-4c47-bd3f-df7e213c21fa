const defaultConfig: IConfig = {
  env: 'default',
  appId: 'uc_baba_farm_task',
  bbzAppId: 'uc_baba_farm_bang', // 帮帮种appid
  moduleCode: '5730f0e844774fde9bf3306acecbf055',
  farmHost: 'https://farm.uc.cn',
  /** 任务服务 */
  taskHost: 'https://coral-task.uc.cn',
  harvestUrl: 'https://pages.tmall.com/wow/hdwk/act/2020nhj-my-fruits',
  cmsUrl: 'https://com-cms.uc.cn',
  coralHost: 'https://coral2.uc.cn',
  diamondKey: 'uc_baba_farm_task',
  businessKey: 'ucbabafarm',
  WORKER_SECRET: 'dWNhY3R0YXNrd29ya2Vy',
  strategyUrl: 'https://broccoli.uc.cn/apps/nt81RWJDj/routes/rXjOTYUetK?uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt',
  downloadTaobaoUrl: 'https://market.m.taobao.com/app/starlink/wakeup-transit/pages/download?star_id=5582&slk_force_set_request=true&hd_from=ucfarm&bc_fl_src=uc_nc_zsrw',
  selectTaobaoAddressUrl: 'https://pages.tmall.com/wow/z/hdwk/2020nhj/farm-uc-address-change?disableNav=YES&force_no_smb=true',
  // 啄木鸟监控ID
  WPK_BID: 'a0pmte7h-y9s5feoy',
  /** 激励广告id 商店包可用（仅活动开发期间使用） */
  REWARD_ID: {
    ios: '945771116',
    android: '945771105',
    mixed: {
      android: {
        aid: 'uc_activity_ad_springpigvideo',
      },
      ios: {
        aid: 'uc_activity_ad_springpigvideo',
      },
    },
    appId: {
      ios: '10217',
      android: '10216',
    },
    slotKey: '10002920',
    rewardedVideoSlotKey: '10002919',
  },
  sharePlanId: 'motr0ca5x0xm',
  shareHost: 'https://coral2.uc.cn',
  redPacketmoduleCode: '2c0e7f4b9f46414c9897f76ffdda8105',
  walletUrl: 'https://broccoli.uc.cn/apps/Y3PLALKZ8/routes/farm-wallet?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmtkp&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&webCompassApp=true&walletType=ucfarm',
  fve: '1.9.0', // 前端版本
  taskResourceCode: 'uc_baba_farm_high_value__for_prod',
  taskWidgetResourceCode: 'uc_baba_farm_task_widget__for_prod',
  getMatryoshkaTaskCode: "uc_baba_farm_task_nest__for_prod",
  getBackInterceptCode: "uc_baba_farm_back_intercept__for_prod",
  getBubbleTaskCode: 'uc_baba_farm_bubble__for_prod',
  backTagResourceCode: 'uc_baba_farm_back_tag__for_prod',
  cmsRedirectCode: 'bbnc_redirect_flz',
  bbzResourceCode: 'uc_baba_farm_bangbangzhong__for_prod',
  homeAdResourceCode: 'bbnc_permanent_video__for_prod',
  HC_AD_API_URL: 'https://huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativead',
  HC_TASK_API: 'https://e.uc.cn/reward',
  wateringPopResourceCode: 'bbnc_watering_pop__for_prod',
}

export interface IConfig {
  env: string;
  appId: string;
  bbzAppId: string;
  moduleCode: string;
  farmHost: string;
  taskHost: string;
  harvestUrl: string;
  cmsUrl: string;
  coralHost: string;
  diamondKey: string;
  strategyUrl: string;
  businessKey: string;
  WORKER_SECRET: string;
  // 啄木鸟监控ID
  WPK_BID: string;
  WORKER_SECRET: string;
  downloadTaobaoUrl: string;
  selectTaobaoAddressUrl: string;
  REWARD_ID: {
    ios: string;
    android: string;
    mixed: {
      android: {
        aid: string;
      };
      ios: {
        aid: string;
      };
    };
    appId: {
      ios: string;
      android: string;
    };
    slotKey: string;
    rewardedVideoSlotKey: string;
  };
  redPacketmoduleCode: string;
  fve: string;
  sharePlanId: string;
  shareHost: string;
  walletUrl: string; // 提现链接
  taskResourceCode: string;
  taskWidgetResourceCode: string;
  getMatryoshkaTaskCode: string;
  getBackInterceptCode: string;
  getBubbleTaskCode: string;
  backTagResourceCode: string;
  cmsRedirectCode: string;
  bbzResourceCode: string; // 帮帮种资源位
  homeAdResourceCode: string; // 首页常驻激励广告资源位
  wateringPopResourceCode: string; // 施肥推荐弹窗素材
  HC_AD_API_URL: string;
  HC_AD_API_PATH_NAME: string;
  HC_TASK_API: string;
}

export default defaultConfig
