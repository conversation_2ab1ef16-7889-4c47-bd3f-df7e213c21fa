export default {
  env: 'prod',
  farmHost: 'https://farm.uc.cn',
  bbzAppId: 'uc_baba_farm_bang', // 帮帮种appid
  /** 任务服务 */
  taskHost: 'https://coral-task.uc.cn',
  harvestUrl: 'https://pages.tmall.com/wow/hdwk/act/2020nhj-my-fruits',
  cmsUrl: 'https://com-cms.uc.cn',
  coralHost: 'https://coral2.uc.cn',
  diamondKey: 'uc_baba_farm_task',
  strategyUrl: 'https://broccoli.uc.cn/apps/nt81RWJDj/routes/rXjOTYUetK?uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt',
  downloadTaobaoUrl: 'https://market.m.taobao.com/app/starlink/wakeup-transit/pages/download?star_id=5582&slk_force_set_request=true&hd_from=ucfarm&bc_fl_src=uc_nc_zsrw',
  selectTaobaoAddressUrl: 'https://pages.tmall.com/wow/z/hdwk/2020nhj/farm-uc-address-change?disableNav=YES&force_no_smb=true',
  redPacketmoduleCode: '2c0e7f4b9f46414c9897f76ffdda8105',
  walletUr: 'https://broccoli.uc.cn/apps/Y3PLALKZ8/routes/farm-wallet?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmtkp&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&webCompassApp=true&walletType=ucfarm',
  sharePlanId: 'motr0ca5x0xm',
  shareHost: 'https://coral2.uc.cn',
  taskResourceCode: 'uc_baba_farm_high_value__for_prod',
  taskWidgetResourceCode: 'uc_baba_farm_task_widget__for_prod',
  getMatryoshkaTaskCode: "uc_baba_farm_task_nest__for_prod",
  getBackInterceptCode: "uc_baba_farm_back_intercept__for_prod",
  getBubbleTaskCode: 'uc_baba_farm_bubble__for_prod',
  backTagResourceCode: 'uc_baba_farm_back_tag__for_prod',
  bbzResourceCode: 'uc_baba_farm_bangbangzhong__for_prod',
  homeAdResourceCode: 'bbnc_permanent_video__for_prod',
  HC_AD_API_URL: 'https://huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativead',
  HC_TASK_API: 'https://e.uc.cn/reward',
  wateringPopResourceCode: 'bbnc_watering_pop__for_prod',
};
