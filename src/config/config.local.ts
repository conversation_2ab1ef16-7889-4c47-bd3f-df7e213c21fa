export default {
  env: 'local',
  appId: 'uc_baba_farm_task',
  bbzAppId: 'uc_baba_farm_bang', // 帮帮种appid
  // farmHost: 'https://farm.uc.cn',
  farmHost: 'https://farm-server.uc.alibaba-inc.com',
  taskHost: 'https://coralnew.uc.alibaba-inc.com',
  harvestUrl: 'https://pages.tmall.com/wow/hdwk/act/2020nhj-my-fruits',
  cmsUrl: 'https://cms-server-debug.uc.alibaba-inc.com',
  coralHost: 'https://coralnew.uc.alibaba-inc.com',
  strategyUrl: 'https://broccoli.uc.cn/apps/nt81RWJDj/routes/rXjOTYUetK?uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt',
  downloadTaobaoUrl: 'https://market.m.taobao.com/app/starlink/wakeup-transit/pages/download?star_id=5582&slk_force_set_request=true&hd_from=ucfarm&bc_fl_src=uc_nc_zsrw',
  selectTaobaoAddressUrl: 'https://pages.tmall.com/wow/z/hdwk/2020nhj/farm-uc-address-change?disableNav=YES&force_no_smb=true',
  sharePlanId: 'nbvl8ls3fxix',
  shareHost: 'https://coral-wechat.uc.alibaba-inc.com',
  redPacketmoduleCode: '765e3a92f3e04603968c6d76a9f8be8c',
  walletUrl: 'https://broccoli.uc.cn/apps/UCwallet/routes/uW8XQJxn2?walletType=ucfarm&__env=pre&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401',
  taskResourceCode: 'uc_baba_farm_high_value__for_prod',
  taskWidgetResourceCode: 'uc_baba_farm_task_widget',
  getMatryoshkaTaskCode: 'uc_baba_farm_task_nest',
  getBackInterceptCode: "uc_baba_farm_back_intercept",
  getBubbleTaskCode: 'uc_baba_farm_bubble',
  backTagResourceCode: 'uc_baba_farm_back_tag',
  bbzResourceCode: 'uc_baba_farm_bangbangzhong',
  HC_AD_API_URL: 'https://test.huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativeadnb',
  HC_TASK_API: 'https://ad-qatest1.sm.cn/reward',
  wateringPopResourceCode: 'bbnc_watering_pop',
}
