export default {
  appId: 'uc_baba_farm_task_pre',
  bbzAppId: 'uc_baba_farm_bang', // 帮帮种appid
  moduleCode: 'd0f4f832636741c68e101071ceed35ea',
  farmHost: 'https://pre-farm-server-uc.alibaba-inc.com',
  /** 任务服务 */
  taskHost: 'https://pre-coral-task.uc.cn',
  cmsUrl: 'https://cms-server-debug.uc.alibaba-inc.com',
  diamondKey: 'uc_baba_farm_task_pre',
  coralHost: 'https://coral2-pre.uc.cn',
  harvestUrl: 'https://pages.tmall.com/wow/hdwk/act/2020nhj-my-fruits',
  strategyUrl: 'https://broccoli.uc.cn/apps/nt81RWJDj/routes/rXjOTYUetK?uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt',
  downloadTaobaoUrl: 'https://market.m.taobao.com/app/starlink/wakeup-transit/pages/download?star_id=5582&slk_force_set_request=true&hd_from=ucfarm&bc_fl_src=uc_nc_zsrw',
  selectTaobaoAddressUrl: 'https://pages.tmall.com/wow/z/hdwk/2020nhj/farm-uc-address-change?disableNav=YES&force_no_smb=true',
  // /**
  //  * 日常环境的分享计划ID和域名
  //  */
  sharePlanId: 'nbvl8ls3fxix',
  shareHost: 'https://coral-wechat.uc.alibaba-inc.com',
  // sharePlanId: 'motr0ca5x0xm',
  // shareHost: 'https://coral2.uc.cn',
  redPacketmoduleCode: '2c0e7f4b9f46414c9897f76ffdda8105',
  walletUrl: 'https://broccoli.uc.cn/apps/UCwallet/routes/uW8XQJxn2?walletType=ucfarm&__env=pre&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401',
  taskResourceCode: 'uc_baba_farm_high_value',
  taskWidgetResourceCode: 'uc_baba_farm_task_widget',
  getMatryoshkaTaskCode: 'uc_baba_farm_task_nest',
  getBackInterceptCode: "uc_baba_farm_back_intercept",
  getBubbleTaskCode: 'uc_baba_farm_bubble',
  backTagResourceCode: 'uc_baba_farm_back_tag',
  bbzResourceCode: 'uc_baba_farm_bangbangzhong',
  homeAdResourceCode: 'bbnc_permanent_video',
  HC_AD_API_URL: 'https://huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativead',
  HC_TASK_API: 'https://e.uc.cn/reward',
  wateringPopResourceCode: 'bbnc_watering_pop',
}
