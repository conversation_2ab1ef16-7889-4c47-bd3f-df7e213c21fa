import { getParam } from '@/lib/qs';
import defaultConfig, {IConfig} from './config.default';
import prodConfig from './config.prod';

const publishEnv: any = PUBLISH_ENV || 'local';
const urlEnv = getParam('env');

const finalConfig: IConfig = {
  ...defaultConfig,
};

// GUIDANCE：
// 线上环境只返回 prod配置
// 其他环境：默认为构建环境，可通过env参数进行环境切换

if (publishEnv === 'prod') {
  Object.assign(finalConfig, prodConfig)
} else {
  const configs = {
    'local': require('./config.local').default,
    'daily': require('./config.daily').default,
    'pre': require('./config.pre').default,
    'prod': prodConfig,
  };
  Object.assign(finalConfig, configs[urlEnv] || configs[publishEnv])
}

export default finalConfig;
