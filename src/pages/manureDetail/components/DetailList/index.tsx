import React, { useEffect, useState, useRef } from 'react';
import './index.scss';
import { useInitialData } from '@ali/compass-app';
import { IAppState } from '@/logic/store/models/app/typings';
import useMxState from '@/hooks/useMxState';
import { isSameDay, formatTimestamp } from '@/lib/utils/date';
import Empty from '../Empty';
// eslint-disable-next-line import/no-named-as-default
import InfiniteScroll from '../infiniteScroll';
import { queryDetailData } from '@/api/detail';
import dispatch from '@/logic/store';
import { disableIOSBounces } from '@/lib/ucapi';

import ManureIcon from './images/manure-detail.png';
import Head from '../Head';

export default function Index() {
  const { data } = useInitialData();
  const [app] = useMxState<IAppState>('app');
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [isEmpty, setIsEmpty] = useState(false);
  const pageNum = useRef(2)
  const detailData = app?.detailInfo?.detailData || [];
  const curTime = app?.detailInfo?.curTime

  useEffect(() => {
    if (data) {
      const hasData = !!data?.detailData?.length;
      setIsEmpty(!hasData)
    }
  }, [data]);

  useEffect(() => {
    setTimeout(() => {
      // 禁用 iOS 弹性滚动
      disableIOSBounces(['scroll-box']);
    }, 300);
  }, []);

  const getDetailClassName = (detail: string) => {
    const isAdd = detail?.substring?.(0, 1) === '+';
    if (isAdd) return 'detail income';
    return 'detail';
  };

  const dealWithTime = (creatTime) => {
    if (isSameDay(creatTime, curTime)) {
      return formatTimestamp(creatTime, 'hh:mm')
    }
    return formatTimestamp(creatTime, 'MM-DD')
  }

  const scrollLoadMore = () => {
    if (loading || !hasMore) return;
    setLoading(true)
    queryDetailData(pageNum.current).then((res) => {
      if (res?.length > 0) {
        if (res?.length >= 20) {
          pageNum.current += 1;
        } else {
          setHasMore(false);
        }
        const newList = [...detailData, ...res];
        dispatch.app.set({
          detailInfo: {
            detailData: newList,
            curTime: data?.detailData?.__meta?.timestamp
          },
        });
      } else {
        setHasMore(false);
      }
      setLoading(false);
    })
  }

  const listenThrottle = scrollLoadMore;

  return (
    <div className="detail-list-copm">
      <Head />
      <div className="detail-wrap">
          <div className="list-wrap" id="scroll-box">
            {
              isEmpty ? <Empty /> : 
                <React.Fragment>
                {detailData?.map((item, index) => {                  
                  return (
                    <div className="list-item" key={item.createTime + index}>
                      <div className="time">{dealWithTime(item.createTime)}</div>
                      <img src={ManureIcon} className="manure-icon" alt="" />
                      <div className="text-wrap">
                        <div className="text-desc">{item.title}</div>
                        <div className="text-from">{item.from}</div>
                      </div>
                      <div className={getDetailClassName(item.detail)}>{item.detail}</div>
                    </div>
                  )
                })}
                {!!detailData?.length && <InfiniteScroll loadMore={listenThrottle} hasMore={hasMore} />}
                </React.Fragment>
            }
          </div>
      </div>
    </div>
  )
}
