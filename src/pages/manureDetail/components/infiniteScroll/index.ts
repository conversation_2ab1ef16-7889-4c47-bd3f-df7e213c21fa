// import { mergeProps } from '../../utils/with-default-props';
import React, { useEffect, useRef, useState } from 'react';
import { useLockFn, useThrottleFn } from 'ahooks';
import { withNativeProps } from './utils/native-props';
import { getScrollParent } from './utils/get-scroll-parent';
import DotLoading from './dot-loading';
import './index.scss';

function isWindow(element) {
  return element === window;
}
const classPrefix = `infinite-scroll`;
const defaultProps = {
  threshold: 250,
  children: (hasMore, failed, retry) => React.createElement(InfiniteScrollContent, {
    hasMore: hasMore,
    failed: failed,
    retry: retry
  })
};
export const InfiniteScroll = p => {
  const props = { ...defaultProps, ...p };
  const [failed, setFailed] = useState(false);
  const doLoadMore = useLockFn(async (isRetry) => {
    try {
      await props.loadMore(isRetry);
    } catch (e) {
      setFailed(true);
      throw e;
    }
  });
  const elementRef = useRef<HTMLElement>(null);
  // Prevent duplicated trigger of `check` function
  const [flag, setFlag] = useState({});
  const nextFlagRef = useRef(flag);
  const [scrollParent, setScrollParent] = useState<HTMLElement>();
  const {
    run: check
  } = useThrottleFn(async () => {
    if (nextFlagRef.current !== flag) return;
    if (!props.hasMore) return;
    const element = elementRef.current;
    if (!element) return;
    if (!element.offsetParent) return;
    const parent = getScrollParent(element);
    setScrollParent(parent);
    if (!parent) return;
    const rect = element.getBoundingClientRect();
    const elementTop = rect.top;
    const current = isWindow(parent) ? window.innerHeight : parent.getBoundingClientRect().bottom;
    if (current >= elementTop - props.threshold) {
      const nextFlag = {};
      nextFlagRef.current = nextFlag;
      await doLoadMore(false);
      setFlag(nextFlag);
    }
  }, {
    wait: 100,
    leading: true,
    trailing: true
  });
  // Make sure to trigger `loadMore` when content changes
  useEffect(() => {
    check();
  });
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    if (!scrollParent) return;
    function onScroll() {
      check();
    }
    scrollParent.addEventListener('scroll', onScroll);
    return () => {
      scrollParent.removeEventListener('scroll', onScroll);
    };
  }, [scrollParent]);
  function retry() {
    return async () => {
      setFailed(false);
      await doLoadMore(true);
      setFlag(nextFlagRef.current);
    };
  }
  return withNativeProps(props, React.createElement("div", {
    className: classPrefix,
    ref: elementRef
  }, typeof props.children === 'function' ? props.children(props.hasMore, failed, retry) : props.children));
};
const InfiniteScrollContent = props => {
  if (!props.hasMore) {
    return React.createElement("img", 
      { src: "https://yes-file.uc.cn/file/1717557643270_3898063970_5803.png", className: "no-more-img"})
  }
  if (props.failed) {
    return React.createElement("span", null, React.createElement("span", {
      className: `${classPrefix}-failed-text`
    }, '加载失败'), React.createElement("a", {
      onClick: () => {
        props.retry()();
      }
    }, '重新加载'));
  }
  return React.createElement(React.Fragment, null, React.createElement("span", null, '加载中'), React.createElement(DotLoading, null));
};

export default InfiniteScroll;
