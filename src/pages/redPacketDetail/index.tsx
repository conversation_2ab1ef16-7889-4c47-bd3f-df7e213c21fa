import React, { Fragment, useEffect } from 'react';
import { InitialDataProvider, useInitialData } from '@ali/compass-app';
import { isWeb } from '@ali/compass-env';
import './index.scss';
import '@/common.scss';
import tracker from '@/lib/tracker';
import { getParam } from '@/lib/qs';
import dispatch from '@/logic/store';
import { gestureDisabled, disableIOSBounces, forbiddenToolbar } from '@/lib/ucapi';
import { disabledPinchToZoom, landscapeBan } from '@/lib/utils/fn';
import DetailList from '@/pages/redPacketDetail/components/DetailList';
import initClient from '@/lib/utils/init-client';
import { FactConfig } from '@/config/fact';

initClient(FactConfig.redPacketConfig);
if (isWeb) {
  const logonline = require('@ali/logonline');
  logonline.init();
}

export const isValidDetailData = (data: any) => {
  return data?.detailData;
};

function PageIndex(props: any) {
  const { isLoading, data } = useInitialData();
  // 首屏数据
  const validInitialData = isValidDetailData(data);
  if (validInitialData) {
    const resData = (data.detailData ?? []).map((item, index) => {
      return {
        ...item,
        dataIndex: index
      }
    });
    dispatch.redPacket.set({
      detailRedPacketInfo: {
        detailData: resData,
      },
    });
  }

  useEffect(() => {
    if (validInitialData) {
      tracker.log({
        category: 144, // 系统自动生成，请勿修改
        msg: '红包明细页初始化', // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 1, // 用于计算"成功率";可选值为0或1
        c1: getParam('entry') || '',
        bl1: data?.detailData !== null ? JSON.stringify(data?.detailData) : ''
      });
    }
  }, [data?.detailData, validInitialData])

  useEffect(() => {
    try {
      gestureDisabled();
      disabledPinchToZoom();
      forbiddenToolbar();
      landscapeBan();
      setTimeout(() => {
        // 禁用 iOS 弹性滚动
        disableIOSBounces(['web_root_scroll']);
      }, 200);
    } catch (error) {
      console.log('error', error);
    }
  }, []);

  const firstScreenComponent = (
    <Fragment>
      <DetailList />
    </Fragment>
  );

  return <div id="app">{firstScreenComponent}</div>;
}

export default function (props: { ctx?: any; initialProps: any }) {
  return (
    <InitialDataProvider {...props}>
      <PageIndex {...props} />
    </InitialDataProvider>
  );
}
