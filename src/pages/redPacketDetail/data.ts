import { queryDetailData } from '@/api/redpacket';

export async function getInitialData() {
  let data: any = null;
  try {
    const [queryDetailRes] = await Promise.all([queryDetailData(1)]);    
    data = {
      detailData: queryDetailRes
    }
  } catch (e) {
    console.log(e);
    data = {}
  }

  return data;
}

export async function getAsyncSsrResult() {
  let pathname;
  const broPackId = window?.__wh_data__?.packId;

  if (DEV) {
    pathname = `/api/v1/ssr/async-fetch${location.pathname}?wh_page_only=true`;
  } else {
    const broccoliPathRegExp = /\/apps\/(\S*)\/routes\/(\S*)/;
    const [_, appCode, routeCode] = window.location.pathname.match(broccoliPathRegExp) || [];
    if (appCode && routeCode) {
      pathname = `/api/v1/ssr/async-fetch/${appCode}/${routeCode}`;
      if (broPackId) pathname += `?broPackId=${broPackId}`;
    }
  }

  const customDocUrl = window.location.href.replace(/skeletonMode/g, 'skeletonBak');

  const pageOnlyData = await fetch(pathname, {
    headers: {
      'custom-doc-url': customDocUrl,
    }
  }).then((res) => res.json());

  if (!DEV && (pageOnlyData?.data?.packId !== broPackId)) return {};

  return pageOnlyData?.data;
}
