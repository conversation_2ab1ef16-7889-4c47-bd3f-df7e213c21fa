import React from 'react';
import './index.scss';
import useMxState from '@/hooks/useMxState';
import { IRedPacketState } from '@/logic/store/models/redPacket/typings';

import { exit, openPage } from '@/lib/ucapi';
import config from '@/config';

import BackIcon from './images/back-icon.png';

export default function Index() {
  const [redPacket] = useMxState<IRedPacketState>('redPacket');
  const detailRedPacketInfo = redPacket?.detailRedPacketInfo?.detailData || [];
  // 返回
  const handleBack = () => {
    exit();
  };
  // 去提现
  const toWithdrawDeposit = () => {
    openPage(config.walletUrl);
  };
  return (
    <div className="head-comp">
      <img src={BackIcon} className="back-icon" onClick={handleBack} alt="返回" />
      <div className="title">红包明细</div>
      {detailRedPacketInfo?.length !== 0 && (
        <div className="withdraw-deposit" onClick={toWithdrawDeposit}>
          去提现
        </div>
      )}
    </div>
  );
}
