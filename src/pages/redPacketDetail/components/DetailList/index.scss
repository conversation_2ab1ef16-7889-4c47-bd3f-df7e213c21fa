.detail-list-copm {
  width: 100%;
  height: 100%;
  position: relative;
  
  .detail-wrap {
    width: 100%;
    height: calc(100% - 192rpx);
    overflow-y: auto;
    position: absolute;
    top: 192rpx;
    background: #f9f9f9;
    border-radius: 34rpx 34rpx 0 0;
    box-sizing: border-box;
    padding: 20rpx;
    padding-bottom: 122rpx;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .no-more-img {
    width: 206rpx;
  }

  .redPacket-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 21rpx 40rpx 24rpx 30rpx;
    margin-bottom: 14rpx;
    background-color: #fff;
    border: 1.5rpx solid #f2f2f2;
    border-radius: 24rpx;
    .redPacket-item-date {
      width: 90rpx;
      font-family: D-DIN-Bold;
      font-size: 25rpx;
      color: #969a9f;
      letter-spacing: -1rpx;
    }
    .redPacket-item-info {
      display: flex;
      flex: 1;
      align-items: center;
      .red-packet-icon {
        width: 87rpx;
        height: 99rpx;
        background-size: cover;
        margin-right: 8rpx;
      }
      .red-packet-title {
        font-family: PingFangSC-Semibold;
        font-size: 26rpx;
        color: #000000;
        line-height: 30rpx;
        font-weight: 600;
        margin-bottom: 5rpx;
      }
      .red-packet-status {
        font-family: PingFangSC-Semibold;
        font-size: 21rpx;
        color: #fe6723;
        letter-spacing: 0;
        line-height: 30rpx;
        font-weight: 600;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .redPacket-item-amount {
      margin-left: 30rpx;
      width: 140rpx;
      overflow: hidden;
      font-family: D-DIN-Bold;
      font-size: 48rpx;
      color: #1aaa34;
      letter-spacing: -1.5rpx;
      text-align: right;
    }
    .stale-dated {
      color: #969A9F !important;
    }
  }
}
