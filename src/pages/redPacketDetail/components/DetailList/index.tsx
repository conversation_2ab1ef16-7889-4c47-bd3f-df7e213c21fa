import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import Head from '../Head';
import Empty from '../Empty';
import RedpacketImg from './images/red-Packet-icon.png';
import RedpacketLoseImg from './images/red-Packet-lose.png';
import { disableIOSBounces } from '@/lib/ucapi';
import { formatTimestamp, calculateDaysBetweenDates } from '@/lib/utils/date';
import { queryDetailData } from '@/api/redpacket';
import { useInitialData } from '@ali/compass-app';
import useMxState from '@/hooks/useMxState';
import { IRedPacketDetailState, IRedPacketState } from '@/logic/store/models/redPacket/typings';
import InfiniteScroll from '@/pages/manureDetail/components/infiniteScroll';
import dispatch from '@/logic/store';
import { IRedPacketDetail, IRedPacketStatus } from '@/logic/store/models/app/typings';

const Index = () => {
  const { data } = useInitialData();
  const [redPacket] = useMxState<IRedPacketState>('redPacket');
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [isEmpty, setIsEmpty] = useState(false);
  const [findFirstPayOut, setFindFirstPayOut] = useState(-1);
  const pageNum = useRef(2);
  const detailData = redPacket?.detailRedPacketInfo?.detailData || [];
  useEffect(() => {
    if (data) {
      const hasData = !!data?.detailData?.length;
      setIsEmpty(!hasData);
    }
  }, [data]);

  useEffect(() => {
    const index = detailData.findIndex(
      (item) => item.state === IRedPacketStatus.PAYOUT_PRE || item.state === IRedPacketStatus.PAYOUT_CONFIRM,
    );
    setFindFirstPayOut(index);
  }, [detailData]);

  useEffect(() => {
    setTimeout(() => {
      // 禁用 iOS 弹性滚动
      disableIOSBounces(['scroll-box']);
    }, 300);
  }, []);

  const scrollLoadMore = () => {
    if (loading || !hasMore) {
      return;
    }
    setLoading(true);
    queryDetailData(pageNum.current).then((res) => {
      if (res?.length > 0) {
        const resp = (res ?? []).map((item, index) => {
          return {
            ...item,
            dataIndex: index + pageNum.current * 20,
          };
        });
        res?.length >= 20 ? (pageNum.current += 1) : setHasMore(false);
        dispatch.redPacket.set({
          detailRedPacketInfo: {
            detailData: [...detailData, ...resp],
          },
        });
      } else {
        setHasMore(false);
      }
      setLoading(false);
    });
  };

  const listenThrottle = scrollLoadMore;

  const keepDecimal = (value, decimal: number) => {
    let num = value?.toString();
    const index = num?.indexOf('.');
    if (index !== -1) {
      num = num.substring(0, decimal + index + 1);
    }
    return parseFloat(num)?.toFixed(decimal);
  };
  const conversionMoney = (value) => {
    if (!value) return 0;
    let money = Number(value) / 100;
    return keepDecimal(money, 2);
  };
  // 获取状态文案
  const getStateText = (item: IRedPacketDetailState) => {
    let time = formatTimestamp(item?.expireTime, 'YYYY/MM/DD hh:mm');
    switch (item.state) {
      case IRedPacketStatus.INCOME_CONFIRM:
        return `${time}后失效`;
      case IRedPacketStatus.PAYOUT_PRE:
        return item.appId === 'expired' ? '已过期' : '提现中';
      case IRedPacketStatus.PAYOUT_CONFIRM:
        return item.appId === 'expired' ? '已过期' : '提现到账';
      case IRedPacketStatus.PAYOUT_CANCEL:
        return '提现失败';
      case IRedPacketStatus.INCOME_EXPIRE:
        return '已过期';
      case IRedPacketStatus.INCOME_USED:
        return '已提现';
      case IRedPacketStatus.INCOME_USING:
        return '提现中';
      default:
        return '';
    }
  };

  const getStateTitle = (item: IRedPacketDetailState) => {
    return item.title;
  };

  // 红包是否已提现或者失效
  const redPackLose = (item: IRedPacketDetailState) => {
    switch (item.state) {
      case IRedPacketStatus.INCOME_CONFIRM:
      case IRedPacketStatus.INCOME_USING:
        return true;
      case IRedPacketStatus.INCOME_USED:
      case IRedPacketStatus.PAYOUT_PRE:
      case IRedPacketStatus.PAYOUT_CONFIRM:
      case IRedPacketStatus.PAYOUT_CANCEL:
      case IRedPacketStatus.INCOME_EXPIRE:
      default:
        return false;
    }
  };
  return (
    <div className="detail-list-copm">
      <Head />
      <div className="detail-wrap">
        <div className="list-wrap" id="scroll-box">
          {isEmpty ? (
            <Empty />
          ) : (
            <React.Fragment>
              {detailData?.map((item, index) => {
                return (
                  <div key={index} className="redPacket-item">
                    <div className="redPacket-item-date">{formatTimestamp(item?.createTime, 'MM-DD')}</div>
                    <div className="redPacket-item-info">
                      <img
                        className="red-packet-icon"
                        src={redPackLose(item) ? RedpacketImg : RedpacketLoseImg}
                        alt=""
                      />
                      <div>
                        <div className="red-packet-title">{getStateTitle(item)}</div>
                        <div className={`red-packet-status ${redPackLose(item) ? '' : 'stale-dated'}`}>
                          {getStateText(item)}
                        </div>
                      </div>
                    </div>
                    <div className={`redPacket-item-amount ${redPackLose(item) ? '' : 'stale-dated'}`}>
                      {item?.state === 'PAYOUT_CONFIRM' ? '-' : '+'}
                      {conversionMoney(item?.amount)}
                    </div>
                  </div>
                );
              })}
              {!!detailData?.length && <InfiniteScroll loadMore={listenThrottle} hasMore={hasMore} />}
            </React.Fragment>
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
