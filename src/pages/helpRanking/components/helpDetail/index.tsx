import React from 'react';
import './index.scss';
import ScrollList from '@/components/common/ScrollList';
import useMxState from '@/hooks/useMxState';
import { EInviteTitleType, IInviteScore } from '@/api/helpRanking/typings';
import { IFarmHelpPlantHomeResponse } from '@/logic/store/models/farmHelpPlant/type';
import { getPrevious30DaysTimestamp } from '../../utils';
import { IUserState } from '@/logic/store/models/user/typings';
import { useInfinite } from '@/hooks/useInfinite';
import tracker from '@/lib/tracker';
import { getErrorInfo } from '@/logic/store/models/utils';
import { queryScoreList } from '@/api/helpRanking';
import { formatTimestamp } from '@/lib/utils/date';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';

function formatTimestampToMMDD(timestamp) {
  const date = new Date(timestamp);
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${month}-${day}`;
}

function groupByDate(originalArray) {
  const groups = {};
  originalArray.forEach((item) => {
    const dateKey = formatTimestampToMMDD(item?.createTime);
    if (!groups[dateKey]) {
      groups[dateKey] = {
        date: dateKey,
        list: [],
      };
    }
    groups[dateKey].list.push(item);
  });
  return Object.values(groups);
}

const PAGE_SIZE = 20;

export default function Index() {
  const [scoreList] = useMxState<IInviteScore[]>('helpRanking.scoreList', []);
  const [rankingHome] = useMxState<IFarmHelpPlantHomeResponse>('helpRanking.rankingHome', {});
  const [userInfo] = useMxState<IUserState>('user', {});
  const fetchScoreList = async (params: { pageNum: number; pageSize: number }) => {
    const queryScoreListMonitor = tracker.Monitor(172);
    try {
      const prev30Timestamp = getPrevious30DaysTimestamp(rankingHome?.curTime || Date.now())
      const startTimestamp = rankingHome?.frontData?.activityStartTime || Date.now()
      const queryDate = prev30Timestamp >= startTimestamp ? prev30Timestamp : startTimestamp;
      const kps = userInfo.kps || '';
      const res = await queryScoreList({ kps, queryCutOffTime: queryDate || 0, ...params });
      queryScoreListMonitor.success({
        msg: '查询成功',
        bl1: JSON.stringify(res),
      });
      return res;
    } catch (e) {
      const { errCode } = getErrorInfo(e);
      queryScoreListMonitor.fail({
        msg: '查询失败',
        c1: errCode,
        bl1: JSON.stringify(e),
      });
      return [];
    }
  };

  const { data, loadMore, loading } = useInfinite({
    fetchNextPage: fetchScoreList,
    initData: scoreList,
    initPageNum: 2,
    initPageSize: PAGE_SIZE,
  });


  const handleTouchScroll = () => {
    stat.click('share_helprecords_click', {
      c: 'target',
      d: 'help',
      click_position: 1,
    });
  };
  return (
    <Fact noUseClick c="activity" d="help" expoLogkey="bbz_helprecords_exposure" className="help-detail-comp">
      <div className="list-wrap row">
        <ScrollList
          onTouchScroll={handleTouchScroll}
          list={groupByDate(data)}
          isEmpty={data.length === 0}
          emptyText={'还未有好友成功助力噢！'}
          loadMore={loadMore}
          hasMore
          loading={loading}
          wrapClassName={'help-list-wrap'}
          renderItem={(item) => {
            return (
              <div className="list-item-wrap" key={item?.date}>
                <div className="date-title">{item?.date}</div>
                <div className="sub-list-wrap">
                  {item?.list?.map((subItem, subIndex) => {
                    const hasExtra = subItem?.extra;
                    const extra = hasExtra ? JSON.parse(subItem?.extra) : {};
                    const isAssist = extra.title_type === EInviteTitleType.ASSIST;
                    const isInvite = extra.title_type === EInviteTitleType.INVITE;
                    return (
                      <div className="sub-list-item row" key={subItem.createTime || subIndex}>
                        <div className="left-wrap row">
                          <div className="time">
                            {subItem?.createTime ? formatTimestamp(subItem?.createTime, 'hh:mm') : ''}
                          </div>
                          {isAssist && <div className="tip-text">我助力了</div>}
                          <div className="name">{(hasExtra ? extra?.display_title : subItem.title) || '好友'}</div>
                          {isInvite && <div className="tip-text tip-right">助力了我</div>}
                        </div>
                        <div className="right-wrap row">
                          <span className="value">+{subItem?.amount}</span>
                          <span className="text">助力值</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          }}
        />
      </div>
    </Fact>
  );
}
