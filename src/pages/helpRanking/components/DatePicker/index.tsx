import React, { useCallback, useMemo } from 'react';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import Picker, { PickerRef } from '@/components/common/Picker';
import { PickerColumnOption } from '@/components/common/Picker/types';
import './index.scss';

dayjs.extend(isSameOrBefore);

interface DatePickerProps {
  visible: boolean;
  value?: string; // YYYY-MM-DD format
  onClose?: () => void;
  onConfirm?: (selectedRow: PickerColumnOption, index: number) => void;
  curTime?: number;
  startTime?: number;
}


const DatePicker: React.FC<DatePickerProps> = ({ visible, value, onClose, onConfirm, curTime, startTime }) => {
  const ref = React.useRef<PickerRef | null>(null);

  const isValidDate = useCallback((date: dayjs.Dayjs) => {
    if (!startTime) return false
    if (!date) return true
    try {
      const start = dayjs(startTime ?? 0);
      const target = dayjs(date);
      return target.isValid() && start.isValid() && (target.isAfter(start) || target.isSame(start, 'day'));
    } catch {
      return false;
    }
  }, [startTime]);
  const dateOptions = useMemo(() => {
    const options: PickerColumnOption[] = [];
    const today = dayjs(curTime) || dayjs();
    const lastMonth = today.subtract(1, 'month');
    let current = lastMonth;
    while (current.isSameOrBefore(today, 'day')) {
      options.unshift({
        // 使用 unshift 让最近的日期在底部
        text: `${current.month() + 1}月${current.date()}日`,
        value: current.format('YYYY-MM-DD'),
        valueKey: current.format('YYYY-MM-DD'),
        textKey: `${current.month() + 1}月${current.date()}日`,
      });
      current = current.add(1, 'day');
    }
    return options.filter(item => {
      const date = dayjs(item.value);
      return isValidDate(date);
    });
  }, [curTime, isValidDate]);
  const handleConfirm = (_val: string, selectedRow: PickerColumnOption, index: number) => {
    onConfirm?.(selectedRow, index);
  };
  const renderFooter = () => {
    return <div className="date-picker-footer">仅保留活动上线后近30天的记录查询</div>;
  };
  return (
    <Picker
      renderFooter={renderFooter}
      ref={ref}
      visible={visible}
      value={value}
      columns={dateOptions}
      onClose={onClose}
      onConfirm={handleConfirm}
    />
  );
};

export default DatePicker;
