import React from 'react';
import './index.scss';

import { exit } from '@/lib/ucapi';

import BackIcon from '@/pages/index/components/NavBack/assets/<EMAIL>';
import stat from '@/lib/stat';

export default function Index() {
  const handleBack = () => {
    stat.click('share_rankingrecords_click', {
      c: 'target',
      d: 'rank',
      click_position: 4,
    });
    exit()
  };

  return (
    <div className="head-comp">
      <img src={BackIcon} className="back-icon" onClick={handleBack} alt="返回" />
    </div>
  )
}
