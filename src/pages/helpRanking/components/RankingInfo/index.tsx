import React from 'react';
import './index.scss';

import UserActivitySummary from './UserActivitySummary';
import RankingList from './RankingList';
import Fact from '@/components/Fact';

export default function Index() {
  return (
    <Fact
      noUseClick
      c="activity"
      d="rank"
      expoLogkey="bbz_rankingrecords_exposure"
      className="ranking-info-comp row"
    >
      <UserActivitySummary />
      <RankingList />
    </Fact>
  );
}
