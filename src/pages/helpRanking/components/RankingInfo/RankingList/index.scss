.ranking-list-comp{
  margin-top: 15rpx;
  width: 720rpx;
  height: calc(100% - 469rpx);
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;

  .list-wrap{
    width: 100%;
    height: 100%;
    padding-top: 32rpx;
    box-sizing: border-box;
    .list-head{
      padding-left: 64rpx;
      padding-right: 64rpx;
      height: 65rpx;
      display: flex;
      align-items: center;
       span {
        display: inline-block;
        font-family: PingFangSC-Semibold;
        font-size: 26rpx;
        color: #444849;
        font-weight: 600;
       }
      .title-one{
        width: 280rpx;
        text-align: left;
      }
      .title-two{
        flex: 1;
        text-align: right;
      }

      .title-three{
        text-align: right;
        width: 168rpx;
      }
    }

   .ranking-list-wrap{
      height: calc(100% - 104rpx);
   }
  }
}
