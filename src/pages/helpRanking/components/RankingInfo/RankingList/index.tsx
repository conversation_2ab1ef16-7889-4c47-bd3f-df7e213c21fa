import React, { useEffect, useRef } from 'react';
import './index.scss';

import { customSeparator } from '@/lib/utils/formatNumber';
import ScrollList from '@/components/common/ScrollList';
import useMxState from '@/hooks/useMxState';
import { IQueryRankHistory } from '@/mockData/queryRankHistory';
import dayjs from 'dayjs';
import { useInfinite } from '@/hooks/useInfinite';
import { IUserState } from '@/logic/store/models/user/typings';
import tracker from '@/lib/tracker';
import { getErrorInfo } from '@/logic/store/models/utils';
import { queryRankHistory } from '@/api/helpRanking';
import stat from '@/lib/stat';

type RankingItem = IQueryRankHistory['rankingInfo']['rankings'][number];
const PAGE_SIZE = 20;
const MAX_RANKINGS = 100;

export default function Index() {
  const [rankingHistory] = useMxState<IQueryRankHistory>('helpRanking.rankingHistory', {});
  const [rankings = []] = useMxState<RankingItem>('helpRanking.rankings', []);
  const [displayRankNum] = useMxState<RankingItem>('helpRanking.displayRankNum');
  const [userInfo] = useMxState<IUserState>('user');
  const queryDateRef = useRef(rankingHistory?.rankingInfo?.startTime)
  const total = useRef(0);
  const maxRankNum = displayRankNum || MAX_RANKINGS
  
  const fetchRankHistory = async (params: { pageNum: number; pageSize: number }) => {
    const queryRankHistoryMonitor = tracker.Monitor(173);
    try {
      const kps = userInfo.kps || '';
      const curTime = queryDateRef.current
      const queryDate = dayjs(new Date(curTime || new Date().getTime())).format('YYYYMMDD');
      const res = await queryRankHistory({
        kps,
        queryDate,
        ...params,
      });
      total.current = res.rankingInfo.totalNumber || 0;
      queryRankHistoryMonitor.success({
        msg: '查询成功',
        bl1: JSON.stringify(res),
      });
      return res?.rankingInfo?.rankings || [];
    } catch (e) {
      const { errCode } = getErrorInfo(e);
      queryRankHistoryMonitor.fail({
        msg: '查询失败',
        c1: errCode,
        bl1: JSON.stringify(e),
      });
      return [];
    }
  };

  const { reset, data, loadMore, loading } = useInfinite({
    buildVersionKeyFn: (arr) => arr?.[0]?.rankingVersion,
    fetchNextPage: fetchRankHistory,
    initData: rankings,
    initPageNum: 2,
    initPageSize: PAGE_SIZE,
    maxRange: maxRankNum,
    version: rankingHistory?.rankingInfo?.rankings?.[0]?.rankingVersion || 0,
    needCheckVersion: true,
    total: total.current,
  });

  useEffect(() => {
    reset(rankingHistory?.rankingInfo?.rankings || []);
    queryDateRef.current = rankingHistory?.rankingInfo?.startTime
    total.current = rankingHistory?.rankingInfo?.totalNumber || 0;
  }, [rankingHistory]);

  const handleTouchScroll = () => {
    stat.click('share_rankingrecords_click', {
      c: 'target',
      d: 'rank',
      click_position: 1,
    });
  }
  return (
    <div className="ranking-list-comp help-ranking-page-ranking-list">
      <div className="list-wrap">
        <div className="list-head">
          <span className="title-one">当日助力排名</span>
          <span className="title-two">助力值</span>
          <span className="title-three">发放奖励</span>
        </div>
        {
          <ScrollList
            onTouchScroll={handleTouchScroll}
            list={data}
            isEmpty={data.length === 0}
            emptyText={'还未有记录哦，明日再来看吧'}
            loadMore={loadMore}
            hasMore={data.length < rankingHistory?.rankingInfo?.totalNumber}
            loading={loading}
            wrapClassName={'ranking-list-wrap'}
            noMoreText={`- 仅展示前${maxRankNum}名用户 -`}
            renderItem={(item, index) => {
              const amount = item?.prizeItems?.[0]?.prizeAmount;
              return (
                <div className={`list-item ${index < 3 ? `top-${index + 1}` : ''}`} key={index}>
                  <div className="rank-num gilroy-num">{index + 1}</div>
                  <div className={`rank-name ${item.own ? 'own-color' : ''}`}>
                    {item.own ? '(我)' : ''}
                    {item.nickname}
                  </div>
                  <div className="rank-value">{customSeparator(item?.score)}</div>
                  <div className="rank-award">
                    <span className="unit">￥</span>
                    <span>{(amount || 0) / 100}</span>
                  </div>
                </div>
              );
            }}
          />
        }
      </div>
    </div>
  );
}
