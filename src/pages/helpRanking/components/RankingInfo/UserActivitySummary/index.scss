.profile-info-comp {
  width: 720rpx;
  height: 454rpx;
  display: flex;
  flex-direction: column;
  padding: 30rpx 32rpx 25rpx 32rpx;
  border-radius: 30rpx;
  background-color: #fff;
  box-sizing: border-box;

  .profile-section {
    display: flex;
    flex-direction: row;
    margin-left: 24rpx;

    .userInfo-container {
      display: flex;
      flex-direction: column;
      align-self: center;
    }

    .user-details-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      align-self: flex-start;

      .user-avatar {
        flex-shrink: 0;
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
      }

      .user-name {
        margin-left: 21rpx;
        white-space: nowrap;
        color: #12161a;
        font-size: 36rpx;
        font-weight: bold;
        width: 350rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    .date-wrap {
      height: 40rpx;
      display: flex;
      align-items: center;
      margin-left: 10rpx;
    }

    .date-text {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #12161a;
      font-weight: 400;
    }

    .select-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .today-stats-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 14rpx;

    .help-wrap,
    .rank-wrap {
      display: flex;
      flex-direction: column;
      width: 328rpx;
      height: 142rpx;
      padding: 25rpx 0;
      box-sizing: border-box;
      .value {
        margin-top: 6rpx;
        font-size: 32rpx;
        font-weight: 600;
        font-family: PINGFangSC-Semibold;
        color: #12161a;
      }
      .bold-value {
        max-width: 300rpx;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-top: 6rpx;
        font-family: D-DIN-Bold;
        font-size: 52rpx;
        color: #12161a;
      }
    }
  }

  .label-text {
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #525e66;
    font-weight: 400;
  }

  .award-info {
    width: 656rpx;
    height: 142rpx;
    background: rgba(224, 241, 227, 0.28);
    border-radius: 24rpx;

    .award-item {
      width: 328rpx;
      height: 100%;
      padding-top: 32rpx;
      padding-left: 28rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .award-value {
        display: flex;
        align-items: center;
        margin-top: 6rpx;
        margin-left: -5rpx;
        .award-icon {
          margin-right: 4rpx;
          width: 40rpx;
          height: 40rpx;
          object-fit: contain;
        }

        img,
        span {
          display: block;
        }

        .award-info-value {
          font-family: D-DIN-Bold;
          font-size: 36rpx;
          color: #12161a;
          max-width: 300rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .cash-value {
          color: #f7534f;
          letter-spacing: -0.64rpx;
          max-width: 200rpx;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }

        .unit {
          font-family: PingFangSC-Semibold;
          font-size: 22rpx;
          color: #f7534f;
          font-weight: 600;
        }
      }
    }

    .right-line {
      position: relative;
      &::after {
        content: '';
        width: 1rpx;
        height: 78rpx;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
