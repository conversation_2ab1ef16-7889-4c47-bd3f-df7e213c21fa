import React, { useState } from 'react';
import './index.scss';
import ArrowMore from '@/assets/arrows-more.png';
import AvatarDefault from '@/assets/uu_default.png';
import DatePicker from '../../DatePicker';
import dayjs from 'dayjs';
import { PickerColumnOption } from '@/components/common/Picker/types';
import fertilizerIcon from './images/fertilizer-icon.png';
import redPacketIcon from './images/redPacket-icon.png';
import useMxState from '@/hooks/useMxState';
import { IFarmHelpPlantHomeResponse } from '@/logic/store/models/farmHelpPlant/type';
import { IQueryRankHistory } from '@/mockData/queryRankHistory';
import dispatch from '@/logic/store';
import { queryRankHistory } from '@/api/helpRanking';
import { IUserState } from '@/logic/store/models/user/typings';
import tracker from '@/lib/tracker';
import { getErrorInfo } from '@/logic/store/models/utils';
import stat from '@/lib/stat';

export default function UserActivitySummary() {
  const [rankingHistory] = useMxState<IQueryRankHistory>('helpRanking.rankingHistory', {});
  const [rankingHome] = useMxState<IFarmHelpPlantHomeResponse>('helpRanking.rankingHome', {});
  const [userInfo] = useMxState<IUserState>('user');

  const buildPriceAmount = (
    prizeItems: Array<{
      prizeName: string;
      prizeIcon: string;
      prizeMark: string;
      prizeAmount: number;
      prizeType: string;
    }>,
  ) => {
    if (!prizeItems) return 0;
    const amount =
      prizeItems
        .filter((item) => item.prizeMark === 'cash')
        .reduce((pre, cur) => {
          return pre + cur.prizeAmount;
        }, 0) || 0;
    return amount;
  };
  const prizeItems = [
    {
      name: '肥料奖励',
      icon: fertilizerIcon,
      value: rankingHistory?.totalAward ?? 0,
    },
    // {
    //   name: '抽奖券奖励',
    //   icon: ticketIcon,
    //   value: rankingHome.rankingAward,
    // },
    {
      name: '红包奖励',
      icon: redPacketIcon,
      value: buildPriceAmount(rankingHistory?.rankingInfo?.myInfo?.prizeItems) / 100,
    },
  ];
  const previousDayTimestamp = dayjs(rankingHome?.curTime).subtract(1, 'day');
  const [visible, setVisible] = useState(false);
  const [date, setDate] = useState(previousDayTimestamp);

  const handleSelectDate = async (column: PickerColumnOption) => {
    setVisible(false);
    if (!column || (column && !column.value)) return;
    column.value && setDate(dayjs(column.value));
    const queryRankHistoryMonitor = tracker.Monitor(173);
    try {
      const kps = userInfo.kps || '';
      const res = await queryRankHistory({ kps, pageNum: 1, queryDate: dayjs(column.value).format('YYYYMMDD') });
      const isEmptyRes = rankingHistory?.rankingInfo?.myInfo && !res?.rankingInfo?.myInfo;
      const mergedMyInfo = !isEmptyRes
        ? res.rankingInfo.myInfo
        : {
          nickname: rankingHistory?.rankingInfo?.myInfo?.nickname,
          avatar: rankingHistory?.rankingInfo?.myInfo?.avatar,
        };

      dispatch.helpRanking.set({
        rankings: res?.rankingInfo?.rankings || [],
        rankingHistory: {
          ...res,
          rankingInfo: {
            ...res.rankingInfo,
            myInfo: mergedMyInfo as any,
          },
        },
        displayRankNum: res?.rankingInfo?.displayRankNum || 100
      });
      queryRankHistoryMonitor.success({
        msg: '查询成功',
        bl1: JSON.stringify(rankingHistory),
      });
    } catch (e) {
      const { errCode } = getErrorInfo(e);
      queryRankHistoryMonitor.fail({
        msg: '查询失败',
        c1: errCode,
        bl1: JSON.stringify(e),
      });
    }
  };
  const dateFormat = (day: dayjs.Dayjs) => {
    return `${day.month() + 1}月${day.date()}日`;
  };
  const handleClickDate = () => {
    setVisible(true)
    stat.click('share_rankingrecords_click', {
      c: 'target',
      d: 'rank',
      click_position: 5,
    })
  }
  return (
    <div className="profile-info-comp">
      <DatePicker
        startTime={rankingHome?.frontData?.activityStartTime}
        curTime={previousDayTimestamp?.valueOf()}
        value={dayjs(date).format('YYYY-MM-DD')}
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={handleSelectDate}
      />
      <div className="profile-section">
        <div className="userInfo-container">
          <div className="user-details-row">
            <img
              className="user-avatar"
              src={rankingHistory?.rankingInfo?.myInfo.avatar || userInfo.avatar || AvatarDefault}
            />
            <span className="user-name">
              {rankingHistory?.rankingInfo?.myInfo.nickname || userInfo.nickname || '-'}
            </span>
            <div className="date-wrap" onClick={handleClickDate}>
              <span className="date-text">{dateFormat(date)}</span>
              <img src={ArrowMore} className="select-icon" />
            </div>
          </div>
          <div className="today-stats-section">
            <div className="help-wrap">
              <span className="label-text">当日助力值</span>
              <span className="bold-value">{rankingHistory?.rankingInfo?.myInfo?.score || '0'}</span>
            </div>
            <div className="rank-wrap">
              <span className="label-text">当日排名</span>
              {rankingHistory?.rankingInfo?.myInfo?.ranking ? (
                <span className="bold-value">{rankingHistory?.rankingInfo?.myInfo?.ranking}</span>
              ) : (
                <span className="value">{'未上榜'}</span>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="award-info row">
        {prizeItems?.map((item, index) => {
          const isLast = prizeItems?.length === index + 1;
          return (
            <div className={`award-item ${!isLast ? 'right-line' : ''}`} key={index}>
              <span className="label-text">{item.name}</span>
              <div className="award-value">
                <img src={item.icon} className="award-icon" alt="" />
                <span className={`award-info-value ${isLast ? 'cash-value' : ''}`}>
                  {typeof item.value === 'number' ? item.value : '-'}
                </span>
                {isLast && <span className="unit">元</span>}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
