import React, { useState } from 'react';
import './index.scss';
import RankingInfo from '../RankingInfo';
import HelpDetail from '../helpDetail';
import stat from '@/lib/stat';

enum ETAB_STAT {
  rank = '2',
  detail = '3',
}
export default function Tab() {
  const [tab, setTab] = useState<'rank' | 'detail'>('rank');

  const handleTab = (item: 'rank' | 'detail') => {
    stat.click('share_rankingrecords_click', {
      c: 'target',
      d: 'rank',
      click_position: ETAB_STAT[item],
    });
    setTab(item);
  };

  return (
    <div className="tab-comp row">
      <div className="tab-wrap row">
        <div className={`tab-item ${tab === 'rank' ? 'tab-active' : ''}`} onClick={() => handleTab('rank')}>
          每日榜单
        </div>
        <div className={`tab-item ${tab === 'detail' ? 'tab-active' : ''}`} onClick={() => handleTab('detail')}>
          助力明细
        </div>
      </div>
      <div className="tab-content">
        {tab === 'rank' && <RankingInfo />}
        {tab === 'detail' && <HelpDetail />}
      </div>
    </div>
  );
}
