import React, { Fragment, useEffect } from 'react';
import { InitialDataProvider, useInitialData } from '@ali/compass-app';
import { isWeb } from '@ali/compass-env';
import './index.scss';
import '@/common.scss';
import initClient from '@/lib/utils/init-client';
import { FactConfig } from '@/config/fact';
import dispatch from '@/logic/store';
import { gestureDisabled, disableIOSBounces, forbiddenToolbar } from '@/lib/ucapi';
import { disabledPinchToZoom, landscapeBan } from '@/lib/utils/fn';
import tracker from '@/lib/tracker';
import { mx } from '@ali/pcom-iz-use';
import Head from './components/Head';
import Tab from './components/Tab';
import { userInfoHandler } from '@/pages/index/utils';
import { getParam } from '@/lib/qs';
import stat from '@/lib/stat';

initClient(FactConfig.helpRankingConfig);
if (isWeb) {
  const logonline = require('@ali/logonline');
  logonline.init();
}

export const isValidDetailData = (data: any) => {
  return data?.scoreList && data?.rankingHome && data?.rankingHistory;
};

function PageIndex(props: any) {
  const { isLoading, data } = useInitialData();
  // 首屏数据
  useEffect(() => {
    const validInitialData = isValidDetailData(data);
    if (!isLoading) {
      if (data?.userInfo?.kps_wg) {
        dispatch.user.set(userInfoHandler(data?.userInfo));
      }
      if (validInitialData) {
        dispatch.helpRanking.set({
          scoreList: data?.scoreList,
          rankingHome: {
            ...data?.rankingHome,
            curTime: data?.rankingHome?.curTime || Date.now(),
          },
          rankingHistory: data?.rankingHistory,
          rankings: data?.rankings,
          displayRankNum: data?.rankingHistory?.rankingInfo?.displayRankNum || 100,
        });
        
        tracker.log({
          category: 171, // 系统自动生成，请勿修改
          msg: '榜单首屏请求成功', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: getParam('entry') || '',
          c2: `${!!data.userInfo?.kps_wg}`,
        });
        // 重试
      } else {
        dispatch.helpRanking.regetData();
        tracker.log({
          category: 171, // 系统自动生成，请勿修改
          msg: '榜单首屏请求重试', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: getParam('entry') || '',
          c2: `${!!data.userInfo?.kps_wg}`,
          bl1: JSON.stringify(data)
        });
      }
      setTimeout(() => {
        statPv()
      }, 0);
    }
  }, [isLoading, data]);

  // pv 上报
  const statPv = () => {
    const { rankingHome = {}, rankingHistory } = mx.store.getStore().helpRanking;
    let assist_rank =
    rankingHistory?.myInfo?.ranking > 0 ? rankingHistory?.rankingInfo?.myInfo?.ranking : rankingHistory?.rankingInfo?.myInfo?.newestScore > 0 ? 0 : '-1';
    stat.updateParam({
      from: getParam('from') ?? 'unknown',
      entry: getParam('entry') ?? 'entry',
      page_status: rankingHome?.ucFarmHasAuth ? '1' : '0',
      bbnc_activity: 'bbzhong',
      assist_value: rankingHistory?.rankingInfo?.myInfo?.newestScore,
      assist_rank,
    });
    stat.pv(FactConfig.helpRankingConfig.page);
  };

  useEffect(() => {
    try {
      gestureDisabled();
      disabledPinchToZoom();
      forbiddenToolbar();
      landscapeBan();
      setTimeout(() => {
        // 禁用 iOS 弹性滚动
        disableIOSBounces(['web_root_scroll']);
      }, 200);
    } catch (error) {
      console.log('error', error);
    }
  }, []);

  const firstScreenComponent = (
    <Fragment>
      <Head />
      <Tab />
    </Fragment>
  );

  return <div id="app">{firstScreenComponent}</div>;
}

export default function (props: { ctx?: any; initialProps: any }) {
  return (
    <InitialDataProvider {...props}>
      <PageIndex {...props} />
    </InitialDataProvider>
  );
}
