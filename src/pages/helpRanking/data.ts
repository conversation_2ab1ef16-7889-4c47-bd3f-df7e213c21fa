import { queryHelpRankingHome, queryRankHistory, queryScoreList } from '@/api/helpRanking';
import { getUserInfo } from '@/lib/ucapi';
import { getPrevious30DaysTimestamp } from './utils';
import { formatTimestamp } from '@/lib/utils/date';

export async function getInitialData() {
  const userInfo: any = await getUserInfo();
  const kps = userInfo?.kps_wg || '';
  let data: any = null;
  try {
    const ONE_DAY_MS = 24 * 60 * 60 * 1000;
    const rankingHome = await queryHelpRankingHome({ kps });
    const timestamp = Date.now()
    const queryDate = formatTimestamp((rankingHome?.curTime || timestamp) - ONE_DAY_MS, 'YYYYMMDD');
    // 数据记录保存30天
    const queryCutOffTime = getPrevious30DaysTimestamp(rankingHome?.curTime || timestamp);
    const [rankingHistory, scoreList] = await Promise.all([
      queryRankHistory({ kps, pageNum: 1, queryDate: queryDate }),
      queryScoreList({ kps, pageNum: 1, queryCutOffTime: queryCutOffTime || 0 }),
    ]);
    data = {
      userInfo,
      scoreList, // 助力明细列表
      rankingHome, // 助力首页数据，助力值等
      rankingHistory, // 每日排行榜
      rankings: rankingHistory?.rankingInfo?.rankings || [],
    };
  } catch (e) {
    console.log(e);
    data = {};
  }
  return data;
}

export async function getAsyncSsrResult() {
  let pathname;
  const broPackId = window?.__wh_data__?.packId;

  if (DEV) {
    pathname = `/api/v1/ssr/async-fetch${location.pathname}?wh_page_only=true`;
  } else {
    const broccoliPathRegExp = /\/apps\/(\S*)\/routes\/(\S*)/;
    const [_, appCode, routeCode] = window.location.pathname.match(broccoliPathRegExp) || [];
    if (appCode && routeCode) {
      pathname = `/api/v1/ssr/async-fetch/${appCode}/${routeCode}`;
      if (broPackId) pathname += `?broPackId=${broPackId}`;
    }
  }

  const customDocUrl = window.location.href.replace(/skeletonMode/g, 'skeletonBak');

  const pageOnlyData = await fetch(pathname, {
    headers: {
      'custom-doc-url': customDocUrl,
    },
  }).then((res) => res.json());

  if (!DEV && pageOnlyData?.data?.packId !== broPackId) return {};

  return pageOnlyData?.data;
}
