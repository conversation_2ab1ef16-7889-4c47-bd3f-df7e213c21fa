// import { queryDetailData } from '@/api/detail';
import { queryHelpPlantHome } from "@/api/helpPlant";
import { getUserInfo } from "@/lib/ucapi";
import { queryByMultiResource } from "@/api/resource";
import config from "@/config";

export async function getInitialData() {
  const userInfo: any = await getUserInfo();
  const kps = userInfo?.kps_wg || '';
  let data
  try {
    const [multiResource, homeData] = await Promise.all([
      queryByMultiResource(config.bbzResourceCode, kps),
      queryHelpPlantHome(kps),
    ]);    
    data = {
      userInfo,
      multiResource,
      homeData,
    }
  } catch (e) {
    console.log(e);
    data = {}
  }
  return data;
}

export async function getAsyncSsrResult() {
  let pathname;
  const broPackId = window?.__wh_data__?.packId;

  if (DEV) {
    pathname = `/api/v1/ssr/async-fetch${location.pathname}?wh_page_only=true`;
  } else {
    const broccoliPathRegExp = /\/apps\/(\S*)\/routes\/(\S*)/;
    const [_, appCode, routeCode] = window.location.pathname.match(broccoliPathRegExp) || [];
    if (appCode && routeCode) {
      pathname = `/api/v1/ssr/async-fetch/${appCode}/${routeCode}?uc_param_str=dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&webCompassApp=true&compass_params=name%3Aucbabafarm&$kps_info`;
      if (broPackId) pathname += `&broPackId=${broPackId}`;
    }
  }

  const customDocUrl = window.location.href.replace(/skeletonMode/g, 'skeletonBak');

  const pageOnlyData = await fetch(pathname, {
    headers: {
      'custom-doc-url': customDocUrl,
    }
  }).then((res) => res.json());

  if (!DEV && (pageOnlyData?.data?.packId !== broPackId)) return {};

  return pageOnlyData?.data;
}
