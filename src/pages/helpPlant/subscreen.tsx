import React, {Fragment, useEffect} from 'react';
import { ShareBase, ShareComponent } from '@ali/act-base-share';
import stat from '@/lib/stat';
import tracker from '@/lib/tracker';
import useMxState from '@/hooks/useMxState';
import { IShareState } from '@/logic/store/models/share';
import '@ali/act-base-share/lib/css/index.css';
import ModalAll from '@/components/modals/ModalAll';
import { defaultConfig } from '@/logic/store/models/share/utils';

export default function SubScreen() {
  const [share] = useMxState<IShareState>('share');
  useEffect(() => {
    ShareBase.init({
      sharePlanId: 'motr0ca5x0xm',
      installFunction: { stat, tracker, trackerCategory: 113 },
      shareConfig: defaultConfig
    });
  }, []);

  useEffect(() => {
    if (share.shareConfig) {
      ShareBase.updateConfig(share.shareConfig);
    }
  }, [share.shareConfig]);
  return (
    <Fragment>
      <ModalAll />
      <ShareComponent />
    </Fragment>
  )
}
