import React, { Fragment, useEffect } from 'react';
import { InitialDataProvider, useInitialData } from '@ali/compass-app';
import { isWeb } from '@ali/compass-env';
import './index.scss';
import tracker from '@/lib/tracker';
import initClient from '@/lib/utils/init-client';
import { FactConfig } from '@/config/fact';
import dispatch from '@/logic/store';
import { gestureDisabled, disableIOSBounces, forbiddenToolbar, ucparams, getUserInfo } from '@/lib/ucapi';
import { disabledPinchToZoom, landscapeBan } from '@/lib/utils/fn';
import SubScreen from './subscreen';
import stat from '@/lib/stat';

import Header from './components/Header';
import IntraDayRankingList from './components/IntraDayRankingList';
import MainInviteBtn from './components/MainInviteBtn';
import Tips from './components/Tips';
import { userInfoHandler } from '@/pages/index/utils';
import { getParam } from '@/lib/qs';
import { mx } from '@ali/pcom-iz-use';
import { setItracePageUuid } from '@/lib/utils/itrace-meta';

initClient(FactConfig.helpPlantConfig);
if (isWeb) {
  const logonline = require('@ali/logonline');
  logonline.init();
}

export const isValidDetailData = (data: any) => {
  return data?.homeData && data.multiResource
};

const setUserInfo = async (payLoad?: any) => {
  const userInfo = payLoad || await getUserInfo()
  dispatch.user.set(userInfoHandler(userInfo));
}
// 首屏请求异常重试
const regetData = async () => {
  await setUserInfo();
  dispatch.helpPlant.updatePageData(true);
}

function PageIndex(props: any) {
  const { isLoading, data } = useInitialData();
  const validInitialData = isValidDetailData(data);
  useEffect(() => {
    setItracePageUuid(data?.multiResource?.x_wpk_traceid ?? '');
    const initSubScreenData = async (firstScreenData) => {
      const { userInfo } = firstScreenData;
      await setUserInfo(userInfo);
      dispatch.helpPlant.initHelpPlant();
      getUcparamsData();
    };
    if (!isLoading) {
      if (validInitialData) {
        console.log('validInitialData:', validInitialData);
        dispatch.helpPlant.set({
          homeData: data.homeData,
          multiResource: data.multiResource,
        });
        initSubScreenData(data);
        tracker.log({
          category: 170, // 系统自动生成，请勿修改
          msg: '首屏请求成功', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: getParam('entry') || '',
          c2: `${!!data.userInfo?.kps_wg}`,
          c3: data.homeData.ucFarmHasAuth,
          c4: getParam('from') ?? 'unknown',
        });
      } else {
        regetData();
        tracker.log({
          category: 170, // 系统自动生成，请勿修改
          msg: '首屏请求重试', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: getParam('entry') || '',
          c2: `${!!data.userInfo?.kps_wg}`,
          c3: !!data?.homeData?.ucFarmHasAuth,
          c4: getParam('from') ?? 'unknown',
          bl1: JSON.stringify(data),
        });
      }
      setTimeout(() => {
        statPv();
      }, 0);
    }
  }, [isLoading, data]);

  // pv 上报
  const statPv = () => {
    const { homeData = {} } = mx.store.getStore().helpPlant;
    stat.updateParam({
      from: getParam('from') ?? 'unknown',
      entry: getParam('entry') ?? 'entry',
      page_status: homeData?.ucFarmHasAuth ? '1' : '0',
      bbnc_activity: 'bbzhong',
    });
    stat.pv(FactConfig.helpPlantConfig.page);
  };

  const getUcparamsData = async () => {
    try {
      const params = await ucparams({ params: 'prvesvut' });
      const appType = params?.pr?.toLowerCase()?.includes('uclite') ? 'UCLite' : 'UCMobile';
      dispatch.helpPlant.set({
        appVersionDetail: {
          appVersion: params?.ve ?? '',
          appSubVersion: params?.sv ?? '',
          utRes: decodeURIComponent(params?.ut ?? ''),
          pr: appType,
          defaultPr: params?.pr ?? '',
        },
      });
      tracker.log({
        category: 130,
        sampleRate: 1,
        w_succ: params?.pr ? 1 : 0,
        msg: params?.pr ? '帮帮种客户端信息获取成功' : '帮帮种客户端信息未获取到',
        c1: params?.pr,
        bl1: JSON.stringify(params),
      });
    } catch (error) {
      tracker.log({
        category: 130,
        sampleRate: 1,
        w_succ: 0,
        msg: '帮帮种客户端信息未获取到',
        bl1: JSON.stringify(error),
      });
    }
  };

  useEffect(() => {
    try {
      gestureDisabled();
      disabledPinchToZoom();
      forbiddenToolbar();
      landscapeBan();
      setTimeout(() => {
        // 禁用 iOS 弹性滚动
        disableIOSBounces(['web_root_scroll']);
      }, 200);
    } catch (error) {
      console.log('error', error);
    }
  }, []);

  const firstScreenComponent = (
    <Fragment>
      <Header />
      <IntraDayRankingList />
      <Tips />
      <MainInviteBtn />
    </Fragment>
  );
  const subScreenComponent = isWeb ? <SubScreen /> : <Fragment />;

  return (
    <div id="app">
      {firstScreenComponent}
      {subScreenComponent}
    </div>
  );
}

export default function (props: { ctx?: any; initialProps: any }) {
  return (
    <InitialDataProvider {...props}>
      <PageIndex {...props} />
    </InitialDataProvider>
  );
}
