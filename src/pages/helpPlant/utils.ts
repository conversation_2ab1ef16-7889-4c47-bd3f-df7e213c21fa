import wechat_icon from '@/pages/index/components/SharePanel/images/wechat_frends.png';
import copy_link from '@/pages/index/components/SharePanel/images/copylink.png';
import qq_icon from '@/pages/index/components/SharePanel/images/<EMAIL>';
import weibo_icon from '@/pages/index/components/SharePanel/images/<EMAIL>';
import { IQueryRankingRes, Rankings } from "@/api/helpPlant/typings";

export interface IShareItem {
  key: string;
  target: 'WechatFriends' | 'QQ' | 'WechatTimeline' | 'Qzone' | 'SinaWeibo' | 'DingDing' | 'CopyLink';
  icon: string;
  title: string;
  clkType?: string;
}

export const buildBbzShareData = (isLite: boolean): IShareItem[] => {
  const WBItem: IShareItem = {
    key: '3',
    target: 'SinaWeibo',
    icon: weibo_icon,
    title: '微博',
    clkType: 'weibo',
  };
  return [
    {
      key: '1',
      target: 'WechatFriends',
      icon: wechat_icon,
      title: '微信好友',
      clkType: 'wechat',
    },
    {
      key: '2',
      target: 'QQ',
      icon: qq_icon,
      title: 'QQ好友',
      clkType: 'qq_space',
    },
    ...(isLite ? [] : [WBItem]),
    {
      key: '4',
      target: 'CopyLink',
      icon: copy_link,
      title: '复制链接',
      clkType: 'copy_link',
    },
  ];
};

// 根据用户最新分数插入或重排排行榜 
export const updateRankingDisplay = (rankingRes: IQueryRankingRes) => {
  if (!rankingRes?.rankings?.length) {
    return rankingRes;
  }
  const { rankings, myInfo, curTime, endTime, needMinScore, displayRankNum } = rankingRes;
  const needSort = myInfo.score !== myInfo.newestScore
  // 最新分数和排行榜分数一致不需要重排
  if (!needSort) {
    return rankingRes;
  }
  
  // 结榜前5分钟不重排
  if (curTime >= (endTime - 1000 * 60 * 5)) {
    return rankingRes;
  }
  // 复制当前的排名数组
  let newRankings = [...rankings];
  // 检查是否在排行榜中（通过 own 字段判断是否是自己）
  const isUserInRankings = newRankings.some(item => item.own === true);

  if (isUserInRankings) {
    // 替换分数
    newRankings = newRankings.map(item => {
      if (item.own === true) {
        return { ...item, score: myInfo.newestScore, newestScore: myInfo.newestScore };
      }
      return item;
    });
  } else if ((rankings.length < (displayRankNum || 100) && myInfo.newestScore >= needMinScore) || myInfo.newestScore > rankings[rankings.length - 1].score) {
    newRankings.push({
      ...myInfo,
      score: myInfo.newestScore,
    });
  }

  // 排序新的数组
  newRankings.sort((a, b) => {
    // 按分数降序排列
    if (b.score !== a.score) {
      return b.score - a.score;
    }
    // 如果分数相同，将 own: true 的信息放在相同分数的最后一个位置
    if (a.own === true) {
      // 如果 a 是自己的信息，将它放在后面
      return 1;
    }
    if (b.own === true) {
      // 如果 b 是自己的信息，将它放在后面
      return -1;
    }
    // 如果都不是自己的信息，保持原有顺序
    return 0;
  });
  // 截取最大上榜数量
  const updatedRankings = newRankings.slice(0, displayRankNum || 100);
  
  updatedRankings.forEach((item, index) => {
    item.ranking = index + 1;
  });
  
  const updatedMyInfo = updatedRankings.find(item => item.own === true) || myInfo;
  
  return {
    ...rankingRes,
    myInfo: updatedMyInfo,
    rankings: updatedRankings,
  }
}
// 获取当前排名对应的奖励
export const getRankPrizeValue = (rankRewardList, currentRank: number) => {
  // 验证currentRank是否为正整数
  if (currentRank < 1 || !Number.isInteger(currentRank)) {
    return 0; // 或者抛出错误
  }

  for (const reward of rankRewardList) {
    const typeValueParts = reward.typeValue.split('-');
    if (typeValueParts.length !== 2) {
      // 处理无效的typeValue格式
      continue; // 跳过当前奖励项
    }

    const start = parseInt(typeValueParts[0], 10);
    const end = parseInt(typeValueParts[1], 10);

    // 处理start和end可能不是数字的情况
    if (isNaN(start) || isNaN(end)) {
      continue; // 跳过当前奖励项
    }

    // 确保start <= end
    const effectiveStart = Math.min(start, end);
    const effectiveEnd = Math.max(start, end);

    if (currentRank >= effectiveStart && currentRank <= effectiveEnd) {
      return Number(reward.prizeValue);
    }
  }

  // 如果没有匹配的区间
  return 0;
}

// 获取下一档排名奖励
export const getNextRankReward = (rankRewardList, currentPrizeValue: number) => {
  // 遍历 rankRewardList，寻找第一个 prizeValue <= currentPrizeValue 的元素
  if (!rankRewardList?.length) {
    return 0
  }
  for (let i = 0; i < rankRewardList.length; i++) {
    if (Number(rankRewardList[i].prizeValue) <= currentPrizeValue) {
      // 返回上一档奖励（即当前元素的前一个元素）
      if (i === 0) {
        // 如果当前元素是第一个，说明没有上一档
        return null;
      }
      return Number(rankRewardList[i - 1].prizeValue);
    }
  }
  // 如果所有元素的 prizeValue 都大于 currentPrizeValue，返回最后一个元素
  return Number(rankRewardList[rankRewardList.length - 1].prizeValue);
}

// 能拿到下一档奖励的最小助力值
export const getNextRewardMinScore = (rankingList: Rankings[], currentRankPrizeValue: number, rankRewardList) => {
  if (!rankingList?.length) {
    return 0
  }
  for (let i = 0; i < rankingList.length; i++) {
    const prizeAmount = rankingList[i].prizeItems?.[0]?.prizeAmount || getRankPrizeValue(rankRewardList, rankingList[i].ranking);
    if (prizeAmount <= currentRankPrizeValue) {
      // 找到第一个满足条件的元素
      if (i === 0) {
        return rankingList[i].score;
      }
      return rankingList[i - 1].score;
    }
  }
  return 0
}
