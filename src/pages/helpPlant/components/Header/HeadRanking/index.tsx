import React from 'react';
import './index.scss'
import useMxState from "@/hooks/useMxState";
import { MyInfo } from "@/api/helpPlant/typings";
import { getNumberLength } from '@/lib/utils/formatNumber';

export default function Index() {
  const [rankRewardList] = useMxState<MyInfo>('helpPlant.homeData.rankRewardList', []);

  const topOnePrizes = rankRewardList?.[0]?.prizeValue;
  const topTwoPrizes = rankRewardList?.[1]?.prizeValue;
  const topThreePrizes = rankRewardList?.[2]?.prizeValue;

  const formatCash = (value: string) => {
    if (!value) return '--';
    return Number(value) / 100;
  }
  
  return (
    <div className="head-ranking-comp">
      <div className={`top-one top-value ${getNumberLength(topOnePrizes) > 2 ? 'one-small' : ''}`}>
        {formatCash(topOnePrizes)}
      </div>
      <div className={`top-two top-value ${getNumberLength(topTwoPrizes) > 2 ? 'two-small' : ''}`}>
        {formatCash(topTwoPrizes)}
      </div>
      <div className={`top-three top-value ${getNumberLength(topThreePrizes) > 2 ? 'three-small' : ''}`}>
        {formatCash(topThreePrizes)}
      </div>
    </div>
  )
}
