.header-comp{
  width: 100%;
  height: 876rpx;
  background-image: url('./images/head-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  background-position-y: -36rpx;

  .head-nav{
    .back-icon{
      width: 72rpx;
      height: 72rpx;
      background-image: url('./images/bbz-back.png');
      background-repeat: no-repeat;
      background-size: cover;
      position: fixed;
      top: 100rpx;
      left: 32rpx;
      z-index: 10;
    }

    .banner{
      width: 486rpx;
      height: 92rpx;
      position: absolute;
      top: 93rpx;
      left: 50%;
      transform: translateX(-50%);
    }

    .nav-slide-btn{
      width: 68rpx;
      height: 68rpx;
      background: rgba(17,0,58, .12);
      border-radius: 17rpx 0 0 17rpx;
      position: absolute;
      right: 0;
      z-index: 9;
      font-family: PingFangSC-Medium;
      font-size: 22rpx;
      color: #fff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      span{
        display: block;
        line-height: 1.1;
      }
    }

    .rule{
      top: 92rpx;
    }

    .record{
      top: 200rpx;
    }

    .detail{
      top: 308rpx;
    }
  }

  .bubble-content-wrap{
    top: 284rpx;
    right: 92rpx;
  }
}