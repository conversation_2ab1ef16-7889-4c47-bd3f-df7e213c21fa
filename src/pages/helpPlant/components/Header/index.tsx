import React, { useRef } from 'react';
import './index.scss';
import { closeCurrentJSAPIBindWindow, exit, openPage } from '@/lib/ucapi';

import BannerTitle from '@/assets/bbz-banner-title.png';
import HeadRanking from './HeadRanking';
import ChallengeProgress from '../ChallengeProgress';
import useMxState from '@/hooks/useMxState';
import { IHelpPlantHomeRes } from '@/api/helpPlant/typings';
import BubblePop, { IMenuItem } from '@/pages/index/components/Bottom/BubblePop';
import stat from '@/lib/stat';
import { addParams, getParam } from '@/lib/qs';
import { isIOS, isLatestVersion } from '@/lib/universal-ua';
import { mx } from '@ali/pcom-iz-use';

export default function Index() {
  const [homeData] = useMxState<IHelpPlantHomeRes>('helpPlant.homeData', {});
  const { historyRankingLink, ruleLink, detailLink, redPacketLink, ucFarmLink, paramsEntry } = homeData?.frontData;

  const bubbleRef = useRef<any>();

  // 主端安卓，客户端版本号: >= 17.6.2.1393
  const returnFarmFun = () => {
    const entry = getParam('entry');
    if (paramsEntry.includes(entry)) {
      const page = addParams(ucFarmLink, {
        entry,
      });
      openPage(page);
    }
  };

  const isSupportReturnFarm = () => {
    const { helpPlant } = mx.store.getStore();
    let { appVersionDetail = null } = helpPlant;
    const pr = appVersionDetail?.pr;
    const appVersion = appVersionDetail?.appVersion;
    if (isIOS) {
      return false;
    }
    if (pr === 'UCLite') {
      return false;
    }
    return isLatestVersion(appVersion, '17.6.2.1393')
  }

  const handleBack = () => {
    try {
      if (!homeData.ucFarmHasAuth || !isSupportReturnFarm()) {
        exit();
        return
      }
      returnFarmFun();
      setTimeout(() => {
        closeCurrentJSAPIBindWindow();
      }, 1000);
    } catch (error) {
      console.log('error', error);
      exit();
    }
  };

  const handleRule = () => {
    openPage(ruleLink);
  };

  const handleRecord = () => {
    openPage(historyRankingLink);
  };

  const handleDetail = () => {
    if (bubbleRef?.current) {
      bubbleRef?.current?.handleShowBubble();
      stat.exposure('bbz_extra_exposure', {
        c: 'activity',
        d: 'extra',
      });
    }
  };

  const handleMenuItem = (record: IMenuItem) => {
    const recordMap = {
      // 肥料明细
      detail: {
        url: detailLink,
        button: 'point',
      },
      // 红包明细
      redPacket: {
        url: redPacketLink,
        button: 'cash',
      },
    };
    stat.click('bbz_extra_click', {
      c: 'activity',
      d: 'extra',
      button: recordMap[record.clickType]['button'],
    });
    openPage(recordMap?.[record.clickType]?.['url'] || '');
  };

  const handleFunctionClick = (func: 'detail' | 'record' | 'rule') => {
    stat.click('bbz_function_click', {
      c: 'activity',
      d: 'function',
      function: func,
    });
    switch (func) {
      case 'detail':
        handleDetail();
        return;
      case 'record':
        handleRecord();
        return;
      case 'rule':
        handleRule();
        return;
      default:
        (() => {})()
    }
  };
  return (
    <div className="header-comp">
      <div className="head-nav">
        <div className="back-icon" onClick={handleBack} />
        <img className="banner" src={BannerTitle} />
        <div className="rule nav-slide-btn" onClick={() => handleFunctionClick('rule')}>
          规则
        </div>
        <div className="record nav-slide-btn" onClick={() => handleFunctionClick('record')}>
          <span>历史</span>
          <span>记录</span>
        </div>
        <div className="detail nav-slide-btn" onClick={() => handleFunctionClick('detail')}>
          <span>奖励</span>
          <span>明细</span>
        </div>
      </div>
      <ChallengeProgress />
      <HeadRanking />
      <BubblePop
        onRef={bubbleRef}
        handleMenuItem={handleMenuItem}
        contentClassName="bubble-content-wrap"
        hiddenItems={['strategy']}
      />
    </div>
  );
}
