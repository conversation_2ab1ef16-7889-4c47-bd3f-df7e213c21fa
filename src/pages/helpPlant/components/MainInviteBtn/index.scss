.main-invite-btn-comp {
  position: fixed;
  border: none;
  bottom: 0;
  z-index: 80;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  height: 188rpx;
  background-image: linear-gradient(180deg, rgba(48, 171, 32, 0) 31%, #30ab20 93%);
  .mx-auto {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .main-invite-btn {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    width: 450rpx;
    height: 155rpx;
    border: none;
    background-image: url('./images/main-invite-btn-bg.png');
    background-color: transparent;
    background-size: cover;
    background-repeat: no-repeat;
    .main-btn-text {
      margin-top: 24rpx;
      display: inline-block;
      font-family: FZLanTingYuanS-Bold !important;
      color: #883407;
      font-size: 38rpx;
      text-align: center;
      line-height: 44rpx;
      letter-spacing: 0;
    }

    .main-btn-desc {
      margin-top: 2rpx;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #a34c01;
      letter-spacing: 0;
      text-align: center;
      font-weight: 500;
      line-height: 36rpx;
      .help-value {
        font-family: D-DIN-Bold;
      }
    }
    .hand-main {
      right: 0rpx;
    }
  }

  .btn-breathing {
    animation-delay: 500ms;
    animation: btn-breathing 1000ms cubic-bezier(0.33, 0, 0.67, 1) infinite;
  }
  @keyframes btn-breathing {
    0%,
    100% {
      transform: scale(0.96);
    }

    50% {
      transform: scale(1);
    }
  }
  @keyframes btn-sharp {
    0% {
      transform: translate(-100%, 0);
    }

    50% {
      transform: translate(200%, 0);
    }

    100% {
      transform: translate(200%, 0);
    }
  }
  .btn-shine-box {
    position: absolute;
    width: 408rpx;
    height: 120rpx;
    left: 21rpx;
    top: 10rpx;

    overflow: hidden;
    border-radius: 50rpx;
    transform: translate(0);
    pointer-events: none;
    -webkit-mask-image: -webkit-radial-gradient(#fff, #000);

    .btn-shine {
      display: block;
      width: 100%;
      height: 100%;
      background-image: url('./images/shine.png');
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
      animation: btn-sharp 3000ms linear infinite;
      pointer-events: none;
    }
  }
}
