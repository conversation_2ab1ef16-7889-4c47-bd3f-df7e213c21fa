import React, {Fragment} from 'react';
import './index.scss';
import useMxState from '@/hooks/useMxState';
import { MyInfo, Rankings } from '@/api/helpPlant/typings';
import dispatch from '@/logic/store';
import BaseGuide from '@/components/common/guide';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { convertRanking } from '@/lib/utils/formatNumber';
import { useDebounce } from '@/hooks';

export default function MainInviteBtn() {
  const [showHandGuide] = useMxState<boolean>('helpPlant.showHandGuide');
  const [myInfo] = useMxState<MyInfo>('helpPlant.rankingList.myInfo', {});
  const [multiResource] = useMxState<Rankings[]>('helpPlant.multiResource', []);
  const taskList = dispatch.helpPlant.getSubTaskList(multiResource);
  const firstTarget = taskList?.[0]?.target ?? 0;
  const firstTaskAwardAmount = taskList?.[0]?.rewardItems?.[0]?.amount ?? 0;
  const [pageDataReady] = useMxState('helpPlant.pageDataReady');
  const { newestScore = 0, ranking = 0 } = myInfo || {};

  const inviteBtnEvent = useDebounce(() => {
    dispatch.share.plantOpenSharePanel('invite_button', false);
    dispatch.helpPlant.set({
      showHandGuide: false,
    });
    stat.click('bbz_invite_click', {
      c: 'activity',
      d: 'invite',
    });
  }, 500);
  
  const renderButtonDesc = () => {
    if (pageDataReady && firstTaskAwardAmount && newestScore < firstTarget) {
      return (
        <Fragment>
          <span>邀</span><span className="din-num">1</span><span>人得</span><span className="din-num">{firstTaskAwardAmount}</span><span>肥料</span>
        </Fragment>
      )
    }
    return <span>助力值{newestScore} · {ranking === 0 ? ' 暂未上榜' : ` 当前第${Number.isFinite(ranking) ? convertRanking(Number(ranking)) : ranking}名`}</span>;
  }

  return (
    <Fact
      className="main-invite-btn-comp"
      onClick={inviteBtnEvent}
      noUseClick
      c="activity"
      d="invite"
      expoLogkey="bbz_invite_exposure"
    >
      {/* <div className="main-invite-btn-comp" onClick={inviteBtnEvent}> */}
      <button className="main-invite-btn btn-breathing">
        <div className="main-btn-text mx-auto">邀请好友</div>
        <div className="main-btn-desc mx-auto">
          {renderButtonDesc()}
        </div>
        <span className="btn-shine-box">
          <span className="btn-shine" />
        </span>
        {showHandGuide && <BaseGuide className="hand-main" play={false} hideShine alignPoint={'center'} />}
      </button>
      {/* </div> */}
    </Fact>
  );
}
