import React, { useCallback } from 'react';
import dispatch from '@/logic/store';
import throttle from 'lodash.throttle';
import './index.scss';
import Toast from '@/lib/universal-toast';

import RefreshIcon from './images/refresh-icon.png';
import TitleBg from './images/rank-head-bg.png';
import RankingText from './images/rank-text.png';

import RankNotify from './RankNotify';
import RankingList from './RankingList';
import useMxState from '@/hooks/useMxState';
import { MyInfo, Rankings } from '@/api/helpPlant/typings';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import {
  getRankPrizeValue,
  getNextRankReward,
  getNextRewardMinScore
} from "@/pages/helpPlant/utils";
import { IQueryByMultiResource } from '@/api/typings';
import { formatRewardNum } from '@/lib/utils/formatNumber';

const DEFAULT_NEED_MIN_SCORE = 2;

export default function Index() {
  const [myInfo] = useMxState<MyInfo>('helpPlant.rankingList.myInfo', {});
  const [rankings = []] = useMxState<Rankings[]>('helpPlant.rankingList.rankings', []);
  const [needMinScore] = useMxState<number>('helpPlant.rankingList.needMinScore', DEFAULT_NEED_MIN_SCORE);
  const [displayRankNum] = useMxState<number>('helpPlant.rankingList.displayRankNum');
  const [rankRewardList] = useMxState<MyInfo>('helpPlant.homeData.rankRewardList', []);
  const [pageDataReady] = useMxState<boolean>('helpPlant.pageDataReady');
  const [multiResource] = useMxState<IQueryByMultiResource>('helpPlant.multiResource', {});
  const { nickname, newestScore, ranking = 0 } = myInfo ?? {};
  const currentRankPrizeValue = getRankPrizeValue(rankRewardList, ranking);
  const nextRankPrizeValue = getNextRankReward(rankRewardList, currentRankPrizeValue);
  const currentPrizeYuan = currentRankPrizeValue / 100;
  const nextPrizeYuan = nextRankPrizeValue / 100;
  const nextAwardMinScore = getNextRewardMinScore(rankings, currentRankPrizeValue, rankRewardList)
  const maxRankNum = displayRankNum || 100;
  const lastRankingScore = rankings?.[maxRankNum - 1]?.score || 0;
  const lastRankingAward = rankings?.[maxRankNum - 1]?.prizeItems?.[0].prizeAmount || 0;
  
  // 助力门槛值
  const helpThresholdValue = needMinScore ?? DEFAULT_NEED_MIN_SCORE;

  const taskList = dispatch.helpPlant.getSubTaskList(multiResource);
  // 未解锁全部累计挑战
  const doingTask = taskList?.find((taskItem) => taskItem?.state === 0);

  // 下一阶段累计挑战肥料奖励
  const nextTaskPoint = formatRewardNum(doingTask?.rewardItems?.[0]?.amount || 0);
  // 下一阶段累计挑战助力目标值
  const nextTaskScore = doingTask?.target;
  
  const handleRefreshFact = () => {
    stat.click('bbz_refresh_click', {
      c: 'activity',
      d: 'rank',
    });
  };

  const throttleRefresh = useCallback(
    throttle(async () => {
      handleRefreshFact()
      await dispatch.helpPlant.updateRankingList();
      Toast.show('刷新成功', { duration: 2000 });
    }, 2000),
    [],
  );

  const renderTipItem = () => {
    // 有排名, 未上榜(即无奖励)
    if (ranking > maxRankNum) {
      // 未解锁全部累计挑战
      if (doingTask && nextTaskScore) {
        const nextTaskNeedScore = nextTaskScore - newestScore
        return (
          <div className="tips">
          仅差<span>{nextTaskNeedScore}</span>助力值再领{nextTaskPoint}肥料
          </div>
        )
      }
      const scoreGap = lastRankingScore - newestScore + 1;
      return (
        <div className="tips">
          差<span>{scoreGap > 0 ? scoreGap : 1}</span>助力值可得<span className="red-color">¥{lastRankingAward / 100}</span>
        </div>
      );
    }
    // 上榜时,显示与前一档分数差值，未上榜时仅显示下一档奖励金额
    if (ranking <= maxRankNum && ranking > 1) {
      const nextAwardNeedScore = Math.abs(nextAwardMinScore - newestScore + 1);
      if (nextAwardNeedScore >= 100) {
        return currentPrizeYuan > 0 ?
        <div className="tips">
          当前奖励<span className="red-color">¥{currentPrizeYuan}</span>, 下一档奖励<span className="red-color">¥{nextPrizeYuan}</span>
        </div> : null
      } else {
        return currentPrizeYuan > 0 ?
          <div className="tips">
            奖励<span className="red-color">¥{currentPrizeYuan}</span><div className="line" />差<span className="din-num">{nextAwardNeedScore}</span>助力值得<span className="red-color">¥{nextPrizeYuan}</span>
          </div> : null
      }
    }
    if (ranking === 1) {
      return currentPrizeYuan > 0 ?
        <div className="tips">
          预计可得<span className="red-color">¥{currentPrizeYuan}</span>元
        </div> : null
    }
    // 助力值达到门槛, 排行榜还未更新
    if (newestScore >= helpThresholdValue && !ranking) {
      return <div className="tips">排名正在计算请稍后查看</div>;
    }

    return pageDataReady ? (
      <div className="tips">
        邀请一人即领{nextTaskPoint}肥料
      </div>
    ) : null
  };

  const getRankingText = () => {
    if (!ranking && newestScore >= helpThresholdValue) {
      return '正在计算';
    }
    if (ranking >= 10000) {
      return '第1万+名';
    }
    if (ranking) {
      return `第${ranking}名`;
    }
    return '暂未上榜';
  };

  return (
    <Fact
      noUseClick
      c="activity"
      d="rank"
      expoLogkey="bbz_rank_exposure"
      className="intra-day-ranking-list-comp"
    >
      <div className="wrap-content">
        <div className="header-content">
          <img className="title-bg" src={TitleBg} />
          <div className="refresh row">
            <div className="text">每日更新，24点结算！</div>
            <div className="btn row" onClick={throttleRefresh}>
              <img src={RefreshIcon} alt="" />
              <span>刷新</span>
            </div>
          </div>
          <RankNotify />
        </div>
        <div className="ranking-content">
          <RankingList />
        </div>
        <div className="footer-content">
          <div className="user-info">
            <img src={RankingText} alt="" />
            <div className="name">{nickname}</div>
          </div>
          <div className="footer-info row">
            <div className="ranking">{getRankingText()}</div>
            <div className="tip-text row">
              {newestScore >= helpThresholdValue ? (
                <React.Fragment>
                  <div className="my-score">
                    助力值<span>{newestScore || 0}</span>
                  </div>
                  <div className="line" />
                </React.Fragment>
              ) : null}
              {renderTipItem()}
            </div>
          </div>
        </div>
      </div>
    </Fact>
  );
}
