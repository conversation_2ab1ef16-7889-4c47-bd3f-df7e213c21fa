import React from 'react';
import './index.scss';

import BaseMarqueeSlide from '@/components/common/marquee-slide';
import CashIcon from '../images/notice-cash-icon.png';
import useMxState from "@/hooks/useMxState";
import { Rankings } from "@/api/helpPlant/typings";
import { convertCentsToPoint } from '@/lib/utils/formatNumber';

export default function Index() {
  const [rankings = []] = useMxState<Rankings[]>('helpPlant.yesterdayRanking.rankingInfo.rankings', []);
  const [pageDataReady] = useMxState<boolean>('helpPlant.pageDataReady');
  const defaultList = [
    '邀好友得助力值',
    '前100名得现金红包',
    '现金红包无门槛提现',
    '邀新用户可得最多助力值'
  ];
  const getRandom20 = (list) => {
    if (!pageDataReady) {
      return [];
    }
    // 没有奖励数值的直接过滤掉
    const needList = list?.filter((rankItem) => rankItem?.prizeItems?.[0]?.prizeAmount);
    if (!Array.isArray(needList) || needList.length === 0) {
      return defaultList;
    }
    if (needList.length <= 20) {
      return [...needList]; // 直接返回原数组的拷贝
    }

    // 如果数组长度大于20，使用 Fisher-Yates 算法随机选取20条
    const result = [];
    const array = [...needList]; // 创建一个拷贝
    let remaining = array.length;

    while (result.length < 20 && remaining > 0) {
      const index = Math.floor(Math.random() * remaining);
      result.push(array[index]);
      array.splice(index, 1);
      remaining--;
    }

    return result;
  }
  
  const showList = getRandom20(rankings);
  
  const formatName = (nickname: string) => {
    if (!nickname) {
      return '';
    }
    if (nickname.length < 2) {
      return nickname
    }
    const first = nickname.slice(0, 1);
    return `${first}*`
  }

  const getRewardItem = (reward) => {
    const mark = reward?.[0]?.prizeMark;
    const rewardAmount = reward?.[0]?.prizeAmount || 0;
    switch (mark) {
      case 'cash':
        return `${Number(rewardAmount) / 100}元`
      default:
        return `${convertCentsToPoint(Number(rewardAmount))}肥料`
    }
  }

  return (
    <div className="rank-notify-comp">
      <img src={CashIcon} className="notice-icon" alt="" />
      <BaseMarqueeSlide
        list={showList}
        renderItem={(item) => {
          return item.data.nickname ? (
            <div key={item.index} className="message-item">
              恭喜{formatName(item?.data?.nickname)}获得{getRewardItem(item?.data?.prizeItems)}
            </div>
          ) : (
            <div key={item.index} className="message-item">
              {item.data}
            </div>
          )
        }}
      />
    </div>
  )
}
