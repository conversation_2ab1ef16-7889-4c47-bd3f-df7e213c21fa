import React, { useState, useRef, useEffect } from 'react';
import './index.scss';
import { customSeparator } from '@/lib/utils/formatNumber';
import useMxState from '@/hooks/useMxState';

import ScrollList from '@/components/common/ScrollList';
import { Rankings, MyInfo } from '@/api/helpPlant/typings';
import stat from '@/lib/stat';
import { getRankPrizeValue } from '@/pages/helpPlant/utils';

import LightIcon from '@/assets/light-icon.png';

export default function Index() {
  const [rankings = []] = useMxState<Rankings[]>('helpPlant.rankingList.rankings', []);
  const [pageDataReady] = useMxState<boolean>('helpPlant.pageDataReady');
  const [rankRewardList] = useMxState<MyInfo>('helpPlant.homeData.rankRewardList', []);
  const [displayRankNum] = useMxState<number>('helpPlant.rankingList.displayRankNum');
  const maxRankNum = displayRankNum || 100;
  const isEmptyList = pageDataReady && !rankings?.length;
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [, setRefresh] = useState<number>(0);
  const topOnePrizes = Number(rankRewardList?.[0]?.prizeValue || 0) / 100;
  const canShowAllRankList = [...(rankings || [])].slice(0, maxRankNum);

  const showNoMoreText = useRef(false);
  const rankList = useRef<Rankings[]>([]);

  const handleLoadMoreFact = () => {
    stat.click('bbz_slide_click', {
      c: 'activity',
      d: 'rank',
    });
  };

  const loadMore = () => {
    if (rankList.current.length < canShowAllRankList.length) {
      const loadList = [...canShowAllRankList].slice(rankList.current.length, rankList.current.length + 20);
      const newShowList = [...rankList.current, ...loadList];
      rankList.current = newShowList;
      showNoMoreText.current = rankList.current.length === canShowAllRankList.length;
      setRefresh(Date.now());
    }
  }
  
  const renderLoadingContent = () => {
    return (
      <div className="loading-list">
        <div className="loading-list-item" />
        <div className="loading-list-item" />
        <div className="loading-list-item" />
      </div>
    )
  }

  const showTipText = (showNoMoreText.current || rankings.length < 3)
  return (
    <div className="ranking-list-comp">
      <div className="list-head">
        <span className="title-0">助力排名</span>
        <span className="title-1">助力值</span>
        <span className="title-2">预估奖励</span>
      </div>
      {
        pageDataReady ?
          <ScrollList
            onTouchScroll={handleLoadMoreFact}
            list={rankList.current.length ? rankList.current : [...canShowAllRankList].slice(0, 20)}
            loadMore={loadMore}
            isEmpty={isEmptyList}
            hasMore={hasMore}
            loading={loading}
            initNum={3}
            wrapClassName={'ranking-list-wrap'}
            noMoreText={showTipText ? (rankings?.length < maxRankNum ? `- 上榜进入前${maxRankNum}领现金 -` : `- 仅展示前${maxRankNum}名用户 -`) : ''}
            renderItem={(item, index) => {
              const amount = getRankPrizeValue(rankRewardList, index + 1) || item?.prizeItems?.[0]?.prizeAmount || 0;
              return (
                <div className={`list-item ${index < 3 ? `top-${index + 1}` : ''}`} key={index}>
                  <div className="rank-num gilroy-num">{index + 1}</div>
                  <div className={`rank-name ${item.own ? 'own-color' : ''}`}>
                    {item.own ? '(我)' : ''}
                    {item.nickname}
                  </div>
                  <div className={`rank-value ${item.own ? 'own-color' : ''}`}>{customSeparator(item.score)}</div>
                  <div className="rank-award-value">
                    <span className="unit">￥</span>
                    <span className="award-value">{amount / 100}</span>
                  </div>
                </div>
              );
            }}
            renderEmpty={() => {
              return (
                <div className="ranking-empty-wrap">
                  <div className="empty-title">暂无人上榜</div>
                  { !!topOnePrizes && (
                    <div className="rank-award-tips row">
                      <img src={LightIcon} className="light-icon" alt="" />
                      <p className="tips-text row">现在上榜即可进入前{maxRankNum}名领取{topOnePrizes}元！</p>
                    </div>
                  )}
                </div>
              );
            }}
          /> : renderLoadingContent()
      }
    </div>
  );
}
