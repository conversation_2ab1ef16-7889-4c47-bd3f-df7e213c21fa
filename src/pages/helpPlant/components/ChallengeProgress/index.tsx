import React from 'react';
import './index.scss';
import { checkTaskFinished } from '@/pages/index/components/TaskPop/TaskList/util';
import { TaskInfo, TASK_STATUS, TASK_EVENT_TYPE } from '@/pages/index/components/TaskPop/TaskList/types';
import { formatRewardNum } from '@/lib/utils/formatNumber';

import UnReceivedIcon from './images/unreceived-icon.png';
import ReceivedIcon from './images/received-icon.png';
import RedHelpIcon from '@/assets/red-love-icon.png';
import StartIcon from './images/unlocked-start-bg.png';
import useMxState from '@/hooks/useMxState';
import { Rankings, MyInfo } from '@/api/helpPlant/typings';
import config from '@/config';
import dispatch from '@/logic/store';
import { geneTaskRequestId } from '@/api/utils';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import { execWithLock } from '@/lib/utils/lock';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import WhiteLoveIcon from '@/assets/white-love-icon.svg';

const { TASK_COMPLETED } = TASK_STATUS;

const PROGRESS_STATUS_TEXT_MAP = {
  2: {
    text: '已领取',
    className: 'item-wrap-has',
  },
  1: {
    text: '可领取',
    className: 'item-wrap-received',
  },
  0: {
    text: '再领取',
    className: 'item-wrap-none',
  },
};

export default function Index() {
  const [multiResource] = useMxState<Rankings[]>('helpPlant.multiResource', []);
  const [myInfo] = useMxState<MyInfo>('helpPlant.rankingList.myInfo', {});
  const [rankRewardList] = useMxState<MyInfo>('helpPlant.homeData.rankRewardList', []);
  const topOnePrizes = rankRewardList?.[0]?.prizeValue;
  const { newestScore = 0 } = myInfo ?? {};
  const taskList = dispatch.helpPlant.getSubTaskList(multiResource);
  const nextDoingTask = taskList?.find((taskItem) => taskItem?.state === 0);

  const buildFinishRewardValue = () => {
    const finishReward = taskList?.filter(
      (taskItem) => taskItem?.state === TASK_COMPLETED || checkTaskFinished(taskItem),
    );
    // 助力累计肥料奖励总和，包含已领取和可领取的
    const totalReward: number =
      finishReward?.reduce((pre, cur) => {
        return pre + (cur?.rewardItems?.[0]?.amount || 0);
      }, 0) || 0;
    return totalReward;
  };

  const getProgressWidth = () => {
    // 过滤出已完成、待领取的任务
    const finishList = taskList?.filter(
      (taskItem) => taskItem?.state === TASK_COMPLETED || checkTaskFinished(taskItem),
    );
    // 占的份数: 1、3、5、7.... ===> 2n - 1
    const holdNum = 2 * finishList?.length - 1;
    // 总的份数
    const allLength = taskList?.length * 2 - 2;
    return (holdNum / allLength) * 100;
  };

  const getClassName = (reward: TaskInfo, index) => {
    const preItem = taskList?.[index - 1];
    // 当前的未解锁并且上一个的状态等于已领取
    if (reward?.state === 0 && (!preItem || checkTaskFinished(preItem))) {
      return 'item-wrap-none-extrude';
    }
    return PROGRESS_STATUS_TEXT_MAP[reward?.state]?.className || PROGRESS_STATUS_TEXT_MAP[0]?.className;
  };
  // 手动批量领取蓄水任务
  const handleClickTask = async () => {
    const completeTasks = taskList?.filter((taskItem) => taskItem?.state === TASK_COMPLETED);
    if (!completeTasks?.length) {
      return;
    }
    const taskIdList = completeTasks.map((taskItem) => taskItem.id);
    const publishList = completeTasks.map((taskItem) => ({
      tid: taskItem.id,
      publishId: taskItem.publishId,
    }));
    execWithLock('batchReward', async (unlock) => {
      const totalRewardAmount = await dispatch.task.batchReward({
        taskIdList,
        publishList,
        requestId: geneTaskRequestId(),
      });
      if (totalRewardAmount) {
        baseModal.open(MODAL_ID.BBZ_PROGRESS_CHALLENGE_AWARD, {
          dataInfo: {
            totalPoint: totalRewardAmount,
            curHelpValue: newestScore,
            nextHelpValue: nextDoingTask ? nextDoingTask.target - newestScore : 0,
            nextAward: nextDoingTask ? nextDoingTask.rewardItems?.[0]?.amount : 0,
            finishAllTask: !nextDoingTask,
            rankMaxAward: topOnePrizes / 100,
          },
          sourceTaskList: completeTasks
        });
      }
      dispatch.helpPlant.updateMultiResource();
      unlock();
    });
  };
  const handleProgressClick = () => {
    stat.click('bbz_add_click', {
      c: 'activity',
      d: 'reward',
      assist_fertilizer_reward: buildFinishRewardValue(),
    });
  };

  const getIcon = (reward: TaskInfo, index) => {
    const preItem = taskList?.[index - 1];
    // 当前的未解锁并且上一个的状态等于已领取
    if (reward?.state === 0 && (!preItem || checkTaskFinished(preItem))) {
      return StartIcon
    }
    return reward?.state === TASK_COMPLETED ? ReceivedIcon : UnReceivedIcon
  }
  return (
    <Fact
      noUseClick
      c="activity"
      d="reward"
      expoLogkey="bbz_add_exposure"
      expoExtra={{
        assist_fertilizer_reward: buildFinishRewardValue(),
      }}
      onClick={handleProgressClick}
      className="challenge-progress-comp"
    >
      <div className="progress-wrap">
        <div className="progress-list row">
          {taskList?.map((item, index) => {
            return (
              <div className="list-item" key={index}>
                <div className={`item-wrap ${getClassName(item, index)}`} onClick={() => handleClickTask()}>
                  <div className="award-wrap">
                    <img src={getIcon(item, index)} alt="" />
                    <div className="award-value din-num">{formatRewardNum(item?.rewardItems?.[0]?.amount)}</div>
                  </div>
                  <div className="item-text">
                    { index === 0 && item?.state === 0 ? '邀1人' : PROGRESS_STATUS_TEXT_MAP[item?.state]?.text || PROGRESS_STATUS_TEXT_MAP[0]?.text}
                  </div>
                  {checkTaskFinished(item) && <div className="footer-status node-status" />}
                  {!checkTaskFinished(item) && (
                    <div className="status-wait node-status">
                      <img src={item?.state === TASK_COMPLETED ? RedHelpIcon : WhiteLoveIcon} alt="" />
                      <span>{item?.target}</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
        {taskList.length > 0 && (
          <div className="progress-bar">
            <div className="bar" style={{ width: `${getProgressWidth()}%` }} />
          </div>
        )}
      </div>
    </Fact>
  );
}
