.challenge-progress-comp{
  width: 580rpx;
  height: 250rpx;
  background-image: url('./images/challenge-progress-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  top: 176rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9;

  .progress-wrap{
    padding-top: 62rpx;
    width: 100%;
    height: 160rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    .progress-list{
      justify-content: center;
    }

    .list-item{
      width: 104rpx;
      height: 115rpx;
      margin-right: 5rpx;
      position: relative;

      .item-wrap{
        background-repeat: no-repeat;
        background-size: contain;
        position: relative;

        .award-wrap{
          width: 74rpx;
          height: 50rpx;
          position: relative;
          margin: 0 auto;
          padding-top: 5rpx;
          margin-bottom: 3rpx;

          img{
           width: 100%;
          }

          .award-value{
            position: absolute;
            bottom: 2rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 41rpx;
            height: 20rpx;
            background-image: linear-gradient(184deg, #FFF9F3 0%, #FFF2DB 88%);
            box-shadow: 0 1px 1px 0 rgba(246,134,42,0.13), 0 1px 3px 0 rgba(238,169,73,0.36), inset 0 -1px 1px 0 #FFE4A9;
            border-radius: 9.9rpx;
            font-family: FZLANTY_ZHONGCUJW--GB1-0;
            font-size: 19rpx;
            color: #6CCA2C;
            letter-spacing: -0.9rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-top: 1rpx;
            box-sizing: border-box;
          }
        }

        .node-status{
          position: absolute;
          bottom: -50rpx;
          left: 50%;
          transform: translateX(-50%);
          background-repeat: no-repeat;
          background-size: 100% 100%;

          img{
           width: 22rpx;
           height: 22rpx;
          }

          span{
            font-family: D-DIN-Bold;
            font-size: 26rpx;
            color: #FFFFFF;
            letter-spacing: 0;
            line-height: 1;
          }
        }

        .item-text{
          padding-top: 1rpx;
          font-family: FZLanTingYuanS-Bold;
          font-size: 18rpx;
          color: #83B300;
          font-weight: 400;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1.2;
        }

        .footer-status{
          width: 45rpx;
          height: 45rpx;
          background-image: url('./images/help-finish-status.png');
        }

        .status-wait{
          padding: 0 10rpx;
          height: 40rpx;
          background-color: #68ADFF;
          border-radius: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          span{
            color: #FFFFFF;
          }
        }
      }

      .item-wrap-has,
      .item-wrap-none{
        width: 84rpx;
        height: 90rpx;
        background-image: url('./images/unlocked-bg.png');
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);

        .award-wrap{
          opacity: .5;
        }

        .item-text{
          color: #D4DADE;
        }
      }

      .item-wrap-none{
        .item-text{
          color: #83B300;
        }
        .node-status{
          bottom: -45rpx;
        }
      }

      .item-wrap-received{
        width: 100%;
        height: 100%;
        background-image: url('./images/locked-bg.png');

        .award-wrap{
          padding-top: 6rpx;
          width: 92rpx;
          height: 62rpx;

          .award-value{
            width: 48rpx;
            height: 24rpx;
            line-height: 24rpx;
            font-size: 24rpx;
          }
        }

        .item-text{
          margin: 0 auto;
          width: 86rpx;
          height: 34rpx;
          background-image: url('./images/received-btn.png');
          background-repeat: no-repeat;
          background-size: cover;
          font-family: FZLANTY_ZHONGCUJW--GB1-0;
          font-size: 20rpx;
          color: #FFFFFF;
          margin-top: -1rpx;
        }

        .status-wait{
          width: 73rpx;
          height: 45rpx;
          background-image: url('./images/node-locked-bg.png');
          padding: 0;

          span{
            color: #FF4600;
          }
        }
      }

      .item-wrap-none{
        .award-wrap{
          opacity: 1;
        }
      }

      .item-wrap-none-extrude{
        width: 100%;
        height: 100%;
        background-image: url('./images/unlocked-bg.png');

        .award-wrap{
          padding-top: 6rpx;
          width: 92rpx;
          height: 62rpx;

          .award-value{
            width: 48rpx;
            height: 24rpx;
            line-height: 24rpx;
            font-size: 24rpx;
          }
        }

        .node-status{
          bottom: -45rpx;
        }

        .item-text{
          height: 32rpx;
          font-size: 22rpx;
        }
      }
    }

    .progress-bar{
      margin-top: 17rpx;
      width: 436rpx;
      height: 20rpx;
      background: #68ADFF;
      border-radius: 100rpx;
      overflow: hidden;
      .bar{
        width: 0;
        height: 18rpx;
        margin-top: 1rpx;
        background-image: linear-gradient(268deg, #FFC127 14%, #FFE166 21%, #FFD31D 50%, #FFE8A3 100%);
        box-shadow: inset -2px 0 3px 1px #FFFFFF, inset 0 -2px 6px 0 rgba(255,176,30,0.95);
        border-radius: 12rpx;
      }
    }
  }
}
