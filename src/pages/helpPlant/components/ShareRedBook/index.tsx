import React from 'react';
import './index.scss';
import BackBg from './images/back-bg.png';
import DoubleArrow from './images/double-arrow.png';
import WhiteArrow from './images/white-arrow.svg';
import OrangeArrow from './images/orange-arrow.svg';
import Column from './images/column.png';
import useMxState from '@/hooks/useMxState';
import { IHelpPlantHomeRes } from '@/api/helpPlant/typings';
import Fact from '@/components/Fact';
import { copyToClipboard, openPage, startApp } from '@/lib/ucapi';
import Toast from '@/lib/universal-toast';
import stat from '@/lib/stat';
import tracker from '@/lib/tracker';

export default function ShareRedBook() {
  const [homeData] = useMxState<IHelpPlantHomeRes>('helpPlant.homeData', {});
  const config = homeData?.frontData?.redBookConfig;
  const copyText = `#合种 #互助 #芭芭农场 #UC帮帮种`;
  const buildCashRange = () => {
    if (config?.highest && config?.lowest) {
      return `${config.lowest}~${config.highest}元`;
    }
    return '';
  };
  const handleClickStat = (position) => {
    stat.click('bbz_xhsshare_click', {
      c: 'activity',
      d: 'xhsshare',
      click_position: position,
    });
  };
  const handleCopy = () => {
    copyToClipboard({
      text: copyText,
    });
    handleClickStat('1');
  };
  const handleNote = async () => {
    const callRedbokkAppMonitor = tracker.Monitor(177);
    const redbookScheme = 'xhsdiscover://home/';
    try {
      handleClickStat('2');
      const startRes = await startApp(redbookScheme);
      if (startRes?.result?.toString() === 'true') {
        // todo 埋点，监控
        callRedbokkAppMonitor.success({
          msg: `唤端成功`,
          c1: '1',
          c2: 'bbz_xhsshare',
          bl1: redbookScheme,
          bl2: JSON.stringify(startRes),
        });
      } else {
        Toast.show('请安装小红书后重试～');
        callRedbokkAppMonitor.success({
          msg: `唤端失败`,
          c1: '0',
          c2: 'bbz_xhsshare',
          bl1: redbookScheme,
          bl2: JSON.stringify(startRes),
        });
      }
    } catch (starErr) {
      Toast.show('请安装小红书后重试～');
      callRedbokkAppMonitor.success({
        msg: `唤端失败`,
        c1: '0',
        c2: 'bbz_xhsshare',
        bl1: redbookScheme,
        bl2: starErr ?? JSON.stringify(starErr),
      });
    }
  };
  const handleAudit = () => {
    config?.serviceLink && openPage(config?.serviceLink);
    handleClickStat('3');
  };
  return (
    // todo 埋点修改
    <Fact noUseClick c="activity" d="xhsshare" expoLogkey="bbz_xhsshare_exposure" className="share-redbook-comp">
      <img className="back-bg" src={BackBg} alt="分享活动到小红书" />
      <img className="column column-left" src={Column} alt="" />
      <img className="column column-right" src={Column} alt="" />
      <div className="share-redbook-container">
        <p className="share-redbook-title">
          <span>分享活动到小红书 </span>
          <span className="title-strong">领{buildCashRange()}现金!</span>
        </p>
        <div className="share-redbook-step">
          <div className="step1">
            <div className="subtitle">
              <div className="order-num">01</div>
              <p>发布笔记</p>
            </div>
            <div className="step-content">
              <p className="content-subtitle">
                1、笔记内容需包含<span className="strong">UC芭芭农场首页截图（展示你种的树噢）+ 帮帮种活动截图 + 以下话题</span>
              </p>
              <div className="text-container flex justify-between">
                <p className="text leading-none">{copyText}</p>
                <span className="copy" onClick={handleCopy}>
                  点击复制
                </span>
              </div>
              <p className="content-subtitle subtitle2">
                2、笔记需要设为公开并保留<span className="strong">≥{config?.timeRange || '72'}小时</span>
              </p>
              <div className="text-container rounded-lg">
                <p className="title">笔记贴士</p>
                <p className="text">
                  * 优质内容可获<span className="strong">{config?.highest ? `最高${config.highest}元` : ''}</span>
                  现金奖励（如：求互助、介绍玩法和奖励、高互动浏览量）
                </p>
                <p className="text">* 不建议发二维码分享图，会被小红书官方隐藏哦</p>
              </div>
              <div onClick={handleNote} className="note-btn btn">
                <span>去小红书发笔记</span>
                <img className="arrow" src={WhiteArrow} alt="" />
              </div>
            </div>
          </div>

          <div className="step2">
            <div className="subtitle ">
              <div className="order-num">02</div>
              <p>笔记审核</p>
            </div>
            <div className="step-content">
              <p className="content-subtitle">1、 点击笔记右上角「…」找到「复制链接」</p>
              <p className="content-subtitle flex items-center mt-3">
                2、上传链接去审核
                <img className="double-arrow" src={DoubleArrow} alt="" />
                审核通过即发奖励
              </p>
              <button onClick={handleAudit} className="audit-btn btn">
                <span className="">提交审核</span>
                <img className="arrow" src={OrangeArrow} alt="" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </Fact>
  );
}
