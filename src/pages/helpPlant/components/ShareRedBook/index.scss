.share-redbook-comp {
  position: relative;
  width: 690rpx;
  height: fit-content;
  background-color: white;
  margin-bottom: 155rpx;
  border-radius: 44rpx;
  padding-bottom: 64rpx;

  .leading-none {
    line-height: normal !important;
  }
  .items-center {
    align-items: center;
  }
  .mt-3 {
    margin-top: 12rpx !important;
  }
  .flex {
    display: flex;
    flex-direction: row;
  }
  .justify-between {
    justify-content: space-between;
  }
  .rounded-lg {
    border-radius: 25rpx !important;
  }
  .back-bg {
    position: absolute;
    top: 4rpx;
    left: 3rpx;
    width: 685rpx;
    height: 141rpx;
  }

  .share-redbook-container {
    width: 100%;
    height: 100%;
    position: relative;
    inset: 0;
    z-index: 1;

    .share-redbook-title {
      font-size: 36rpx;
      line-height: 42rpx;
      color: #000000;
      letter-spacing: 0;
      margin-top: 60.2rpx;
      margin-left: auto;
      margin-right: auto;
      text-align: center;

      span {
        font-family: FZLanTingYuanS-Bold !important;
        font-weight: 400;
      }

      .title-strong {
        color: #ff801c;
      }
    }
    .share-redbook-step {
      margin-top: 50rpx;
      margin-left: 35rpx;
      padding-right: 56rpx;
      .order-num {
        background: #ff655b;
        width: 46rpx;
        height: 32rpx;
        border-radius: 6rpx 6rpx 14rpx 6rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: D-DIN-Bold;
        font-size: 25rpx;
        color: #ffffff;
        letter-spacing: 0;
        text-align: center;
      }
      .subtitle {
        display: flex;
        flex-direction: row;
        align-items: center;
        p {
          margin-left: 10rpx;
          font-family: PingFangSC-Semibold;
          font-size: 30rpx;
          color: #000000;
          letter-spacing: 0;
          font-weight: 600;
        }
      }
      .step-content {
        margin-left: 57rpx;
        .btn {
          margin-left: 19rpx;
          width: 470rpx;
          height: 100rpx;
          border-radius: 50rpx;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          .arrow {
            width: 32rpx;
            height: 32rpx;
            margin-left: 8rpx;
          }
          span {
            font-family: PingFangSC-Semibold;
            font-size: 32rpx !important;
            letter-spacing: 0;
          }
        }
        .audit-btn {
          margin-top: 34rpx;
          background: #ffffff;
          border: 3rpx solid rgba(250, 100, 37, 0.43);
          span {
            font-size: 32rpx !important;
            color: #FA6425 !important;
            font-weight: 600;
          }
        }
        .note-btn {
          margin-top: 24rpx;
          background: #ff2442;
          background-image: linear-gradient(90deg, #f4701b 0%, #fb304b 100%);
          span {
            color: #ffffff !important;
            font-weight: 600;
          }
        }
        .content-subtitle {
          font-family: PingFangSC-Semibold;
          font-size: 24rpx;
          color: #444849;
          letter-spacing: 0;
          line-height: 33rpx;
          font-weight: 500;
          .double-arrow {
            width: 28rpx;
            height: 24rpx;
            margin-left: 5rpx;
            margin-right: 5rpx;
          }
          .strong {
            color: #fa6425 !important;
            font-weight: 600;
          }
        }
        .subtitle2 {
          margin-top: 22rpx;
        }
        .text-container {
          padding-top: 20rpx;
          padding-left: 27rpx;
          padding-right: 24rpx;
          padding-bottom: 20rpx;
          margin-top: 16rpx;
          background: rgba(245,246,247, 0.8);
          border-radius: 12rpx;
          .title {
            font-family: PingFangSC-Semibold;
            font-size: 22rpx;
            color: #000000;
            letter-spacing: 0;
          }
          .text {
            font-family: PingFangSC-Regular;
            font-size: 22rpx;
            color: #364047;
            letter-spacing: 0;
            line-height: 42rpx;
          }
          .strong {
            font-family: PingFangSC-Semibold;
            color: #fa6425 !important;
            font-weight: 600;
          }
          .copy {
            font-family: PingFangSC-Regular;
            font-size: 22rpx;
            color: #2696FF;
            letter-spacing: 0;
            text-align: right;
          }
        }
      }

      .step1{
        position: relative;
        .step-content{
          position: relative;
          padding-bottom: 47rpx;
          padding-top: 26rpx;
          &::after{
            content: '';
            position: absolute;
            top: 50%;
            left: -34rpx;
            transform: translateY(-50%);
            height: 96%;
            width: 1rpx;
            opacity: .45;
            background: repeating-linear-gradient(
              to bottom,
              #FF655B 0 4rpx,
              transparent 4rpx 8rpx
            );
          }
        }
      }

      .step2 .subtitle {
        margin-bottom: 26rpx;
      }
    }
  }
  .column-left {
    left: 74rpx;
  }
  .column-right {
    right: 74rpx;
  }
  .column {
    position: absolute;
    top: -42rpx;
    border-radius: 11rpx;
    width: 16rpx;
    height: 78rpx;
  }
}
