import React from 'react';
import './index.scss';
import Tips2Bg from './images/tips2-bg.png';
import { buildBbzShareData, IShareItem } from '@/pages/helpPlant/utils';
import dispatch from '@/logic/store';
import useMxState from '@/hooks/useMxState';
import { IHelpPlantState } from '@/logic/store/models/helpPlant';
import Fact from '@/components/Fact';

export default function Index() {
  const [helpPlant] = useMxState<IHelpPlantState>('helpPlant');
  const shareClick = (actItem: IShareItem) => {
    const target = actItem.target;
    dispatch.share.openAppointTarget(target);
  };
  const isLite = helpPlant.appVersionDetail?.pr === 'UCLite';

  const shareItem = (item: IShareItem) => {
    return (
      <div className="share-operation-box" key={item.target} onClick={() => shareClick(item)}>
        <img className="share-operation-img" src={item.icon} alt="" />
        <div className="share-operation-text">{item.title}</div>
      </div>
    );
  };
  return (
    <Fact noUseClick c="activity" d="share" expoLogkey="bbz_share_exposure" className="tips-share-comp">
      <img className="tips2-bg" src={Tips2Bg} />
      <div className="share-tip-container">
        <p className="share-tip-title">
          <span>分享活动到</span>
          <span className="title-strong">社交媒体</span>
          <span>，让更多人助力！</span>
        </p>
        <div className="share-wraper mx-auto">
          {buildBbzShareData(isLite).map((item, index) => {
            return shareItem(item);
          })}
        </div>
        <p className="share-tip-desc mx-auto">邀请其他&ldquo;农场好友&rdquo;可加速助力哦!</p>
        <div className="tip-text-container mx-auto">
          <p>
            <span className="num">1、</span>去<span className="strong-text">小红书</span>
            搜“芭芭农场”帖子，评论邀请
          </p>
          <p>
            <span className="num">2、</span>去<span className="strong-text">微博</span>
            搜“芭芭农场”超话，跟帖邀请
          </p>
          <p>
            <span className="num">3、</span>去<span className="strong-text">QQ</span>
            搜“芭芭农场”Q群，群聊邀请
          </p>
        </div>
      </div>
    </Fact>
  );
}
