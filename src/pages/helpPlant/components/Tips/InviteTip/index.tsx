import React from 'react';
import './index.scss';
import Tips1Bg from './images/tips1-bg.png';
import UpIcon from './images/btn-up-icon.png';
import useMxState from '@/hooks/useMxState';
import { IAssistInfo, AssistUserType } from '@/api/helpPlant/typings';
import Fact from '@/components/Fact';

interface IInviteItem {
  text: string;
  assistUserType: AssistUserType;
}

const inviteData: IInviteItem[] = [
  {
    text: '邀新用户',
    assistUserType: AssistUserType.BIZ_NEW,
  },
  {
    text: '邀老用户回归',
    assistUserType: AssistUserType.BIZ_RECALL_USER,
  },
  {
    text: '邀活跃用户',
    assistUserType: AssistUserType.ALL_USER,
  },
];

export default function Index() {
  const [dailyAssistMaxNum] = useMxState<number>('helpPlant.homeData.inviteInfo.dailyAssistMaxNum', 1);
  const [assistInfo] = useMxState<IAssistInfo[]>('helpPlant.homeData.inviteInfo.assistInfo', []);
  const helpScores = assistInfo
    .map((item) => {
      return item.score;
    })
    .join('/');
  const renderItemList = inviteData.map((item, index) => {
    const itemAssistInfo = assistInfo.find((assist) => {
      return assist.assistUserType === item.assistUserType;
    });
    return {
      ...item,
      ...itemAssistInfo,
    };
  });
  // 过滤掉没有助力值的类型
  const showList = renderItemList.filter((item) => item.score);
  return (
    <Fact noUseClick c="activity" d="withdraw" expoLogkey="bbz_method_exposure" className="tips-invite-comp">
      <img className="tips1-bg" src={Tips1Bg} alt="冲榜攻略" />
      <div className="invite-tip-container">
        <p className="invite-tip-title">
          <span>邀</span>
          <span className="title-strong">新用户</span>
          <span>获得大额助力值！</span>
        </p>
        <p className="invite-tip-desc">为他人助力也可获得{helpScores}不同助力值</p>
        <div className="card-wraper" style={{ justifyContent: showList.length < 3 ? 'space-around' : 'space-between' }}>
          {showList.map((item, index) => {
            return inviteCardItem(item, index);
          })}
        </div>
        <p className="invite-tip-note note1">* 新用户指首次在UC芭芭农场授权开通种树的用户</p>
        <p className="invite-tip-note">* 老用户指UC芭芭农场30天及以上未活跃的用户</p>
        <p className="invite-tip-note">* 单个用户每日助力他人最多{dailyAssistMaxNum}次，被助力次数不限</p>
      </div>
    </Fact>
  );
}

const inviteCardItem = (item, index) => {
  return (
    <div className="invite-card" key={item.assistUserType}>
      <span className="invite-card-value mx-auto">{item.inviterScore}</span>
      <span className="invite-card-desc mx-auto">助力值</span>
      <button className="invite-card-btn mx-auto">
        <span>{item.text}</span>
        {item.assistUserType === AssistUserType.BIZ_NEW && (
          <img className="invite-card-btn-up-icon" src={UpIcon} alt="新用户" />
        )}
      </button>
    </div>
  );
};
