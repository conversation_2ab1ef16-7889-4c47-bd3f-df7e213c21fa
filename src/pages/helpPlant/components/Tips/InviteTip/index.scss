.tips-invite-comp {
  position: relative;
  width: 690rpx;
  height: 567rpx;
  padding-bottom: 40rpx;
  background-color: white;
  margin-top: 37rpx;
  border-radius: 44rpx;

  .tips1-bg {
    position: absolute;
    top: -17rpx;
    width: 690rpx;
    height: 160rpx;
  }

  .invite-tip-container {
    width: 100%;
    height: 100%;
    position: absolute;
    inset: 0;
    z-index: 1;

    .invite-tip-title {
      font-size: 36rpx;
      line-height: 42rpx;
      color: #000000;
      letter-spacing: 0;
      margin-top: 63rpx;
      margin-left: 45rpx;

      span {
        font-family: FZLanTingYuanS-Bold !important;
        font-weight: 400;
      }

      .title-strong {
        color: #FF801C;
      }
    }

    .invite-tip-desc {
      margin-top: 12rpx;
      margin-left: 44rpx;
      text-align: start;
      font-size: 24rpx;
      color: #444849;
      font-family: PingFangSC-Regular;
    }

    .card-wraper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 630rpx;
      margin-top: 34rpx;
      margin-left: auto;
      margin-right: auto;

      .invite-card {
        width: 200rpx;
        height: 230rpx;
        display: flex;
        flex-direction: column;
        background-image: url('./images/invite-card-bg.png');
        background-size: cover;
        background-repeat: no-repeat;

        .invite-card-value {
          margin-top: 32rpx;
          font-family: Gilroy-Bold;
          text-align: center;
          color: #FA6425;
          font-size: 95rpx;
          line-height: 90rpx;
          min-height: 90rpx;
        }

        .invite-card-desc {
          font-family: PingFangSC-Regular;
          color: #12161a;
          opacity: 0.67;
          font-size: 24rpx;
          line-height: 33rpx;
        }

        .invite-card-btn {
          position: relative;
          width: fit-content;
          height: 46rpx;
          margin-top: 10rpx;
          background-image: linear-gradient(179deg, #FF9C55 0%, #FF4C00 73%, #FF7968 100%);
          box-shadow: 0 9px 7px 0 rgba(255, 169, 59, 0.33), inset 0 0.5px 0 0 #FFFFFF;
          border: 1px solid rgba(255, 117, 117, 1);
          border-radius: 24rpx;
          padding: 0 15rpx;
          display: flex;
          justify-content: center;
          align-items: center;

          span {
            font-family: FZLanTingYuanS-Bold !important;
            color: white;
            font-size: 24rpx;
            text-align: center;
          }
          .invite-card-btn-up-icon {
            position: absolute;
            top: -11.5rpx;
            right: -33.5rpx;
            width: 60rpx;
            height: 60rpx;
          }
        }
      }
    }

    .invite-tip-note {
      margin-left: 43rpx;
      opacity: 0.42;
      line-height: 40rpx;
      font-family: PingFangSC-Regular;
      font-size: 22rpx;
      color: #444849;
      letter-spacing: 0;
      font-weight: 400;
    }
    .note1 {
      margin-top: 24rpx;
    }
  }

}
