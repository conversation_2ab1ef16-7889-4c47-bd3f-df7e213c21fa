import React, { useEffect, useRef } from 'react';
import { useInitialData, InitialDataProvider, getPerf } from '@ali/compass-app';
import dispatch from '@/logic/store';
import { isWeb } from '@ali/compass-env';
import '@/common.scss';
import './index.scss';
import PageIndex from './pageIndex';
import tracker from '@/lib/tracker';
import initClient from '@/lib/utils/init-client';
import { IFirstData } from '@/api/typings';
import { userInfoHandler } from '@/pages/index/utils';
import { FactConfig } from '@/config/fact';
import { getParam } from '@/lib/qs';
import toast from '@/lib/universal-toast/component/toast';
import { getUserInfo, ucparams } from '@/lib/ucapi';
import { queryHomeData } from '@/api/home';
import { IMainInfo } from '@/logic/store/models/app/typings';
import { homeBusinessErrCode } from '@/lib/network/businessErrCode';
import { RES_CODES } from '@/logic/store/models/cms/typings';
import stat from '@/lib/stat';
import { setItracePageUuid } from '@/lib/utils/itrace-meta';

initClient(FactConfig.indexConfig);
if (isWeb) {
  const logonline = require('@ali/logonline');
  logonline.init();
}

export const isValidHomeData = (data: any) => {
  return data?.mainInfo && data?.mainInfo?.userInfo;
};

export const isValidHighData = (data: any) => {
  return data?.multiResource;
};

function PageData(props) {
  const { isLoading, data } = useInitialData();
  // 首屏数据
  const validInitialData = isValidHomeData(data);
  const validInitialMultiResource = isValidHighData(data);
  const ssrDataRender = useRef(!isLoading && validInitialData);

  /**
   * 初始化首屏数据
   * @param userInfo 个人信息
   * @param mainInfo 主要接口
   * @param cmsData CMS信息
   */
  const initFirstScreenData = (initialData: IFirstData) => {
    // 个人信息
    if (initialData?.mainInfo?.userInfo) {
      dispatch.app.set({
        mainInfo: initialData?.mainInfo,
      });
    }
  
    // 批量资源位code
    if (initialData?.multiResource) {
      dispatch.resource.queryResource({
        firstInit: true,
        resData: initialData?.multiResource
      });
    }
  
    // CMS 数据合并
    if (initialData?.cmsData) {
      dispatch.cms.setCmsData(initialData?.cmsData);
      // 重定向数据存储
      if (initialData?.cmsData[RES_CODES.CMS_REDIRECT_CODE]) {
        dispatch.redirect.set({
          defaultCmsData: initialData?.cmsData
        });
      }
    }
  }
  
  useEffect(() => {
    setItracePageUuid(data?.multiResource?.x_wpk_traceid);
    initFirstScreenData(data);
  }, [data]);

  const updatePageData = async (pageData: IFirstData) => {
    const { mainInfo, userInfo } = pageData;
    await dispatch.app.set({
      mainInfo,
      isHomeDataLoading: false
    });
    if (userInfo?.kps_wg) {
      await dispatch.user.set(userInfoHandler(userInfo));
    }
    // 首屏数据完成加载
    await dispatch.app.appInit();
    // pagePerfMark(renderAction.pageDataOnload);
    ssrDataRender.current = true;
  };

  const getUcparamsData = async () => {
    try {
      const params = await ucparams({ params: 'prvesvut' });
      tracker.log({
        category: 130,
        sampleRate: 1,
        w_succ: params?.pr ? 1 : 0,
        msg: params?.pr ? '获取成功' : '未获取到',
        c1: params?.pr,
        bl1: JSON.stringify(params),
      });
      const appType = params?.pr?.toLowerCase()?.includes('uclite') ? 'UCLite' : 'UCMobile';
      dispatch.app.set({
        pr: appType,
        defaultPr: params?.pr ?? '',
        appVersionDetail: {
          appVersion: params?.ve ?? '',
          appSubVersion: params?.sv ?? '',
          utRes: decodeURIComponent(params?.ut ?? '')
        },
      });
    } catch (error) {
      tracker.log({
        category: 130,
        sampleRate: 1,
        w_succ: 0,
        msg: '未获取到',
        bl1: JSON.stringify(error),
      });
    }
  };

  useEffect(() => {
    // 重定向功能
    dispatch.redirect.handleRedirectEvent(data?.cmsData);
    getUcparamsData();
  }, []);

  useEffect(() => {
    const regetData = async () => {
      // 首屏异常重试
      const userInfo: any = await getUserInfo();
      const kps = userInfo?.kps_wg || '';
      if (!kps) {
        updatePageData({
          mainInfo: {},
          userInfo,
        });
        return;
      }
      await queryHomeData(kps)
        .then((res: IMainInfo & { code: string }) => {
          if (res?.userInfo || homeBusinessErrCode.includes(res?.code)) {
            console.log('[首屏重试成功]');
            tracker.log({
              category: 100, // 系统自动生成，请勿修改
              msg: '首屏重试成功', // 将根据msg字段聚合展示在平台的top上报内容中
              w_succ: 1, // 用于计算"成功率";可选值为0或1
              c1: getParam('entry') || '',
              c2: String(res?.userInfo?.ucFarmHasAuth), // 是否认证过
              c5: getParam('from') ?? 'unknown',
              w_trace_reqid: res['x_wpk_reqid'] ?? ''
            });            
            stat.updateParam({
              deviceLevel: res?.deviceLevel,
              tree_level: res?.gameInfo?.plantInfo?.seedStage?.stageLevel ?? '-1',
            });
            updatePageData({
              mainInfo: res,
              userInfo,
            });
          } else {
            tracker.log({
              category: 100, // 系统自动生成，请勿修改
              msg: '首屏重试失败-无数据', // 将根据msg字段聚合展示在平台的top上报内容中
              w_succ: 0, // 用于计算"成功率";可选值为0或1
              c1: getParam('entry') || '',
              c5: getParam('from') ?? 'unknown',
              w_trace_reqid: res['x_wpk_reqid'] ?? ''
            });
            updatePageData({
              mainInfo: res,
              userInfo,
            });
          }
        })
        .catch((e) => {
          tracker.log({
            category: 100, // 系统自动生成，请勿修改
            msg: `首屏重试-${e?.code || '网络异常'}`, // 将根据msg字段聚合展示在平台的top上报内容中
            w_succ: 0, // 用于计算"成功率";可选值为0或1
            c1: getParam('entry') || '',
            c5: getParam('from') ?? 'unknown',
            c3: e?.code,
            bl1: JSON.stringify(e),
          });
          if (!e?.code) {
            toast.show('网络不佳，请检查网络状况');
          }
          updatePageData({
            userInfo,
            mainInfo: {},
          });
        });
    };
    if (!isLoading) {
      /**
       * ssr场景hydrate数据
       * 1、已经登录绑定
       * 2、登录未绑定
       */
      if (validInitialData) {
        console.log(`首屏成功 ${data.userInfo?.kps_wg ? '已登录' : '未登录'}`);
        tracker.log({
          category: 100, // 系统自动生成，请勿修改
          msg: '首屏请求-成功', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: getParam('entry') || '',
          c2: data.mainInfo?.userInfo?.ucFarmHasAuth, // 认证过
          c4: `${!!data.userInfo?.kps_wg}`,
          c5: getParam('from') ?? 'unknown',
          bl1: JSON.stringify(data),
          w_trace_reqid: data?.mainInfo?.['x_wpk_reqid'] ?? ''
        });
        stat.updateParam({
          deviceLevel: data.mainInfo?.deviceLevel,
          tree_level: data.mainInfo?.gameInfo?.plantInfo?.seedStage?.stageLevel ?? '-1',
        });
        updatePageData(data);
      } else {
        const kps = data.userInfo?.kps_wg;
        tracker.log({
          category: 100, // 系统自动生成，请勿修改
          msg: `首屏请求-${kps ? '已登录' : '未登录'}-未绑定`, // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: getParam('entry') || '',
          c2: data.mainInfo?.userInfo?.ucFarmHasAuth, // 认证过
          c3: data.errCode,
          c4: data.userInfo?.kps_wg || '',
          c5: getParam('from') ?? 'unknown',
          bl1: JSON.stringify(data),
          w_trace_reqid: data?.mainInfo?.['x_wpk_reqid'] ?? ''
        });

        if (!homeBusinessErrCode.includes(data?.mainInfo?.code)) {
          // 非业务错误，才需要重试
          regetData();
        } else {
          // 业务错误，显示游客态
          updatePageData({
            ...data,
            mainInfo: {},
          });
        }
      }

      // 批量资源位 | kps_wg 为空，接口重试
      if (!validInitialMultiResource || !data.userInfo?.kps_wg) {
        tracker.log({
          category: 156, // 系统自动生成，请勿修改
          msg: `首屏-获取批量资源位-接口重试`,
          w_succ: 0,
          bl1: JSON.stringify(data?.multiResource || {}),
        });
        // 资源接口重试
        dispatch.resource.queryResource({});
      }
    }
  }, [isLoading, validInitialData, data, validInitialMultiResource]);

  useEffect(() => {
    if (isLoading) {
      return;
    }
    try {
      // eslint-disable-next-line no-inner-declarations
      function rectify(num = 0) {
        return num > 18000 ? 18000 : num;
      }
      setTimeout(() => {
        const preInfo = getPerf();
        const info = {
          msg: preInfo?.documentType,
          category: 119,
          wl_avgv1: rectify(preInfo?.entryExecute),
          wl_avgv2: rectify(preInfo?.cacheInnerHTMLTime),
          wl_avgv3: rectify(preInfo?.innerHTMLTime),
          wl_avgv4: rectify(preInfo?.bootstrapExecute),
          wl_avgv5: rectify(preInfo?.appHydrated),
          wl_avgv6: rectify(preInfo?.appGotFirstScreenData),
        };
        // console.log('preInfo:', preInfo, info);
        tracker.log(info);
      }, 300);
      // 延迟2.5S、采集APP安装情况
      setTimeout(() => {
        dispatch.app.checkAppInstall();
      }, 2500);
    } catch (e) {
      console.log(e);
    }
  }, [isLoading]);

  return <PageIndex />;
}

export default function (props: { ctx?: any; initialProps: any }) {
  return (
    <InitialDataProvider {...props}>
      <PageData {...props} />
    </InitialDataProvider>
  );
}
