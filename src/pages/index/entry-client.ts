import {
  preFetchInitialData,
  setInitialData,
  waitAndFetchInitialData,
  wrapAsyncCSRPrefFetchInitialData,
  getInitialDataTimeoutFromDocument,
  detectStreamFirstData,
} from '@ali/compass-app/lib/utils/entry';
import { geSnapshotCache } from '@ali/farm-utils';
import {
  setAsyncSSRCache,
  getAsyncSSRCache,
  clearExpiredAsyncSSRCache,
} from '@ali/compass-app/lib/utils/async-ssr-cache';
import { perfRenderAction, perfMark } from '@ali/compass-app/lib/utils/pref';
import { getRenderType } from '@ali/compass-app/lib/utils/client';
import { getInitialData } from './data.ts';
import { SNAPSHOT_CACHE_KEY } from "@/constants";
import '../../../src/common.scss';

const renderType = getRenderType();
console.log('[compass entry js] renderType ', renderType);
// 记录entry js 开始执行的时间
perfMark(perfRenderAction.entryExecute);

// 开启react snapshot，等external的React加载完成才执行bootstrap
const snapshotJs = '';
console.log('[compass entry js] snapshotJs:', snapshotJs);

const PAGE_NAME = 'index';

let isSnapShotLoaded = false; // snapshot资源是否加载完成
let bootstrapDataLoaded = false; // 并行SSR的数据是否加载完并上屏

if (snapshotJs) {
  document.body.addEventListener('SNAPSHOT_JS_LOADED', () => {
    isSnapShotLoaded = true;
    perfMark(perfRenderAction.reactSnapshotLoaded);
    if (bootstrapDataLoaded) {
      // bootstrap 数据相关的流程先于snapshot事件，导入bootstrap
      perfMark(perfRenderAction.bootstrapLoadStart);
      import(/* webpackChunkName: "pages/index/index_bootstrap" */ './bootstrap.tsx');
    }
  });
}

const routeConfig = { asyncSsrCache: true, asyncSsrCacheExpiredTime: 86400000 };
const USE_ASYNCSSR_CACHE = routeConfig.asyncSsrCache || false;
const CACHE_EXPIRED_TIME = routeConfig.asyncSsrCacheExpiredTime || 24 * 60 * 60 * 1000;
(async function () {
  switch (renderType) {
    case 'NSR':
      // TODO: NSR相关待沟通。
      break;
    case 'SSR':
      break;
    case 'ASYNC_SSR':
      // TODO: 并行SSR细节处理
      // 1. 版本号校验
      // 2. 本地 & 线上 区分
      // eslint-disable-next-line no-case-declarations
      try {
        // 使用并行SSR缓存。
        // 不同页面的缓存key需要不一样，否则会导致不同页面使用同一份html缓存。
        const uniKey = `${PAGE_NAME}_${BUILD_UUID}`;
        const rootContainer = document.getElementById('root');

        // 开启并行SSR缓存时，使用缓存
        if (USE_ASYNCSSR_CACHE) {
          const cachedHtml = getAsyncSSRCache(uniKey);

          if (cachedHtml && rootContainer) {
            console.log('[compass entry js]使用并行SSR缓存');
            perfMark(perfRenderAction.cacheInnerHTMLTime);
            rootContainer.innerHTML = cachedHtml;
          }
        }

        const getAsyncSsrResult = await new Promise((rs, rj) => {
          import('./data.ts').then((res) => {
            if (res.getAsyncSsrResult) {
              rs(res.getAsyncSsrResult);
            } else {
              console.error(
                '[compass entry js]当前为并行SSR模式，未检测到 getAsyncSsrResult 方法，请检查对应页面的data.ts文件。',
              );
              rj();
            }
          });
        });

        // 并行SSR接口 & 上屏
        perfMark(perfRenderAction.preFetchApiStart);
        const { html, initialProps } = await getAsyncSsrResult(); // 请求加到事件循环，其它已加载js文件能够执行（比如snapshot）
        perfMark(perfRenderAction.preFetchApiEnd);
        perfMark(perfRenderAction.preFetchApiSpend);
        if (!html || !initialProps) throw new Error();
        perfMark(perfRenderAction.innerHTMLTime);
        rootContainer.innerHTML = html;
        setInitialData(initialProps.initialData);
        // 开启并行SSR缓存时，设置缓存
        if (USE_ASYNCSSR_CACHE) {
          // 设置缓存
          setAsyncSSRCache(uniKey, CACHE_EXPIRED_TIME, html);
          console.info('[compass entry js]存储并行SSR缓存');

          setTimeout(() => {
            // 清除过期缓存
            clearExpiredAsyncSSRCache();
          }, 10e3);
        }
      } catch {
        // 兜底获取数据
        console.warn('[compass entry js]并行SSR兜底');
        preFetchInitialData(getInitialData());
      }
      break;
    case 'STREAM_CSR':
      // 流式CSR
      waitAndFetchInitialData(getInitialData);
      break;
    case 'STREAM_SSR':
      // 流式SSR
      try {
        // 这里要设置一个超时，避免chunk2没有回来但链接被服务端主端关闭而进入一直等待状态
        await detectStreamFirstData(getInitialDataTimeoutFromDocument());
        console.log('[compass entry js]流式SSR 已上屏，设置首屏数据，即将进入hydrate');
      } catch (err) {
        console.warn('[compass entry js]流式SSR兜底-走降级CSR渲染', err);
        // 做个标记，chunk2回来后不上屏，以及用于统计流式SSR 成功率
        window.__STREAM_SSR_FALLBACK__ = 1;
        preFetchInitialData(getInitialData());
      }
      break;
    case 'ASYNC_CSR':
      try {
        const getAsyncCSRResult = await new Promise((rs, rj) => {
          import('./data.ts').then((res) => {
            if (res.getAsyncCSRResult) {
              rs(res.getAsyncCSRResult);
            } else {
              console.error(
                '[compass entry js]当前为并行CSR模式，未检测到 getAsyncCSRResult 方法，请检查对应页面的data.ts文件。',
              );
              rj();
            }
          });
        });
        wrapAsyncCSRPrefFetchInitialData(getAsyncCSRResult, getInitialData);
        break;
      } catch {
        // 兜底获取数据
        console.warn('[compass entry js]并行CSR兜底');
        preFetchInitialData(getInitialData());
      }
      break;
    case 'CSR':
    default:
      preFetchInitialData(getInitialData());
  }
  if (['SSR', 'ASYNC_SSR'].includes(renderType)) {
    setTimeout(() => {
      try {
        const cacheCanvasImg = geSnapshotCache(`${SNAPSHOT_CACHE_KEY}_IMAGE`);
        const cacheCanvasElement = document.getElementById(`FARM_CANVAS_SNAPSHOT_${SNAPSHOT_CACHE_KEY}_IMAGE`) as HTMLImageElement | null;
        if (cacheCanvasImg && cacheCanvasElement) {
          cacheCanvasElement.src = cacheCanvasImg;
          cacheCanvasElement.style.width = '100%';
        }
      } catch (e) {
        console.error(e);
      }
    }, 0);
  }

  // 未开启snapshot或者snapshot资源已加载，导入bootstrap
  if (!snapshotJs || isSnapShotLoaded) {
    perfMark(perfRenderAction.bootstrapLoadStart);
    import(/* webpackChunkName: "pages/index/index_bootstrap" */ './bootstrap.tsx');
  }
  bootstrapDataLoaded = true; // 标记bootstrap数据相关的流程已执行
})();
