

html,
#root
{
  height: 100%;
  font-family: D-DIN-Bold;
}
#root {
  background: #3c7026;
  .comp-game {
    background-image: url("./components/Game/assets/<EMAIL>");
    .bottom-comp,
    {
      top: 1180rpx;
    }
  }
  .new-year-bg {
    background-image: url("./components/Game/assets/NewYear/<EMAIL>");
  }
}
// 高屏幕换背景，调整元素距离顶部高度
html[device-type='high'] {
  #root {
    .comp-game {
      .bottom-comp{
        top: 1300rpx !important;
        .tobe-exchanged {
          top: -174rpx !important;
        }
      }
    }
    .new-year-bg {
      background-image: url("./components/Game/assets/NewYear/<EMAIL>") !important;
    }
    .bbz-entry-comp{
      top: 848rpx !important;
    }
    .combine-img,
     {
      top: 920rpx !important;
    }
    .tourist {
      top: 1300rpx !important;
    }
  }
}
html[farm-type='night'] {
  #root {
    background: #1a4625 !important;
    .comp-game {
      background-image: url("./components/Game/assets/<EMAIL>") !important;
    }
    .new-year-bg {
      background-image: url("./components/Game/assets/NewYear/<EMAIL>") !important;
    }
  }
}
html[farm-type='night'][device-type='high'] {
  #root {
    background: #1a4625 !important;
    .new-year-bg {
      background-image: url("./components/Game/assets/NewYear/<EMAIL>") !important;
    }
  }
}

// 静态树位置
.farm-tree-area .tree-container {
  position: absolute;
  bottom: 47rpx !important;
  margin: 0 auto;
  left: 50%;
  transform: translateX(-49%) !important;
}

* {
  // 修复ios点击弹窗关闭按钮出现阴影
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important; /* 设置透明度为0 */
}

.wrc-modal-body {
  img {
    uc-perf-stat-ignore: image;
  }
  .btn-confirm {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 470rpx;
    height: 100rpx;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
    line-height: 100rpx;
  }
}
img {
  -webkit-user-select: none !important;
  -webkit-user-drag: none !important;
}

#app {
  width: 100vw;
  min-height: 100%;
  overflow-x: hidden;
}

.loading {
  position: fixed;
  left: 0;
  top: 45%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60rpx;
  line-height: 60rpx;
}

.loading-img {
  width: 30rpx;
  height: 30rpx;
  background-color: rgb(40,40,245);
  animation: aaa 1s linear infinite;
  margin-right: 20rpx;
}

@keyframes aaa {
  0% {
    transform: rotateZ(0);
  }

  100% {
    transform: rotateZ(360deg);
  }
}

.row {
  display: flex;
  flex-direction: row;
}

.column {
  display: flex;
  flex-direction: column;
}

.align-c {
  align-items: center;
}

.j-center {
  justify-content: center;
}

.flex-center{
  display: flex;
  align-items: center;
  justify-content: center;
}
