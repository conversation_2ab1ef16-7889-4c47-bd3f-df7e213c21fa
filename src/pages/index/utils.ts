import {IGetUserInfoRes} from "@/api/typings";
import buwang from '@ali/uc-toolkit/lib/buwang';
import { getParam } from '@/lib/qs';
import { mx } from "@ali/pcom-iz-use";
import { IFarmHelpPlantState } from '@/logic/store/models/farmHelpPlant/type';
import { QueryCardInfoRes } from "@/api/doublePointsCard/typings";

export const buwangDownload = () => {
  const entry: string = getParam('entry') || 'others';
  try {
    // @ts-ignore
    buwang.download({
      entry,
    });
  } catch (e) {
    console.warn('布网调端异常');
  }
};

export const userInfoHandler = (userInfo: IGetUserInfoRes) => {
  if (!userInfo) return {};
  const { kps_wg, service_ticket, loginStatus, sign_wg, uId, utdId, nickname, avatar_url, uidWg, vCode } = userInfo;
  return {
    userInfoInit: true,
    kps: kps_wg,
    serviceTicket: service_ticket,
    isLogin: loginStatus,
    signWg: sign_wg,
    nickname: nickname || '',
    avatar: avatar_url || '',
    vcode: vCode,
    uId,
    utdId,
    uidWg,
  }
}

// 助力失败文案
export const HELP_CODE_MAP = {
  "INVITE:INVALID_CODE": {
    title: "助力失败",
    subTitle: '无效助力码，让TA重新分享一个吧',
    status: 2,
  },
  LOCK_TIMEOUT: {
    title: "助力失败",
    subTitle: '网络太繁忙，请重新试试吧',
    status: 3,
  },
  INVALID_PARAM: {
    title: "助力失败",
    subTitle: '请求出问题啦，请重新试试吧',
    status: 4,
  },
  "INVITE:CODE_EXPIRED": {
    title: "助力失败",
    subTitle: '链接已过期，让TA重新分享一个吧',
    status: 5,
  },
  "INVITE:BIND_CODE_DAILY_LIMIT": {
    title: "助力失败",
    subTitle: '当天助力次数已达上限，明天再来吧',
    status: 6,
  },
  "INVITE:INVITEE_NUM_LIMIT": {
    title: "助力失败",
    subTitle: '当天助力用户数已达上限，明天再来吧',
    status: 7,
  },
  "INVITE:ALREADY_BIND_CODE": {
    title: "助力失败",
    subTitle: '你已帮TA助力过',
    status: 8,
  },
  HELP_SEND_FAIL: {
    title: "助力成功，但肥料发放出了点问题",
    subTitle: '正在为您补发肥料，可进任务查看进度~',
    status: 9,
  },
  "INVITE:OWN_CODE": {
    title: "助力失败",
    subTitle: '不能给自己助力哟，快去邀请好友吧',
    status: 10,
  },
  NON_TARGET_USER: {
    title: "助力失败",
    subTitle: '非活动目标邀请用户，助力失败',
    status: 11,
  },
  /** 用户账号异常 */
  'INVITE:UNUSUAL_USER': {
    title: "助力失败",
    subTitle: '账号存在异常，无法助力',
    status: 12,
  },
  /** 发起人账号异常 */
  'INVITE:UNUSUAL_MASTER_USER': {
    title: "助力失败",
    subTitle: '您的好友账号存在异常，无法助力',
    status: 13,
  },
  BANG_BANG_CODE: {
    title: "助力失败",
    subTitle: '当前版本过低，请更新后重试',
    status: 14,
  },
  'INVITE:BIND_CODE_LIMIT': {
    title: "助力失败",
    subTitle: '只能给用户助力一次',
    status: 6,
  },
  /** 邀请人重合用户已被助力过 */
  'INVITE:MASTER_OVERLAP_BE_ASSIST': {
    title: "助力失败",
    subTitle: '网络太繁忙，请稍后再试',
    status: 15,
  },
  /** 多活动助力超每日上限  -- 普通、帮帮种都是这个错误码 */
  MULTI_ASSIST_DAILY_LIMIT: {
    title: "助力失败",
    subTitle: '你已帮TA助力过',
    status: 16,
  },
  ACT_END: {
    title: "助力失败",
    subTitle: '感谢您的参与！活动已结束！',
    status: 17,
  }
}

export const TasklistSource = {
  auto: 'auto',
  collect_fertilizer: 'collect_fertilizer',
  tomorrow_fertilizer: 'tomorrow_fertilizer',
  fertilizer_gift: 'fertilizer_gift',
  fertilizer_lack: 'fertilizer_lack',
  award_task: 'award_task',
  double_card: 'double_card',
};

/** 获取农场主页帮帮种埋点公共参数 */
export const getFarmHelpPlantFactParams = () => {
  const farmHelpPlant: IFarmHelpPlantState = mx.store.getStore().farmHelpPlant;
  const ranking = farmHelpPlant?.helpPlantHome?.ranking;
  const assist_value = farmHelpPlant?.helpPlantHome?.score;
  let assist_rank = ranking > 0 ? ranking : assist_value > 0 ? 0 : '-1';
  return {
    bbnc_activity: 'bbzhong',
    assist_value,
    assist_rank,
  }
};

export const getDoubleCardFactParams = () => {
  const cardInfo = mx.store.getStore().doublePointsCard;
  const curDoubleNum = cardInfo?.todayCardInfo?.doubleNum // 当前卡倍数
  const toDayHasDraw = cardInfo?.drawInfo?.totalDrawTimes // 已抽奖次数
  // 是否抽中翻倍卡
  const buildHasDrawCard = () => {
    if (!toDayHasDraw) return 3;
    return curDoubleNum > 0 ? 1 : 0
  }
  return {
    bbnc_activity: 'fdcard',
    assist_value: buildHasDrawCard(), // 是否抽中翻倍卡 1（表示抽中）0（表示没有抽中）3（表示没有参与活动）
    final_multiplier: curDoubleNum > 0 ? curDoubleNum : '-1', // 最终倍数（卡今日最大倍数）
  }
}

/**
 * 获取农场主页帮帮种入口状态埋点
 */
export const getFarmHelpPlantEntryStatusFact = () => {
  const farmHelpPlant: IFarmHelpPlantState = mx.store.getStore().farmHelpPlant;
  const {score, ranking, unReceiveAward, totalAward, helpFirstVisit, rankingAward} = farmHelpPlant.helpPlantHome;

  const getIconStatus = () => {
    // 未参与
    if (!score) {
      return 1
    }
    // 待领奖
    if (unReceiveAward) {
      return 2
    }
    // 上榜
    if (ranking) {
      return 4
    }
    // 未上榜
    return 3
  }

  const getCardStatus = () => {
    // 未参与
    if (helpFirstVisit) {
      return 1
    }
    // 待领奖
    if (unReceiveAward) {
      return 5
    }
    if (ranking && ranking <= 100 && rankingAward) {
      return 3
    }
    if (ranking) {
      return 4
    }
    return 2
  }

  return {
    icon_status: getIconStatus(),
    card_status: getCardStatus(),
    assist_fertilizer_reward: totalAward,
  }
};
