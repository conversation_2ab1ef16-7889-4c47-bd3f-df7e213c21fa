import React, { useEffect, useRef } from 'react';
import { isWeb } from '@ali/compass-env';
import mx from '@ali/pcom-mx';
import dispatch from '@/logic/store';
import useMxState from '@/hooks/useMxState';
import { isIOS } from '@/lib/universal-ua';
import './index.scss';
import '@ali/farm-react-component/lib/css/index.css';
import SubScreen from '@/components/subscreen';
import Game from './components/Game/index';
import stat from '@/lib/stat';
import { EventMap } from '@/lib/utils/events';
import { init } from '@/logic/initEvent';
import { IAppState } from '@/logic/store/models/app/typings';
import { gestureDisabled, forbiddenToolbar, disableIOSBounces, notifyVisitBaBaFarm, finishDesktopTask, lockOrientation
} from '@/lib/ucapi';
import { updateWidget, widget } from '@/lib/utils/updateWidget';
import { disabledPinchToZoom, landscapeBan } from '@/lib/utils/fn';
import { FactConfig } from '@/config/fact';
import { getParam } from '@/lib/qs';
import { imgsPreload } from "@/pages/index/preloadImgs";
import {usePageVisibilityListener} from '@/hooks/useVisibilitychange';
import { IUserState } from '@/logic/store/models/user/typings';
import tracker from '@/lib/tracker';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import {getBgColor} from '@/logic/store/models/app/new_year_time';
import adPlayerCache from "@/pages/index/components/TaskPop/TaskList/adPlayerCache";
import { browseAdPlayerInstance } from '@/lib/adPlayer/ad_video';
import { TaskInfo } from './components/TaskPop/TaskList/types';
import { RES_CODES } from '@/logic/store/models/cms/typings';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import Toast from '@/lib/universal-toast';
import { setItraceDocumentMeta } from '@/lib/utils/itrace-meta';

function PageIndex() {
  const timer = useRef<NodeJS.Timeout>();
  const canResize = useRef<boolean>(true);
  const [app] = useMxState<IAppState>('app');
  const [user] = useMxState<IUserState>('user');
  const jsapiHandler = async () => {
    const entry = getParam('entry');
    try {
      await notifyVisitBaBaFarm();
      if (isIOS && entry === 'farm_desktop') {
        // 从桌面组件进来 通知ios收起组件引导弹框
        await finishDesktopTask('farm');
      }
    } catch (e) {
      console.log('jsapiHandler:', e);
    }
  };
  useEffect(() => {
    statPv();
    disabledPinchToZoom();
    landscapeBan();
    try {
      gestureDisabled();
      forbiddenToolbar();
      imgsPreload()
      console.log('useEffect call jsapiHandler');
      jsapiHandler();
      if (isIOS) {
        // ios 才有锁定竖屏
        lockOrientation()
      }
      setTimeout(() => {
        // 禁用 iOS 弹性滚动
        disableIOSBounces(['web_root_scroll']);
      }, 200);
    } catch (error) {
      console.log('error==>禁止手势返回、长按工具栏', error);
    }
  }, []);
  useEffect(() => {
    if (!app.isHomeDataLoading && !app.isHomeDataFail && app.mainInfo) {
      init(app);
    }
  }, [app]);
  // 监听用户切换登录态
  useEffect(() => {
    const onLoginChange = async ({ isLogin }) => {
      if (isLogin) {
        await dispatch.app.onLoginSuccess();
        stat.click('login_agree', {
          c: 'login',
          d: 'agree',
        });

        // 登录状态变化后，高价值奖励弹窗回收
        const baseCurrentOpenModal = baseModal.getCurrentOpenModalObj();
        if (Object.keys(baseCurrentOpenModal).length && baseCurrentOpenModal[MODAL_ID.HIGH_VALUE_AWARD]) {
          baseModal.close(MODAL_ID.HIGH_VALUE_AWARD);
          baseModal.removeCacheModal(MODAL_ID.HIGH_VALUE_AWARD);
        }
      }
    };

    let initWidth = window.screen.width;
    let initHeight = window.screen.height;
    const checkScreenSize = () => {
      if (!canResize.current) return;
      const newWidth = window.screen.width;
      const newHeight = window.screen.height;
      // 判断是否由竖屏切换到横屏, 有横屏切到竖屏
      if (initWidth === newHeight || initHeight === newWidth || window.orientation === 90 || window.orientation === -90 || initWidth === newWidth) {
        initWidth = newWidth;
        initHeight = newHeight;
      } else {
      // 修复折叠屏从收起到展开时,样式异常
        tracker.log({
          category: 153,
          sampleRate: 1,
          w_succ: 1,
          msg: '页面刷新了',
        });
        location.reload();
      }
    }
    // 监听屏幕尺寸变化
    window.addEventListener('resize', checkScreenSize)
    mx.event.on(EventMap.LoginChange, onLoginChange);
    document.addEventListener('UCEVT_Welfare_onReadOnceMissionFinished', onReadOnceMissionFinished);
    return () => {
      mx.event.off(EventMap.LoginChange, onLoginChange);
      document.removeEventListener('UCEVT_Welfare_onReadOnceMissionFinished', onReadOnceMissionFinished)
    };
  }, []);

  const onReadOnceMissionFinished = async (state) => {
    const readOnceMonitor = tracker.Monitor(162);
    console.log('state===收到回调', state);
    const handleSearchTask = async (rewardAmount, rewardMark) => {
      const taskList: TaskInfo[] = mx.store.getStore().task.taskList;
      const curTask = taskList.find(item => item.id === JSON.parse(state.detail.tid));
      if (curTask) {
        const taskReward = curTask?.rewardItems?.[0];
        const cmsStore = mx.store.getStore()?.cms;
        const showListMatrioska = cmsStore[RES_CODES.OPEN_TASK_LIST_MATRIOSKA]?.items?.[0]?.openMatrioska === '1';
        const taskFromType = localStorage.getItem(LocalStorageKey.FINISH_TASK_FROM);
        const idNeedMatrioska = ['list', 'permanent_ad'].includes(taskFromType || '');
        const openMatrioska = taskFromType === 'taskBubble' || (idNeedMatrioska && showListMatrioska);
        if (openMatrioska) {
          // 等资源回来在出套娃弹框, 避免当前任务已经完成了还出当前的套娃任务
          await dispatch.resource.queryResource({});
          baseModal.open(MODAL_ID.PROGRESS_BUBBLE, {
            isReward: true,
            pointAmount: rewardAmount || taskReward?.amount,
            rewardMark: rewardMark || taskReward?.mark,
            modalType: taskFromType,
            curTask,
          });
        } else {
          Toast.show('搜索任务完成, 奖励已发放');
        }
      }
    }
    try {
      const extParams = JSON.parse(state?.detail?.ext_params);
      const rewardMark = state?.detail?.reward_mark;
      const rewardAmount = state?.detail?.reward_amount;
      readOnceMonitor.success({
        msg: '收到关键词完成回调',
        c1: state?.detail?.tid,
        c2: extParams?.q,
        c3: state?.detail?.scene,
        c4: JSON.stringify({
          mark: state?.detail?.reward_mark,
          amount: state?.detail?.reward_amount,
        }),
        bl1: JSON.stringify(state?.detail),
      });
      if (state && state.detail && state.detail.tid && state.detail.ext_params) {
        const PageVisible = () => {
          handleSearchTask(rewardAmount, rewardMark);
          mx.event.off(EventMap.PageVisible, PageVisible);
        }
        mx.event.on(EventMap.PageVisible, PageVisible);
      }
    } catch (e) {
      console.log('关键词任务回调上报报错：', e);
      readOnceMonitor.fail({
        msg: '收到关键词完成回调-数据出错',
        c1: state?.detail?.tid,
        c2: state?.detail?.ext_params,
        c3: state?.detail?.scene,
        bl1: JSON.stringify(state?.detail),
      });
    }
  }

  // 上报设备等级字段
  useEffect((
  ) => {
    if (user.deviceInfo?.deviceLevel !== 'unknown') {
      stat.exposure('user_device_level', {
        deviceLevel: user.deviceInfo?.deviceLevel,
        c: 'user',
        d: 'device'
      });
      
      setItraceDocumentMeta('__lp_disable', user.deviceInfo?.deviceLevel ?? 'unknown');
    }
  }, [user.deviceInfo?.deviceLevel])

  // pv 上报
  const statPv = () => {
    stat.updateParam({
      deviceLevel: app.mainInfo?.deviceLevel,
      tree_level: app.mainInfo?.gameInfo?.plantInfo?.seedStage?.stageLevel ?? '-1',
    });
    stat.pv(FactConfig.indexConfig.page);
  };

  // 监听页面 active
  const handleVisible = () => {
    console.log('handleVisible');
    if (!isIOS) {
      console.log('visible jsapiHandler');
      jsapiHandler();
    }
    dispatch.task.set({showTaskToast: true});
    dispatch.app.updateAllData(false, false, app.needPageVisibleUpdate);
    // 仅看视频任务回来时触发查奖，避免和其他任务的提示冲突
    if (!app.needPageVisibleUpdate) {
      // 保证异步查询奖励和激励视频收到回调不同时发奖
      setTimeout(() => {
        dispatch.app.dealwithVideoAward(false);
      }, 2000)
    }
    preloadNextAd();
    timer.current = setTimeout(async () => {
      dispatch.app.setIsPageActive(true);
      mx.event.emit(EventMap.PageVisible);
      dispatch.app.onPageVisible();
      canResize.current = true;
      dispatch.app.set({needPageVisibleUpdate: true});
    }, 500);
  };

  // 如果之前看了视频，回到页面后预加载相同任务的下一个视频
  const preloadNextAd = () => {
    const lastShowAdTask = adPlayerCache.getLastShowAdTask();
    if (lastShowAdTask) {
      browseAdPlayerInstance.adTaskPreload(lastShowAdTask);
    }
  }

  // 监听页面 hidden
  const handleHidden = () => {
    console.log('handleHidden');
    canResize.current = false;
    if (timer.current) clearTimeout(timer.current);
    updateWidget(widget);
    dispatch.app.setIsPageActive(false);
    mx.event.emit(EventMap.PageHidden);
  };


  usePageVisibilityListener((visible: boolean) => {
    console.log('[page visible]', visible);
    console.log('[page visible] handle', dispatch.app.isAppInit);
    // 页面未初始化不触发
    if (!dispatch.app.isAppInit) {
      return;
    }
    if (visible) {
      handleVisible();
      return;
    }
    handleHidden()
  });

  const firstScreenComponent = (
    <React.Fragment>
      <Game />
    </React.Fragment>
  );

  const subScreenComponent = isWeb ? <SubScreen /> : null;
  return (
    <div style={{...getBgColor()}} id="app">
      {firstScreenComponent}
      {subScreenComponent}
    </div>
  );
}

export default PageIndex;
