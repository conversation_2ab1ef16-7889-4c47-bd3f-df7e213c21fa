import React from 'react';
import './index.scss';
import store from '@/logic/store';
import TitleLogo from './assets/<EMAIL>';
import Banner from './assets/<EMAIL>';
import NoticeIcon from './assets/notice_icon.png';
import MessageScroll from '@/components/common/MessageScroll/index';
import mx from '@ali/pcom-mx';
import {logoImg} from '@/logic/store/models/app/new_year_time';
function BannerWrap() {
  const app = mx.store.get('app');
  const noticeList = app?.mainInfo?.frontData?.noticeList || [];
  const handleClick = () => {
    store.user.checkLoginAndBind(0, 3);
    // mx.event.emit('needBindTaobao')
  };
  return (
    <div className="banner-wrap" onClick={handleClick}>
      <img src={TitleLogo} className="title" alt="芭芭农场，免费领水果，助果农增收" />
      <img src={app?.isSpringPeriod ? logoImg : Banner} className="banner" alt="一分钱领一箱水果" />
      {!!noticeList?.length && (
        <div className="notic">
          <img className="notic-icon" src={NoticeIcon} alt="" />
          <MessageScroll
            messages={noticeList}
            interval={5000}
            renderItem={(item, index) => {
              return (
                <div key={index} className="message-item">
                  <div className="message-item-text">{item?.text}</div>
                </div>
              );
            }}
          />
        </div>
      )}
    </div>
  );
}

export default BannerWrap;
