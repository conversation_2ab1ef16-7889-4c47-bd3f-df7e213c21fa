import React, { Fragment, useEffect, useState } from 'react';
import './index.scss';
import { isWeb } from 'universal-env';
import BannerWrap from '@/pages/index/components/BannerWrap';
import NavBack from '@/pages/index/components/NavBack';
import UCGameMain from '@/components/uc/gameMain';
import Tourist from '@/pages/index/components/Tourist';
import Bottom from '@/pages/index/components/Bottom';
import TomorrowFertilizer from '@/gameObject/uc/tomorrowFertilizer';
import ResourceNiche from '@/pages/index/components/ResourceNiche';
import useMxState from '@/hooks/useMxState';
import { IAppState } from '@/logic/store/models/app/typings';
import TreeSnapshot from '@/pages/index/components/TreeSnapshot';
import { isHighMode } from "@/gameObject/utils/screen";
import LimitedTimeBenefits from "@/pages/index/components/LimitedTimeBenefits"
import { ITimeLimitTaskState } from '@/logic/store/models/limit/types';
import TaskWidgetList from "@/pages/index/components/TaskWidgetList";
import {getBgImg} from '@/logic/store/models/app/new_year_time';

function Game() {
  const [isTourist, setIsTourist] = useState(false);
  // const [isFuzzyBackground, setIsFuzzyBackground] = useState<boolean>(false); // 羽化
  const [app] = useMxState<IAppState>('app');
  const [timeLimitTask] = useMxState<ITimeLimitTaskState>('timeLimitTask');
  const [minHeight, setMinHeight] = useState<string | number>('100%')
  useEffect(() => {
    if (!app.isHomeDataLoading && app?.mainInfo) {
      setIsTourist(!app?.mainInfo?.userInfo?.ucFarmHasAuth);
    }
  }, [app]);
  useEffect(() => {
    const resetDeviceType = () => {
      console.log('isHighMode:', isHighMode())
      if (isHighMode()) {
        document.documentElement.setAttribute("device-type", "high")
      } else {
        document.documentElement.removeAttribute("device-type")
      }
    }
    resetDeviceType()
    // 羽化监听
    // mx.event.on(MainAPI.FuzzyBackground, (value = false) => setIsFuzzyBackground(value));
    // 根据背景图比例设置尺寸
    const mh = window.innerWidth * (3248 / 1500)
    setMinHeight(mh)
  }, []);

  return (
    <div style={{ minHeight: minHeight}} className={`comp-game ${getBgImg()}`}>
      {!timeLimitTask.showLimitTask ? <BannerWrap /> : <LimitedTimeBenefits />}
      <NavBack />
      <Bottom />
      <TreeSnapshot />
      <ResourceNiche />
      <TaskWidgetList />
      {/* <ExtendTask /> */}
      {isWeb ? (
        <Fragment>
          {
            isTourist ? <Tourist /> : null
          }
          <UCGameMain showGameMain={!isTourist} />
          <TomorrowFertilizer />
        </Fragment>
      ) : null}
    </div>
  );
}

export default Game;
