import React, { memo, useCallback, useEffect, useMemo } from 'react';
import Fact from '@/components/Fact';
import { TaskInfo } from '../TaskPop/TaskList/types';
import { useTaskHandler } from '@/hooks/useTaskHandler';
import tracker from '@/lib/tracker';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import stat from '@/lib/stat';
import { getTaskAward } from '../TaskPop/TaskList/util';

interface TaskContainerProps {
  currentTask: TaskInfo | null;
  source: 'bubble' | 'list';
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

/**
 * 任务容器：只关注埋点和点击的中转
 * @param props 
 * @returns 
 */
const TaskContainer: React.FC<TaskContainerProps> = memo(({
  currentTask,
  source,
  children,
  className,
  onClick
}) => {
  const { handleTask, getCommonTrackParams } = useTaskHandler({
    currentTask,
    source
  });

  const trackParams = useMemo(() => 
    getCommonTrackParams(currentTask), 
  [currentTask, getCommonTrackParams]);

  const defaultClickHandler = useCallback(async () => {
    const tbTaskExtra = JSON.parse(currentTask?.extra || '{}');
    tracker.log({
      category: 129,
      sampleRate: 1,
      w_succ: 1,
      msg: `点击首页气泡资源位-${currentTask?.name}`,
      c1: '首页气泡',
      c2: `${currentTask?.id}`,
      bl1: JSON.stringify(currentTask),
      bl2: JSON.stringify(currentTask),
      bl3: JSON.stringify(tbTaskExtra),
    })
    stat.click('resource_click', {
      c: 'module',
      d: 'taobao',
      resource_location: 'hotair_balloon'
    })
    stat.click('task_click', {
      c: 'module',
      d: 'taobao',
      ...trackParams,
      resource_location: 'hotair_balloon'
    })
    localStorage.setItem(LocalStorageKey.RESOURCE_NICHE, 'resourceNiche');
    handleTask();
  }, [handleTask]);

  const clearCache = () => {
    setTimeout(() => {
      localStorage.removeItem(LocalStorageKey.RESOURCE_NICHE);
    }, 3000);
  }
  useEffect(() => {
    clearCache()
    return () => {
      clearCache()
    }
  }, [currentTask]);

  if (!currentTask) return null;

  return (
    <Fact
      c={source === 'bubble' ? 'module' : source}
      d="taobao"
      expoLogkey="task_exposure"
      expoExtra={{...trackParams, resource_location: 'hotair_balloon'}}
      noUseClick
      className={className}
      onClick={onClick || defaultClickHandler}
    >
      <Fact
        c="module"
        d="taobao"
        expoLogkey="resource_exposure"
        expoExtra={{resource_location: 'hotair_balloon'}}
        noUseClick
      >
        {children}
      </Fact>
    </Fact>
  );
});

export default TaskContainer; 
