import { ITaskState } from '@/logic/store/models/task/typing';
import { IResourceState } from '@/logic/store/models/resource/typings';
import { checkTaskFinished } from '../TaskPop/TaskList/util';
import { getArraysIdIntersection } from '@/utils/array';
import finalConfig from '@/config';

export interface BubbleInfo {
  taskId: number;
  img: string;
  abbreviation: string;
}

/**
 * 获取当前有效任务，只关注如何获取当前的有效任务
 * @param task 
 * @param resource 
 * @returns 
 */
export const useValidTask = (task: ITaskState | null, resource: IResourceState | null) => {
  const taskList = task?.taskList;
  const bubbleResource = resource?.multiResource?.[finalConfig.getBubbleTaskCode];
  const bubbleInfo = bubbleResource?.attributes?.bubbleInfo || [];
  const taskInfo = bubbleResource?.taskList || [];

  if (!taskList?.length || !bubbleInfo.length || !taskInfo.length) {
    return null;
  }

  const unfinishedTasks = taskList.filter(item => !checkTaskFinished(item));
  if (!unfinishedTasks.length) return null;

  const bubbleTaskIds = (bubbleInfo as BubbleInfo[]).map(item => ({ id: +item.taskId }));

  const validTasks = getArraysIdIntersection([
    taskInfo,
    unfinishedTasks,
    bubbleTaskIds
  ]);

  return validTasks?.[0] || null;
}; 