import React, { useEffect, useState, memo, useMemo, useCallback, useRef} from 'react';
import './index.scss';
import useMxState from '@/hooks/useMxState';
import { ITaskState } from '@/logic/store/models/task/typing';
import { getTaskAward, getTaskAmountType, AMOUNT_TYPE } from '../TaskPop/TaskList/util';
import TaobaoIcon from './images/taobao-resource.png';
import { IResourceState } from '@/logic/store/models/resource/typings';
import TaskContainer from './TaskContainer';
import { BubbleInfo, useValidTask } from './useValidTask';
import { TaskInfo } from '../TaskPop/TaskList/types';
import { mx } from '@ali/pcom-iz-use';
import RedPacketIcon from './images/redbag.png';
import PointIcon from './images/point.png';
import dispatch from '@/logic/store';
import config from '@/config';

interface TaskDisplayInfo {
  amount: number;
  amountType: AMOUNT_TYPE;
  img: string;
}

/**
 * 气泡资源位
 * UI 组件，只关注UI上的事情
 * @returns 
 */ 
const ResourceNiche: React.FC = () => {
  const [task] = useMxState<ITaskState>('task');
  const [resource] = useMxState<IResourceState>('resource');
  const bindTaobao = mx.store.get('user.bindTaobao');
  const [displayInfo, setDisplayInfo] = useState<TaskDisplayInfo>({
    amount: 0,
    amountType: AMOUNT_TYPE.POINT,
    img: ''
  });

  const currentTask = useValidTask(task, resource);
  const preExpoTaskId = useRef(0)

  const bubbleInfoMap = useMemo(() => {
    const bubbleInfo = resource?.multiResource?.[config.getBubbleTaskCode]?.attributes?.bubbleInfo || [];
    return (bubbleInfo as BubbleInfo[]).reduce((acc, item) => {
      acc[item.taskId] = item;
      return acc;
    }, {} as Record<number, BubbleInfo>);
  }, [resource]);

  const getTaskImg = useCallback((task: TaskInfo) => {
    const img = bubbleInfoMap[task.id]?.img;
    return img;
  }, [bubbleInfoMap]);

  useEffect(() => {
    if (!currentTask) return;
    const img = getTaskImg(currentTask);
    const rawAmount = getTaskAward(currentTask);
    const amountType = getTaskAmountType(currentTask);
    const amount = amountType === AMOUNT_TYPE.CASH ? rawAmount / 100 : rawAmount;
    
    setDisplayInfo({
      amount,
      amountType,
      img: img || TaobaoIcon
    });
  }, [currentTask, getTaskImg]);

  useEffect(()=>{
    if (bindTaobao && currentTask?.id && currentTask?.id !== preExpoTaskId.current) {
      preExpoTaskId.current = currentTask?.id;
      dispatch.highValueTask.resourceExposure(currentTask, 'EXPOSURE',config.getBubbleTaskCode)
    }
  }, [currentTask])  

  if (!bindTaobao) return null;

  if (!currentTask) return null;

  const { amount, amountType, img } = displayInfo;

  return (
    <TaskContainer
      currentTask={currentTask}
      source="bubble"
      className="bubble-resource"
    >
      {
        img ? (
          <img src={img} className="img-icon" />
        ) : null
      }
      <div className="content-wrap">
        <img className="amount-type" src={amountType === AMOUNT_TYPE.CASH ? RedPacketIcon : PointIcon} alt="" />
        <div className="amount-count">
          {amountType === AMOUNT_TYPE.CASH && <span className="amount-symbol">￥</span>}
          <span className="amount-value">{amount}</span>
        </div>
      </div>
    </TaskContainer>
  );
};

export default memo(ResourceNiche);
