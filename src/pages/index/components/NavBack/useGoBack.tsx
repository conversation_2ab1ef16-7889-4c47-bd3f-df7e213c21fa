import { isIOS } from '@/lib/universal-ua';
import { TASK_LOCATION, TaskInfo } from '../TaskPop/TaskList/types';
import { useBackIntercept } from '@/hooks/useBackIntercept';
import { exit, notifyChanged } from '@/lib/ucapi';
import { widget } from '@/lib/utils/updateWidget';
import { getParam } from '@/lib/qs';
import { mx } from '@ali/pcom-iz-use';
import stat from '@/lib/stat';
import finalConfig from '@/config';
import { getArraysIdIntersection } from '@/utils/array';
import { checkTaskFinished } from '../TaskPop/TaskList/util';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import { handleTask } from '@/utils/taskHandler';

const defaultBackInterceptData = {
  attributes: {
    retentionInfo: [],
    interceptDayCount: 5,
    interceptPageCount: 1,
    title: '',
    leftIcon: '',
    mainName: '',
    subName: '',
  },
  taskList: [],
  hiddenTaskIdList: []
};

export function useGoBack() {
  const getBackInterceptTask = (): TaskInfo | null => {
    const resource = mx.store.get('resource');
    if (!resource) {
      return null;
    }
    const interceptData = resource?.multiResource?.[finalConfig.getBackInterceptCode] || defaultBackInterceptData;
    // note: 挽留配置的任务
    const taskList = interceptData?.taskList || [];
    // note: 挽留配置信息
    const attributes = interceptData?.attributes;
    const retentionInfo = attributes?.retentionInfo || [];
    const retentionConfig = (retentionInfo as any[]).map(item => ({...item, id: +item.taskId}));
    // note: 任务列表
    const allTaskList = (mx.store.get('task')?.taskList || []).filter(item => !checkTaskFinished(item));
    const validTaskList = getArraysIdIntersection<TaskInfo>([taskList, allTaskList, retentionConfig]);
    const taskInfo = validTaskList[0]
    if (!taskInfo) {
      return null;
    }
    const retentionSelectInfo = retentionConfig.find(item => item.id === taskInfo.id);
    Object.assign(taskInfo, {
      title: attributes?.title,
      leftIcon: attributes?.leftIcon,
      mainName: retentionSelectInfo?.name,
      subName: retentionSelectInfo?.desc,
      imgs: retentionSelectInfo?.imgs,
    });
    return taskInfo;
  };

  const getResourceCountInfo = () => {
    const resource = mx.store.get('resource');
    const interceptData = resource?.multiResource?.[finalConfig.getBackInterceptCode] || defaultBackInterceptData;
    return {
      maxDailyInterceptCount: +interceptData.attributes.interceptDayCount,
      maxLifecycleInterceptCount: +interceptData.attributes.interceptPageCount
    };
  }

  const goBackHandler = async () => {
    const app = mx.store.get('app');
    // 安卓返回会触发页面不可见,触发两次通知组件更新;ios不会
    if (isIOS) {
      // 通知客户端更新桌面小组件
      try {
        await notifyChanged(widget.typeId, widget.widgetReceiverName);
      } catch (error) {
        console.log('error', error);
      }
    }
    // // 判断是否需要挽留弹窗
    // if (app?.uc20ExitFlag && getParam('entry') === '20th_anniversary') {
    //   mx.event.emit('modal_retention');
    //   return;
    // }
    stat.click('babafarm_exit_click', {
      c: 'function',
      d: 'exit',
    });
    exit();
  };

  const openBackInterceptPopup = (params?: {
    onClose?: () => void;
    onConfirm?: () => void;
    taskInfo: TaskInfo;
    [key: string]: any;
  }) => {
    baseModal.open(MODAL_ID.BACK_INTERCEPT, params);
  }

  const taskActionHandler = (taskInfo: TaskInfo) => {
    handleTask({currentTask: taskInfo, resource_location: TASK_LOCATION.RETAIN_POPUP})
  }

  const shouldAllowIntercept = () => {
    // 没有绑定淘宝不拦截
    const bindTaobao = mx.store.get('user.bindTaobao');
    if (!bindTaobao) {
      return false;
    }
    return true;
  };
  
  const { useInterceptEffect, leftIconClickHandler } = useBackIntercept<TaskInfo>({ 
    getBackInterceptTask,
    goBackHandler,
    openBackInterceptPopup,
    taskActionHandler,
    getResourceCountInfo,
    shouldAllowIntercept
  });
  useInterceptEffect()
  return {
    leftIconClickHandler
  }
} 
