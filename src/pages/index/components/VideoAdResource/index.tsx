import React, { Fragment, useEffect, useRef, useState } from 'react';
import useMxState from '@/hooks/useMxState';
import { IResourceState } from '@/logic/store/models/resource/typings';
import { ITaskState } from '@/logic/store/models/task/typing';
import './index.scss';
import config from '@/config';
import Manure from '@/pages/index/components/TaskPop/TaskList/images/manure-award-icon.png';
import { TASK_LOCATION, TASK_STATE, TaskInfo } from '../TaskPop/TaskList/types';
import {
  checkTaskCountDown,
  checkTaskFinished,
  getTargetTimeDiff,
  getTaskAward,
  getTaskCurDayTimeProcess,
  getTaskCurDayTimeTarget,
  isAdVideoTask,
  logToFinishTask,
} from '../TaskPop/TaskList/util';
import { mx } from '@ali/pcom-iz-use';
import dayjs from 'dayjs';
import dispatch from '@/logic/store';
import { formatLeftTime } from '@/lib/utils/date';
import CountDown from '@/components/common/CountDown';
import { geneTaskRequestId, isOpenQueryAward } from '@/logic/store/models/utils';
import stat from '@/lib/stat';
import Toast from '@/lib/universal-toast';
import { execWithLock } from '@/lib/utils/lock';
import { taskActionHandler } from '../TaskPop/TaskList/help';
import { IUserState } from '@/logic/store/models/user/typings';
import { getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import { browseAdPlayerInstance } from '@/lib/adPlayer/ad_video';
import { dealWithPreloadSuccessAdTask, ifShowTask } from '@/logic/store/models/task/helper';
import { IAppState } from '@/logic/store/models/app/typings';
import Fact from '@/components/Fact';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';

const VideoAdResource = () => {
  const [user] = useMxState<IUserState>('user');
  const [app] = useMxState<IAppState>('app');
  const [task] = useMxState<ITaskState>('task');
  const [resource] = useMxState<IResourceState>('resource');
  const { curTime } = mx.store.get('task') as ITaskState;
  const stateRef = useRef('');
  const isPreLoading = useRef(false);
  // @ts-ignore
  const multiResourceCurTime: number = resource?.multiResource?.__meta?.timestamp ?? curTime;
  const [preLoaded, setPreLoaded] = useState<boolean>(false);

  const homeAdResourceList = resource?.multiResource?.[config.homeAdResourceCode]?.taskList ?? [];

  // 可做任务列表
  const adTaskList = homeAdResourceList.filter((item) => {
    return !checkTaskFinished(item) && isAdVideoTask(item);
  });

  // 当前可做的任务: 同任务列表一样，经视频预加载逻辑处理
  const preLoadedAdTaskList = (adTaskList ?? []).filter((item) => ifShowTask(item));
  // 获取当前要做的任务
  const taskInfo = (preLoadedAdTaskList?.[0] ?? {}) as TaskInfo;

  // 埋点公共参数
  const factTaskInfo = () => {
    if (!taskInfo?.id) {
      const finishTask = homeAdResourceList.filter((item) => {
        return checkTaskFinished(item) && isAdVideoTask(item);
      });
      const curTask = finishTask?.length ? finishTask[finishTask.length - 1] : ({} as any as TaskInfo);
      return {
        task_id: curTask?.id,
        task_name: curTask?.name,
        taskclassify: curTask?.taskClassify,
        groupcode: curTask?.groupCode,
        award_amount: Object.keys(curTask).length ? getTaskAward(curTask) : 0,
        task_count: curTask?.dayTimes?.progress || 0,
        isfinish: checkTaskFinished(curTask) ? 1 : 0,
        resource_location: 'permanent_video',
      };
    }
    return {
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      taskclassify: taskInfo?.taskClassify,
      groupcode: taskInfo?.groupCode,
      award_amount: Object.keys(taskInfo).length ? getTaskAward(taskInfo) : 0,
      task_count: taskInfo?.dayTimes?.progress || 0,
      isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
      resource_location: 'permanent_video',
    };
  };

  useEffect(() => {
    if (user.bindTaobao && adTaskList.length) {
      adPlayInit();
      return;
    }
    // 任务完成，不需要视频预加载
    setTimeout(() => {
      if (!isPreLoading.current && !adTaskList?.length) {
        setPreLoaded(true);
      }
    }, 3000);
  }, [adTaskList, user?.bindTaobao]);

  const handleEndPreLoad = (isLast) => {
    if (!isLast) {
      return;
    }
    isPreLoading.current = false;
    setPreLoaded(true);
  };
  // 激励视频预加载
  const adPlayInit = async () => {
    const isLite = app?.pr === 'UCLite';
    const appVersion = await dispatch.app.getAppVersion();
    const isOpenQueryReward = isOpenQueryAward(appVersion, isLite);
    const preloadAdTaskMap = task.preloadAdTaskMap;
    if (adTaskList?.length) {
      isPreLoading.current = true;
    }
    adTaskList.forEach((adTask, index) => {
      const isLast =
        index === adTaskList.length - 1 &&
        adTaskList.findIndex((item) => item?.id === adTask?.id) === adTaskList.length - 1;
      if (preloadAdTaskMap.has(`${adTask?.id}`)) {
        handleEndPreLoad(isLast);
        return;
      }
      const adData = getIncentiveAdSlotData(adTask);
      if (adData?.slotKey) {
        preloadAdTaskMap.set(`${adTask?.id}`, adTask?.id);
        browseAdPlayerInstance.init({
          task: adTask,
          slotKey: adData?.slotKey,
          appId: adData?.appId,
          coralAppId: config?.appId,
          enableAsyncQueryReward: isOpenQueryReward,
          finishPreload: (res) => {
            // 加载成功
            if (res) {
              dealWithPreloadSuccessAdTask(adTask);
            }
            handleEndPreLoad(isLast);
          },
        });
      } else {
        handleEndPreLoad(isLast);
      }
    });
  };

  // 获取资源状态
  const getResourceState = () => {
    const { beginTime } = taskInfo;
    // 明日再来
    if (!taskInfo?.id || checkTaskFinished(taskInfo)) {
      return TASK_STATE.TOMORROW;
    }
    if (checkTaskCountDown(taskInfo, multiResourceCurTime)) {
      const { isSameDay } = getTargetTimeDiff(taskInfo.beginTime, multiResourceCurTime);
      const endOfD = dayjs(multiResourceCurTime).endOf('day');
      const isToday = isSameDay && endOfD.diff(beginTime, 's') > 60;
      if (!isToday) {
        return TASK_STATE.TOMORROW;
      }
      return TASK_STATE.COUNTDOWN;
    }
    return TASK_STATE.FEASIBLE;
  };
  const taskState = getResourceState();
  useEffect(() => {
    if (!homeAdResourceList?.length || !preLoaded) {
      return;
    }
    if (taskState === stateRef.current) {
      return;
    }
    stat.exposure('resource_exposure', {
      c: 'pop',
      d: 'video',
      resource_location: 'permanent_video',
      resource_state: taskState,
    });
    stateRef.current = taskState;
  }, [taskState, homeAdResourceList, preLoaded]);

  // 获取任务描述
  const getTaskDesc = () => {
    const { diff } = getTargetTimeDiff(taskInfo?.beginTime, multiResourceCurTime);
    const resourceState = getResourceState();
    // 无任务可做 ｜ 倒计时结束是明天
    if (!taskInfo?.id || [TASK_STATE.TOMORROW].includes(resourceState)) {
      return <div className="tomorrow mx-auto">明日再来</div>;
    }
    // 倒计时
    if ([TASK_STATE.COUNTDOWN].includes(resourceState)) {
      return (
        <div className="count-down mx-auto">
          <CountDown
            diff={diff}
            formatFunc={(time) => {
              const { hour, min, second } = formatLeftTime(time * 1000);
              return `${hour}:${min}:${second}`;
            }}
            onComplete={() => {
              dispatch.resource.queryResource({ firstInit: false });
              dispatch.task.queryTaskList(false);
            }}
          />
        </div>
      );
    }
    // 状态可做中: 展示任务进度
    return (
      <div className="progress mx-auto">
        {taskInfo?.dayTimes?.target
          ? `(${getTaskCurDayTimeProcess(taskInfo)}/${getTaskCurDayTimeTarget(taskInfo)})`
          : ''}
      </div>
    );
  };
  const handleResourceClick = () => {
    stat.click('resource_click', {
      c: 'pop',
      d: 'video',
      resource_location: 'permanent_video',
      resource_state: getResourceState(),
    });
  };
  const handleTask = () => {
    stat.click('task_click', {
      c: 'pop',
      d: 'video',
      ...factTaskInfo(),
    });
    // 已完成
    if (!taskInfo.id || checkTaskFinished(taskInfo)) {
      return;
    }
    // 待解锁
    if (checkTaskCountDown(taskInfo, multiResourceCurTime)) {
      return Toast.show('倒计时结束解锁，先去做其它的吧');
    }
    const requestId = geneTaskRequestId();
    execWithLock(
      'finish_task_lock',
      async () => {
        logToFinishTask(taskInfo, TASK_LOCATION.TASK_PREMANENT_AD);
        localStorage.setItem(LocalStorageKey.FINISH_TASK_FROM, 'permanent_ad');
        await taskActionHandler(taskInfo, requestId, { location: TASK_LOCATION.TASK_PREMANENT_AD });
      },
      2000,
    );
  };

  const getTaskPointAwardText = (taskinfo: TaskInfo) => {
    const taskPrize = taskinfo?.rewardItems?.[0] || {};
    const awardText = getTaskAward(taskinfo);
    if (!awardText) {
      return '';
    }
    return taskPrize?.randomAmount ? (
      <Fragment>
        <span className="max-text">最高</span>
        { awardText >= 10000 ? `${(awardText / 10000).toFixed(0)}万` : `${awardText}` }
      </Fragment>
    ) : (
      `+${awardText}`
    );
  };
  const renderTaskAwardTip = (taskDetail: TaskInfo) => {
    const rewardIcon = taskDetail?.rewardItems?.[0]?.icon;
    return (
      <Fragment>
        <img className="prize-icon" src={rewardIcon || Manure} alt="肥料" />
        <div className="num-text">{getTaskPointAwardText(taskDetail)}</div>
      </Fragment>
    );
  };

  // 无任务或者视频预加载没有回来的时候，不展示任务
  if (!homeAdResourceList.length || !preLoaded) {
    return null;
  }
  return (
    <Fact
      c="pop"
      d="video"
      noUseClick
      onClick={handleResourceClick}
      // expoLogkey="resource_exposure"
      noUseExpos
      // expoExtra={{
      //   resource_location: 'permanent_video',
      //   resource_state: getResourceState(),
      // }}
      className="comp-video-ad-resource"
    >
      <Fact
        c="pop"
        d="video"
        noUseClick
        expoLogkey="task_exposure"
        expoExtra={{
          ...factTaskInfo(),
        }}
      >
        {getResourceState() !== TASK_STATE.TOMORROW && <div className="bubble">{renderTaskAwardTip(taskInfo)}</div>}
        <div className="tv-content" onClick={handleTask}>
          <span className="title">看视频</span>
          {getTaskDesc()}
        </div>
      </Fact>
    </Fact>
  );
};

export default VideoAdResource;
