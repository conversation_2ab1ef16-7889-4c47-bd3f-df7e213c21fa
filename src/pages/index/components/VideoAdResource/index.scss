.comp-video-ad-resource {
  position: absolute;
  z-index: 1;
  right: 40rpx;
  top: 728rpx;
  width: 130rpx;
  height: 130rpx;
  background-image: url('./images/tv-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  box-sizing: border-box;
  uc-perf-stat-ignore: image;
  .prize-icon{
    uc-perf-stat-ignore: image;
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
  .bubble {
    position: absolute;
    top: -33rpx;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.92);
    // box-shadow: inset 0 0 8px 4px rgba(255, 255, 255, 0.74);
    border-radius: 990rpx;
    width: auto;
    // min-width: 108rpx;
    height: 42rpx;
    display: flex;
    flex-direction: row;
    // justify-content: center;
    align-items: center;
    padding: 0 10rpx;
    // text-align: center;
    flex-wrap: nowrap;
    .num-text {
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Medium;
      font-size: 20rpx;
      color: #fa6425;
      letter-spacing: 0;
      line-height: 28rpx;
      text-wrap: nowrap;
      white-space: nowrap;
      .max-text {
        font-family: PingFangSC-Medium;
        font-size: 20rpx;
        color: #8a4519;
        letter-spacing: 0;
        font-weight: 500;
      }
    }
    .prize-icon {
      width: 28rpx;
      height: 28rpx;
      object-fit: contain;
    }
  }
  /* 小箭头 */
  .bubble::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -7rpx; /* 箭头高度 */
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8rpx solid transparent;
    border-right: 8rpx solid transparent;
    border-top: 8rpx solid rgba(255, 255, 255, 0.92); /* 与气泡背景一致 */
    // filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1)); /* 保持阴影一致 */
  }
  .tv-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 55rpx;
    padding-right: 4.5rpx;

    .title {
      font-family: FZLanTingYuanS-Bold;
      font-size: 20rpx;
      color: #fa6425;
      letter-spacing: 0;
      text-align: right;
      text-align: center;
    }
    .count-down {
      display: flex;
      flex-direction: row;
      justify-content: center;
      span {
        opacity: 0.76;
        font-family: D-DIN-Bold;
        font-size: 20rpx;
        color: #714837;
        letter-spacing: 0;
        text-align: center;
        line-height: 22rpx;
        margin-right: auto;
        margin-left: auto;
      }
    }
    .tomorrow {
      opacity: 0.76;
      font-family: PingFangSC-Semibold;
      font-size: 18rpx;
      color: rgba(113, 72, 55, 0.68);
      letter-spacing: 0;
      text-align: center;
      line-height: 22rpx;
      font-weight: 600;
      padding-left: 1rpx;
    }
    .progress {
      opacity: 0.76;
      font-family: D-DIN-Bold;
      font-size: 20rpx;
      color: #fa6425;
      letter-spacing: 0;
      text-align: center;
      line-height: 22rpx;
      text-align: center;
    }
    .complete,
    .finish {
      opacity: 0.76;
      font-family: PingFangSC-Semibold;
      font-size: 18rpx;
      color: rgba(113, 72, 55, 0.68);
      letter-spacing: 0;
      text-align: center;
      line-height: 22rpx;
      font-weight: 600;
    }
  }
}
html[device-type='high'] {
  .comp-video-ad-resource {
    top: 790rpx !important;
  }
}
