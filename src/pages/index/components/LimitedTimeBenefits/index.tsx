import React, { Fragment, useEffect, useState, useMemo } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import MessageScroll from '@/components/common/MessageScroll/index';
import BenefitsLogoImg from './assets/benefits-logo.png';
import TitleImg from './assets/title-img.png';
import RightMoreImg from './assets/right-more.svg';
import CheckImg from './assets/check.png';
import useMxState from '@/hooks/useMxState';
import { ITimeLimitTaskState } from '@/logic/store/models/limit/types';
import { uniqueDateFormat } from '@/lib/utils/date';
import DecorateLeftImg from './assets/decorate-left.png';
import DecorateRightImg from './assets/decorate-right.png';
import { checkTaskFinished, isAdVideoTask } from '@/pages/index/components/TaskPop/TaskList/util';
import Fact from '@/components/Fact';
import { getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import { ITaskState } from '@/logic/store/models/task/typing';
import { browseAdPlayerInstance } from '@/lib/adPlayer/ad_video';
import config from '@/config';
import { IAppState } from '@/logic/store/models/app/typings';
import { getAppVersion } from '@/lib/universal-ua';
import { isOpenQueryAward } from '@/logic/store/models/utils';
import { getShowLimitTaskList } from '@/logic/store/models/limit/utils';
import stat from '@/lib/stat';
import dispatch from '@/logic/store';

// 限时福利面板
const Index = () => {
  const [app] = useMxState<IAppState>('app');
  const [task] = useMxState<ITaskState>('task');
  const [timeLimitTask] = useMxState<ITimeLimitTaskState>('timeLimitTask');
  const [scheduleFill, setScheduleFill] = useState<number>(0);
  const [dateList, setDateList] = useState<Array<{ checked: boolean; date: string }>>([]);
  const { preTaskList = [], preExtraTaskList = [] } = timeLimitTask;
  const adTimeLimit = [...preTaskList, ...preExtraTaskList].filter((itAdTask) => isAdVideoTask(itAdTask));

  const needFinishList = getShowLimitTaskList();
  
  useEffect(() => {
    if (timeLimitTask.needReceive) {
      return;
    }
    if (adTimeLimit?.length) {
      adPlayInit()
    }
    // 进度条有几个节点 当前已经完成几个节点
    const list = Array.from({ length: timeLimitTask.target || 1 }, (_, index) => {
      return {
        checked: timeLimitTask.cycleTotalDay >= index + 1,
        date: `${index + 1}天`,
      };
    });

    // 进度条显示今天
    // const indexDay = dayTaskList?.length ? timeLimitTask?.cycleTotalDay : timeLimitTask?.cycleTotalDay - 1;
    // list[indexDay] = { ...list?.[indexDay], date: '今天' };

    const value = scheduleFillFun(timeLimitTask.target || 1, timeLimitTask?.cycleTotalDay);
    setScheduleFill(value);

    setDateList(list);
  }, [timeLimitTask.needReceive, timeLimitTask.dayRange, timeLimitTask.cycleTotalDay]);

  // 获取进度条进度 支持半格进度条
  const scheduleFillFun = (listLen, indexDay) => {
    if (indexDay === 0) {
      return 0;
    }
    const value = (indexDay / listLen) * 100;
    return indexDay <= 2 ? value : value + 5;
  };
  // 初始化激励视频
  const adPlayInit = async () => {
    const isLite = app?.pr === 'UCLite';
    const appVersion = await dispatch.app.getAppVersion();

    const isOpenQueryReward = isOpenQueryAward(appVersion, isLite);
    const preloadAdTaskMap = task.preloadAdTaskMap;
    adTimeLimit?.forEach((adTask) => {
      if (preloadAdTaskMap.has(`${adTask?.id}`)) {
        return;
      }
      const adData = getIncentiveAdSlotData(adTask);
      if (adData?.slotKey) {
        preloadAdTaskMap.set(`${adTask?.id}`, adTask?.id);
        browseAdPlayerInstance.init({
          task: adTask,
          slotKey: adData?.slotKey,
          appId: adData?.appId,
          coralAppId: config?.appId,
          enableAsyncQueryReward: isOpenQueryReward,
          finishPreload: (res) => {
          },
        });
      }
    });
  }
  // 获取副标题
  const getBenefitsDateText = () => {
    if (timeLimitTask?.cycleTotalDay) {
      return `再完成${timeLimitTask?.target - timeLimitTask?.cycleTotalDay}天即领`;
    } else {
      return `${uniqueDateFormat(timeLimitTask?.endTime)}内完成${timeLimitTask?.target}天即领`;
    }
  };

  // 过滤完成的任务
  const dayTaskList = needFinishList?.filter((item) => {
    return !checkTaskFinished(item);
  });

  // 打开任务弹窗
  const openBenefitsTaskModal = () => {
    baseModal.open(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
  };
  return (
    <div className="limited-time-benefits">
      <div className="benefits-logo">
        <img src={BenefitsLogoImg} alt="" />
      </div>
      <Fact
        c="panel"
        d="task"
        expoLogkey="award_task_exposure"
        logkey="award_task_click"
        expoExtra={{
          days_process: dayTaskList?.length ? 0 : 1,
          task_id: timeLimitTask?.id,
          task_name: timeLimitTask?.name,
        }}
        ckExtra={{
          days_process: dayTaskList?.length ? 0 : 1,
          task_id: timeLimitTask?.id,
          task_name: timeLimitTask?.name,
        }}
        onClick={openBenefitsTaskModal}
      >
        <div className="panel">
          <div className="panel-hd">
            <img className="panel-decorate-left" src={DecorateLeftImg} alt="" />
            <img className="panel-decorate-right" src={DecorateRightImg} alt="" />
            <div className="panel-hd-text">
              <div className="benefits-title">
                <img src={TitleImg} alt="" />
              </div>
              <div className="benefits-date">{getBenefitsDateText()}</div>
            </div>
            <div className="panel-hd-schedule">
              <div className="benefits-schedule">
                <div className="schedule">
                  <div style={{ width: `${scheduleFill}%` }} className="benefits-schedule-fill" />
                </div>
                <div className="circle">
                  {dateList?.map((item, index) => {
                    return (
                      <div key={index} className={`circle-item ${item?.checked ? 'circle-checked' : ''}`}>
                        {item?.checked && <img className="checked-img" src={CheckImg} alt="" />}
                        <div className="date">{item?.date}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            <div className="final-award">
              <div id="final-award-img-ani" className="final-award-img">
                <img className="manure-icon" src={timeLimitTask?.showRewardList[0]?.icon} alt="" />
                <div className="award-text">{timeLimitTask?.showRewardList[0]?.name}</div>
              </div>
            </div>
          </div>
          <div className="panel-fd">
            {!!dayTaskList?.length && (
              <div className="benefits-notic">
                <MessageScroll
                  messages={dayTaskList}
                  interval={5000}
                  renderItem={(item, index) => {
                    return (
                      <div key={index} className="benefits-notic-item">
                        <div className="benefits-notic-text">{item?.name}</div>
                      </div>
                    );
                  }}
                />
              </div>
            )}
            <div className="benefits-task-number">
              {dayTaskList?.length ? (
                <Fragment>
                  今日任务余{dayTaskList?.length}
                  <img className="right-more" src={RightMoreImg} alt="" />
                </Fragment>
              ) : (
                `今日任务已完成 明天再来！`
              )}
            </div>
          </div>
        </div>
      </Fact>
    </div>
  );
};

export default Index;
