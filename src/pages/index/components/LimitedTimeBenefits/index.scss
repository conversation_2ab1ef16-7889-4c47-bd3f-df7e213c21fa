.limited-time-benefits {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 0 auto;
  padding-top: 115rpx;
  img {
    // 限时福利是次屏内容，图片不需要计算在T2
    uc-perf-stat-ignore: image;
  }
  .benefits-logo {
    width: 486rpx;
    img {
      width: 361rpx;
      height: 31rpx;
      background-size: cover;
    }
  }
  .panel {
    margin-top: 30rpx;
    width: 486rpx;
    border-radius: 22rpx;
    background: linear-gradient(180deg, #ffffff 46%, #fffdfa 68%, #fff4d7 100%);
    .panel-hd {
      position: relative;
      .panel-decorate-left {
        position: absolute;
        top: 7rpx;
        left: -27rpx;
        width: 59rpx;
        height: 59rpx;
        background-size: cover;
      }
      .panel-decorate-right {
        position: absolute;
        top: -18rpx;
        right: -10rpx;
        width: 182rpx;
        height: 166rpx;
        background-size: cover;
      }
      .panel-hd-text {
        padding-top: 24rpx;
        padding-bottom: 21rpx;
        margin-left: 30rpx;
        display: flex;
        align-items: flex-end;
        font-family: PingFangSC-Regular;
        .benefits-title {
          img {
            width: 124rpx;
            height: 32rpx;
            background-size: cover;
          }
        }
        .benefits-date {
          margin-left: 8rpx;
          font-size: 20rpx;
          color: #7b4932;
        }
      }
      .panel-hd-schedule {
        padding-bottom: 50rpx;
        padding-left: 30rpx;
        height: 26rpx;
        .benefits-schedule {
          position: relative;
          width: 300rpx;
          .schedule {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 100%;
            height: 10rpx;
            border-radius: 10rpx;
            background: #ffe29e;
            overflow: hidden;
            .benefits-schedule-fill {
              width: 0;
              height: 10rpx;
              background-image: linear-gradient(270deg, #ff6d05 0%, #ff4600 100%),
                linear-gradient(-85deg, #ff9309 21%, #ff7609 97%);
              border-radius: 10rpx;
            }
          }
          .circle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .circle-item {
              position: relative;
              width: 20rpx;
              height: 20rpx;
              border-radius: 50%;
              background: #ffe29e;
              .date {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                bottom: -32rpx;
                white-space: nowrap;
                font-family: PingFangSC-Regular;
                font-size: 20rpx;
                line-height: 28rpx;
                color: #7b4932;
              }
              .checked-img {
                width: 26rpx;
                height: 26rpx;
                background-size: cover;
              }
            }
            .circle-checked {
              width: 26rpx;
              height: 26rpx;
              background-color: #ff4600;
              .date {
                color: #fe6723;
                bottom: -29rpx;
              }
            }
          }
        }
      }
      .final-award {
        position: absolute;
        right: 26rpx;
        top: 8rpx;
        .final-award-img {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .manure-icon {
            width: 103rpx;
            height: 103rpx;
            background-size: cover;
          }
          .award-text {
            margin-top: -23rpx;
            width: 114rpx;
            height: 43rpx;
            background-image: url(./assets/award-img.png);
            background-repeat: no-repeat;
            background-size: 114rpx 43rpx;
            font-family: PingFangSC-Semibold;
            font-size: 22rpx;
            color: #ffffff;
            text-align: center;
            font-weight: 600;
            line-height: 48rpx;
          }
        }
      }
    }
    .panel-fd {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 26rpx;
      padding: 15rpx 0 16rpx 0;
      border-top: 1rpx solid rgba(123, 73, 50, 0.15);
      .benefits-notic {
        width: 253rpx;
        .message-scroll-container {
          height: 33rpx;
          line-height: 33rpx;
          .benefits-notic-item {
            font-family: PingFangSC-Semibold;
            font-size: 24rpx;
            color: #7b4932;
            font-weight: 600;
            .benefits-notic-text {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }
      .benefits-task-number {
        display: flex;
        align-items: center;
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #7b4932;
        text-align: right;
        .right-more {
          width: 26rpx;
          height: 26rpx;
          background-size: cover;
        }
      }
    }
  }
}
