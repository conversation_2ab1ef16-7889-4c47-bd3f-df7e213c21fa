import React, { useEffect} from 'react';
import DoubleHead from '../images/task-double-head.png';
import ArrowIcon from '../images/arrow-icon.png';
import Fact from '@/components/Fact';
import useMxState from '@/hooks/useMxState';
import { QueryCardInfoRes } from '@/api/doublePointsCard/typings';
import stat from '@/lib/stat';
import dispatch from '@/logic/store';
import modals from '@/components/modals';
import { EDoubleFactFromParam } from '@/components/modals/modal_double_card';
import { mx } from '@ali/pcom-iz-use';
import { MainAPI } from '@/logic/type/event';

export const DoubleTitleEntry = () => {
  const [cardInfo] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { doubleNum } = cardInfo?.todayCardInfo || {};
  const { totalDrawTimes } = cardInfo?.drawInfo || {};
  const isDoubling = cardInfo && doubleNum > 0; // 翻倍卡生效中
  const doubleStatus = totalDrawTimes === 0 ? 1 : isDoubling ? 2 : 1; // 1（未参与/未抽中）2（翻倍生效中）

  useEffect(() => {
    dispatch.doublePointsCard.doubleCardExposure();
  }, []);

  const handleCloseTaskPop = async () => {
    mx.event.emit(MainAPI.HideTaskPop);
  };

  const handleClickDoubleCardEntry = async () => {
    stat.click('fdcard_task_click', {
      c: 'fdcard',
      d: 'guide',
      status: doubleStatus,
    });
    const loginStatus = await dispatch.user.checkLoginAndBind(0, 7);
    // 登陆拦截
    if (!loginStatus) {
      return;
    }
    dispatch.doublePointsCard.queryCardInfo();
    handleCloseTaskPop();
    modals.openDoubleCardModal({
      from: EDoubleFactFromParam.TASK_ENTRY,
    });
  };
  return (
    <Fact
      noUseClick
      expoLogkey="fdcard_task_exposure"
      c="fdcard"
      d="guide"
      expoExtra={{
        status: doubleStatus,
      }}
      onClick={handleClickDoubleCardEntry}
      className="double-card-title"
    >
      <img src={DoubleHead} alt="" />
      <div className="title">
        {isDoubling ? `${doubleNum / 100}倍肥料膨胀中` : '抽肥料膨胀卡'}
        <img className="arrow-icon" src={ArrowIcon} alt="" />
      </div>
    </Fact>
  );
};
