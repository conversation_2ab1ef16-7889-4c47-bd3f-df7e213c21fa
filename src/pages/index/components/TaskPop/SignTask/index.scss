.sign-task-comp {
  width: 720rpx;
  display: flex;
  border-radius: 30rpx;
  box-sizing: border-box;
  padding-top: 12rpx;
  padding-left: 35rpx;
  position: relative;
  z-index: 3;
  background: #fff;

  img {
    width: 100%;
  }

  .task-progress-wrapper {
    width: 488rpx;
    height: 140rpx;
    position: relative;
    overflow: hidden;
    overflow-x: scroll;
    margin-right: 18rpx;


    &::-webkit-scrollbar {
      display: none;
    }

    .scroll-content {
      width: 486rpx;
      height: 140rpx;
      display: flex;
      justify-content: space-between;
      position: absolute;
      padding: 14rpx 0 0 0;

    }

    .sign-task-item {
      width: 80rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20rpx;
    }

    .task-item-opacity {
      opacity: 0.5;
    }

    .task-progress {
      width: 80rpx;
      height: 68rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      

      .sgin-icon {
        width: 100%;
      }

      .award-text {
        width: 80rpx;
        height: 30rpx;
        background-color: rgba(254, 228, 139, 1);
        border-radius: 34.5rpx;
        position: absolute;
        bottom: -9rpx;
        z-index: 2;
        font-size: 22rpx;
        white-space: nowrap;
        
        .amount{
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          font-family: PingFangSC-Semibold;
          color: rgba(175, 94, 15, 1);
          font-weight: 700;
          font-family: PingFangSC-Semibold;
        }
      }
      .small-fs{
        font-size: 20rpx;
      }

      .award-text::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -24rpx;
        transform: translateY(-50%);
        width: 26rpx;
        height: 5rpx;
        background: #FBE49E;
        border-radius: 3px;
        z-index: 1;
      }
    }

    .sign-task-item:last-child .task-progress .award-text::after {
      display: none;
    }

    .task-text {
      margin-top: 16rpx;
      font-size: 22rpx;
      color: #525E66;
      letter-spacing: 0;
      font-weight: 400;
      font-family: PingFangSC-Regular;
      display: flex;
      align-items: center;
      justify-content: center;

      .tick-icon{
        margin-top: 4rpx;
        width: 18rpx;
      }
    }

    .comp-text{
      color: #AAB5BB;
    }

    .today {
      color: #C48D26;
      font-weight: 700;
    }
  }

  .sign-btn-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 20rpx;
    right: 35rpx;

    &::after{
      content: '';
      width: 72rpx;
      height: 118rpx;
      background-image: linear-gradient(90deg, rgba(255,255,255,0.00) 0%, #FFFFFF 100%);
      position: absolute;
      top: 0;
      left: -80rpx;
      z-index: 9;
    }

    .red-dot {
      position: relative;

      &::after {
        content: '';
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background: #F7534F;
        border: 2rpx solid #FFFFFF;
        position: absolute;
        top: -10rpx;
        right: 10rpx;
      }

    }

    .sign-btn {
      margin-top: 18rpx;
      width: 140rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #2AC638;
      border-radius: 100rpx;
      margin-bottom: 12rpx;
      font-size: 28rpx;
      color: #FFFFFF;
      letter-spacing: 0.5rpx;
      font-weight: 700;
      -webkit-tap-highlight-color: rgba(0,0,0,0);
    }

    .finshed-btn {
      background: #D4DADE;
    }

    .sign-award {
      width: 140rpx;
      font-size: 24rpx;
      color: #CE9E70;
      letter-spacing: 0;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Semibold;
      img {
        width: 30rpx;
      }
    }

    .finshed-text {
      font-size: 22rpx;
      color: #525E66;
      font-weight: 400;
    }

  }
}

.padding-b{
  padding-bottom: 20rpx;
}