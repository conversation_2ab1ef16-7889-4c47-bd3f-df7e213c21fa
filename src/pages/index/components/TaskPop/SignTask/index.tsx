import React, { useEffect, useRef } from 'react';
import './index.scss';
import useMxState from '@/hooks/useMxState';
import dispatch from '@/logic/store';
import { ITaskState, SignTaskInfo, SignType } from '@/logic/store/models/task/typing';
import { execWithLock } from '@/lib/utils/lock';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { IUserState } from '@/logic/store/models/user/typings';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { preLoadLottieResource } from '../TaskList/preloadResource';
import { checkIsShowBBZEnter } from '@/logic/store/models/app/new_year_time';
import classNames from 'classnames';

import SignIcon from './images/sign-icon.png';
import TickIcon from './images/tick-icon.png';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';
import { mx } from '@ali/pcom-iz-use';
import { RES_CODES } from '@/logic/store/models/cms/typings';

export default function SignTask(props) {
  const [task] = useMxState<ITaskState>('task');
  const [user] = useMxState<IUserState>('user');
  const todayTask = task?.signTask?.find((item) => item?.signInState === 'START');
  // 取出已经完成签到的
  const completeSignList = task?.signTask?.filter((item) => item?.signInState === 'COMPLETED')
  // 取出最后一次签到的
  const lastSignTask = completeSignList[completeSignList?.length - 1];
  const canScrollTo = useRef(true);
  const isBangBangZhongPeriod = checkIsShowBBZEnter();
  const cmsStore = mx.store.getStore()?.cms;
  const bindTaobao = mx.store.get('user.bindTaobao')

  usePageVisibilityListener((visible) => {
    if (!visible) {
      return;
    }
    const isOpenAutoSign = cmsStore[RES_CODES.BBNC_AUTO_SIGN_SWITCH]?.items?.[0]?.open === '1';
    if (!cmsStore || task?.todayCompleted || !isOpenAutoSign || !bindTaobao) {
      return;
    }
    handleSign(SignType.AUTO)
  })

  useEffect(() => {
    const curTaskIndex = task?.signTask?.findIndex((item) => item?.signInState === 'START');
    // 当天为签到,切当天为3、7天时预加载动画资源
    if (curTaskIndex === 2 || curTaskIndex === 6) {
      preLoadLottieResource(curTaskIndex + 1)
    }
  }, [task?.signTask])

  useEffect(() => {
    const scrollDOM = document.querySelector('.task-progress-wrapper');
    const startIndex = task?.signTask?.findIndex((item) => item?.signInState === 'START');
    if (!scrollDOM || !canScrollTo.current) return
    const items = scrollDOM.getElementsByClassName('sign-task-item');
    let targetOffsetLeft = 0;

    if (startIndex === 3 || completeSignList?.length > 3) {
      canScrollTo.current = false;
      targetOffsetLeft = items[3]?.offsetLeft;
    } else if (startIndex === 2 || completeSignList?.length > 2) {
      canScrollTo.current = false;
      targetOffsetLeft = items[2]?.offsetLeft;
    } else if (startIndex === 1 || completeSignList?.length === 2) {
      canScrollTo.current = false;
      targetOffsetLeft = items[1]?.offsetLeft;
    }
    scrollDOM?.scrollTo(targetOffsetLeft, 0)
  }, [task?.signTask])

  // auto: 自动签到 hand: 手动签到
  const handleSign = async (type: SignType) => {
    const requestId = geneTaskRequestId();
    stat.click('signin_click', {
      c: 'card0',
      d: 'signin',
      sign_number: task?.continuousCompletedDay + 1,
      page_status: user?.bindTaobao ? 1 : 0,
      task_id: todayTask?.curTask?.id,
      task_name: todayTask?.curTask?.name,
      requestId,
      tasklist_source: props?.tasklist_source,
      taskclassify: todayTask?.curTask?.taskClassify,
      groupcode: todayTask?.curTask?.groupCode,
      award_amount: todayTask?.prizes?.[0]?.rewardItem?.amount || '',
      sign_type: type
    })
    const loginStatus = await dispatch.user.checkLoginAndBind(0, 6)
    if (!loginStatus) return;
    if (task?.todayCompleted) return;
    execWithLock('signTask', async (unlock) => {
      await dispatch.task.finishSignTask(requestId, type)
      unlock()
    })
  };

  const getSignIconUrl = (taskInfo: SignTaskInfo, isComplete: boolean, isToday: boolean) => {
    const { curTask } = taskInfo;
    const taskExtra = JSON.parse(curTask?.extra || '{}');
    // 当天未领取
    if (isToday && !isComplete) {
      return taskExtra?.todayIcon || curTask?.icon
    }
    // 已领取
    // if (isComplete) {
    //   return prizes?.length && prizes[0]?.rewardItem?.icon || curTask?.icon;
    // }
    return curTask?.icon
  }

  const getTextName = (taskInfo: SignTaskInfo, isComplete: boolean, isToday: boolean) => {
    if (isToday) {
      return (
        <React.Fragment>
          {isComplete && <img src={TickIcon} className="tick-icon" alt="" />}
          <span>今天</span>
        </React.Fragment>
      )
    }
    if (isComplete) {
      return (
        <React.Fragment>
          <img src={TickIcon} className="tick-icon" alt="" />
          <span>已领</span>
        </React.Fragment>
      )
    }
    return <span>{taskInfo?.curTask?.name}</span>
  };

  const getTextNameClass = (isComplete, isToday) => {
    if (!isComplete && isToday) {
      return 'task-text today'
    }
    if (isComplete) {
      return 'task-text comp-text'
    }
    return 'task-text'
  }

  return (
    <div className={classNames('sign-task-comp ',
      isBangBangZhongPeriod ? '' : 'padding-b')}
    >
      <div className="task-progress-wrapper">
        <div className="scroll-content">
          {task?.signTask?.map((signTask, index) => {
            const isComplete = signTask?.signInState === 'COMPLETED';
            /**
             * 1、当天未签到 signInState === 'START' 为当天的任务;
             * 2、当天已签到 signInState === 'COMPLETED' 的最后一个 为当天的任务
             */
            const isToday = task?.todayCompleted ? signTask?.curTask?.id === lastSignTask?.curTask?.id : todayTask?.curTask?.id === signTask?.curTask?.id
            const amount = signTask?.prizes[0]?.rewardItem?.amount;
            return (
              <Fact
                c="card0"
                d="signin"
                expoLogkey="signin_exposure"
                expoExtra={{
                  isfinish: task?.todayCompleted ? 1 : 0,
                  sign_number: task?.continuousCompletedDay,
                  task_id: signTask?.curTask?.id,
                  task_name: signTask?.curTask?.name,
                  tasklist_source: props?.tasklist_source,
                  taskclassify: todayTask?.curTask?.taskClassify,
                  groupcode: todayTask?.curTask?.groupCode,
                  award_amount: amount || '',
                }}
                noUseClick
                key={signTask?.curTask?.id}
                className={`sign-task-item`}
              >
                <div>
                  <div className={`task-progress ${isComplete ? 'task-item-opacity' : ''}`}>
                    <LazyLoadImage src={getSignIconUrl(signTask, isComplete, isToday) || SignIcon} className="sgin-icon" alt="签到肥料" />
                    <div className={`award-text ${(index === 2 || index === 6) && !isComplete ? 'small-fs' : ''}`}>
                      <div className="amount">
                        {isComplete && amount ? `+${amount}` : signTask?.curTask?.desc}
                      </div>
                    </div>
                  </div>
                  <div className={getTextNameClass(isComplete, isToday)}>
                    {getTextName(signTask, isComplete, isToday)}
                  </div>
                </div>
              </Fact>
            )
          })}
        </div>
      </div>
      <div className="sign-btn-wrapper">
        <div className={`sign-btn ${task?.todayCompleted ? 'finshed-btn' : 'red-dot'}`} onClick={() => handleSign(SignType.HAND)}>
          {task?.todayCompleted ? '已领取' : '签到'}
        </div>
        <div className={`sign-award ${task?.todayCompleted ? 'finshed-text' : ''}`}>
          {task?.todayCompleted ? '明天再来!' : (
            <React.Fragment>
              <img src={SignIcon} alt="" />{todayTask?.curTask?.desc}
            </React.Fragment>
          )}
        </div>
      </div>
    </div >
  )
}
