import React, { useCallback, useEffect, useRef, useState } from 'react';
import './index.scss';
import useMxState from '@/hooks/useMxState';
import { ICmsResData, ITaskBottomBanner, RES_CODES, BannerItem } from '@/logic/store/models/cms/typings';
import { openPage } from '@/lib/ucapi/index';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { ITaskState } from '@/logic/store/models/task/typing';
import { getExtraInfo } from '@/logic/store/models/task/helper';
import {jumpApp} from './utils';

const TASK_BANNER_CLICK_KEY = 'TASK_BANNER_CLICK_KEY';
interface TaskBannerProps {
  popTime: number;
  setVisible: (visible: boolean) => void;
  tasklist_source: string;
}
function TaskBottomBanner(props: TaskBannerProps) {
  const { popTime, setVisible } = props;
  const [banner] = useMxState<ICmsResData<ITaskBottomBanner>>(`cms.${RES_CODES.TASK_BOTTOM_BANNER}`);
  const [task] = useMxState<ITaskState>('task');
  const [currentBanner, setCurrentBanner] = useState<BannerItem | null>(null);
  useEffect(() => {
    const bannerSize = banner?.items?.[0]?.banner_list.length;
    let bannerIndex = 0;
    const clickCache = localStorage.getItem(TASK_BANNER_CLICK_KEY);
    if (bannerSize > 1 && clickCache) {
      const taskBannerClickInfo = JSON.parse(clickCache || '{}');
      const clickIndex = banner?.items?.[0]?.banner_list.findIndex((item) => {
        return item.id === taskBannerClickInfo.id;
      });
      // 任务面板没有重新打开，还展示原来的任务
      if (taskBannerClickInfo.popTime === popTime) {
        bannerIndex = clickIndex;
      } else {
        // 如果点击的是最后一条数据，展示第一条数据
        bannerIndex = clickIndex === bannerSize - 1 ? 0 : clickIndex + 1; // 点击banner后面还有数据，展示下一条
      }
    }
    const showBanner = banner?.items?.[0]?.banner_list[bannerIndex];
    setCurrentBanner(showBanner);
    setVisible(showBanner?.is_float === '1');
  }, [setVisible, banner, popTime]);

  const handleClick = useCallback(async () => {
    stat.click('ncbanner_click', {
      c: 'card100',
      d: 'task',
      ncbanner: currentBanner?.app,
      tasklist_source: props?.tasklist_source,
    });
    localStorage.setItem(
      TASK_BANNER_CLICK_KEY,
      JSON.stringify({
        id: currentBanner?.id,
        popTime,
      }),
    );
    let scheme = currentBanner?.dplink;
    let download_url = currentBanner?.download_url || '';
    const bannerTask = task?.bannerTaskList?.find(item => {
      return item?.id.toString() === currentBanner?.taskId
    })
    if (bannerTask) {
      const extraObj = getExtraInfo(bannerTask)
      scheme = extraObj?.scheme
      download_url = bannerTask?.url
    }
    switch (currentBanner?.app) {
      case 'taobao':
        jumpApp(currentBanner?.pkgName || 'com.taobao.taobao', scheme ?? 'tbopen://', download_url, bannerTask, 'task-banner');
        break;
      case 'alipay':
        jumpApp(currentBanner?.pkgName || 'com.eg.android.AlipayGphone', scheme ?? 'alipays://', download_url, bannerTask, 'task-banner');
        break;
      default:
        if (currentBanner?.pkgName && currentBanner?.dplink) {
          jumpApp(currentBanner?.pkgName, scheme || '', download_url, bannerTask, 'task-banner')
        } else {
          openPage(currentBanner?.download_url as string);
        }
        break;
    }
  }, [currentBanner, popTime]);

  if (!currentBanner) {
    return null;
  }
  return (
    <div
      className={currentBanner.is_float === '1' ? 'task-bottom-banner-float' : 'task-bottom-banner-default'}
      onClick={handleClick}
    >
      <div className="banner-wrap-content">
        <div className="icon">
          <img src={currentBanner?.icon} />
        </div>
        <div>
          <div className="title">{currentBanner?.title}</div>
          <div className="subtitle">{currentBanner?.sub_title}</div>
        </div>
        <Fact
          c="card100"
          d="task"
          expoLogkey="ncbanner_exposure"
          expoExtra={{
            ncbanner: currentBanner.app,
            tasklist_source: props?.tasklist_source,
          }}
          noUseClick
        >
          <div className="btn">前往</div>
        </Fact>
      </div>
    </div>
  );
}

export default TaskBottomBanner;
