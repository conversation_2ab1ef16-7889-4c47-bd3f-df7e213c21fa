
import { openPage, startApp } from '@/lib/ucapi';
import { getTaobaoUrlParams, handleJumpAppURL } from '@/lib/utils/rouseApp';
import tracker from '@/lib/tracker';
import { mx } from '@ali/pcom-iz-use';
import {TaskInfo} from "@/pages/index/components/TaskPop/TaskList/types";
import {checkTaskFinished} from "@/pages/index/components/TaskPop/TaskList/util";
import stat from "@/lib/stat";

export const jumpApp = async (pkgName: string, scheme: string, download_url: string, taskInfo: TaskInfo | undefined, position = 'task-banner') => {
  const task = mx.store.get('task');
  const creatCallAppMonitor = tracker.Monitor(128);
  const newScheme = handleJumpAppURL({
    scheme,
    packageName: pkgName
  });

  console.log('========= newScheme', newScheme);
  let taskStat;
  if (taskInfo) {
    taskStat = {
      task_id: taskInfo.id,
      task_name: taskInfo.name,
      taskclassify: taskInfo.taskClassify || '',
      groupcode: taskInfo.groupCode || '',
      award_amount: taskInfo.rewardItems[0]?.amount || '',
      task_count: taskInfo.dayTimes?.progress || 0,
      isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
    }
  }
  try {
    const starRes = await startApp(newScheme);
    console.log('starRes==', starRes);
    if (starRes?.result?.toString() === 'true') {
      creatCallAppMonitor.success({
        msg: `唤端成功-${pkgName}`,
        c1: '1',
        c2: pkgName,
        c3: position,
        c4: `${taskInfo?.id}`,
        bl2: JSON.stringify(newScheme),
        bl3: JSON.stringify(starRes),
        bl4: JSON.stringify(task?.bannerTaskList),
      });
      if (taskInfo) {
        stat.custom('task_call_app', {
          call_app_result: 'success',
          ...taskStat
        })
      }
      return;
    }
    if (taskInfo) {
      stat.custom('task_call_app', {
        call_app_result: 'fail',
        ...taskStat
      })
    }
    creatCallAppMonitor.fail({
      msg: `唤端失败-${pkgName}`,
      c1: '1',
      c2: pkgName,
      c3: position,
      c4: `${taskInfo?.id}`,
      bl2: JSON.stringify(newScheme),
      bl3: JSON.stringify(starRes),
      bl4: JSON.stringify(task?.bannerTaskList),
    });
    openPage(download_url as string);
  } catch (starErr) {
    if (taskInfo) {
      stat.custom('task_call_app', {
        call_app_result: 'fail',
        ...taskStat
      })
    }
    creatCallAppMonitor.fail({
      msg: `唤端失败-${pkgName}`,
      c1: '1',
      c2: pkgName,
      c3: position,
      c4: `${taskInfo?.id}`,
      bl2: JSON.stringify(newScheme),
      bl3: JSON.stringify(starErr),
      bl4: JSON.stringify(task?.bannerTaskList),
    });
    openPage(download_url as string);
  }
};
