import { localStorageSet } from "@/lib/utils/localStorage";
import { LocalStorageKey } from "@/lib/utils/localStorage_constant";

export class AdPlayerCache {
  private invokeTaskIds: number[]; // 已调起但未领奖的任务id
  private lastShowAdTask: TaskInfo | null; // 上次看的视频任务
  constructor() {
    this.invokeTaskIds = [];
  }
  
  setLastShowAdTask(task: TaskInfo) {
    this.lastShowAdTask = task;
  }

  getLastShowAdTask() {
    return this.lastShowAdTask;
  }

  getInvokeTaskIds(getFromLocal = false) {
    let invokeTaskIds = this.invokeTaskIds;
    if (getFromLocal) {
      invokeTaskIds = JSON.parse(localStorage.getItem(LocalStorageKey.INVOKED_TASK_ID) || '[]');
    }
    return Array.from(new Set(invokeTaskIds));
  }

  addInvokeTaskIds(taskId: number) {
    this.invokeTaskIds.push(taskId);
    // 本地也存下，兼容视频调起后H5崩溃恢复的场景。
    localStorage.setItem(LocalStorageKey.INVOKED_TASK_ID, JSON.stringify(this.invokeTaskIds));
  }

  removeInvokeTaskIds(taskId: number, removeAll = false) {
    if (removeAll) {
      // 发奖成功时，移除所有这个id的调起记录
      this.invokeTaskIds = this.invokeTaskIds.filter(item => item !== taskId);
    } else {
      const index = this.invokeTaskIds.findIndex(item => item === taskId);
      if (index > -1) {
        this.invokeTaskIds.splice(index, 1);
      }
    }
    localStorage.setItem(LocalStorageKey.INVOKED_TASK_ID, JSON.stringify(this.invokeTaskIds));
  }
}

const adPlayerCache = new AdPlayerCache()
export default adPlayerCache

