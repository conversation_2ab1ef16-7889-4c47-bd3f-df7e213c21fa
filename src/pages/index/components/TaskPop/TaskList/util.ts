import { AllRewardItems, RewardItem, TASK_EVENT_TYPE, TASK_STATUS, TaskInfo, TanxRewardType } from "./types";
import { openPage } from "@/lib/ucapi";
import tracker from "@/lib/tracker";
import Toast from "@/lib/universal-toast";
import { findMissingTaskIds, getExtraInfo, getHighValueTaskList, ifShowTask, taskHiddenReport } from "@/logic/store/models/task/helper";
import { getParam } from "@/lib/qs";
import { RES_CODES } from '@/logic/store/models/cms/typings';
import mx from '@ali/pcom-mx';
import { convertCentsToPoint, convertCentsToYuan } from "@/lib/utils/formatNumber";
import dayjs from "dayjs";

export let taskOverTimer;

const canContiuneToLinkLisk = [
  TASK_EVENT_TYPE.JUMP_LINK,
  TASK_EVENT_TYPE.IFLOW_BROWSE,
  TASK_EVENT_TYPE.IFLOW_VIDEO,
  TASK_EVENT_TYPE.NOVEL_READ,
  TASK_EVENT_TYPE.SEARCH
];

// 下载APP任务类型
export const DOWNLOAD_APP_EVENT = [
  TASK_EVENT_TYPE.CALL_APP_DOWNLOAD,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD,
  TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT
];

// 次数任务，state=7时代表完成，其他任务state=2代表完成
export const checkTaskFinished = (task: TaskInfo) => {
  switch (task.taskType) {
    case 'everydayTimes': // 每日次数任务，根据dayTimes判断
      return task.dayTimes?.target === task.dayTimes?.progress;
    default: // 其他任务任务类型根据state判断
      return task.state === TASK_STATUS.TASK_CONFIRMED || task.state === TASK_STATUS.TASK_FINISH;
  }
}

/** 兼容服务端引号&quot转义错误 */
export const dealTaskExtra = (extra) => {
  const arrEntities = { quot: '"' };
  return extra.replace(/&(quot);/ig, (all, t) => { return arrEntities[t]; });
};

// 去完成任务监控
export function logToFinishTask(task: TaskInfo, position = 'list') {
  tracker.log({
    category: 115,
    sampleRate: 1,
    msg: `${task.id}_${task.event}_${task.name}`,
    w_succ: 1,
    c1: task.event,
    c2: `${task.id}`,
    c3: position || 'list',
    bl1: task.url || '',
    bl2: JSON.stringify(task)
  })
}

export function tanxAdvancedTaskMonitor(msg: string, task: TaskInfo, slot: string, rewardType?: TanxRewardType) {
  tracker.log({
    category: 169,
    msg,
    w_succ: 1,
    c1: task.event,
    c2: slot,
    c3: task.id,
    c4: getTaskAward(task),
    c5: String(rewardType ?? ''),
    bl1: JSON.stringify(task)
  })
}

/**
 * 不展示领取状态的任务
 * @param task
 * @returns
 */
export const hideReceiveBtnTask = (task: TaskInfo) => {
  const eventList = [
    TASK_EVENT_TYPE.JUMP_LINK,
    TASK_EVENT_TYPE.CALL_TAOBAO,
    TASK_EVENT_TYPE.CALL_APP_LINK,
    TASK_EVENT_TYPE.FARM_ANNOUNCE
  ]
  return eventList?.includes(task?.event);
}

/**
 * 处理超时没有响应函数(只适用用于页面隐藏的场景)
 * @param delay 延时
 * @param message toast文案
 */
export const handleOverTimeFn = (delay = 1000, message = '视频在努力加载中') => {
  if (taskOverTimer) {
    clearTimeout(taskOverTimer);
  }
  taskOverTimer = setTimeout(() => {
    Toast.show(message);
    clearTimeout(taskOverTimer)
  }, delay);
}

/**
 * 完成情况需要提示的任务
 * @param task
 */
export const completionToastTask = (task: TaskInfo) => {
  const showToastTaskEvents = [
    TASK_EVENT_TYPE.CALL_TAOBAO,
    TASK_EVENT_TYPE.IFLOW_BROWSE,
    TASK_EVENT_TYPE.IFLOW_VIDEO,
    TASK_EVENT_TYPE.NOVEL_READ,
    TASK_EVENT_TYPE.SEARCH,
    TASK_EVENT_TYPE.CLIENT_MISSION_DRAMA,
    TASK_EVENT_TYPE?.SEARCH_READ_CLICK,
    TASK_EVENT_TYPE?.SEARCH_READ_ONCE,
  ]
  return showToastTaskEvents.includes(task.event);
};

/**
 * 跳转任务完成可以继续点击
 * @param task
 */
export const canContinueGoLinkTask = (task: TaskInfo) => {
  if (!task.url) return;
  const canGoLink = canContiuneToLinkLisk.includes(task.event) || task?.event?.includes('link')
  if (canGoLink) {
    openPage(task.url)
  }
}

/**
 * 是否为激励广告任务
 */
export const isAdVideoTask = (task: TaskInfo) => {
  return task?.event?.includes(TASK_EVENT_TYPE.INCENTIVE_AD_TAG) || task?.event === TASK_EVENT_TYPE.VIDEO_AD;
}

/**
 * 获取任务奖励数值
 * @param task
 */
export const getTaskAward = (task: TaskInfo) => {
  return task?.rewardItems?.[0]?.amount || getExtraInfo(task)?.awardCount || 0;
}
/**
 * 获取肥料奖励文案
 */
export const getTaskPointAwardText = (task: TaskInfo) => {
  const taskPrize = task?.rewardItems?.[0] || {};
  const awardText = getTaskAward(task);
  if (!awardText) {
    return '';
  }
  return taskPrize?.randomAmount ? `最高${awardText}` : `+${awardText}`;
}


// 获取分享任务的奖励
export const getShareTaskAwardText = (task: TaskInfo) => {
  let defaultRewardItem: AllRewardItems | RewardItem = task?.rewardItems[0];
  // 默认奖励
  const defaultRewardItems = (task.allRewardItems ?? []).filter((item) =>
    (item?.userTagList ?? []).some((tag) => tag?.toLowerCase().includes('default')));
  if (defaultRewardItems.length) {
    defaultRewardItem = defaultRewardItems[0];
  }
  // 业务老用户
  const bizOldItem = (task.allRewardItems ?? []).filter((item) =>
    (item?.userTagList ?? []).some((tag) => tag?.toLowerCase().includes('biz_old')))[0];
  // 业务新
  const bizNewItem = (task.allRewardItems ?? []).filter((item) =>
    (item?.userTagList ?? []).some((tag) => tag?.toLowerCase().includes('biz_new')))[0];

  const getAward = (item: AllRewardItems | RewardItem | null) => {
    if (!item || Object.keys(item).length === 0) {
      return ''
    }
    const mark = item.mark ?? 'point';
    const rewardRandomAmount = item.randomAmount;
    const rewardAmount = item.amount ?? 0;
    const defaultStr = rewardRandomAmount ? '最高' : '';
    switch (mark) {
      case 'cash':
        return `${defaultStr}${convertCentsToYuan(Number(rewardAmount))}元红包`;
      default:
        return rewardAmount >= 10000 ? `${defaultStr}+${convertCentsToPoint(rewardAmount)}` : `${defaultStr}+${rewardAmount}`;
    }
  };
  return {
    defaultRewardItem,
    bizOldItem,
    bizNewItem,
    defaultRewardText: getAward(defaultRewardItem),
    bizOldRewardText: getAward(bizOldItem),
    bizNewRewardText: getAward(bizNewItem)
  };
};

export enum AMOUNT_TYPE {
  CASH = 'cash',
  POINT = 'point',
}

export const getTaskAmountType = (task: TaskInfo): AMOUNT_TYPE => {
  const amountType = task.rewardItems?.[0]?.mark || AMOUNT_TYPE.POINT;
  return amountType as AMOUNT_TYPE;
};

/**
 * 将字符串格式化为js对象
 * @param str
 * @returns
 */
export const formatStrToObj = (str: string) => {
  let obj;
  try {
    obj = JSON.parse(str || '{}')
  } catch (error) {
    obj = {}
  }
  return obj
}

/**
 * 获取时长区间
 * @param value 时长(秒)
 * @returns
 */
export const getDateSecondRange = (value: number) => {
  const ranges = [
    { min: 0, max: 30, label: "0-30" },
    { min: 30, max: 40, label: "30-40" },
    { min: 40, max: 50, label: "40-50" },
    { min: 50, max: 60, label: "50-60" },
  ];

  for (let range of ranges) {
    if (value >= range.min && value <= range.max) {
      return range.label;
    }
  }

  return "超过1分钟";
};

// 拼接搜索任务名称
export const getJointSearchTaskTitle = (task: TaskInfo) => {
  const word = task?.ext?.words?.[0];
  if (!word?.name) {
    return;
  }
  const title = `搜索`;
  if (word?.name?.length <= 5) {
    return `${title}“${word?.name}”`;
  }
  return `${title}“${`${word?.name?.slice(0, 5)}...`}”`;
}

// 获取搜索任务from渠道 先取任务url上渠道不存在取ext渠道号 最后cms兜底
export const getSearchKeyFromAndQuery = (task: TaskInfo) => {
  if (!isReadTypeSearchWordsTask(task)) {
    return {}
  }
  const from = getTaskFromNewSearch(task);
  const query = task?.ext?.words?.[0]?.name || '';
  return {from, query}
}

export const isReadTypeSearchWordsTask = (taskInfo: TaskInfo) => {
  return [
    TASK_EVENT_TYPE?.SEARCH_READ_CLICK,
    TASK_EVENT_TYPE?.SEARCH_READ_ONCE,
  ]?.includes(taskInfo?.event);
}

// 新搜索from获取
export const getTaskFromNewSearch = (task: TaskInfo) => {
  let from = getParam('from', task?.url);
  if (!from) {
    let extObj = task?.ext?.words?.[0];
    if (extObj?.from) {
      from = extObj?.from
    } else {
      const cmsStore = mx.store.getStore()?.cms;
      const cmsChannels = cmsStore[RES_CODES.FARM_SEARCH_CHANNEL_CONFIG]?.items?.[0]?.eventArray;
      if (Array.isArray(cmsChannels)) {
        const cmsChannelEvent = cmsChannels.find(channel => channel.eventName === task.event)
        if (cmsChannelEvent?.channelId) {
          from = cmsChannelEvent.channelId
        }
      }
    }
  }
  return from
}

// 获取现在距离目标时间的时间差s
export const getTargetTimeDiff = (targetTime: number, now: number) => {
  if (!targetTime) {
    return {
      diff: 0,
      isSameDay: false
    }
  }
  const targetTimeD = dayjs(targetTime);
  const nowD = dayjs(now);
  // 时间秒差
  const diff = targetTimeD.diff(nowD, 's');
  const isSameDay = targetTimeD.isSame(nowD, 'd');
  return {
    diff,
    isSameDay
  }
}

/**
 *
 * @param timestamp 当前时间，取服务端时间
 * @param task 任务内容
 */
export const checkTaskCountDown = (task: TaskInfo, timestamp: number) => {
  const { dayTimes = {progress: 0, target: 0}} = task;
  const isFinish = dayTimes.progress === dayTimes.target
  const taskBeginTimeDiff = getTargetTimeDiff(task.beginTime, timestamp);
  return !isFinish && isAdVideoTask(task) && taskBeginTimeDiff.diff > 1
}

/**
 * 获取任务当前次数批次目标总进度
 * @param task
 * @param now
 */
export const getTaskCurDayTimeTarget = (task: TaskInfo) => {
  const {isCustomDaytimes = false } = getExtraInfo(task);
  const { dayTimes = {progress: 0, target: 0}, dayTimeIntervalMap = {}} = task;
  const targetArr = Object.keys(dayTimeIntervalMap).sort((pre, next) => Number(pre) - Number(next)).filter(item => Number(item ?? 0) <= Number(dayTimes.target ?? 0));
  // 非自定义完成次数间隔 或则 不是激励广告类任务
  if (!targetArr?.length || !isAdVideoTask(task)) {
    return dayTimes.target;
  }

  const curDayTimeTargetIndex = targetArr?.findIndex((item) => Number(item) > dayTimes?.progress);
  const target = targetArr[curDayTimeTargetIndex];
  const lastTarget = targetArr[targetArr.length - 1];

  // 展示自定义次数
  if (isCustomDaytimes) {
    // 第一间隔总次 <= 任务总次数
    if (dayTimes?.target <= Number(targetArr[0])) {
      return dayTimes?.target
    }

    // 配置最大次数 > 任务总次数
    if (dayTimes.progress === dayTimes.target) {
      const preTarget = Number(lastTarget) === Number(dayTimes?.target) ? Number(targetArr[(targetArr.length >= 2 ? targetArr.length - 2 : 0)]) : Number(lastTarget);
      return dayTimes.target - preTarget;
    }

    // 最后自定义间隔 < 任务总次数
    if (!target) {
      return dayTimes?.target - Number(targetArr[targetArr.length - 1]);
    }

    // 第一批次
    if (Number(dayTimes?.progress ?? 0) < Number(targetArr[0])) {
      return Number(targetArr[0])
    }

    const preTarget = Number(targetArr[curDayTimeTargetIndex - 1])
    return Number(target) - preTarget
  }

  if (!target || Number(target) > dayTimes.target) {
    return dayTimes.target
  }

  return target;
}

/**
 * 获取任务当前自定义进度次数
 * @param task
 * @returns
 */
export const getTaskCurDayTimeProcess = (task: TaskInfo) => {
  const {isCustomDaytimes = false } = getExtraInfo(task);
  const { dayTimes = {progress: 0, target: 0}, dayTimeIntervalMap = {}} = task;

  if (!dayTimes.progress || !isCustomDaytimes || !isAdVideoTask(task) || !Object.keys(dayTimeIntervalMap).length) {
    return dayTimes?.progress;
  }
  const targetArr = Object.keys(dayTimeIntervalMap).sort((pre, next) => Number(pre) - Number(next)).filter(item => Number(item) <= dayTimes.target);
  const curDayTimeTargetIndex = targetArr?.findIndex((item) => Number(item) > dayTimes?.progress);
  const target = targetArr[curDayTimeTargetIndex];
  const lastTarget = targetArr[targetArr.length - 1];

  // 第一批次
  if (Number(dayTimes?.progress ?? 0) < Number(targetArr[0])) {
    return dayTimes?.progress
  }

  // 任务做满
  if (dayTimes.progress === dayTimes.target) {
    // 10: 2
    const preTarget = Number(lastTarget) === dayTimes?.progress ? Number(targetArr[targetArr.length >= 2 ? targetArr.length - 2 : 0]) : Number(lastTarget);
    return dayTimes?.progress - preTarget;
  }

  // 最后自定义间隔 < 任务总次数
  if (!target) {
    return dayTimes?.progress - Number(targetArr[targetArr.length - 1]);
  }

  return dayTimes?.progress - Number(targetArr[curDayTimeTargetIndex - 1]);
}

export const reportCurrentHiddenTaskIds = (taskList: TaskInfo[]) => {
  const newShowAdTask = taskList.filter(task => ifShowTask(task));
  const hiddenTaskIds = findMissingTaskIds(taskList, newShowAdTask);
  const highValueTaskList = getHighValueTaskList() ?? [];

  let report = {
    c2: hiddenTaskIds.join(','),
    c3: highValueTaskList.join(',')
  };
  const taskReportMonitor = tracker.Monitor(182);
  taskReportMonitor.success({
    msg: '任务列表隐藏',
    ...report
  })
}
