import { TaskInfo, TASK_EVENT_TYPE, TASK_STATUS, SEARCH_EVENT_MATCH, PlayRes, TanxRewardType, TASK_LOCATION } from './types';
import {
  dealTaskExtra,
  completionToastTask,
  isAdVideoTask,
  formatStrToObj,
  getTaskAward,
  getDateSecondRange,
  checkTaskFinished,
  getTaskFromNewSearch,
  tanxAdvancedTaskMonitor
} from './util';
import { startApp, openPage, crateMission, dongfengMonitoring } from '@/lib/ucapi';
import mx from '@ali/pcom-mx';
import config from '@/config';
import dispatch from '@/logic/store';
import { MainAPI } from '@/logic/type/event';
import tracker from '@/lib/tracker';
import Toast from '@/lib/universal-toast';
import { widget, installDesktopWidget, whetherWidget } from '@/lib/utils/updateWidget';
import stat from '@/lib/stat';
import { addParams, getParam } from '@/lib/qs';
import { handleJumpAppURL } from '@/lib/utils/rouseApp';
import { EventMap } from '@/lib/utils/events';
import { isIOS, isAndroid, isLatestVersion } from '@/lib/universal-ua';
import { browseAdPlayerInstance } from '@/lib/adPlayer/ad_video';
import { checkTaskTimeout, getExtraInfo } from '@/logic/store/models/task/helper';
import workerBridge from "@/logic/worker";
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import { notifyAdAwardSuccess, getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import { ShareBase } from '@ali/act-base-share';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import adPlayerCache from '@/pages/index/components/TaskPop/TaskList/adPlayerCache';
import { ResCompleteTask } from '@/logic/store/models/task/typing';
import { shouldShowPopup } from "@/components/modals/modalControl";
import { SharePanelSubTitle } from './SharePanelSubTitle';
import { getSearchParamObject } from '@/lib/utils/url';
import { IRewardInfoItem } from '@/lib/adPlayer/typings';
import modals from '@/components/modals';
import { EDoubleFactFromParam } from '@/components/modals/modal_double_card';
import { checkEnableDoubleCard } from '@/logic/store/models/app/new_year_time';

export interface ICallAppParams {
  url: string;
  extra?: string;
  event?: string;
}

/**
 * 唤端方法
 * @param params
 * @param position 位置
 * @returns
 */
export const handleCallApp = async (
  params: ICallAppParams | TaskInfo | undefined,
  position: string,
  requestId = '',
) => {
  let extraObj;
  const creatCallAppMonitor = tracker.Monitor(128, { sampleRate: 1 });
  if (params?.extra) {
    try {
      extraObj = JSON.parse(dealTaskExtra(params?.extra));
    } catch (error) {
      extraObj = {};
    }
  } else {
    extraObj = {};
  }
  const { scheme, pkgName } = extraObj;

  if (!scheme) {
    console.log('scheme不存在');
    creatCallAppMonitor.fail({
      msg: `没有配置scheme`,
      c2: pkgName,
      c3: position,
      bl1: JSON.stringify(extraObj),
      bl3: JSON.stringify(params?.url),
      bl5: JSON.stringify(params),
    });
    dealwithOpenPageTask(params, requestId);
    return;
  }
  // 换量回传，直接跳H5链接
  if (params?.event === TASK_EVENT_TYPE.APP_TOKEN) {
    const taskInfo = params as TaskInfo;
    openPage(taskInfo.url);
    return;
  }

  if (params?.event === TASK_EVENT_TYPE.CALL_TAOBAO) {
    const taskInfo = params as TaskInfo;
    dispatch.task.set({ curHandleTask: taskInfo });
  }
  const newScheme = handleJumpAppURL({
    scheme,
    packageName: pkgName,
  });
  return handleStartApp(params, newScheme, pkgName, position, creatCallAppMonitor, requestId);
};

const handleStartApp = async (
  params: ICallAppParams | undefined,
  newScheme: string,
  pkgName: string,
  position: string,
  monitor,
  requestId = '',
) => {
  // 有任务id的时候才上报打点
  let taskInfo = params as TaskInfo;
  let taskStat;
  if (taskInfo?.id) {
    taskStat = {
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      award_amount: taskInfo?.rewardItems[0]?.amount || '',
      task_count: taskInfo?.dayTimes?.progress || 0,
      isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
    };
  }
  try {
    const startRes = await startApp(newScheme);
    console.log('startRes==', startRes);
    if (startRes?.result?.toString() === 'true') {
      monitor.success({
        msg: `唤端成功-${pkgName}`,
        c1: '1',
        c2: pkgName,
        c3: position,
        bl1: JSON.stringify(params?.extra),
        bl3: JSON.stringify(newScheme),
        bl4: JSON.stringify(startRes),
        bl5: JSON.stringify(params),
      });
      if (taskInfo?.id) {
        stat.custom('task_call_app', {
          call_app_result: 'success',
          ...taskStat,
        });
      }
      if (pkgName.includes('taobao')) {
        stat.click('open_taobao', {
          c: 'all',
          d: 'taobao',
          type: 'open_success',
          position,
        });
      }
      if (taskInfo?.event === TASK_EVENT_TYPE.CALL_APP_LINK) {
        const delayAwardFlag = taskDelayCompleteByExtra(taskInfo, requestId);
        if (!delayAwardFlag) {
          dispatch.task.finishTask({
            taskId: taskInfo?.id,
            type: 'complete',
            traceId: requestId,
            useUtCompleteTask: !!taskInfo?.useUtCompleteTask,
            publishId: taskInfo.publishId,
          });
        }
      }
      return;
    }
    if (taskInfo?.id) {
      stat.custom('task_call_app', {
        call_app_result: 'fail',
        ...taskStat,
      });
    }
    if (pkgName.includes('taobao')) {
      stat.click('open_taobao', {
        c: 'all',
        d: 'taobao',
        type: 'open_fail',
        position,
      });
    }
    monitor.fail({
      msg: `唤端失败-${pkgName}`,
      c1: '0',
      c2: pkgName,
      c3: position,
      bl1: JSON.stringify(params?.extra),
      bl3: JSON.stringify(newScheme),
      bl4: JSON.stringify(startRes),
      bl5: JSON.stringify(params),
    });
    dealwithOpenPageTask(params, requestId);
  } catch (starErr) {
    monitor.fail({
      msg: `唤端失败-${pkgName}`,
      c1: '0',
      c2: pkgName,
      c3: position,
      bl1: JSON.stringify(params?.extra),
      bl3: JSON.stringify(newScheme),
      bl4: JSON.stringify(starErr),
      bl5: JSON.stringify(params),
    });
    if (taskInfo?.id) {
      stat.custom('task_call_app', {
        call_app_result: 'fail',
        ...taskStat,
      });
    }
    if (pkgName.includes('taobao')) {
      stat.click('open_taobao', {
        c: 'all',
        d: 'taobao',
        type: 'download',
        position,
      });
    }
    dealwithOpenPageTask(params, requestId);
  }
};

const dealwithOpenPageTask = (params: ICallAppParams | undefined, requestId: string) => {
  if (params?.event === TASK_EVENT_TYPE.CALL_APP_LINK) {
    const taskInfo = params as TaskInfo;
    handleLinkTask(taskInfo, requestId);
  } else {
    openPage(params?.url || '');
  }
};

// 客户端浏览任务
export const handleBrowseTask = async (task: TaskInfo, requestId: string) => {
  const taskExtra = JSON.parse(task?.extra || '{}');
  const creatTaskMonitor = tracker.Monitor(112);
  try {
    if (!taskExtra?.scene) {
      creatTaskMonitor.fail({
        msg: '任务扩展字段配置有误',
        bl1: JSON.stringify(task),
        bl3: task?.url,
      });
      return;
    }
    const params = {
      appId: config?.appId,
      id: `${task?.id}`,
      name: task?.name,
      event: task?.event,
      scene: taskExtra?.scene,
      target: taskExtra?.taskTime, // 目标时长
      from: taskExtra?.from,
      entry: taskExtra?.entry || getParam('entry'),
      requestId,
    };
    const res = await crateMission(params);
    if (res?.result?.toString() === '0') {
      creatTaskMonitor.success({
        msg: '创建任务成功',
        c1: `${task?.id}`,
        c2: `${task?.name}`,
        bl1: JSON.stringify(task),
        bl2: JSON.stringify(res),
        bl3: task?.url,
      });
      openPage(task?.url);
      return;
    }
    creatTaskMonitor.fail({
      msg: '创建任务失败',
      bl1: JSON.stringify(task),
      bl2: JSON.stringify(res),
      bl3: task?.url,
    });
  } catch (error) {
    console.log('创建任务回调=error', error);
    creatTaskMonitor.fail({
      msg: '创建任务失败-异常',
      bl1: JSON.stringify(task),
      bl2: JSON.stringify(error),
      bl3: task?.url,
    });
  }
};

// 弹窗引流任务 - 打开弹窗自动完成任务
export const handleToModalTask = (task: TaskInfo, requestId) => {
  mx.event.emit(MainAPI.HideTaskPop)
  const extraObj = getExtraInfo(task);
  const modal_id = extraObj?.modal_id || '';
  // 延迟发奖逻辑
  const delayAwardTime = extraObj?.awardTime ?? 0;

  const finishTaskFn = () => {
    const timer = setTimeout(() => {
      dispatch.task.finishTask({
        taskId: task?.id,
        type: 'complete',
        traceId: requestId,
        showToast: true,
        useUtCompleteTask: !!task?.useUtCompleteTask,
        publishId: task.publishId,
      });
      clearTimeout(timer);
    }, delayAwardTime);
  };

  switch (modal_id) {
    case MODAL_ID.DOUBLE_CARD_LOTTERY: {
      const doubleCardEnable = checkEnableDoubleCard();
      if (doubleCardEnable) {
        modals.openDoubleCardModal({
          from: EDoubleFactFromParam.DO_TASK,
          autoLottery: true,
        });
        finishTaskFn();
      }
      break;
    }
    default: {
      baseModal.open(modal_id, {})
      finishTaskFn()
    }
  }
};

// 分享任务
export const handleShareTask = (task: TaskInfo) => {
  // 判断是否有邀请码
  const app = mx.store.getStore().app;
  const share = mx.store.getStore().share;
  const taskExtra = JSON.parse(task?.extra || '{}');

  // 任务关联的分享配置
  const taskByShareConfig: {
    shareModuleId: string;
    inviteCodeTaskId?: number;
  } = app?.pr === 'UCLite' ? taskExtra?.ucLite : taskExtra?.uc;

  let inviteCodeTaskId = task.id;
  const inviteCode = share.inviteTaskIdMap.get(inviteCodeTaskId)?.inviteCode;
  if (!inviteCode) {
    dispatch.share.queryInviteInfoList();
    Toast.show('当前参与人数较多，请稍后再试');
    tracker.log({
      category: 110, // 系统自动生成，请勿修改
      msg: '分享失败-无邀请码', // 将根据msg字段聚合展示在平台的top上报内容中
      w_succ: 0, // 用于计算"成功率";可选值为0或1
    });
    return;
  }
  const shareModuleId =
    app?.pr === 'UCLite'
      ? taskByShareConfig?.shareModuleId || share.ucLiteShareModuleId
      : taskByShareConfig?.shareModuleId || share.ucShareModuleId;
  ShareBase.share({
    shareModuleId,
    urlAppendSearchParam: {
      inviteCode,
      entry: app?.pr === 'UCLite' ? 'speed_farm_share' : 'farm_share',
    },
    panelCustomConfig: {
      panelSubTitle: SharePanelSubTitle({
        inviteCode
      }),
    },
  });
};

// 拉起分享渠道任务
export const handleShareTargetTask = (taskInfo: TaskInfo, requestId: string) => {
  const app = mx.store.getStore().app;
  const {activity = '', shareModuleId = '', target = ''} = getExtraInfo(taskInfo);
  const entry = app?.pr === 'UCLite' ? 'speed_farm_share' : 'farm_share';
  const delayAwardFlag = taskDelayCompleteByExtra(taskInfo, requestId);
  const finishTaskFn = () => {
    if (!delayAwardFlag) {
      dispatch.task.finishTask({
        taskId: taskInfo?.id,
        type: 'complete',
        traceId: requestId,
        useUtCompleteTask: !!taskInfo?.useUtCompleteTask,
        publishId: taskInfo.publishId,
        showToast: true,
      });
    }
    mx.event.off(EventMap.PageVisible, finishTaskFn);
  }
  mx.event.on(EventMap.PageVisible, finishTaskFn);
  dispatch.share.openAppointTargetByFarm(target, shareModuleId, activity, entry, taskInfo);
}

// 链接跳转任务
export const handleLinkTask = (task: TaskInfo, requestId: string) => {
  if (!task?.url) {
    return;
  }
  const delayAwardFlag = taskDelayCompleteByExtra(task, requestId);
  // 监听页面隐藏后在触发任务完成
  const tofinishTaskfn = () => {
    if (!delayAwardFlag) {
      dispatch.task.finishTask({
        taskId: task?.id,
        type: 'complete',
        traceId: requestId,
        useUtCompleteTask: !!task?.useUtCompleteTask,
        publishId: task.publishId,
      });
    }
    mx.event.off(EventMap.PageHidden, tofinishTaskfn);
  };
  mx.event.on(EventMap.PageHidden, tofinishTaskfn);
  const timer = setTimeout(() => {
    mx.event.off(EventMap.PageHidden, tofinishTaskfn);
    clearTimeout(timer);
  }, 2000);
  openPage(task?.url);
};

export const handleRTACallTask = (task: TaskInfo, requestId: string) => {
  // 监听页面隐藏后在触发任务完成
  const tofinishTaskfn = () => {
    console.log('[AD] finish task');
    dispatch.task.finishTask({
      taskId: task?.id,
      type: 'complete',
      traceId: requestId,
      showToast: false,
      useUtCompleteTask: !!task?.useUtCompleteTask,
      publishId: task.publishId,
    });
    mx.event.off(EventMap.PageHidden, tofinishTaskfn);
  };
  mx.event.on(EventMap.PageHidden, tofinishTaskfn);
  const timer = setTimeout(() => {
    mx.event.off(EventMap.PageHidden, tofinishTaskfn);
    clearTimeout(timer);
  }, 2000);
  dispatch.task.rtaAdClick(task);
};

export const handleRTANuTask = (task: TaskInfo, requestId: string) => {
  // 监听页面隐藏后在触发任务领领取
  const toReceiveTask = () => {
    dispatch.task.receiveTask({
      taskId: task?.id,
      traceId: requestId,
      publishId: task.publishId,
      useUtCompleteTask: !!task.useUtCompleteTask,
    });
    mx.event.off(EventMap.PageHidden, toReceiveTask);
  };
  mx.event.on(EventMap.PageHidden, toReceiveTask);

  // 监听页面显示后在触发任务完成
  const toFinishTaskfn = () => {
    dispatch.task.checkAppDownloadFinish(task);
    mx.event.off(EventMap.PageVisible, toFinishTaskfn);
  };
  mx.event.on(EventMap.PageVisible, toFinishTaskfn);

  const timer = setTimeout(() => {
    mx.event.off(EventMap.PageHidden, toReceiveTask);
    mx.event.off(EventMap.PageVisible, toFinishTaskfn);
    clearTimeout(timer);
  }, 1000);

  dispatch.task.rtaAdClick(task);
};
/**
 * 下载类任务领取并去下载页面
 */
export const handleCallAppDownload = (task: TaskInfo, requestId: string, position = 'list') => {
  const checkResult = clickCheckAppInstall(task);
  if (checkResult) {
    Toast.show('该任务已过期，快去做其它任务吧');
    tracker.log({
      category: 115,
      sampleRate: 1,
      msg: `${task.id}_${task.event}_${task.name}_已安装`,
      w_succ: 1,
      c1: task.event,
      c2: `${task.id}`,
      c3: position || 'list',
      bl1: task.url || '',
      bl2: JSON.stringify(task)
    })
    dispatch.app.updateAllData();
    return;
  }
  // 监听页面隐藏后在触发任务领领取
  const toReceiveTask = () => {
    dispatch.task.receiveTask({
      taskId: task?.id,
      traceId: requestId,
      publishId: task.publishId,
      useUtCompleteTask: !!task.useUtCompleteTask,
    });
    mx.event.off(EventMap.PageHidden, toReceiveTask);
  };
  mx.event.on(EventMap.PageHidden, toReceiveTask);

  // 监听页面显示后在触发任务完成
  const toFinishTaskfn = () => {
    dispatch.task.checkAppDownloadFinish(task);
    mx.event.off(EventMap.PageVisible, toFinishTaskfn);
  };
  mx.event.on(EventMap.PageVisible, toFinishTaskfn);

  const timer = setTimeout(() => {
    mx.event.off(EventMap.PageHidden, toReceiveTask);
    mx.event.off(EventMap.PageVisible, toFinishTaskfn);
    clearTimeout(timer);
  }, 1000);

  openPage(task.url);
};

const clearAdStorage = () => {
  localStorage.removeItem(LocalStorageKey.InvokeStartTime);
};

// 兼容主端android，17.4.6 及 17.4.8 子版本uctrial64，激励视频会下单任务bug，
export const skipCompleteOrderTask = async (task: TaskInfo) => {
  const clientType = mx.store.get('app.pr');
  const isLite = clientType === 'UCLite';
  const sv = mx.store.get('app.appVersionDetail.appSubVersion');
  const appVersion = await dispatch.app.getAppVersion();
  return task.event !== TASK_EVENT_TYPE.VIDEO_AD_BROWSE && isAndroid && !isLite && (!isLatestVersion(appVersion, '17.4.8') || (appVersion.startsWith('17.4.8') && sv === 'uctrial64'));
}

/** 完成激励广告任务 */
export const finishIncentiveAdTask = (task: TaskInfo, requestId: string) => {
  const browseAdPlayer = browseAdPlayerInstance.getAdPlayer(task.id);
  const adData = getIncentiveAdSlotData(task);
  const recordInvokeInfo = () => {
    adPlayerCache.addInvokeTaskIds(task.id);
    localStorage.setItem(LocalStorageKey.InvokeStartTime, `${Date.now()}`);
  };
  clearAdStorage();
  if (!browseAdPlayer) {
    Toast.show('广告正在加载中，请稍后再试');
  }
  const adTaskPreloadResult = browseAdPlayerInstance.getAdTaskPreloadResult(task.id);
  browseAdPlayerInstance.removeAdTaskPreloadResult(task.id);
  browseAdPlayer?.play(
    async (playRes: PlayRes) => {
      // 广告播放回调
      let playResObj = formatStrToObj(playRes?.msg);
      console.log('playResObj======res', playResObj);
      const rewardInfoList: IRewardInfoItem[] = playResObj?.reward_info_list || [];
      if (playRes.success) {
        const tanxRewardTypes = [TanxRewardType.BROWSE, TanxRewardType.ORDER, TanxRewardType.BROWSE_ORDER];
        // 收到发奖回调，移除调起广告的记录
        adPlayerCache.removeInvokeTaskIds(task.id, true);
        const skipCompleteOrder = await skipCompleteOrderTask(task);
        if (!skipCompleteOrder && tanxRewardTypes.includes(playResObj.reward_type)) {
          dispatch.task.completeTanxProgressiveTask({
            task,
            rewardType: playResObj.reward_type,
          });
          tanxAdvancedTaskMonitor('tanx进阶广告发放奖励', task, adData?.slotKey, playResObj.reward_type)
        } else {
          dispatch.task.finishTask({
            taskId: task?.id,
            type: 'complete',
            traceId: requestId,
            showToast: true,
            useUtCompleteTask: !!task?.useUtCompleteTask,
            publishId: task.publishId,
          });
        }
        // 告诉客户端用户已经领取奖励
        if (playResObj.reward_success_id) {
          notifyAdAwardSuccess(
            task,
            {
              slotKey: playResObj.aid,
              rewardId: playResObj.reward_success_id,
              requestId: `${Date.now()}`,
              appId: rewardInfoList?.[0]?.app_id || adData?.appId,
            },
            'playRes',
            requestId,
          );
        }
      }
      if (!playRes.success) {
        // 同步通知去掉提示, 等异步查奖再无结果时提示,
        // ios异步查奖耗时需5s，先走原来逻辑。
        if (isIOS && !playResObj.is_ended && task?.event === TASK_EVENT_TYPE.VIDEO_AD) {
          Toast.show('任务未完成');
        }
      }
      stat.custom('incentive_ad_play', {
        playad_result: playRes.success ? 'finish' : 'unfinish',
        task_id: task?.id,
        task_name: task?.name,
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        slot_id: rewardInfoList?.[0]?.slot_id || playResObj?.aid || '',
        adapp_id: rewardInfoList?.[0]?.app_id || adData?.appId,
        adn_id: rewardInfoList?.[0]?.adn_id || '',
        sid: rewardInfoList?.[0]?.sid || '',
        price: rewardInfoList?.[0]?.price || '',
        pid: rewardInfoList?.[0]?.pid || '',
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        is_tanx_advanced: adTaskPreloadResult?.isTanxAdvanced ? '1' : '0',
        reward_type: playResObj.reward_type
      });
      const leaveTime =
        (Date.now() - Number(localStorage.getItem(LocalStorageKey.InvokeStartTime) || `${Date.now()}`)) / 1000;
      const timeSection = getDateSecondRange(leaveTime);
      mx.event.off(EventMap.PageHidden, recordInvokeInfo);
      tracker.log({
        category: 158, // 系统自动生成，请勿修改
        msg: `离开页面时长`, // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 1, // 用于计算"成功率";可选值为0或1
        c1: String(leaveTime > 30),
        c2: String(timeSection),
        c3: `${task.id}_${task.event}`,
        c4: String(task.name),
        c5: String(leaveTime),
        c6: String(playResObj?.aid || adData),
        c7: String(playResObj.is_ended),
        bl1: JSON.stringify(task), // 自定义长文本bl1 对应 错误信息
      });
    },
    (invokeRes) => {
      const fail_reason = JSON.stringify(invokeRes?.msg).replace(/\s|\+/g, '').toLowerCase().includes('nofill')
        ? 'empty'
        : 'error';
      let invokeResObj = formatStrToObj(invokeRes?.msg);
      const rewardInfoList: IRewardInfoItem[] = invokeResObj?.reward_info_list || [];
      console.log('invokeRes===', invokeRes);
      const adParams = invokeRes?.params;
      stat.custom('incentive_ad_turnup', {
        turnup_result: invokeRes.success ? 'success' : 'fail',
        task_id: task?.id,
        task_name: task?.name,
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        slot_id: rewardInfoList?.[0]?.slot_id || adParams?.slot_key || '',
        adapp_id: rewardInfoList?.[0]?.app_id || adParams?.appId || '',
        adn_id: rewardInfoList?.[0]?.adn_id || '',
        sid: rewardInfoList?.[0]?.sid || '',
        price: rewardInfoList?.[0]?.price || '',
        pid: rewardInfoList?.[0]?.pid || '',
        turnup_fail_reason: invokeRes.success ? '' : fail_reason,
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        is_tanx_advanced: adTaskPreloadResult?.isTanxAdvanced ? '1' : '0',
      });
      // 广告调起成功在监听
      if (invokeRes.success) {
        dispatch.app.set({ needPageVisibleUpdate: false });
        if (adTaskPreloadResult?.isTanxAdvanced) {
          tanxAdvancedTaskMonitor('tanx进阶广告调起', task, adData?.slotKey)
        }
        adPlayerCache.setLastShowAdTask(task);
        mx.event.on(EventMap.PageHidden, recordInvokeInfo);
      } else {
        Toast.show('暂无广告填充，请先做其它任务');
      }
    },
  );
};

/**
 * 完成公告类任务
 * @param task
 * @param requestId
 * @returns
 */
const finishAnnounceTask = (task: TaskInfo, requestId: string) => {
  console.log('公告任务', task);
  // 有配置跳转链接,跳转后完成,
  if (task?.url) {
    handleLinkTask(task, requestId);
    return;
  }
  // 没有配置链接,点击即完成, 有奖励才toast提示,无奖励不提示
  const showToast = !!task?.rewardItems?.[0]?.amount;
  dispatch.task.finishTask({
    taskId: task?.id,
    type: 'complete',
    traceId: requestId,
    showToast: showToast,
    useUtCompleteTask: !!task?.useUtCompleteTask,
    publishId: task.publishId,
  });
};

/** 登录绑定任务 */
export const handleLogin = () => {
  const user = mx.store.get('user');
  if (!user?.isLogin) {
    mx.event.emit(MainAPI.OpenLoginProtocolPanel);
    return false;
  }
  if (!user?.bindTaobao) {
    dispatch.user.toBindTaobao('high_value_modal');
    return false;
  }
  return true;
};

export const taskActionHandler = (task: TaskInfo, requestId: string, params?: { location: string }) => {
  console.log('taskActionHandler', task);
  const needAddTagTaskIdList: string[] = mx.store.get('app.mainInfo.frontData')?.needAddTagTaskIdList || [];
  const workerRegisterTaskIds = workerBridge.getRegisterTaskIds();
  const tasKEvent = task.event;
  // 检测任务是否已经超时
  const checkTaskTimeoutFlag = checkTaskTimeout(task);
  if (checkTaskTimeoutFlag) {
    // 更新任务列表
    Toast.show('任务已过期');
    dispatch.task.taskInit();
    return;
  }

  // 添加实时标签
  if (needAddTagTaskIdList?.includes(`${task?.id}`)) {
    dealWithRealTimeTagFn(task);
  }

  // appworker注册了监听的任务，仅跳转
  if (workerRegisterTaskIds.includes(task.id)) {
    stat.custom('worker_to_do_task', {
      action: 'todotask',
      task_id: task.id || '',
      task_name: task.name || '',
    })
    tracker.log({
      category: 178,
      msg: `点击去完成任务`,
      sampleRate: 1,
      w_succ: 1,
      c1: `${task.id}`,
      c2: getParam('entry'),
    });
    openPage(task.url);
    return;
  }
  // 下载任务
  if (task?.event === TASK_EVENT_TYPE.CALL_APP_DOWNLOAD) {
    handleCallAppDownload(task, requestId, params?.location);
    return;
  }
  // 换量任务(唤端)
  if (tasKEvent?.includes('call_app') || [TASK_EVENT_TYPE.CALL_TAOBAO, TASK_EVENT_TYPE.APP_TOKEN].includes(task?.event)) {
    handleCallApp(task, 'tasklist', requestId);
    return;
  }

  // 加个标志, 用于返回h5时检查任务是否完成,未完成需要给个toast
  if (completionToastTask(task)) {
    dispatch.task.set({ curHandleTask: task });
  }

  if (isAdVideoTask(task)) {
    // 激励广告自定义打点
    const adData = getIncentiveAdSlotData(task);
    const adTaskPreloadResult = browseAdPlayerInstance.getAdTaskPreloadResult(task.id);
    stat.custom('incentive_ad_click', {
      task_id: task?.id,
      task_name: task?.name,
      task_count: task?.dayTimes?.progress,
      taskclassify: task?.taskClassify,
      groupcode: task?.groupCode,
      award_amount: getTaskAward(task),
      task_progress: task?.dayTimes?.progress || '',
      adapp_id: adData?.appId,
      slot_id: adData?.slotKey,
      is_tanx_advanced: adTaskPreloadResult?.isTanxAdvanced ? '1' : '0',
    })
    // console.log('adTaskPreloadResult:', adTaskPreloadResult);
    if (checkShowTanxAdPop(task, !!adTaskPreloadResult?.isTanxAdvanced, params?.location)) {
      tanxAdvancedTaskMonitor('tanx进阶广告显示奖励弹窗', task, adData?.slotKey)
      baseModal.open(MODAL_ID.TANX_AD, {
        adTask: task,
        adTaskPreloadResult,
        requestId,
      });
      return;
    }
    finishIncentiveAdTask(task, requestId);
    return;
  }
  switch (tasKEvent) {
    // 直接拉起分享渠道任务
    case TASK_EVENT_TYPE.UC_SHARE_TARGET:
      handleShareTargetTask(task, requestId)
      break;
    // 分享任务
    case TASK_EVENT_TYPE.SHARE:
    case TASK_EVENT_TYPE.TASK_SUB_SEARCH:
      handleShareTask(task);
      break;
    // 客户端浏览任务
    case TASK_EVENT_TYPE.SEARCH:
    case TASK_EVENT_TYPE.IFLOW_BROWSE:
    case TASK_EVENT_TYPE.IFLOW_VIDEO:
    case TASK_EVENT_TYPE.NOVEL_READ:
    case TASK_EVENT_TYPE.CLIENT_MISSION_DRAMA:
      handleBrowseTask(task, requestId);
      break;
    // 小组件任务
    case TASK_EVENT_TYPE.ADD_WIDGET:
    case TASK_EVENT_TYPE.UC_DESKTOP_ADD:
      desktopVisit(task, params);
      localStorage.setItem(LocalStorageKey?.requestId, requestId);
      break;
    // 桌面小组件访问
    case TASK_EVENT_TYPE?.UC_DESKTOP_VISIT:
      desktopVisit(task, params);
      break;
    // 链接跳转任务
    case TASK_EVENT_TYPE.JUMP_LINK:
      handleLinkTask(task, requestId);
      break;
    case TASK_EVENT_TYPE.RTA_CALL_TAOBAO:
      handleRTACallTask(task, requestId);
      break;
    case TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU:
    case TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD:
      handleRTANuTask(task, requestId);
      break;
    case TASK_EVENT_TYPE.FARM_ANNOUNCE:
      finishAnnounceTask(task, requestId);
      break;
    case TASK_EVENT_TYPE.UC_LOGIN:
      handleLogin();
      baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
      break;
    case TASK_EVENT_TYPE.HIGH_VALUE_TASK:
      baseModal.open(MODAL_ID.HIGH_VALUE_TASK);
      break;
    case TASK_EVENT_TYPE.SEARCH_READ_CLICK:
    case TASK_EVENT_TYPE.SEARCH_READ_ONCE:
      handleSearchReadTask(task);
      break;
    case TASK_EVENT_TYPE.AD_HUICHUAN_BRAND: // 汇川品牌
      handleHcAdBrandTask({
        task,
        position: params?.location ?? 'list',
        requestId
      });
      break;
    case TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT: // 汇川效果
      handleHcAdCorpTask({ task, position: params?.location ?? 'list', requestId });
      break;
    case TASK_EVENT_TYPE.OPEN_MODAL:
      handleToModalTask(task, requestId);
      break;
    default:
      openPage(task.url);
  }
};

/*
* 是否显示tanx下单任务弹窗：下单任务可做 && 下一个是tanx进阶广告 && 点击任务列表 && 不在频控范围
* @param task: TaskInfo
* @param isTanxAd: boolean
* @param location: TASK_LOCATION
* return boolean
* */
const checkShowTanxAdPop = (adTask: TaskInfo, isTanxAd: boolean, location: TASK_LOCATION) => {
  const showPopLocations = [TASK_LOCATION.LIST, TASK_LOCATION.TASK_WIDGET, TASK_LOCATION.TASK_PREMANENT_AD];
  if (!isTanxAd || !showPopLocations.includes(location)) {
    return false
  }
  // 激励视频，非激励浏览任务
  const isVideoAdNewTask = isAdVideoTask(adTask) && adTask.event !== TASK_EVENT_TYPE.VIDEO_AD_BROWSE;
  const progressiveIncentiveOrderTask = mx.store.get('task.progressiveIncentiveOrderTask');
  const tanx_order_task_dialog = mx.store.get('cms.tanx_order_task_dialog');
  const { showTimeGap, maxDailyDisplayCount } = tanx_order_task_dialog?.items?.[0] || {showTimeGap: 0.5, maxDailyDisplayCount: 10};
  const isOrderTaskAvailable = progressiveIncentiveOrderTask && !checkTaskFinished(progressiveIncentiveOrderTask);
  const showPopUp = shouldShowPopup(MODAL_ID.TANX_AD, showTimeGap, maxDailyDisplayCount);
  return isVideoAdNewTask && isOrderTaskAvailable && showPopUp;
}

// 搜索任务
const handleSearchReadTask = (task: TaskInfo) => {
  console.log('taskSearch', task);
  const newTask = searchTaskEventReplace(task)
  const wordSearch = newTask?.ext?.words?.[0];
  const word = encodeURIComponent(wordSearch?.name || '');
  const ext_params = encodeURIComponent(JSON.stringify({ q: word || '' }));
  // biz_type: 标记业务类型, 需要透传给客户端，用于区分接口 https://aliyuque.antfin.com/aone650644/lm2m5t/zf7gv9idrwb3hs0t
  const url = `${newTask.url}&biz_type=farm&app_id=${config?.appId}&word=${word}&q=${word}&ext_params=${ext_params}`;
  console.log('openPage', url);
  openPage(url);
}

// 新增搜索event替换
const searchTaskEventReplace = (task: TaskInfo) => {
  const [url] = task.url.split('?');
  const from = getTaskFromNewSearch(task);
  const clientType = mx.store.getStore()?.app?.pr === 'UCLite' ? 'UCLite' : 'UC';
  const event = SEARCH_EVENT_MATCH?.[task?.event]?.[clientType]
  const urlQueryObj = getSearchParamObject(task.url);

  let data = {
    ...urlQueryObj,
    // 覆盖掉原来的from参数
    from,
    event,
    tid: task?.id
  }
  task.url = addParams(url, data);
  return task
}

export function filterDongfengTask(task: TaskInfo) {
  const { extra = '' } = task;
  if (!extra) {
    return false;
  }
  try {
    const json = JSON.parse(extra);
    return Boolean(json.monitorClickUrl) || Boolean(json.monitorExposureUrl);
  } catch (error) {
    return false;
  }
}

export function filterHcTask(task: TaskInfo) {
  if ([TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT, TASK_EVENT_TYPE.AD_HUICHUAN_BRAND].includes(task.event)) {
    return `task-hc-${task.id}`
  }
  return ''
}


export function dongfengTaskReport(task: TaskInfo, action: 'expose' | 'click') {
  const { extra = '' } = task;
  if (!extra) {
    return;
  }
  try {
    const json = JSON.parse(extra);
    if (!json.monitorClickUrl || !json.monitorExposureUrl) {
      return;
    }
    stat.custom(action === 'expose' ? 'task_exposure_monitoring' : 'task_click_monitoring', {
      c: 'task',
      d: 'dongfeng',
      task_id: task.id,
      task_name: task.name,
    });
    const url = action === 'expose' ? json?.monitorExposureUrl : json?.monitorClickUrl
    dongfengMonitoring(url, action)
      ?.then((data) => {
        tracker.log({
          category: 146, // 系统自动生成，请勿修改
          msg: '东风检测上报', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: String(task.id ?? ''), // 自定义字段c1 对应 任务ID
          c2: String(action), // 自定义字段c2 对应 行为action
          c3: String(url), // 自定义字段c3 对应 检测链接url
          bl1: '', // 自定义长文本bl1 对应 错误信息
        });
      })
      .catch((error) => {
        tracker.log({
          category: 146, // 系统自动生成，请勿修改
          msg: '东风检测上报', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 0, // 用于计算"成功率";可选值为0或1
          c1: String(task.id ?? ''), // 自定义字段c1 对应 任务ID
          c2: String(action), // 自定义字段c2 对应 行为action
          c3: String(url), // 自定义字段c3 对应 检测链接url
          bl1: JSON.stringify(error), // 自定义长文本bl1 对应 错误信息
        });
      });
  } catch (error) {
    console.log(error);
  }
}

/**
 * 汇川曝光
 */
export function hcAdTaskReport(task: TaskInfo) {
  const { extra = '', event, accountId = '', slotId = '' } = task;
  const debug = getParam('debug');
  if (event === TASK_EVENT_TYPE.AD_HUICHUAN_BRAND) {
    dispatch.hcAd.taskBrandExposure(task);
  }
  if (event === TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT) {
    debug && console.log('[hc-ad] hcAdTaskReport', accountId, slotId, task);
    dispatch.hcAd.corpTaskExposure({
      accountId,
      slotId,
      task
    });
  }
}

// 延迟发奖
const taskDelayCompleteByExtra = (task: TaskInfo, requestId: string) => {
  const extraObj = getExtraInfo(task);
  // 没有awardTime走之前逻辑
  if (!extraObj?.awardTime) {
    return false;
  }
  let startTime = 0;
  const PageVisible = async () => {
    if (startTime && Date.now() - startTime >= Number(extraObj?.awardTime)) {
      await dispatch.task.finishTask({
        taskId: task?.id,
        type: 'complete',
        traceId: requestId,
        useUtCompleteTask: !!task?.useUtCompleteTask,
        publishId: task.publishId,
        showToast: true,
      });
      if (task.event === TASK_EVENT_TYPE.AD_HUICHUAN_BRAND) {
        dispatch.hcAd.setTaskBrandFromStore(task);
      }
    } else {
      Toast.show('任务未完成');
    }
    mx.event.off(EventMap.PageHidden, PageHidden);
    mx.event.off(EventMap.PageVisible, PageVisible);
  };
  const PageHidden = () => {
    startTime = Date.now();
  };
  mx.event.on(EventMap.PageHidden, PageHidden);
  mx.event.on(EventMap.PageVisible, PageVisible);
  return true;
};

// 检查是否支持组件链路优化的客户端版本
export const checkUpdateToastVersion = async () => {
  // ios 不支持
  if (isIOS) {
    return false;
  }
  const clientType = mx.store.get('app.pr');
  const isLite = clientType === 'UCLite';
  const sv = mx.store.get('app.appVersionDetail.appSubVersion');
  const appVersion = await dispatch.app.getAppVersion();
  if (!isLite) {
    // 主端安卓, 17.4.6.1377
    return isLatestVersion(appVersion, '17.4.6.1377')
  }
  if (appVersion.startsWith('17.2.9.1360')) {
    return sv === 'ucliteplusrelease5'
  }
  // 极速版 安卓：> 17.2.9.1360 支持，= 17.2.9.1360 +子版本号： ucliteplusrelease5
  return isLatestVersion(appVersion, '17.2.9.1360')
}
// 桌面小组件访问
export const desktopVisit = async (task: TaskInfo, params?: { location: string }) => {
  const { location = '' } = params || {};
  // 复访任务 IOS 无法判断用户是否安装桌面小组件
  if (isIOS && [TASK_EVENT_TYPE?.UC_DESKTOP_VISIT].includes(task.event)) {
    Toast.show('添加桌面组件并访问才可领取奖励哦~');
    return;
  }
  const checkIsLastVersion = await checkUpdateToastVersion();
  // 先判断是否安装，安装了引导toast
  if (isAndroid) {
    const widgetInfoMonitor = tracker.Monitor(159);
    try {
      const widgetInfo = await whetherWidget(widget, { taskInfo: task, resource_location: location });
      widgetInfoMonitor.success({
        msg: '检测小组件安装-获取成功',
        c1: String(task.id),
        c2: `${widgetInfo?.isInstalled}`,
        bl1: JSON.stringify(widgetInfo),
      });
      if (widgetInfo?.isInstalled) {
        if (checkIsLastVersion) return
        return Toast.show('从桌面组件访问才可领取奖励哦~');
      }
    } catch (error) {
      widgetInfoMonitor.fail({
        c1: String(task.id),
        msg: '检测小组件安装-失败',
        bl1: JSON.stringify(error),
      });
    }
  }
  try {
    const timer = setTimeout(() => {
      !checkIsLastVersion && Toast.show('可从桌面添加组件，安装完成后点击组件进入，即可完成任务');
      clearTimeout(timer);
    }, 2000);
    stat.custom('task_install_desktop', {
      event_id: '19999',
      task_id: task.id,
    });
    const installRes = await installDesktopWidget(widget, { taskInfo: task, resource_location: location });
    if (checkIsLastVersion && installRes.fail) {
      Toast.show('可从桌面添加组件，安装完成后点击组件进入，即可完成任务');
    }
  } catch (error) {
    Toast.show('当前客户端版本不支持，请升级客户端版本 ~');
  }
};

// 桌面组件每日进入任务 和 安装桌面组件任务
export const widgetTaskHandle = async () => {
  const entry = getParam('entry');
  if (entry === 'farm_desktop') {
    const task = mx.store.get('task');
    const taskList = task?.taskList?.filter((item) => {
      if (TASK_EVENT_TYPE.UC_DESKTOP_VISIT === item?.event && item?.state === TASK_STATUS.TASK_DOING) {
        const statParams = {
          c: 'pop',
          d: 'widget',
          task_id: item?.id,
          task_name: item?.name,
          taskclassify: item?.taskClassify || '',
          groupcode: item?.groupCode || '',
          award_amount: getTaskAward(item),
          task_count: item?.dayTimes?.progress,
          isfinish: 1,
        };
        stat?.exposure('task_exposure', { ...statParams });
        stat?.click('task_click', { ...statParams });
      }
      return (
        [TASK_EVENT_TYPE.UC_DESKTOP_VISIT, TASK_EVENT_TYPE.ADD_WIDGET, TASK_EVENT_TYPE?.UC_DESKTOP_ADD]?.includes(
          item?.event,
        ) && item?.state === TASK_STATUS.TASK_DOING
      );
    });
    if (!taskList?.length) {
      return;
    }
    const resDataList: ResCompleteTask[] = await dispatch?.task?.serialTaskTrigger(taskList);
    const winList = resDataList?.filter((item) => {
      return item?.prizes?.[0]?.win && item?.prizes?.[0]?.rewardItem?.amount;
    });
    // 发奖失败
    if (!winList?.length) {
      Toast.show('网络异常，请重新试一试~');
      return;
    }
    // 奖励数额
    const pointAmount = winList?.reduce((pre, item) => {
      let total = pre + item?.prizes?.[0]?.rewardItem?.amount;
      return total;
    }, 0);
    // 奖励说明文本
    let subheadingText;
    if (winList?.length > 1) {
      subheadingText = '添加和访问小组件奖励已领取';
    } else {
      const curFinishTask = taskList?.find((task) => task?.id === winList?.[0]?.curTask?.id) as TaskInfo;
      subheadingText = getExtraInfo(curFinishTask)?.dialogSubTitle || '';
    }
    baseModal.open(MODAL_ID.PROGRESS_BUBBLE, {
      isReward: true,
      modalType: 'desktopTask',
      subheadingText,
      pointAmount,
    });
    dispatch.app.updateAllData();
  }
};

// 处理添加实时人群包标签函数
export const dealWithRealTimeTagFn = (task: TaskInfo) => {
  const PageHidden = () => {
    dispatch.task.addRealTimeTag(task);
    mx.event.off(EventMap.PageHidden, PageHidden);
  };
  mx.event.on(EventMap.PageHidden, PageHidden);
};

// 汇川品牌广告任务
export const handleHcAdBrandTask = (params: {
  task: TaskInfo; position: string;requestId: string;
}) => {
  const { task, position = 'list', requestId } = params;
  const canOpen = task.token && task.slotId && task.accountId;
  if (!canOpen) {
    tracker.log({
      category: 115,
      sampleRate: 1,
      msg: `${task.id}_${task.event}_${task.name}_任务参数不对`,
      w_succ: 1,
      c1: task.event,
      c2: `${task.id}`,
      c3: position || 'list',
      bl1: task.url || '',
      bl2: JSON.stringify(task),
    });
    dispatch.app.updateAllData();
    return;
  }
  // 汇川点击上报
  dispatch.hcAd.taskBrandClick(task);

  // 延迟发奖
  const delayAwardFlag = taskDelayCompleteByExtra(task, requestId);
  if (!delayAwardFlag) {
    dispatch.task.finishTask({
      taskId: task?.id,
      type: 'complete',
      traceId: requestId,
      useUtCompleteTask: !!task?.useUtCompleteTask,
      publishId: task.publishId,
    });
    dispatch.hcAd.setTaskBrandFromStore(task);
  }
}

// 点击前确认是否已经安装
export const clickCheckAppInstall = (task: TaskInfo) => {
  const extraObj = getExtraInfo(task)
  const { scheme, pkgName } = extraObj;
  if (!scheme || !pkgName) {
    return false;
  }

  const appPkg = isIOS ? scheme : pkgName;
  const currentAppInstallMap = mx.store.get('app.appInstallMap');
  const currentResult = currentAppInstallMap.get(appPkg);
  return !!currentResult?.installed
}


export const handleHcAdCorpTask = async (params: {
  task: TaskInfo; position: string;requestId: string;
}) => {
  const { task, position = 'list', requestId } = params;
  const canOpen = task.token && task.slotId && task.accountId;
  if (!canOpen) {
    tracker.log({
      category: 115,
      sampleRate: 1,
      msg: `${task.id}_${task.event}_${task.name}_任务参数不对`,
      w_succ: 1,
      c1: task.event,
      c2: `${task.id}`,
      c3: position || 'list',
      bl1: task.url || '',
      bl2: JSON.stringify(task),
    });
    dispatch.app.updateAllData();
  }

  const extraObj = getExtraInfo(task) || {};
  /**
   * 通过扩展字段判断是否为下载类任务
   * 1、taskType === "download" 为下载类
   * 2、taskType === "url" 为纯跳转
   */
  const isAppDownloadTask = extraObj?.taskType === 'download';

  // 非下载类的任务
  if (!isAppDownloadTask) {
    await dispatch.hcAd.corpTaskClick({
      taskToken: task.token!,
      slotId: task.slotId!,
      accountId: String(task.accountId),
      task,
    });
    // 延时发奖 && 手动调用
    if (extraObj?.awardTime && extraObj?.isHttpFinish === true) {
      taskDelayCompleteByExtra(task, requestId)
    }
    return;
  }

  // 下载任务
  const checkResult = clickCheckAppInstall(task);
  if (isAppDownloadTask && !checkResult) {
    await dispatch.hcAd.corpTaskClick({
      taskToken: task.token!,
      slotId: task.slotId!,
      accountId: String(task.accountId),
      task,
    });

    // 已经领取过了，就不需要了
    if (task.state !== TASK_STATUS.TASK_NOT_COMPLETED) {
      dispatch.task.receiveTask({
        taskId: task?.id,
        traceId: requestId,
        publishId: task.publishId,
        useUtCompleteTask: !!task.useUtCompleteTask,
      });
    }
    // 更新汇川效果缓存
    dispatch.hcAd.setTaskCorpFromStore(String(task.id), String(task.accountId));
  } else if (isAppDownloadTask && checkResult) {
    Toast.show('该任务已过期，快去做其它任务吧');
    tracker.log({
      category: 115,
      sampleRate: 1,
      msg: `${task.id}_${task.event}_${task.name}_已安装`,
      w_succ: 1,
      c1: task.event,
      c2: `${task.id}`,
      c3: position || 'list',
      bl1: task.url || '',
      bl2: JSON.stringify(task)
    })
    dispatch.app.updateAllData();
  }
}
