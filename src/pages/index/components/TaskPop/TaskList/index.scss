.task-list-comp {
  width: 721rpx;

  .task-item {
    margin: 13rpx 15rpx 0 15rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 24rpx;

    .task-icon {
      margin-left: 20rpx;
      margin-right: 24rpx;
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      overflow: hidden;
    }

    .task-wrap {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 24rpx;
      border-bottom: 1rpx solid #e0e5e8;
      padding: 14rpx 0;
      .task-info {
        display: flex;
        flex-direction: column;
        justify-content: center;

        .task-name {
          font-family: PingFangSC-Medium;
          font-size: 30rpx;
          color: #12161a;
          letter-spacing: 0;
          font-weight: 500;
          display: flex;
          align-items: center;
          .name {
            max-width: 300rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        .task-desc {
          margin-top: 8rpx;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          max-width: 380rpx;
          padding-bottom: 4rpx;
          font-family: PingFangSC-Regular;
        }
        .desc-text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 26rpx;
          color: #859199;
          line-height: 1.3;
        }

        .manure-icon {
          width: 30rpx;
          height: 30rpx;
        }

        .desc-amount {
          font-family: PingFangSC-Semibold;
          font-size: 26rpx;
          color: rgba(175,94,15,0.65);
          letter-spacing: 0;
          font-weight: 700;
          display: flex;
          align-items: center;
        }

        .reward-tip {
          background-color: rgba(255, 217, 59, 0.3) !important;
          border-radius: 10rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          padding: 0 8rpx 0 4.5rpx;
          font-family: PingFangSC-Semibold;
          font-size: 26rpx;
          color: rgba(175,94,15,0.65);
          letter-spacing: 0;
          // font-weight: 600;
          .mt-1 {
            margin-top: 1rpx !important;
          }
        }
      }

      .task-btn {
        position: relative;
        width: 140rpx;
        height: 60rpx;
        border-radius: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC-Semibold;
        font-size: 28rpx;
        font-weight: 700;
        color: #ffffff;
      }
      .btn-finish {
        background: #aab5bb;
      }
      .btn-receive {
        background: #f9c84e;
      }
      .btn-doing {
        background: #2ac638;
      }

      .btn-count-down {
        background: #d4dade;
        position: relative;

        .count-down-num {
          position: absolute;
          right: 0;
          top: -20rpx;
          width: 100rpx;
          height: 30rpx;
          background-image: linear-gradient(90deg, #ff8a4c 0%, #ff422a 100%);
          border-radius: 16rpx 16rpx 16rpx 2rpx;
          font-family: PingFangSC-Semibold;
          font-size: 18rpx;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          line-height: 30rpx;
        }
      }

      .btn-count-down-text {
        font-family: PingFangSC-Semibold;
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .reward-tag {
        position: absolute;
        right: 0;
        top: -20rpx;
        height: 30rpx;
        background-image: linear-gradient(90deg, #ff8a4c 0%, #ff422a 100%);
        border-radius: 16rpx 16rpx 16rpx 2rpx;
        font-family: PingFangSC-Semibold;
        font-size: 18rpx;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        padding: 0 13rpx;
      }
    }
  }
}
