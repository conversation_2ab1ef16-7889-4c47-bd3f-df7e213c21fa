import React, { useEffect } from 'react';
import './index.scss';
import useMxState from '@/hooks/useMxState';
import dispatch from "@/logic/store";
import { ITaskState } from '@/logic/store/models/task/typing';
import { getHighValueTaskList, ifShowTask } from '@/logic/store/models/task/helper';

import TaskItem from './TaskItem';
import { HcAdState } from '@/logic/store/models/ad/type';
import { reportCurrentHiddenTaskIds } from './util';

export default function TaskList(props) {
  const { tasklist_source } = props;
  const [task] = useMxState<ITaskState>('task');
  const [hcAd] = useMxState<HcAdState>('hcAd');
  const {taskBrandAd, taskCorpAd } = hcAd;
  // 获取高价值任务
  const highValueTaskList = getHighValueTaskList();
  // 过滤 广告任务没有配置扩展字段、 加载失败的广告任务
  let showTaskList = task?.taskList?.filter((curTask) => ifShowTask(curTask));
  //  过滤高价值任务
  showTaskList = (showTaskList ?? []).filter((curTask) => !highValueTaskList.includes(String(curTask.id)));

  useEffect(() => {
    if (showTaskList?.length !== task?.taskList?.length) {
      reportCurrentHiddenTaskIds([...(task?.taskList ?? [])]);
    }
  }, [showTaskList.length]);

  useEffect(() => {
    if (taskBrandAd) {
      dispatch.task.combineBrandAdTask();
    }
  }, [taskBrandAd]);

  useEffect(() => {
    if (taskCorpAd && Object.keys(taskCorpAd).length > 0) {
      dispatch.task.combineCorpAdTask();
    }
  }, [taskCorpAd]);


  return (
    <div className="task-list-comp">
      {showTaskList?.map((taskItem, index) => {
        return <TaskItem tasklist_source={tasklist_source} taskInfo={taskItem} key={taskItem?.id} index={index} />;
      })}
    </div>
  )
}
