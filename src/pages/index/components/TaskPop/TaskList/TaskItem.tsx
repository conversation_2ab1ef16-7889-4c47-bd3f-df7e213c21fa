import React, { Fragment, useEffect, useState } from 'react';
import Manure from './images/manure-award-icon.png';
import { TaskInfo, TASK_STATUS, TASK_EVENT_TYPE, TASK_LOCATION } from './types';
import {
  getJointSearchTaskTitle,
  getSearchKeyFromAndQuery,
  checkTaskFinished,
  logToFinishTask,
  canContinueGoLinkTask,
  getTaskAward,
  isAdVideoTask,
  isReadTypeSearchWordsTask,
  getShareTaskAwardText,
  checkTaskCountDown,
  getTargetTimeDiff,
  getTaskCurDayTimeTarget,
  getTaskCurDayTimeProcess,
  DOWNLOAD_APP_EVENT,
} from './util';
import { dongfengTaskReport, filterDongfengTask, filterHcTask, hcAdTaskReport, taskActionHandler } from './help';
import { execWithLock } from '@/lib/utils/lock';
import dispatch from "@/logic/store";
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import useMxState from '@/hooks/useMxState';
import { IUserState } from '@/logic/store/models/user/typings';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { isAndroid } from '@/lib/universal-ua';
import {IAppState, TaobaoRtaConfig} from '@/logic/store/models/app/typings';
import { bindObserver } from '@/lib/utils/help';
import { mx } from '@ali/pcom-iz-use';
import { checkUcLoginTask, getExtraInfo } from '@/logic/store/models/task/helper';
import { getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import {EventMap} from '@/lib/utils/events';
import tracker from '@/lib/tracker';
import { widget } from '@/lib/utils/updateWidget';
import { IShareState } from '@/logic/store/models/share';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import { convertCentsToYuan } from '@/lib/utils/formatNumber';
import CountDown from '@/components/common/CountDown';
import { formatLeftTime } from '@/lib/utils/date';
import Toast from '@/lib/universal-toast';
import { ITaskState } from '@/logic/store/models/task/typing';
import dayjs from "dayjs";

interface IProps {
  taskInfo: TaskInfo;
  index: number;
  tasklist_source: string;
}

export default function TaskItem(props: IProps) {
  const [user] = useMxState<IUserState>('user');
  const [app] = useMxState<IAppState>('app');
  const [share] = useMxState<IShareState>('share');
  const [taobaoRtaConfig] = useMxState<TaobaoRtaConfig>('app.frontData.taobaoRtaConfig');
  const highValueTask = mx.store.get('highValueTask');
  const {curTime} = mx.store.get('task') as ITaskState;
  const taobaoRtaInfo = app.taobaoRtaInfo;
  const taskInfo = props?.taskInfo || {};
  // tanx下单任务
  const progressiveIncentiveOrderTask = mx.store.get('task.progressiveIncentiveOrderTask');
  const isOrderTaskAvailable = progressiveIncentiveOrderTask && !checkTaskFinished(progressiveIncentiveOrderTask);
  const isTaobaoRta = [TASK_EVENT_TYPE.RTA_CALL_TAOBAO, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD].includes(taskInfo.event) && taobaoRtaInfo;
  const isHcAdTask = [TASK_EVENT_TYPE.AD_HUICHUAN_BRAND, TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT].includes(taskInfo.event);
  const [exposureAdTaskNum, setExposureAdTaskNum] = useState(0);

  const word = taskInfo?.ext?.words?.[0] || { name: '', type: 0, from: '' };
  const [taskCurrentWord, setTaskCurrentWord] = useState('');
  const [exposureKeyNum, setExposureKeyNum] = useState(0);

  // 出来搜索任务词曝光问题
  useEffect(() => {
    if (isReadTypeSearchWordsTask(taskInfo)) {
      setTaskCurrentWord(word.name);
      if (!taskCurrentWord) {
        return;
      }
      if (exposureKeyNum && taskCurrentWord !== word.name) {
        stat.exposure('task_exposure', {
          c: `card${props?.index + 1}`,
          d: 'task',
          task_id: taskInfo?.id,
          task_name: taskInfo?.name,
          isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
          task_count: taskInfo?.dayTimes?.progress,
          tasklist_source: props?.tasklist_source,
          taskclassify: taskInfo?.taskClassify,
          groupcode: taskInfo?.groupCode,
          award_amount: getTaskAward(taskInfo),
          task_progress: taskInfo?.dayTimes?.progress || '',
          ...getSearchKeyFromAndQuery(taskInfo), // from渠道号关键词 只有搜索任务需要
        });
      }
    }
  }, [exposureKeyNum, word]);

  useEffect(() => {
    if (taskInfo.event === TASK_EVENT_TYPE.ADD_WIDGET && isAndroid) {
      document.addEventListener('UCEVT_Widget_Installed', monitorWidgetinatsll(taskInfo));
    }
    // 淘宝RTA任务曝光
    if (isTaobaoRta) {
      dispatch.task.ratTaskExposure();
    }

    // 高价值多步骤RTA任务曝光
    if (taskInfo.event === TASK_EVENT_TYPE.HIGH_VALUE_TASK) {
      dispatch.highValueTask.rtaTaskExposure();
    }

    const PageVisible = () => {
      if (isReadTypeSearchWordsTask(taskInfo)) {
        setExposureKeyNum(new Date().getTime())
      }
      setTimeout(() => {
        setExposureAdTaskNum(new Date().getTime())
      }, 2000)
    };
    mx.event.on(EventMap.PageVisible, PageVisible);

    return () => {
      if (taskInfo.event === TASK_EVENT_TYPE.ADD_WIDGET && isAndroid) {
        document.removeEventListener('UCEVT_Widget_Installed', monitorWidgetinatsll(taskInfo));
      }
      mx.event.off(EventMap.PageVisible, PageVisible);
    }
  }, []);

  useEffect(() => {
    if (isAdVideoTask(taskInfo)) {
      const adData = getIncentiveAdSlotData(taskInfo);
      stat.custom('incentive_ad_show', {
        task_id: taskInfo?.id,
        task_name: taskInfo?.name,
        isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
        task_count: taskInfo?.dayTimes?.progress,
        page_status: user?.bindTaobao ? 1 : 0,
        tasklist_source: props?.tasklist_source,
        taskclassify: taskInfo?.taskClassify,
        groupcode: taskInfo?.groupCode,
        award_amount: getTaskAward(taskInfo),
        task_progress: taskInfo?.dayTimes?.progress || '',
        adapp_id: adData?.appId,
        slot_id: adData?.slotKey,
      })
    }
  }, [exposureAdTaskNum])

  useEffect(() => {
    // 判断是否是登录任务
    const ucLoginTask = checkUcLoginTask(taskInfo);
    if (ucLoginTask.hasUcLoginTask) {
      dispatch.app.set({
        ucLoginTask
      })
    }
  }, [])

  useEffect(() => {
    // 东风曝光上报
    const container = document.getElementById(`task-dongfeng-${taskInfo.id}`) as HTMLElement;
    container &&
        bindObserver(
          container,
          () => {
            dongfengTaskReport(taskInfo, 'expose');
          },
          () => {},
        );
      
    // 汇川曝光上报
    const hcAdContainer = document.getElementById(`task-hc-${taskInfo.id}`) as HTMLElement;
    hcAdContainer &&
    bindObserver(
      hcAdContainer,
      () => {
        hcAdTaskReport(taskInfo);
      },
      () => {},
    );
  }, [])

  // 监听小组件是否安装
  const monitorWidgetinatsll = (params) => {
    const widgetInfoMonitor = tracker.Monitor(159);
    return async (state) => {
      console.log('监听小组件是否安装==', state);
      widgetInfoMonitor.fail({
        msg: `监听小组件是否安装-${state?.detail?.typeId === widget.typeId}`,
        bl1: JSON.stringify(state)
      })
      // if (state && state.detail) {
      //   if (state?.detail?.typeId === widget.typeId && params?.event === TASK_EVENT_TYPE.ADD_WIDGET) {
      //     Toast.show('安装成功，从桌面复访即可拿肥料');
      //   }
      // }
    };
  };

  const renderRewardTag = (task: TaskInfo) => {
    const { rewardTag = '' } = getExtraInfo(task);
    const displayRewardTag = rewardTag?.length > 5 ? `${rewardTag?.slice(0, 5)}...` : rewardTag;
    return (
      displayRewardTag &&
        <div className="reward-tag">
          {displayRewardTag}
        </div>
    )
  }

  const renderRewardTip = (task: TaskInfo) => {
    const { rewardTip = '' } = getExtraInfo(task);
    const rewardIcon = task?.rewardItems?.[0]?.icon;
    return (
      rewardTip &&
        <div className="reward-tip">
          <LazyLoadImage className="manure-icon mt-1" src={rewardIcon || Manure} alt="肥料" />
          {rewardTip}
        </div>
    )
  }
  const receiveAward = () => {
    console.log('领取奖励')
    dispatch.task.finishTask({
      taskId: taskInfo?.id,
      type: "award",
      useUtCompleteTask: !!taskInfo?.useUtCompleteTask,
      publishId: taskInfo.publishId,
      showToast: true
    });
    // 汇川任务完成后，删除缓存
    if (taskInfo.event === TASK_EVENT_TYPE.AD_HUICHUAN_EFFECT) {
      dispatch.hcAd.deleteTaskCorpFromStore(String(taskInfo.id), String(taskInfo.accountId));
    }
  }

  const toFinishTask = async (requestId: string) => {
    localStorage.setItem(LocalStorageKey.FINISH_TASK_FROM, 'list');
    // 去完成任务监控
    logToFinishTask(taskInfo, TASK_LOCATION.LIST);
    return taskActionHandler(taskInfo, requestId, {location: TASK_LOCATION.LIST});
  }

  const handleTask = async () => {
    const requestId = geneTaskRequestId();
    // 东风点击上报
    dongfengTaskReport(taskInfo, 'click');

    stat.click('task_click', {
      c: `card${props?.index + 1}`,
      d: 'task',
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
      task_count: taskInfo?.dayTimes?.progress,
      page_status: user?.bindTaobao ? 1 : 0,
      tasklist_source: props?.tasklist_source,
      taskclassify: taskInfo?.taskClassify,
      groupcode: taskInfo?.groupCode,
      award_amount: getTaskAward(taskInfo),
      task_progress: taskInfo?.dayTimes?.progress || '',
      requestId,
      ...getSearchKeyFromAndQuery(taskInfo), // from渠道号关键词 只有搜索任务需要
      ...(isTaobaoRta ? {
        taobao_rta_type: taobaoRtaInfo?.category,
        sid: taobaoRtaInfo.adInfo?.sid,
        rta_price: taobaoRtaInfo.adInfo?.price,
        scene: taobaoRtaConfig?.sceneId,
      } : {}),
      ...(isHcAdTask ? {
        ad_type: taskInfo.event,
        slot_id: taskInfo.slotId,
        account_id: taskInfo.accountId
      } : {})
    })

    const { state } = taskInfo;
    // 已完成
    if (checkTaskFinished(taskInfo)) {
      canContinueGoLinkTask(taskInfo);
      return
    }

    if (highValueTask.currentTaskInfo?.id !== taskInfo.id) {
      // 登录拦截,高价值不拦截
      const loginStatus = await dispatch.user.checkLoginAndBind(0, 7);
      // 高价值任务不拦截
      if (!loginStatus && highValueTask.currentTaskInfo?.id !== taskInfo.id) {
        return;
      }
    }

    if (state === TASK_STATUS.TASK_NOT_COMPLETED && DOWNLOAD_APP_EVENT.includes(taskInfo.event)) {
      const result = await dispatch.task.checkAppDownloadFinish(taskInfo, highValueTask.currentTaskInfo?.id !== taskInfo.id);
      if (result) {
        return;
      }
    }
    // 完成待领取
    if (state === TASK_STATUS.TASK_COMPLETED) {
      return receiveAward();
    }

    if (checkTaskCountDown(taskInfo, curTime)) {
      return Toast.show('倒计时结束解锁，先去做其它的吧')
    }
    // 去完成
    execWithLock('finish_task_lock', async () => {
      await toFinishTask(requestId)
    }, 2000);
  }

  const getTaskBtn = () => {
    const { state, event, beginTime } = taskInfo;
    if (state === TASK_STATUS.TASK_NOT_COMPLETED && [TASK_EVENT_TYPE.RTA_CALL_TAOBAO, ...DOWNLOAD_APP_EVENT].includes(event)) {
      return <div className="task-btn btn-receive">待核销</div>;
    }
    if (state === TASK_STATUS.TASK_COMPLETED) {
      return <div className="task-btn btn-receive">领取</div>;
    }

    // 处在冷却中的任务，显示倒计时
    if (checkTaskCountDown(taskInfo, curTime)) {
      const {diff, isSameDay} = getTargetTimeDiff(taskInfo.beginTime, curTime);
      const endOfD = dayjs(curTime).endOf('day');
      const isToday = isSameDay && endOfD.diff(beginTime, 's') > 60;

      return (
        <div className="task-btn btn-count-down">
          {isToday && (
            <div className="count-down-num">
            <CountDown
              diff={diff}
              formatFunc={(time) => {
                const {hour, min, second} = formatLeftTime(time * 1000);
                return `${hour}:${min}:${second}`;
              }}
              onComplete={() => {
                dispatch.resource.queryResource({firstInit: false})
                dispatch.task.queryTaskList(false);
              }}
            />
            </div>
          )}
          <div className="btn-count-down-text">{isToday ? '待解锁' : '明日再来'}</div>
        </div>
      )
    }

    if (checkTaskFinished(taskInfo)) {
      return <div className="task-btn btn-finish">已完成</div>
    }

    return (
      <div className="task-btn btn-doing">
        {taskInfo?.btnName?.length > 3 ? '去完成' : taskInfo?.btnName || '去完成'}
        {renderRewardTag(taskInfo)}
      </div>
    )
  };
  // 获取奖励文案
  const getAwardText = (task: TaskInfo) => {
    const taskPrize = task?.rewardItems?.[0] || {};
    const mark = taskPrize.mark;
    const rewardRandomAmount = taskPrize.randomAmount;
    const rewardAmount = taskPrize.amount || 0;
    switch (mark) {
      case 'cash':
        return `${rewardRandomAmount ? '最高' : '必得'}${convertCentsToYuan(Number(rewardAmount))}元红包`
      default:
        return `${getTaskAward(task) ? `+${getTaskAward(task)}` : ''}`
    }
  }

  // 获取任务名称
  const getTaskName = () => {
    if ([TASK_EVENT_TYPE?.SEARCH_READ_CLICK, TASK_EVENT_TYPE?.SEARCH_READ_ONCE]?.includes(taskInfo?.event)) {
      const title = getJointSearchTaskTitle(taskInfo);
      return title || taskInfo?.name
    }
    return taskInfo?.name
  }

  const renderTaskAward = (task: TaskInfo) => {
    const rewardIcon = task?.rewardItems?.[0]?.icon;
    const { rewardTip = '' } = getExtraInfo(task);
    const isRenderMax = isAdVideoTask(task) && rewardTip

    return (
      <div className="desc-amount">
        {
          isRenderMax ?
            renderRewardTip(task) :
          <Fragment>
            <LazyLoadImage className="manure-icon" src={rewardIcon || Manure} alt="肥料" />
            <span>{getAwardText(task)}</span>
          </Fragment>
        }

      </div>
    )
  }
  const renderTaskItemDesc = () => {
    /** 浏览下单 */
    if (taskInfo.event === TASK_EVENT_TYPE.VIDEO_AD_BROWSE && isOrderTaskAvailable) {
      return (
        <div className="task-desc">
          <div className="desc-text">浏览</div>
          {renderTaskAward(taskInfo)}
          <div className="desc-text">&nbsp;&nbsp;下单</div>
          {renderTaskAward(progressiveIncentiveOrderTask)}
        </div>
      );
      /** 分享邀请 */
    } else if (taskInfo.event === TASK_EVENT_TYPE.SHARE) {
      const { defaultRewardItem, bizOldItem, bizNewItem, defaultRewardText, bizOldRewardText, bizNewRewardText } =
        getShareTaskAwardText(taskInfo);
      return (
        <div className="task-desc">
          {bizOldItem && bizNewItem ? (
            <Fragment>
              <span className="desc-text">老用户</span>
              <div className="desc-amount">
                <LazyLoadImage className="manure-icon" src={bizOldItem.icon || Manure} alt="肥料" />
                <span>{bizOldRewardText}</span>
              </div>
              <span className="desc-text">&nbsp;新用户</span>
              <div className="desc-amount">
                <LazyLoadImage className="manure-icon" src={bizNewItem.icon || Manure} alt="肥料" />
                <span>{bizNewRewardText}</span>
              </div>
            </Fragment>
          ) : (
            <div className="desc-amount">
              <LazyLoadImage className="manure-icon" src={defaultRewardItem.icon || Manure} alt="肥料" />
              <span>{defaultRewardText}</span>
            </div>
          )}
        </div>
      );
    } else {
      return (
        <div className="task-desc">
          <div className="desc-text">{taskInfo?.desc}</div>
          {!!getAwardText(taskInfo) && renderTaskAward(taskInfo)}

        </div>
      );
    }
  };

  return (
    <div
    onClick={handleTask}
    className="task-item"
    id={`${filterDongfengTask(taskInfo) ? `task-dongfeng-${taskInfo.id}` : filterHcTask(taskInfo)}`}
    >
      <LazyLoadImage src={taskInfo?.icon} className="task-icon" alt={taskInfo?.name} />
      <div className="task-wrap">
        <div className="task-info">
          <div className="task-name">
            <div className="name">{getTaskName()}</div>
            <div>{taskInfo?.dayTimes?.target ? `(${getTaskCurDayTimeProcess(taskInfo)}/${getTaskCurDayTimeTarget(taskInfo)})` : ''}</div>
          </div>
          {renderTaskItemDesc()}
        </div>
        <Fact
          c={`card${props?.index + 1}`}
          d="task"
          expoLogkey="task_exposure"
          expoExtra={{
            task_id: taskInfo?.id,
            task_name: taskInfo?.name,
            isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
            task_count: taskInfo?.dayTimes?.progress,
            tasklist_source: props?.tasklist_source,
            taskclassify: taskInfo?.taskClassify,
            groupcode: taskInfo?.groupCode,
            award_amount: getTaskAward(taskInfo),
            task_progress: taskInfo?.dayTimes?.progress || '',
            ...getSearchKeyFromAndQuery(taskInfo), // from渠道号关键词 只有搜索任务需要
            ...(isTaobaoRta ? {
              taobao_rta_type: taobaoRtaInfo.category,
              sid: taobaoRtaInfo.adInfo?.sid,
              rta_price: taobaoRtaInfo.adInfo?.price
            } : {}),
            ...(isHcAdTask ? {
              ad_type: taskInfo.event,
              slot_id: taskInfo.slotId,
              account_id: taskInfo.accountId
            } : {})
          }}
          noUseClick
        >
          {getTaskBtn()}
        </Fact>
      </div>
    </div>
  )
}
