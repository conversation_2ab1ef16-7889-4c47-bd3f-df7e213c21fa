
import { preloadImg, preloadFileWithFetch } from "@/lib/utils/preloadImg";

export enum SignDay {
  THREE = 3,
  SEVEN = 7
}
/**
 * 相关资源列表
 */
export const resourceMap = {
  [SignDay.THREE]: {
    json: 'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/data.json',
    imgList: [
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_0.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_1.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_2.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_3.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_4.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_5.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_6.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_7.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_8.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_9.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_10.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_11.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/images/img_12.png'
    ]
  },
  [SignDay.SEVEN]: {
    json: 'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/data.json',
    imgList: [
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_0.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_1.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_2.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_3.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_4.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_5.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_6.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_7.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_8.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_9.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_10.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_11.png',
      'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/images/img_12.png'
    ]
  }
};

/**
 * @title 预加载lottie素材资源
 * @param value 第几天
 */
export const preLoadLottieResource = (value: SignDay) => {  
  try {
    preloadFileWithFetch(resourceMap[value].json)
    preloadImg(resourceMap[value].imgList)
  } catch (error) {
    // 捕获一下error
    console.error(error);
  }
}
