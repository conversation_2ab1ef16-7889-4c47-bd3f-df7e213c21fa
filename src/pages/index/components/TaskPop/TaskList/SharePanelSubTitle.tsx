import React from 'react';
import { mx } from '@ali/pcom-iz-use';
import { IInviteInfoListItem } from '@/logic/store/models/share/typing';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import Manure from './images/manure-award-icon.png';
import { RewardItem, TaskInfo } from './types';
import { convertCentsToYuan } from '@/lib/utils/formatNumber';

interface Props {
  inviteCode: string;
}
// 分享自定义副标题
export const SharePanelSubTitle = (props: Props) => {
  const { inviteCodeMap } = mx.store.getStore().share;
  const shareDetail: IInviteInfoListItem = inviteCodeMap.get(props?.inviteCode);


  const otherRewardItems = (shareDetail?.targetTask?.allRewardItems ?? []).filter((item) =>
    (item?.userTagList ?? []).some(
      (tag) =>
        tag?.toLowerCase().includes('biz_new') ||
        tag?.toLowerCase().includes('client_new') ||
        tag?.toLowerCase().includes('recall_old')
    ));
  // 不允许非目标奖励或者没有设置奖励
  if (!otherRewardItems.length || !shareDetail.allowNonTargetUser) {
    return;
  }

  const getUserText = () => {
    switch (shareDetail?.assistUserType) {
      case 'BIZ_NEW':
      case 'CLIENT_NEW':
        return '新用户';
      case 'RECALL_USER':
        return '回流老用户';
      default:
        return '普通用户';
    }
  };

  const getRewardDetail = () => {
    const rewardItems = otherRewardItems[0];
    return {
      ...rewardItems,
      icon: rewardItems.icon || Manure,
      name: rewardItems.name || '肥料',
      rewardAmount: rewardItems.mark === 'cash' ? convertCentsToYuan(Number(rewardItems.amount)) : rewardItems.amount,
    } satisfies RewardItem & {
      rewardAmount: number | string;
    };
  };

  return (
    <div className="share-panel-subTitle">
      <span>
        邀{getUserText()}开通农场助力 {getRewardDetail().randomAmount ? '最高' : '必得'}
      </span>
      <div className="task-reward">
        <LazyLoadImage className="manure-icon" src={getRewardDetail().icon} alt="肥料" />
        <span> +{getRewardDetail().rewardAmount}</span>
      </div>
    </div>
  );
};
