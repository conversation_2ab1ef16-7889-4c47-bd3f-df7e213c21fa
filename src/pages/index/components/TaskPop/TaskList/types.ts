/** 任务事件编码 */
export const enum TASK_EVENT_TYPE {
  /** 签到任务 */
  UCLITE_SIGN = 'baba_farm_signin',
  /** 信息流浏览任务 */
  IFLOW_BROWSE = 'baba_farm_browse',
  /** 信息流视频任务 */
  IFLOW_VIDEO = 'baba_farm_watch',
  /** 搜索 */
  SEARCH = 'baba_farm_search',
  /** 搜索任务 */
  SEARCH_READ_CLICK = 'search_read_click',
  SEARCH_READ_ONCE = 'search_read_once',
  // 老搜索任务event
  UC_READ_ONCE = 'uc_read_once',
  UC_READ_CLICK = 'uc_read_click',
  UCLITE_READ_ONCE = 'uclite_read_once',
  UCLITE_READ_CLICK = 'uclite_read_click',
  /** 分享 */
  SHARE = 'baba_farm_invite',
  /** 小说阅读 */
  NOVEL_READ = 'baba_farm_novel_read',

  // 添加组件到桌面 任务
  ADD_WIDGET = 'baba_farm_desktop', // 添加小组件任务2*2 旧event
  UC_DESKTOP_ADD = 'uc_desktop_add', // 添加小组件任务2*2 新event
  // 每日小组件访问任务
  UC_DESKTOP_VISIT = 'uc_desktop_visit',
  // 激励视频任务
  VIDEO_AD = 'baba_farm_video',
  // 跳转任务
  JUMP_LINK = 'link',

  // 唤端
  CALL_TAOBAO = 'baba_farm_call_tao_bao',

  // RTA淘宝首唤
  RTA_CALL_TAOBAO = 'rta_call_taobao',
  // RTA淘宝新登
  RTA_CALL_TAOBAO_NU = 'rta_call_taobao_nu',

  // RTA淘宝NU 下载任务 对应 rta_call_taobao_nu
  RTA_CALL_TAOBAO_DOWNLOAD = 'rta_call_taobao_download',

  // 事件包含"time_limit_day"的为限时天任务
  TIME_LIMIT_TASK = 'time_limit_day',

  // 施肥次数任务
  TASK_SIGN_SUB_STORE = 'baba_farm_sub_watering',

  // 助力次数任务
  TASK_SUB_SEARCH = 'baba_farm_sub_invite_help',
  // 换量任务
  CALL_APP_TOKEN = 'call_app_token',
  CALL_APP_LINK = 'call_app_link',
  APP_TOKEN = 'app_token',
  // 任务面板底部banner Token 回调任务
  BANNER_CALL_APP_TOKEN = 'banner_call_app_token',

  /** 通用下载安装判断 */
  CALL_APP_DOWNLOAD = 'call_app_download',
  /* 登录绑定 */
  UC_LOGIN = 'uc_login',

  /** 视频浏览 */
  VIDEO_AD_BROWSE = 'video_ad_browse',

  // tanx进阶浏览下单
  PROGRESSIVE_INCENTIVE_ORDER = 'progressive_incentive__order',

  /** 高价值多步骤任务 */
  HIGH_VALUE_TASK = 'high_value_task',
  /**
   * 激励广告类任务事件标签
   */
  INCENTIVE_AD_TAG = 'video_ad',

  /** 公告类任务 */
  FARM_ANNOUNCE = 'uc_farm_announce',

  // 返回标签任务
  CLL_APP_NO_AWARD = 'call_app_no_award',

  // 客户端短剧任务
  CLIENT_MISSION_DRAMA = 'client_mission_drama',

  SUB_INVITE_HELP = 'baba_farm_sub_invite_help',

  UC_SHARE_TARGET = 'uc_share_target',
  /** 汇川效果  */
  AD_HUICHUAN_EFFECT = 'ad_huichuan_effect',
  /** 汇川品牌  */
  AD_HUICHUAN_BRAND = 'ad_huichuan_brand',
  /** 弹窗引流 */
  OPEN_MODAL = 'open_modal',
}

export enum TASK_STATUS {
  TASK_DOING = 0,
  TASK_COMPLETED = 1,
  TASK_CONFIRMED = 2,
  TASK_REPEAT = 3,
  TASK_NOT_READY = 4,
  TASK_PRE_TASK_NOT_FINISH = 5,
  TASK_TIMES_LIMIT = 6,
  TASK_FINISH = 7,
  TASK_NOT_COMPLETED = 8,
  TASK_INVALID_TIME = 9,
}

export interface RewardItem {
  name: string;
  mark: string;
  amount: number;
  icon: string;
  // 标识奖品是否配置随机发放
  randomAmount?: boolean;
}
export interface AllRewardItems extends RewardItem {
  // 用户表示标识
  userTagList: string[];
}

export interface WidgetTaskList {
  taskId: string;
  widgetName: string;
}

export interface TaskInfo {
  /** 任务id */
  id: number;
  preId?: number;
  /** 任务名称 */
  name: string;
  /** 任务描述 */
  desc: string;
  /** 任务事件 */
  event: TASK_EVENT_TYPE;
  /** 任务完成目标 */
  target: number;
  /** 任务进度 */
  progress: number;
  /** 排序 */
  sort: number;
  /** 图标 */
  icon: string;
  /** 任务跳转链接 */
  url: string;
  /** 任务按钮名称 */
  btnName: string;
  /** 0 任务进行中 1 任务完成 2 任务奖励完成 3 任务重复完成 4 任务未完成 5 前置任务未完成 6 任务受限,次数限制 7 任务不存在 8等待完成中 */
  state: TASK_STATUS;
  /** 预告文案 */
  preMessage?: string;
  rewardItems: RewardItem[];
  /** 画像 */
  profile?: string;
  /** 任务完成时间 */
  completeTime: number;
  /* 任务开始时间 */
  beginTime: number;
  /** 任务结束时间 */
  endTime: number;
  // 双倍奖励结束时间
  doubleAwardEndTime: number;
  /** 限时效果结束时间 */
  timeLimitEndTime?: number;
  /* 每日每次完成间隔 */
  dayTimesInterval?: number;
  /** 周期内总天数--循环周期会重置  --********新增 */
  cycleTotalDay: number;

  /** 不分周期连续天数，断了从1开始 --********新增 */
  continuousDay: number;

  adId?: string; // 广告任务附加的属性，在前端将广告id填入
  sid?: string; // 广告任务附加的属性，广告请求id
  accountId?: string; // 广告账户id
  slotId?: string; // 广告slotId

  token?: string; // 广告用的加密串，服务端返回的。
  extra?: string; // 额外字段

  toDelete?: boolean; // 数据处理过程中，用于筛选准备删除的任务

  nextSign?: TaskInfo; // 明天签到任务
  isLastDay?: boolean; // 是否是最后一天签到

  dayTimes?: {
    /** 任务完成总目标 */
    target: number;
    /** 任务当前次数 */
    progress: number;
  }; // 视频任务总目标数和进度

  /** 分类 */
  taskClassify: string;
  /** 分组 */
  groupCode: string;

  /** 限时任务list */
  preTaskList: TaskInfo[];

  /** 是否领取过任务 */
  needReceive: boolean;
  /** 完成任务信息提示 */
  toast?: {
    btnName: string;
    content: string;
    title: string;
    url: string;
  };
  ext?: {
    words?: {
      name: string;
      type: number;
      from?: string;
    };
  };
  /** 任务类型 */
  taskType: string;
  /** 资源位ID */
  publishId: number;

  /** 奖励信息 */
  prizes?: Array<{
    errorCode: string;
    extra: Record<string, string>;
    rewardItem: RewardItem;
    rewardType: string;
    win: boolean;
  }>;

  /** 是否用ut 完成任务 */
  useUtCompleteTask: boolean;
  allRewardItems: AllRewardItems[];
  taskSource: string;
  /** 自定义任务次数间隔控制时间 */
  dayTimeIntervalMap: {
    [key: number]: number;
  };
  title?: string;
  fromCache?: boolean;
  packageName?: string;
}

/** 表示用户已经领取过任务的状态 */
export const HAS_RECEIVE_TASK_STATUS = [
  TASK_STATUS.TASK_COMPLETED,
  TASK_STATUS.TASK_CONFIRMED,
  TASK_STATUS.TASK_FINISH,
  TASK_STATUS.TASK_NOT_COMPLETED,
];
export interface PlayRes {
  success: boolean;
  msg: string;
  reward_success_id: string;
}

export enum TanxRewardType {
  BROWSE = 0,
  ORDER = 1,
  BROWSE_ORDER = 2,
}

export const SEARCH_EVENT_MATCH = {
  [TASK_EVENT_TYPE.SEARCH_READ_ONCE]: {
    UC: TASK_EVENT_TYPE.UC_READ_ONCE,
    UCLite: TASK_EVENT_TYPE.UCLITE_READ_ONCE,
  },
  [TASK_EVENT_TYPE.SEARCH_READ_CLICK]: {
    UC: TASK_EVENT_TYPE.UC_READ_CLICK,
    UCLite: TASK_EVENT_TYPE.UCLITE_READ_CLICK,
  },
};
// 做任务的位置
export const enum TASK_LOCATION {
  LIST = 'list', // 任务列表
  TASK_WIDGET = 'taskWidget', // 首页任务气泡
  LIMITED_TIME_BENEFITS = 'limitedTimeBenefits', // 限时福利弹窗
  MATRIOSKA_POP = 'matrioska_pop', // 套娃弹窗
  HIGH_VALUE_POP = 'high_value_pop', // 高价值任务弹窗
  RETAIN_POPUP = 'retain_popup', // 挽留弹窗
  TASK_PREMANENT_AD = 'task_permanent_ad', // 首页常驻激励广告
  WATERING_POP = 'watering_pop', // 施肥推荐弹窗
}
export const enum TASK_STATE {
  /**
   * 可做任务状态
   */
  FEASIBLE = 'feasible',
  /**
   * 倒计时等待状态
   */
  COUNTDOWN = 'countdown',
  /**
   * 明日再来
   */
  TOMORROW = 'tomorrow',
}
