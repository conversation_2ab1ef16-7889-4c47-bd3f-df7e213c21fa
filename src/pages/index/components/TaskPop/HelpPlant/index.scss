.help-plant-entry-comp{
  width: 720rpx;
  height: 198rpx;
  position: relative;
  z-index: 3;
  background-image: url('./images/bbz-help-task-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 135rpx;
  background-color: #fff;
  border-radius: 30rpx;

  .text-title{
    position: absolute;
    top: 17.83rpx;
    left: 37.17rpx;
    font-family: FZLanTingYuanS-Extra-Bold !important;
    font-size: 36rpx;
    color: #000000;
    letter-spacing: 0;
    line-height: 42rpx;
    font-weight: 400;
    text-shadow: 2rpx -2rpx 0 #fff, 2rpx -2rpx 0 #fff, -2rpx 2rpx 0 #fff, 2rpx 2rpx 0 #fff;
  }

  .help-text-bg{
    width: 126rpx;
    height: 49rpx;
    background-image: url('./images/help-value-text.png');
    background-repeat: no-repeat;
    background-size: cover;
    position: absolute;
    top: 43rpx;
    right: 49rpx;
  }

  .info-wrap{
    margin-top: 73rpx;
    width: 100%;
    height: 45rpx;
    padding-left: 40rpx;
    padding-right: 42rpx;
    box-sizing: border-box;
    align-items: center;
    justify-content: space-between;
    font-family: PingFangSC-Semibold;
    font-size: 24rpx;
    color: #12161A;
    letter-spacing: 0;
    position: relative;
    font-weight: 600;

    .left-info{
      align-items: baseline;
      .rank-value,
      .award-value{
        margin-left: 6rpx;
        font-family: D-DIN-Bold;
        font-size: 44rpx;
        font-weight: normal;
      }

      .rank-unit{
        font-size: 24rpx;
        margin-right: 2rpx;
      }

      .award-wrap{
        margin-left: 50rpx;
        position: relative;
        display: flex;
        align-items: baseline;
        span {
          display: inline-block;
          height: 33rpx;
          line-height: 33rpx;
        }
        &::after{
          content: '';
          width: 1rpx;
          height: 23rpx;
          background-color: #AAB5BB;
          position: absolute;
          left: -25rpx;
          bottom: 6rpx;
        }
      }
      .red-dot{
        position: relative;
        &::before {
          content: '';
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
          background-image: linear-gradient(179deg, #FF9C55 0%, #FF422A 100%);
          border: 3rpx solid #FFFFFF;
          box-sizing: border-box;
          position: absolute;
          right: 16rpx;
          top: 2rpx;
        }
      }

      .dot-p{
        &::before{
          right: -12rpx;
        }
      }
      .arrows-right{
        display: block;
        width: 20rpx;
        height: 20rpx;
        margin-left: 8rpx;
      }
    }
    .right-value{
      font-size: 48rpx;
      width: 140rpx;
      text-align: center;
      font-weight: normal;
    }
  }

  .footer-wrap{
    margin: 0 auto;
    margin-top: 6rpx;
    padding-right: 16rpx;
    width: 679rpx;
    height: 52rpx;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    position: relative;

    &::after{
      content: '';
      width: 15rpx;
      height: 9rpx;
      clip-path: polygon(50% 0, 100% 100%, 0 100%);
      background: #E5FF95;
      position: absolute;
      left: 68rpx;
      top: -8rpx;
    }

    .tips-text{
      width: 625rpx;
      height: 50rpx;
      padding-left: 20rpx;
      box-sizing: border-box;
      background-image: linear-gradient(90deg, #DAFF6A 3%, rgba(255,255,255,0.24) 95%);
      border-radius: 26rpx;
      align-items: center;

      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #525E66;
      letter-spacing: 0;
      font-weight: 400;
      flex-wrap: nowrap;

      span{
        color: #525E66;
        font-weight: 400;
      }
    }

    .invite-btn{
      width: 182rpx;
      height: 52rpx;
      align-items: center;
      justify-content: center;
      background-image: linear-gradient(90deg, #FF8A4C 0%, #FF422A 100%);
      border-radius: 30rpx;
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      color: #FFFFFF;
      font-weight: 600;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .arrow-p{
    &::after{
      left: 74rpx;
    }
  }
}

.help-plant-init-comp {
  margin-top: 13rpx;
  width: 720rpx;
  height: 158rpx;
  position: relative;
  z-index: 3;
  background-image: url('./images/bbz-init-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 158rpx;
  background-color: #fff;
  border-radius: 30rpx;

  .text-title{
    width: 286rpx;
    height: 60rpx;
    position: absolute;
    right: 109rpx;
    top: -20rpx;
  }

  .left-init-img{
    width: 261rpx;
    height: 188rpx;
    position: absolute;
    left: 24rpx;
    bottom: 0;
  }

  .right-wrap{
    padding-top: 38rpx;
    padding-left: 208rpx;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;

    .tips{
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #12161A;
      font-weight: 400;

      .cash-symbol{
        font-size: 26rpx;
      }

      span {
        font-family: D-DIN-Bold;
        font-size: 32rpx;
        color: #F7534F;
      }
    }

    .join-btn{
      margin-top: 12rpx;
      padding: 7rpx 12rpx 7rpx 25rpx;
      background-image: linear-gradient(179deg, #FF9C55 0%, #FF422A 100%);
      border-radius: 30rpx;
      align-items: center;
      span {
        font-family: PingFangSC-Semibold;
        font-size: 26rpx;
        color: #FFFFFF;
        font-weight: 600;
      }

      .arrow-wrap{
        margin-left: 12rpx;
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        background-color: #fff;
        align-items: center;
        justify-content: center;
        img{
          width: 24rpx;
          height: 24rpx;
        }
      }

    }
  }
}
