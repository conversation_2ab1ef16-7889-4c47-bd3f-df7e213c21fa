import React, { useEffect, useRef } from 'react';
import './index.scss';

import Title from './images/bbz-task-title.png';
import ArrowsBlackIcon from '@/assets/arrow-black-icon.svg';
import ArrowsOrangeIcon from '@/assets/arrow-orange-icon.svg';
import useMxState from '@/hooks/useMxState';
import { IFarmHelpPlantState } from '@/logic/store/models/farmHelpPlant/type';
import dispatch from '@/logic/store';
import InitImg from './images/bbz-init-img.png';
import stat from '@/lib/stat';
import { getFarmHelpPlantFactParams, getFarmHelpPlantEntryStatusFact } from '@/pages/index/utils';
import { convertCentsToPoint, convertToPoint, formatRewardNum } from '@/lib/utils/formatNumber';
import { TASK_EVENT_TYPE } from '../TaskList/types';

export default function Index() {
  const [farmHelpPlant] = useMxState<IFarmHelpPlantState>('farmHelpPlant');
  const finishFact = useRef(false);
  /**
   * score: 助力值,
   * ranking: 当前排名
   * rankingAward: 当前排名奖励
   * totalAward:  当前累计肥料奖励值
   * helpFirstVisit:  帮帮种是否首次访问
   * preRankAwardInfo: 下一个阶段排名奖励信息
   */
  const {
    score = 0,
    rankingAward = 0,
    ranking = 0,
    unReceiveAward = 0,
    totalAward = 0,
    helpFirstVisit,
    displayRankNum,
    preRankAwardInfo,
    nextStageAward = 0,
    nextStageScore = 0,
  } = farmHelpPlant.helpPlantHome;
  const taskList = farmHelpPlant.accumulatedTaskList || [];
  /**
   * prizeValue: 奖励值
   */
  const { prizeValue } = farmHelpPlant.topOnePrizeInfo;
  // 有奖励待领取
  const hasReceiveAward = unReceiveAward > 0;
  // 榜一名奖励
  const rankMaxAward = prizeValue ? `￥${Number(prizeValue) / 100}` : '大额';
  // 当前排名预计奖励
  const curRankAward = Number(rankingAward ?? 0) / 100;

  // 蓄水任务的肥料
  const calculateRewardAmount = convertCentsToPoint(farmHelpPlant.calculateRewardAmount ?? 0);
  // 累计获得肥料
  const totalPoint = convertCentsToPoint(totalAward, false);
  // 上榜展示数量(有奖励), 有排名无奖励则未上榜
  const maxRankNum = displayRankNum || 100;
  // 蓄水任务的第一阶段肥料
  const oneAwardPoint = convertToPoint(taskList?.[0]?.rewardItems?.[0]?.amount);

  // 未解锁全部累计挑战
  const doingTask = taskList?.find((taskItem) => taskItem?.state === 0 && taskItem?.event === TASK_EVENT_TYPE.SUB_INVITE_HELP);
  // 下一阶段累计挑战肥料奖励
  const nextTaskPoint = formatRewardNum(nextStageAward);

  // 下一阶段排名的最低分数
  const preRankScore = preRankAwardInfo?.score ?? 0;
  // 下一阶段排名的奖励
  const preRankAward = Number(preRankAwardInfo?.prizeItems?.[0]?.prizeAmount || 0) / 100;

  useEffect(() => {
    if (Object.keys(farmHelpPlant?.helpPlantHome ?? {}).length && !finishFact.current) {
      finishFact.current = true;
      stat.exposure('bbz_card_exposure', {
        c: 'bbzhong',
        d: 'card',
        card_status: getFarmHelpPlantEntryStatusFact().card_status || '',
        assist_fertilizer_reward: getFarmHelpPlantEntryStatusFact().assist_fertilizer_reward || '',
        ...getFarmHelpPlantFactParams(),
      })
    }
  }, [farmHelpPlant?.helpPlantHome])

  const clickEvent = async (e) => {
    const targetClassName = e.target?.className ?? '';
    const isClickShare = targetClassName.includes('invite-btn')
      || targetClassName.includes('right-value')
      || targetClassName.includes('help-text-bg');

    stat.click('bbz_card_click', {
      c: 'bbzhong',
      d: 'card',
      card_status: getFarmHelpPlantEntryStatusFact().card_status || '',
      assist_fertilizer_reward: getFarmHelpPlantEntryStatusFact().assist_fertilizer_reward || '',
      ...getFarmHelpPlantFactParams(),
      click_position: isClickShare ? 2 : 1
    })
    const loginStatus = await dispatch.user.checkLoginAndBind(0, 7);
    // 登陆拦截
    if (!loginStatus) {
      return;
    }
    if (isClickShare) {
      dispatch.share.plantOpenSharePanel('list_card', true);
      return;
    }
    dispatch.farmHelpPlant.openActivityPage('card');
  };

  const renderRankingItem = () => {
    // 排名在前一百且能拿到奖励的时候
    if (ranking && ranking <= maxRankNum) {
      return (
        <div className="left-info row">
          <span>排名</span>
          <span className="rank-value">{ranking}</span>
          <span>名</span>
          <div className={`award-wrap ${hasReceiveAward ? 'red-dot' : ''}`}>
            {curRankAward ? (
              <React.Fragment>
                <span>预计奖励</span>
                <span className="award-value">{curRankAward}</span>
                <span>元</span>
              </React.Fragment>
            ) : (
              <React.Fragment>
                <span>肥料累计</span>
                <span className="award-value">{totalPoint}</span>
                <span>万</span>
              </React.Fragment>
            )}
            <img src={ArrowsBlackIcon} className="arrows-right" />
          </div>
        </div>
      );
    }
    if (ranking > maxRankNum) {
      return (
        <div className="left-info row">
          <span>排名</span>
          <div className="rank-value">
            {ranking >= 10000 ? (
              <React.Fragment>
                1<span className="rank-unit">万+</span>
              </React.Fragment>
            ) : ranking}
          </div>
          <span>名</span>
          <div className={`award-wrap ${hasReceiveAward ? 'red-dot' : ''}`}>
            <span>肥料累计</span>
            <span className="award-value">{totalPoint}</span>
            <span>万</span>
            <img src={ArrowsBlackIcon} className="arrows-right" />
          </div>
        </div>
      );
    }
    return (
      <div className="left-info row">
        <span>{totalAward ? '暂未上榜' : '邀1人助力'}</span>
        {totalAward ? (
          <div className={`award-wrap ${hasReceiveAward ? 'red-dot dot-p' : ''}`}>
            <span>肥料累计</span>
            <span className="award-value">{totalPoint}</span>
            <span>万</span>
          </div>
        ) : (
          <div className="award-wrap">
            <span>可领</span>
            <span className="award-value">{oneAwardPoint}</span>
            <span>千肥料</span>
          </div>
        )}
        <img src={ArrowsBlackIcon} className="arrows-right" />
      </div>
    );
  };

  const renderTipsText = () => {
    // 上榜(有排名奖励)
    if (ranking && ranking <= displayRankNum) {
      const nextRankNeedScore = preRankScore - score + 1;
      return ranking === 1 ? (
        <div className="tips-text row">
          保持优势,<span>{rankMaxAward}</span>红包即将到手,去参与&nbsp;{'>'}
        </div>
      ) : (
        <div className="tips-text row">
         {nextRankNeedScore > 0 && <span>再得{nextRankNeedScore}助力值,</span>}红包将升级至{preRankAward}元,去参与&nbsp;{'>'}
        </div>
      );
    }
    // 未上榜(有排名无奖励)
    if (ranking && ranking > displayRankNum) {
      const showPointTips = doingTask && nextStageScore && (nextStageScore - score) < (preRankScore - score);
      const rankNeedMinScore = preRankScore - score + 1;
      return showPointTips ? (
        <div className="tips-text row">
          再得{nextStageScore - score}助力值,即可再领{nextTaskPoint}肥料,去参与&nbsp;{'>'}
        </div>
      ) : (
        <div className="tips-text row">
          {rankNeedMinScore > 0 && <span>再得{rankNeedMinScore}助力值,</span>}进入前{displayRankNum}名得{preRankAward}元,去参与&nbsp;{'>'}
        </div>
      );
    }
    if (farmHelpPlant.calculateRewardAmount && rankMaxAward) {
      return (
        <div className="tips-text row">
          最高{calculateRewardAmount}肥料,{rankMaxAward}红包天天领！去参与&nbsp;{'>'}
        </div>
      );
    }
    return <div className="tips-text row">参与活动，天天领大额肥料礼包！</div>;
  };

  const renderInitStatus = () => {
    return (
      <div className="help-plant-init-comp" onClick={clickEvent}>
        <img src={Title} className="text-title" alt="" />
        <img src={InitImg} className="left-init-img" alt="" />
        <div className="right-wrap row">
          <div className="tips">
            最高<span className="cash-symbol">¥</span>
            <span>{Number(prizeValue || 8800) / 100}</span>元红包、
            <span>{convertCentsToPoint(farmHelpPlant.calculateRewardAmount ?? 0, false)}</span>万肥料天天领!
          </div>
          <div className="join-btn row">
            <span>立即参加</span>
            <div className="arrow-wrap row">
              <img src={ArrowsOrangeIcon} alt="" />
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (helpFirstVisit) {
    return renderInitStatus();
  }

  return (
    <div className="help-plant-entry-comp" onClick={clickEvent}>
      <div className="entry-content">
        <div className="text-title">
          帮帮种·领现金
        </div>
        <div className="help-text-bg" />
        <div className="info-wrap row">
          {renderRankingItem()}
          <div className="right-value din-num">{score}</div>
        </div>
        <div className={`footer-wrap row ${ranking ? 'arrow-p' : ''}`}>
          {renderTipsText()}
          <div className="invite-btn row">邀人助力</div>
        </div>
      </div>
    </div>
  );
}
