.task-pop-comp{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  .task-list-container {
    padding-top: 0rpx;
    padding-bottom: 80rpx;
    margin-top: 15rpx;
    position: absolute;
    z-index: 3;
    top: 172.75rpx;
    border-radius: 30rpx 30rpx 0 0;
    background: #fff;
  }
  .bbz-top {
    top: 200rpx !important;
  }

  .task-mask{
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.7;
    position: absolute;
    top: 0;
    left: 0;
  }
  .pt-small {
    padding-top: 66rpx !important;
  }
  .content-task{
    width: 100%;
    height: 80%;
    min-height: 69vh;
    background: #50C22B;
    box-shadow: inset 0 8rpx 6rpx 0 rgba(106,255,101,0.20);
    border-radius: 36rpx 36rpx 0 0;
    position: absolute;
    bottom: 0;
    transform: translateY(0);
    box-sizing: border-box;
    padding-top: 90rpx;
    .task-title{
      width: 460rpx;
      height: 200rpx;
      position: absolute;
      top: -97rpx;
      left: 50%;
      transform: translateX(-50%);
    }
    .double-card-title {
      width: 460rpx;
      height: 205rpx;
      position: absolute;
      top: -145rpx;
      left: 50%;
      transform: translateX(-50%);
      .title {
        font-family: FZLanTingYuanS-Bold;
        position: absolute;
        top: 142rpx;
        left: 50%;
        transform: translateX(-50%);
        font-size: 40rpx;
        color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        white-space: nowrap;
        text-wrap: nowrap;
        .arrow-icon {
          width: 28rpx;
          height: 28rpx;
          object-fit: contain;
          margin: auto 0;
          margin-left: 4rpx;
        }
      }
      img {
        width: 100%;
        height: 100%;
      }
    }

    .task-close{
      width: 60rpx;
      height: 60rpx;
      position: absolute;
      top: 12rpx;
      right: 12rpx;
    }

    .task-wrap-ovflow{
      width: 100%;
      overflow: hidden;
    }
    .has-banner .task-list-comp {
      padding-bottom: 84rpx !important;
    }
    .task-wrapper{
      height: 100%;
      box-sizing: border-box;
      overflow-x: hidden;
      overflow-y: auto;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20rpx;
      &::-webkit-scrollbar {
        display: none;
      }

      .task-line{
        width: 750rpx;
        position: absolute;
        top: 126rpx;
        z-index: 2;
      }
    }
  }
}

.fade-in .content-task{
  animation: contentIn .3s ease-in forwards;
}
.task-pop-comp .animated-out{
  animation: contentOut .3s ease-out forwards;
}

@keyframes contentIn {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes contentOut {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(100%);
  }
}

