import React, { useState, useEffect, useRef, Fragment } from 'react';
import './index.scss';
import mx from '@ali/pcom-mx';
import { MainAPI } from '@/logic/type/event';
import stat from '@/lib/stat';
import useMxState from '@/hooks/useMxState';
import { IUserState } from '@/logic/store/models/user/typings';
import { IAppState } from '@/logic/store/models/app/typings';
import { ITaskState } from '@/logic/store/models/task/typing';
import config from '@/config';
import classnames from 'classnames';
import { TasklistSource } from '@/pages/index/utils';
import TaskTitle from './images/task-pop-title.png';
import TaskClose from './images/task-pop-close.png';
import SignTask from './SignTask';
import TaskList from './TaskList';
import TaskBottomBanner from './TaskBottomBanner';
import { browseAdPlayerInstance } from '@/lib/adPlayer/ad_video';
import { isAdVideoTask } from './TaskList/util';
import { dealWithPreloadSuccessAdTask } from '@/logic/store/models/task/helper';
import dispatch from '@/logic/store';
import { getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import { isOpenQueryAward } from '@/logic/store/models/utils';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import HelpPlant from './HelpPlant';
import { disableIOSBounces } from '@/lib/ucapi';
import { checkEnableDoubleCard, checkIsShowBBZEnter } from '@/logic/store/models/app/new_year_time';
import { QueryCardInfoRes } from '@/api/doublePointsCard/typings';
import { DoubleTitleEntry } from './DoubleTitleEntry';

export default function TaskPop() {
  const [user] = useMxState<IUserState>('user');
  const [task] = useMxState<ITaskState>('task');
  const [app] = useMxState<IAppState>('app');
  const [ifShowTaskPop, setIfShowTaskPop] = useState(false);
  const [hasBanner, setHasBanner] = useState(false);
  const [cardInfo] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const [tasklistSource, setTasklistSource] = useState(TasklistSource.collect_fertilizer);
  const popKey = useRef(Date.now());
  const adTaskList = task?.taskList?.filter((itAdTask) => isAdVideoTask(itAdTask));
  // 帮帮种活动时间
  const isBangBangZhongPeriod = checkIsShowBBZEnter();

  const [isFinishPreload, setIsFinishPreload] = useState(false);
  const doubleCardEnable = cardInfo && checkEnableDoubleCard();

  useEffect(() => {
    mx.event.on(MainAPI.ShowTaskPop, handleShowTaskPop);
    mx.event.on(MainAPI.HideTaskPop, handleCloseTaskPop);
  }, []);

  useEffect(() => {
    if (ifShowTaskPop) {
      stat.exposure('task_list_exposure', {
        c: 'panel',
        d: 'task',
        page_status: user?.bindTaobao ? 1 : 0,
        tasklist_source: tasklistSource,
      });
      setTimeout(() => {
        // 禁用 iOS 弹性滚动
        disableIOSBounces(['scroll-box']);
      }, 200);
    }
  }, [ifShowTaskPop]);

  useEffect(() => {
    if (adTaskList?.length && user.bindTaobao) {
      adPlayInit();
      /**
       * 确保预加载后在调queryRewards查询奖励
       */
      setTimeout(() => {
        setIsFinishPreload(true);
      }, 2000);
    }
  }, [task?.taskList, user.bindTaobao]);

  useEffect(() => {
    if (isFinishPreload) {
      dispatch.app.dealwithVideoAward(true);
    }
  }, [isFinishPreload]);

  const adPlayInit = async () => {
    const isLite = app?.pr === 'UCLite';
    const appVersion = await dispatch.app.getAppVersion();
    const isOpenQueryReward = isOpenQueryAward(appVersion, isLite);
    const preloadAdTaskMap = task.preloadAdTaskMap;
    // eslint-disable-next-line array-callback-return
    adTaskList?.map((adTask) => {
      if (preloadAdTaskMap.has(`${adTask?.id}`)) {
        return;
      }
      const adData = getIncentiveAdSlotData(adTask);
      if (adData?.slotKey) {
        preloadAdTaskMap.set(`${adTask?.id}`, adTask?.id);
        browseAdPlayerInstance.init({
          task: adTask,
          slotKey: adData?.slotKey,
          appId: adData?.appId,
          coralAppId: config?.appId,
          enableAsyncQueryReward: isOpenQueryReward,
          finishPreload: (res) => {
            // 加载成功
            if (res) {
              dealWithPreloadSuccessAdTask(adTask);
            }
          },
        });
      }
    });
  };

  const handleShowTaskPop = (data) => {
    const tasklist_source = data?.tasklist_source;
    setTasklistSource(tasklist_source);
    setIfShowTaskPop(true);
    popKey.current = Date.now();
    // 存一下任务面板的状态, 下一次进入页面或者刷新页面时,根据这个状态来判断是否需要自动展开任务面板
    localStorage.setItem('taskPopStatus', 'open');
    localStorage.removeItem(LocalStorageKey.FINISH_TASK_FROM);
  };

  const handleCloseTaskPop = async () => {
    setIfShowTaskPop(false);
    localStorage.removeItem('taskPopStatus');
    localStorage.removeItem(LocalStorageKey.FINISH_TASK_FROM);
  };

  return (
    ifShowTaskPop && (
      <div className={`task-pop-comp ${ifShowTaskPop ? 'fade-in' : ''}`}>
        <div className="task-mask" />
        <div className={`content-task ${doubleCardEnable ? 'pt-small' : ''}`}>
          {doubleCardEnable ? <DoubleTitleEntry /> : <img src={TaskTitle} className="task-title" alt="做任务赚肥料" />}
          <img src={TaskClose} className="task-close" onClick={handleCloseTaskPop} alt="关闭" />
          <div className={classnames('task-wrap-ovflow', { 'has-banner': hasBanner })}>
            <div className="task-wrapper" id="scroll-box">
              {isBangBangZhongPeriod ? (
                <Fragment>
                  <HelpPlant />
                  <div className="task-list-container bbz-top">
                    <SignTask tasklist_source={tasklistSource} />
                    <TaskList tasklist_source={tasklistSource} />
                    <TaskBottomBanner
                      tasklist_source={tasklistSource}
                      popTime={popKey.current}
                      setVisible={setHasBanner}
                    />
                  </div>
                </Fragment>
              ) : (
                <Fragment>
                  <SignTask tasklist_source={tasklistSource} />
                  <div className="task-list-container">
                    <TaskList tasklist_source={tasklistSource} />
                    <TaskBottomBanner
                      tasklist_source={tasklistSource}
                      popTime={popKey.current}
                      setVisible={setHasBanner}
                    />
                  </div>
                </Fragment>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  );
}
