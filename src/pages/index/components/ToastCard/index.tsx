import React, { useEffect, useState } from 'react';
import './index.scss';
import mx from '@ali/pcom-mx';

import ToastSuccess from './images/toast-success.png';
import ToastFail from './images/toast-fail.png';


export default function ToastCard() {
  const [showToastCard, setShowToastCard] = useState(false);
  const [isOut, setIsOut] = useState(false);
  const [toastData, setToastData] = useState({
    type: 'success',
    title: '',
    subTitle: '',
  });

  useEffect(() => {
    mx.event.on('showToastCard', openToastCard)
  }, []);

  const openToastCard = (data) => {
    setToastData(data)
    if (showToastCard) return;
    setShowToastCard(true);
    setTimeout(() => {
      setIsOut(true);
    }, data?.delay || 3000)
  };

  const listenerAniEnd = (envent) => {
    setIsOut(false)
    if (envent?.animationName === 'toastOut') {
      setShowToastCard(false)
    }
  }

  return (
    <div
      className={`toast-card-comp ${showToastCard ? 'toast-in' : ''}`}
      style={{ visibility: showToastCard ? 'visible' : 'hidden' }}
    >
      <div
        className={`toast-content ${isOut ? 'toast-out' : ''}`}
        onAnimationEnd={listenerAniEnd}
      >
        <img src={toastData?.type === 'success' ? ToastSuccess : ToastFail} className="toast-icon" />
        <div className="right-wrap">
          <div className="title">{toastData?.title}</div>
          <div className="sub-title">{toastData?.subTitle}</div>
        </div>
      </div>
    </div>
  )
}
