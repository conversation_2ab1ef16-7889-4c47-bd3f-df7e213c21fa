import React, { useEffect, useRef } from 'react';
import { CacheCanvas } from '@ali/farm-react-component';
import { TAKE_CANVAS_SNAPSHOT } from '@ali/farm-utils';
import { SNAPSHOT_CACHE_KEY } from '@/constants';
import mx from '@ali/pcom-mx';
import tracker from '@/lib/tracker';


function TreeSnapshot() {
  const snapshotRef = useRef<any>();
  const plantInfo = mx.store.get('app.mainInfo.gameInfo.plantInfo');
  useEffect(() => {
    const hasHitSnapshot = snapshotRef.current?.hasHitSnapshotCache();
    console.log('hasHitSnapshot:', hasHitSnapshot);
    tracker.log({
      category: 125,
      msg: '缓存命中率',
      w_succ: hasHitSnapshot ? 1 : 0,
    });
    mx.event.on(TAKE_CANVAS_SNAPSHOT, async (tree: { seedImg?: string; stageLevel?: string }) => {
      try {
        // 缓存过期时间设置为1周
        await snapshotRef.current?.cacheCanvasToStorage(SNAPSHOT_CACHE_KEY, tree, 7 * 24 * 60 * 60 * 1000);
        console.log('TAKE_CANVAS_SNAPSHOT');
      } catch (err) {
        console.log(err);
      }
    });
  }, []);
  return <CacheCanvas enable={!!plantInfo} cacheKey={SNAPSHOT_CACHE_KEY} ref={snapshotRef} />;
}

export default TreeSnapshot;
