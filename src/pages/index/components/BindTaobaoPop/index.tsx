import React, {useEffect, useState} from 'react';
import './index.scss';
import mx from '@ali/pcom-mx';
import {MainAPI} from "@/logic/type/event";
import dispatch from "@/logic/store";
import stat from "@/lib/stat";

import CloseIcon from './assets/<EMAIL>';
import TbIcon from './assets/<EMAIL>';
import UCIcon from './assets/<EMAIL>';
import BindIcon from './assets/bind-logo.png';
import BubbleAngle from './assets/bubble-angle.png';
import feiliaoIcon from './assets/<EMAIL>';

const BindTaobaoPop = () => {
  const [visible, setVisible] = useState(false);
  const [from, setFrom] = useState(0)
  useEffect(() => {
    const handleShowPop = (data) => {
      const from_number = data?.from;
      setFrom(from_number || 0)
      stat.exposure('bind_taobao_exposure', {
        c: 'panel',
        d: 'bind'
      })
      setVisible(true)
    }
    mx.event.on(MainAPI.ShowBindTaobaoPop, handleShowPop)
    mx.event.on(MainAPI.HideBindTaobaoPop, handleClose)
  }, []);

  const handleBind = async () => {
    stat.click('bind_taobao_click', {
      c: 'panel',
      d: 'bind'
    })
    dispatch.user.toBindTaobao(`pop_${from}`)
  }

  const handleClose = () => {
    setVisible(false)
  }


  return (
    <div className={`pop-bind-taobao`} style={{visibility: visible ? 'visible' : 'hidden'}}>
      <div className="pop-mask" onClick={handleClose} />
      <div className={`pop-content-wrap ${visible ? 'pop-appear' : 'pop-disappear'}`}>
        <img src={CloseIcon} className="close-icon" onClick={handleClose} />
        <div className="pop-content column align-c">
          <div className="title">仅差一步！绑定淘宝立享水果权益</div>
          <div className="bind-icons row">
            <img src={TbIcon} className="brand-icon" />
            <img src={BindIcon} className="bind-icon" />
            <img src={UCIcon} className="brand-icon" />
          </div>
          <div className="bind-btn row align-c j-center" onClick={handleBind}>
            一键绑定
            <div className="btn-tip">
              送大额肥料!
              <img src={feiliaoIcon} className="feiliao-icon" />
              <img src={BubbleAngle} className="bubble-angle" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BindTaobaoPop;
