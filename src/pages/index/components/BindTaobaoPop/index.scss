.pop-bind-taobao {
  $--drawer-default-wh: 580rpx;
  position: fixed;
  top: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10;
  img {
    uc-perf-stat-ignore: image;
  }
  .pop-mask {
    width: 100%;
    height: 100%;
  }
  .pop-content-wrap {
    position: absolute;
    width: 100vw;
    left: 0;
    height: $--drawer-default-wh;
    background: #FFFFFF;
    border-radius: 48rpx 48rpx 0 0;
    bottom: 0;
    //animation: bottom-appear .3s ease-in-out;
    .close-icon {
      position: absolute;
      top: 30rpx;
      right: 30rpx;
      width: 32rpx;
      height: 32rpx;
    }
  }
  .pop-appear {
    animation: bottom-appear .3s ease-in-out;
  }
  .pop-disappear {
    animation: bottom-disappear .3s ease-in-out;
  }
  .pop-content {
    padding: 64rpx;
    .title {
      font-size: 40rpx;
      color: #12161A;
      letter-spacing: 0;
      text-align: center;
      font-weight: 700;
      margin-bottom: 64rpx;
    }
    .bind-icons {
      width: 400rpx;
      justify-content: space-between;
      margin-bottom: 80rpx;
      .brand-icon {
        width: 112rpx;
        height: 112rpx;
      }
      .bind-icon {
        width: 128rpx;
        height: 128rpx;
      }
    }
    .bind-btn {
      width: 622rpx;
      height: 88rpx;
      background: #12161A;
      border-radius: 20rpx;
      position: relative;
      font-size: 34rpx;
      color: #FFFFFF;
      letter-spacing: 0;
      font-weight: 500;
      .btn-tip {
        display: flex;
        align-items: center;
        position: absolute;
        top: -56rpx;
        right: 90rpx;
        width: 196rpx;
        height: 56rpx;
        padding-left: 15rpx;
        box-sizing: border-box;
        background-image: linear-gradient(90deg, #FFF2D8 0%, #FEE0AF 76%);
        border-radius: 16rpx;
        font-size: 24rpx;
        color: #7D3B3B;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        .feiliao-icon {
          position: absolute;
          width: 60rpx;
          height: 62rpx;
          right: -20rpx;
        }
        .bubble-angle {
          width: 16rpx;
          height: 16rpx;
          position: absolute;
          bottom: -16rpx;
          left: 30rpx;
        }
      }
    }
  }

  @keyframes bottom-appear {
    from { bottom: -580rpx; }
    to { bottom: 0; }
  }
  @keyframes bottom-disappear {
    from { bottom: 0; }
    to { bottom: -580rpx; }
  }
}
