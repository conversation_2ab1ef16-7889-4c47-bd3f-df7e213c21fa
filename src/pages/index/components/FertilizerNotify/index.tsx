import React, { useEffect, useState, useRef } from 'react';
import Lottie from 'lottie-web';
import './index.scss';
import NumberScroll from "@/pages/index/components/FertilizerNotify/components/NumberScroll";
import useMxState from "@/hooks/useMxState";
import stat from "@/lib/stat";
import { fertilizerNotifyLottieImgs, fertilizerNotifyDoubleCardLottieImgs } from "@/pages/index/components/FertilizerNotify/resource";
import { preloadImg } from "@/lib/utils/preloadImg";
import tracker from "@/lib/tracker";

// 昨日肥料收益通知
const Index = () => {
  const [assetGatherInfo] = useMxState<number>('app.assetGatherInfo');
  const { yesterdayTotalAmount = 0, yesterdayDoubleAmount = 0, doubleNum = 0 } = assetGatherInfo || {};
  const totalAmount = yesterdayTotalAmount + yesterdayDoubleAmount;
  const [showNotify, setShowNotify] = useState<boolean>(false);
  const [showSkipBtn, setShowSkipBtn] = useState<boolean>(false);
  const [waterTimes, setWaterTimes] = useState<number>(0);
  const [showWaterTimes, setShowWaterTimes] = useState<boolean>(false);
  const [isExiting, setIsExiting] = useState(false);
  const [countDown, setCountDown] = useState<number>(3);
  const aniRef = useRef(null);
  const countDownRef = useRef(countDown);
  const intervalRef = useRef(null);
  const lottieStartTSRef = useRef(Date.now());
  const SHOW_ANI_MIN_AMOUNT = 10000; // 显示动画的肥料数量阈值
  useEffect(() => {
    if (totalAmount >= SHOW_ANI_MIN_AMOUNT) {
      const times = Math.floor(yesterdayTotalAmount / 600);
      preloadImg(yesterdayDoubleAmount ? fertilizerNotifyDoubleCardLottieImgs : fertilizerNotifyLottieImgs);
      setTimeout(() => {
        setShowNotify(true);
      }, 800)
      setWaterTimes(times);
      stat.exposure('reward_splash_show', {
        c: 'home',
        d: 'home',
        fertilizer_income: totalAmount,
        fertilization_frequency: times,
      })
    }
  }, [totalAmount]);
  
  useEffect(() => {
    if (!showNotify || aniRef.current) {
      return
    }
    const aniParams = yesterdayDoubleAmount ? 
      {
        animationData: require('@/lib/animation/fertilizerNotifyDoubleCard.json'),
        assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202507/dbfcaf/images/',
      } : 
      {
        animationData: require('@/lib/animation/fertilizerNotify.json'), 
        assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202505/536065/images/',
      }
    aniRef.current = Lottie.loadAnimation({
      name: 'notify',
      container: document.getElementById('notify-lottie') as HTMLElement,
      renderer: 'svg',
      loop: false,
      autoplay: true,
      ...aniParams,
    });
    // 帧数变化时会触发enterFrame
    let frameStart;
    const enterFrameHandler = () => {
      if (frameStart) {
        return;
      }
      // 播放第一帧
      setShowSkipBtn(true);
      lottieStartTSRef.current = Date.now();
      setTimeout(() => {
        setShowWaterTimes(true);
      }, 542);
      frameStart = true;
      aniRef.current.removeEventListener('enterFrame', enterFrameHandler);
    }
    aniRef.current.addEventListener('enterFrame', enterFrameHandler);
  }, [showNotify])
  
  useEffect(() => {
    if (showSkipBtn) {
      setTimeout(() => {
        intervalRef.current = setInterval(() => {
          setCountDown(prevCount => {
            const currentCount = prevCount - 1;
            countDownRef.current = currentCount;
            return currentCount;
          });
        }, 1000);
      }, totalAmount >= 100000 ? 1000 : 0)
    }
  }, [showSkipBtn]);
  
  useEffect(() => {
    if (countDownRef.current <= 0) {
      handleClose();
    }
  }, [countDownRef.current]);
  
  const handleClose = (skip = false) => {
    const playTime = Date.now() - lottieStartTSRef.current;
    setIsExiting(true);
    tracker.log({
      category: 153,
      msg: '肥料收益动画',
      w_succ: 1,
      wl_avgv1: `${playTime > 10000 ? 10000 : playTime}`,
      c1: totalAmount,
    })
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (skip) {
      stat.click('reward_splash_click', {
        c: 'home',
        d: 'home',
        fertilizer_income: totalAmount,
        fertilization_frequency: waterTimes,
        click_area: 'skip',
      })
    }
    setTimeout(() => {
      setShowNotify(false);
    }, 250)
  }

  return showNotify ? (
    <div className={`fertilizer-notify ${isExiting ? 'exiting' : ''}`}>
      {
        showSkipBtn ? (
          <div className="user-date">
            <div className="skip-btn" onClick={() => handleClose(true)}>跳过{countDown}</div>
            <div className="fertilizer-num-wrapper">
              <NumberScroll duration={500} value={totalAmount} delayPerDigit={50} />
            </div>
          </div>
        ) : null
      }
      {
        showWaterTimes ?
        <div className="water-times num-color" style={{paddingRight: waterTimes < 100 ? `${24 / 7.5}vw` : 0}}>
          {waterTimes}
        </div> : null
      }
      <div id="notify-lottie" />
    </div>) : null
};

export default Index;
