.fertilizer-notify {
  position: absolute;
  width: 100%;
  min-height: 100%;
  top: 0;
  left: 0;
  z-index: 99999;
  background: rgba(0, 0, 0, 0.70);
  opacity: 1;
  transition: opacity 0.25s ease-in-out;
  img, image{
    uc-perf-stat-ignore: image;
  }
  .user-date {
    position: relative;
    z-index: 1;
  }
  .fertilizer-num-wrapper {
    position: absolute;
    top: 458rpx;
    width: 100%;
  }
  .water-times {
    font-family: D-DIN-Bold;
    font-size: 138rpx;
    line-height: 138rpx;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    position: absolute;
    top: 1029rpx;
    right: 346rpx;
    transform: scale(2);
    opacity: 0;
    z-index: 9;
    /* 同时应用两个动画 */
    animation:
            scale-center 0.3s ease-in-out,
            fade 0.1s ease-in-out;
    animation-fill-mode: forwards; /* 保持动画结束时的状态 */
  }

  /* 定义中心缩放动画 */
  @keyframes scale-center {
    0% {
      transform: scale(2);
    }
    100% {
      transform: scale(1);
    }
  }
  /* 定义渐隐渐现动画 */
  @keyframes fade {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  .num-color {
    background: linear-gradient(to bottom, #FFFFFF 0%, #FFFFCC 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .skip-btn {
    position: absolute;
    top: 185rpx;
    right: 48rpx;
    width: 92rpx;
    height: 42rpx;
    line-height: 42rpx;
    border-radius: 21rpx;
    border: 1rpx solid #FFFFFF;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    color: #FFFFFF;
    letter-spacing: 0;
    font-weight: 400;
  }
  #notify-lottie {
    //position: absolute;
    width: 750rpx;
    max-width: 800rpx;
    height: 1624rpx;
    display: flex;
    justify-content: center;
    //left: 0;
    top: 0;
  }
}
.fertilizer-notify.exiting {
  opacity: 0;
}
