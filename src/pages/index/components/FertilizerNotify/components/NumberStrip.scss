.number-strip-container {
  display: inline-block;
  border-radius: 8px;
  margin: 0 2px;
}

.number-strip-window {
  height: 70px;
  overflow: hidden;
  position: relative;
}

.number-strip-window::before,
.number-strip-window::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 20px;
  z-index: 1;
  pointer-events: none;
}

.number-strip-window::before {
  top: 0;
}

.number-strip-window::after {
  bottom: 0;
}

.number-strip {
  display: flex;
  flex-direction: column;
  will-change: transform;
}

.number-item {
  font-family: D-DIN-Bold;
  height: 70px;
  width: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 76px;
  color: #fff;
  user-select: none;
}
