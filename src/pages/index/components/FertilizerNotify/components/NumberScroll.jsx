import React from 'react';
import NumberStrip from './NumberStrip';
import './NumberScroll.scss';

const NumberScroll = ({ value = 0, duration = 1000, delayPerDigit = 100, digitHeight = 70 }) => {
  // 将数字转换为字符串并填充前导零
  const digits = value.toString().padStart(1, '0').split('').map(Number);

  return (
    <div className="number-scroll">
      {digits.map((digit, index) => (
        <NumberStrip
          key={index}
          targetNumber={digit}
          duration={duration}
          delay={index * delayPerDigit}
          digitHeight={digitHeight}
        />
      ))}
    </div>
  );
};

export default NumberScroll;
