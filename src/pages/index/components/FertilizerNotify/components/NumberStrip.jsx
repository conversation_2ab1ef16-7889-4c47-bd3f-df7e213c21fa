import React, { useEffect, useRef } from 'react';
import './NumberStrip.scss';

const NumberStrip = ({ targetNumber, duration = 500, delay = 0, digitHeight = 70 }) => {
  const stripRef = useRef(null);
  const numbers = Array.from({ length: 20 }, (_, i) => i % 10); // 两组0-9

  useEffect(() => {
    const strip = stripRef.current;
    if (!strip) return;

    // 计算目标位置
    const basePosition = targetNumber === 0 ? 10 : targetNumber; // 如果是0，则滚动到第二组的0
    const targetPosition = -(basePosition * digitHeight);

    // 重置位置
    strip.style.transition = 'none';
    strip.style.transform = 'translateY(0)';
    strip.offsetHeight; // 强制重排

    // 添加过渡效果并滚动到目标位置
    strip.style.transition = `transform ${duration}ms cubic-bezier(0.23, 1, 0.32, 1) ${delay}ms`;
    strip.style.transform = `translateY(${targetPosition}px)`;
  }, [targetNumber, duration, delay]);

  return (
    <div className="number-strip-container">
      <div className="number-strip-window">
        <div className="number-strip" ref={stripRef}>
          {numbers.map((num, index) => (
            <div key={`${num}_${index}`} className="number-item num-color">
              {num}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default NumberStrip;
