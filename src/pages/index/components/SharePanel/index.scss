.share-panel-comp{
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 999;

  .panel-mask{
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.5;
    position: absolute;
    top: 0;
    left: 0;
  }

  .share-operation-content{
    width: 100%;
    height: auto;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    transform: translateY(0);
    background-image: url('./images/head_panel.jpg');
    background-repeat: no-repeat;
    background-size: 100% auto;
    border-radius: 40rpx 40rpx 0 0;
    padding-top: 140rpx;
    padding-bottom: 88rpx;

    .title-wrap{
      width: 100%;
      position: absolute;
      top: 0;

      .panel-uu{
        width: 233rpx;
        position: absolute;
        left: 14rpx;
        top: -96rpx;
      }

      .panel_close{
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        top: 30rpx;
        right: 30rpx;
        opacity: 0.5;
      }

      .panel-title{
        position: absolute;
        top: 44rpx;
        left: 218rpx;
        font-family: PingFangSC-Semibold;
        font-size: 36rpx;
        color: #005210;
        letter-spacing: 0;
        font-weight: 700;
      }
    }

    .operation-list{
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: start;
      flex-wrap: wrap;
      padding-left: 84rpx;
      .share-operation-box {
        display: flex;
        height: 140rpx;
        box-sizing: border-box;
        flex-direction: column;
        align-items: center;
        text-align: center;
        margin-right: 65rpx;
        margin-top: 20rpx;
        -webkit-tap-highlight-color: rgba(0,0,0,0);
  
        .share-operation-img {
          width: 97rpx;
          height: 97rpx;
        }
  
        .share-operation-text {
          font-family: PingFangSC-Medium;
          margin-top: 12rpx;
          color: #525E66;
          font-size: 20rpx;
          font-weight: 500;
          letter-spacing: 0;
          text-align: center;
        }
      }
    }

  }

  .panel-appear{
    animation: panelIn .3s ease-in forwards;
  }
}

@keyframes panelIn {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}