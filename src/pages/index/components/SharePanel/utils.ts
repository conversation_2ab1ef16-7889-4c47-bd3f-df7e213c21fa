import wechat_icon from './images/wechat_frends.png';
import wechat_friends from './images/<EMAIL>';
import qq_icon from './images/<EMAIL>';
import qqZone_icon from './images/<EMAIL>';
import weibo_icon from './images/<EMAIL>'; 
import dingding_icon from './images/<EMAIL>';
import copylink_icon from './images/copylink.png';
import { IPr } from '@/logic/store/models/app/typings';

export interface IOperationItem {
  key: string;
  target: 'WechatFriends' | 'QQ' | 'WechatTimeline' | 'Qzone' | 'SinaWeibo' | 'DingDing' | 'CopyLink';
  icon: string;
  title: string;
  /** 埋点用 */
  clkType?: string;
  /** 屏蔽客户端 */
  shield: IPr[];
}

export const operationData: IOperationItem[] = [
  {
    key: '1',
    target: 'WechatFriends',
    icon: wechat_icon,
    title: '微信好友',
    clkType: 'wechat',
    shield: []
  },
  {
    key: '2',
    target: 'WechatTimeline',
    icon: wechat_friends,
    title: '朋友圈',
    clkType: 'moments',
    shield: []
  },
  {
    key: '3',
    target: 'QQ',
    icon: qq_icon,
    title: 'QQ好友',
    clkType: 'qq',
    shield: []
  },
  {
    key: '4',
    target: 'Qzone',
    icon: qqZone_icon,
    title: 'QQ空间',
    clkType: 'qq_space',
    shield: []
  },
  {
    key: '5',
    target: 'SinaWeibo',
    icon: weibo_icon,
    title: '微博',
    clkType: 'weibo',
    shield: ['UCLite']
  },
  {
    key: '6',
    target: 'DingDing',
    icon: dingding_icon,
    title: '钉钉',
    clkType: 'dingtalk',
    shield: ['UCLite']
  },
  {
    key: '7',
    target: 'CopyLink',
    icon: copylink_icon,
    title: '复制链接',
    clkType: 'link',
    shield: []
  },
];

export const hasSharedStatus = ['WechatFriends', 'QQ'];

/**
 * 页面返回后触发
 * - maxDuration hide 后最多等待多久
 */

export const waitPageRevisible = (maxDuration = 1500) => {
  let hided = false;
  let timer;
  return new Promise((resolve) => {
    const triggerResolve = (reason: string) => {
      clearTimeout(timer);
      console.log('waitPageRevisible triggerResolve by', reason);
      // document.removeEventListener('visibilitychange', handleChange);
      resolve(document.visibilityState === 'visible');
    };

    function handleChange() {
      if (document.visibilityState === 'hidden') {
        hided = true;
        timer = setTimeout(() => {
          console.log('resolve-by timeout', maxDuration);
          triggerResolve('timeout');
        }, maxDuration);
      }
      if (document.visibilityState === 'visible') {
        if (hided) {
          triggerResolve('visible');
        }
      }
    }

    // document.addEventListener('visibilitychange', handleChange);
  });
};
