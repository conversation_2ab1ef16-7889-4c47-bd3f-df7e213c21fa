import React, { useState, useEffect } from 'react';
import './index.scss';
import mx from '@ali/pcom-mx';
import { operationData, IOperationItem } from './utils';
import { shareDomShot, copyToClipboard } from '@/lib/ucapi';
import { isIOS, isAndroid } from '@/lib/universal-ua';
import { ITaskState } from '@/logic/store/models/task/typing';
import useMxState from '@/hooks/useMxState';
import { addParams } from '@/lib/qs';
import { MainAPI } from '@/logic/type/event';
import tracker from '@/lib/tracker';
import stat from '@/lib/stat';
import { IAppState } from '@/logic/store/models/app/typings';
import { TASK_EVENT_TYPE } from '../TaskPop/TaskList/types';
import { getTaskAward } from '../TaskPop/TaskList/util';

import PanelClose from './images/panel_close.png';
import UuImg from './images/<EMAIL>';

export default function Index() {
  const [task] = useMxState<ITaskState>('task');
  const [app] = useMxState<IAppState>('app');
  const [showPanle, setShowPanel] = useState(false);
  const panelList = operationData?.filter((item) => !item?.shield?.includes(app?.pr || 'UCLite'));
  const helpTask = task?.taskList?.find((taskItem) => taskItem?.event === TASK_EVENT_TYPE.SHARE);

  useEffect(() => {
    mx.event.on(MainAPI.ShowSharePanel, openSharePanel)
    mx.event.on(MainAPI.HideSharePanel, closeSharePanel)
  }, []);

  useEffect(() => {
    if (showPanle) {
      stat.exposure('share_panel_exposure', {
        c: 'panel',
        d: 'share',
        task_id: helpTask?.id,
        task_name: helpTask?.name,
        taskclassify: helpTask?.taskClassify,
        groupcode: helpTask?.groupCode,
        award_amount: getTaskAward(helpTask)
      })
    }
  }, [showPanle]);

  const openSharePanel = () => {
    setShowPanel(true);
  };

  const closeSharePanel = () => {
    setShowPanel(false);
  };

  const onHandle = (actItem: IOperationItem) => {
    const frontData = app.mainInfo?.frontData;
    const shareObj = app?.pr === 'UCLite' ? frontData?.ucLiteShareConfig : frontData?.shareConfig;
    stat.click('share_panel_click', {
      c: 'panel',
      d: 'share',
      share_type: actItem?.clkType,
      task_id: helpTask?.id,
      task_name: helpTask?.name,
      taskclassify: helpTask?.taskClassify,
      groupcode: helpTask?.groupCode,
      award_amount: getTaskAward(helpTask),
    })
    const baseLink = actItem?.target === 'SinaWeibo' ? shareObj?.weiboLink || shareObj?.link : shareObj?.link; 
    const shareLink = addParams(baseLink || '', {
      inviteCode: task?.inviteCode,
      entry: app?.pr === 'UCLite' ? 'speed_farm_share' : 'farm_share',
    })

    const params = {
      target: actItem?.target,
      title: shareObj?.title,
      // 解决双端分享到微博的文案取值不同
      content: isAndroid && actItem?.target === 'SinaWeibo' ? shareObj?.title : shareObj?.content,
      sourceUrl: shareLink,
      imageUrl: shareObj?.iconUrl,
      source: isIOS ? '' : 'UC浏览器' // iOS不支持小尾巴，会拼接在content后面
    }
    tracker.log({
      category: 113, // 系统自动生成，请勿修改
      msg: `点击分享`, // 将根据msg字段聚合展示在平台的top上报内容中
      w_succ: 1, // 用于计算"成功率";可选值为0或1
      c1: actItem?.target,
      c2: JSON.stringify(task?.inviteCode),
      c3: `${app?.pr}`,
      bl1: JSON.stringify(params)
    });
    if (actItem?.target === 'CopyLink') {
      copyToClipboard({ text: params.sourceUrl || ''})
      return;
    }
    shareDomShot(params).then((code) => {
      tracker.log({
        category: 113, // 系统自动生成，请勿修改
        msg: `分享到-${actItem?.target}`, // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 1, // 用于计算"成功率";可选值为0或1
        c1: actItem?.target,
        c2: JSON.stringify(task?.inviteCode),
        c3: `${app?.pr}`,
        bl1: JSON.stringify(params),
        bl2: JSON.stringify(code),
      });
    })
  };

  const operationItem = (item: IOperationItem) => {
    return (
      <div className="share-operation-box" key={item.target} onClick={() => onHandle(item)}>
        <img className="share-operation-img" src={item.icon} alt="" />
        <div className="share-operation-text">{item.title}</div>
      </div>
    );
  };

  return (
    <div className="share-panel-comp" style={{ display: showPanle ? 'block' : 'none' }}>
      <div className="panel-mask" onClick={closeSharePanel} />
      <div className={`share-operation-content ${showPanle ? 'panel-appear' : ''}`}>
        <div className="title-wrap">
          <img src={UuImg} className="panel-uu" alt="" />
          <img src={PanelClose} className="panel_close" onClick={closeSharePanel} alt="" />
          <div className="panel-title">邀请好友助力，双方得肥料</div>
        </div>
        <div className="operation-list">
          {panelList.map((item) => {
            return operationItem(item);
          })}
        </div>
      </div>
    </div>
  )
}
