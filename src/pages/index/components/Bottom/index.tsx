import React, { Fragment, useEffect, useState, useRef } from 'react';
import './index.scss';
import mx from '@ali/pcom-mx';
import dispatch from '@/logic/store';
import { IAppState } from '@/logic/store/models/app/typings';
import useMxState from '@/hooks/useMxState';
import { ITaskState } from '@/logic/store/models/task/typing';
import { MainAPI } from '@/logic/type/event';
import ManureIcon from './images/manure-icon.png';
import ManureDoubleIcon from './images/manure-double-icon.png';
import bubbleImg from './images/bubble.png';
import harvestImg from './images/harvest.png';
import MoreIcon from './images/more-icon.png';
// import combineImg from './images/combine-icon.png';
import ToBeExchangedImg from './images/to-be-exchanged.png';
import WelfareIcon from './images/welfare-icoon.png';
import WaterBtn from './images/water-btn.png';
import Toast from '@/lib/universal-toast';
import Lottie from 'lottie-web';
import { isUc } from '@/lib/universal-ua';
import { TaobaoRouseApp } from '@/lib/utils/rouseApp';
import { StoreName } from '@/logic/type/store';
import config from '@/config';
import stat from '@/lib/stat';
import Fact from '@/components/Fact';
import { openPage, exit } from '@/lib/ucapi';
import { IUserState } from '@/logic/store/models/user/typings';
import wormholeData from '@/lib/wormhole-data';
import { getParam } from '@/lib/qs';
import { TasklistSource, getFarmHelpPlantFactParams, getFarmHelpPlantEntryStatusFact } from '@/pages/index/utils';
import BubblePop from './BubblePop';
import BackTag from './BackTag';
import { Welfare_Link } from '@/constants';
import BbzEntryIcon from './images/bbz-img.png';
import { IFarmHelpPlantState } from '@/logic/store/models/farmHelpPlant/type';
import { checkEnableDoubleCard, checkPlantActivityCurActPeriod } from '@/logic/store/models/app/new_year_time';
import { convertCentsToPoint } from '@/lib/utils/formatNumber';
import { useThrottle } from '@/hooks';
import VideoAdResource from '../VideoAdResource';
import { QueryCardInfoRes } from '@/api/doublePointsCard/typings';
import DoubleCardEntry from './DoubleCardEntry';
import { LocalStorageKey } from "@/lib/utils/localStorage_constant";

const link = wormholeData?.page?.['10010']?.link || {};

export default function Bottom() {
  const [task] = useMxState<ITaskState>('task');
  const [app] = useMxState<IAppState>('app');
  const [user] = useMxState<IUserState>('user');
  const [farmHelpPlant] = useMxState<IFarmHelpPlantState>('farmHelpPlant');
  const [cardInfo] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { doubleNum } = cardInfo?.todayCardInfo || {};
  const [isShowGuidance, setIsShowGuidance] = useState<boolean>(false); // 是否显示引导动画
  const [showStaticBtn, setShowStaticBtn] = useState(true);
  const [showDoubleCardAwardTip, setShowDoubleCardAwardTip] = useState<boolean>(false);
  const bubbleRef = useRef<any>();
  const frontData = app.mainInfo?.frontData;
  const isDoubling = cardInfo && doubleNum > 0; // 翻倍卡生效中
  const showIsDoubling = isDoubling && cardInfo && checkEnableDoubleCard();

  // 帮帮种当天是否有参与通过是否有助力值判断
  const helpValue = farmHelpPlant.helpPlantHome.score ?? 0;
  // 帮帮种累计挑战肥料待领
  const pointReceiveValue = farmHelpPlant.helpPlantHome?.unReceiveAward ?? 0;
  // 榜一奖励
  const rankMaxAward = farmHelpPlant.topOnePrizeInfo?.prizeValue;
  const isBangBangZhongPeriod = checkPlantActivityCurActPeriod();


  useEffect(() => {
    const onWater = () => {
      setIsShowGuidance(false);
    };
    const onCreateButtonGO = () => {
      setShowStaticBtn(false);
    };
    const onReceivedDoubleCardAward = () => {
      const currentCount = localStorage.getItem(LocalStorageKey.DOUBLE_CARD_AWARD_TIP) || 0;
      // 只提示三次
      if (Number(currentCount) >= 3) {
        return;
      }
      setShowDoubleCardAwardTip(true);
      const newCount = Number(currentCount) + 1;
      localStorage.setItem(LocalStorageKey.DOUBLE_CARD_AWARD_TIP, newCount);
    };
    mx.event.on(MainAPI.WaterGame, onWater);
    mx.event.on(MainAPI.CreateButtonGO, onCreateButtonGO);
    mx.event.on(MainAPI.RECEIVED_DOUBLE_CARD_AWARD, onReceivedDoubleCardAward);
    return () => {
      mx.event.off(MainAPI.WaterGame, onWater);
      mx.event.off(MainAPI.CreateButtonGO, onCreateButtonGO);
      mx.event.off(MainAPI.RECEIVED_DOUBLE_CARD_AWARD, onReceivedDoubleCardAward);
    };
  }, []);
  
  useEffect(() => {
    if (showDoubleCardAwardTip) {
      // 10s后主动消失
      setTimeout(() => {
        setShowDoubleCardAwardTip(false);
      }, 10000)
    }
  }, [showDoubleCardAwardTip])
  
  useEffect(() => {
    if (!app.isHomeDataLoading && !app.isHomeDataFail) {
      if (
        !app?.mainInfo?.gameInfo?.plantInfo?.exchangeCount &&
        app?.mainInfo?.gameInfo?.plantInfo?.seedStage?.totalValue === 0
      ) {
        setIsShowGuidance(true);
      }
    }
  }, [app]);

  useEffect(() => {
    if (!isShowGuidance) {
      return;
    }
    Lottie.loadAnimation({
      name: 'hand',
      container: document.getElementById('guidance-hand') as HTMLElement,
      renderer: 'canvas',
      loop: true,
      autoplay: true,
      animationData: require('@/lib/animation/hand.json'),
      assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202405/6c4713/images/',
    });
  }, [isShowGuidance]);
  // 更多
  const handleMore = () => {
    stat.click('more_click', {
      c: 'function',
      d: 'more',
    });
    // openPage(link?.strategyUrl || config?.strategyUrl);
    if (bubbleRef?.current) {
      bubbleRef?.current?.handleShowBubble();
    }
    if (showDoubleCardAwardTip) {
      setShowDoubleCardAwardTip(false);
    }
  };
  // 收获
  const harvest = () => {
    stat.click('crop_click', {
      c: 'function',
      d: 'crop',
      exchange_status: app?.mainInfo?.gameInfo?.plantInfo?.exchangeCount ? 'redeened' : 'unredeemed',
    });
    TaobaoRouseApp(link?.harvestUrl || config?.harvestUrl, 'harvest');
  };
  // 合种木牌 这期不需要
  // const combineBoard = () => {
  //   console.log('合种木牌');
  // };

  // 跳转到福利猪
  const toGoWelfare = useThrottle(async () => {
    stat.click('pig_click', {
      c: 'function',
      d: 'pig',
      page_status: user?.bindTaobao ? 1 : 0,
    });
    if (!isUc) {
      Toast.show('请使用UC浏览器打开');
      return;
    }
    // const loginStatus = await dispatch.user.checkLoginAndBind(0, 4);
    // if (!loginStatus) return;
    // 从福利场来农场的直接回退
    const entry = getParam('entry');
    if (frontData?.paramsEntry?.includes(entry)) {
      exit();
    } else {
      openPage(frontData?.welfareLink || Welfare_Link);
    }
  }, 500);

  // 集肥料
  const handleManure = useThrottle(async () => {
    if (!isUc) {
      Toast.show('请使用UC浏览器打开');
      return;
    }
    if (!app.finishInitTask) {
      return;
    }
    // 标记要更新任务列表排序
    localStorage.setItem('isUpdateNewTask', '1');
    dispatch.task.taskInit();
    if (!task?.signTask?.length || !task?.taskList?.length) {
      return Toast.show('当前参与人数较多，请稍后再试');
    }
    mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: TasklistSource.collect_fertilizer });
  }, 500);
  // 兑换
  const tobeExchanged = () => {
    stat.click('exchange_click', {
      c: 'function',
      d: 'exchange',
    });
    TaobaoRouseApp(mx.store.get(StoreName.WaitExchangeUrl), 'harvest');
  };

  // 帮帮种
  const handleBbzEntry = useThrottle(() => {
    stat.click('bbz_bubble_click', {
      c: 'bbzhong',
      d: 'bubble',
      icon_status: getFarmHelpPlantEntryStatusFact().icon_status || '',
      ...getFarmHelpPlantFactParams(),
    });
    dispatch.farmHelpPlant.openActivityPage('bubble');
  }, 500);

  // 使用state来控制延迟组件的渲染
  const [shouldRender, setShouldRender] = useState(false);
  useEffect(() => {
    // 延迟1000ms, 低频率操作，可等果树渲染完后
    const timer = setTimeout(() => {
      setShouldRender(true);
    }, 1000);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  const renderBbzTipsText = () => {
    // 未参与
    if (!helpValue && rankMaxAward) {
      return <div className="bubble-text">领¥{Number(rankMaxAward) / 100}元红包</div>;
    }
    // 有肥料待领取
    if (pointReceiveValue) {
      return <div className="bubble-text long-bg">{convertCentsToPoint(pointReceiveValue, false)}万肥料待领</div>;
    }
    if (rankMaxAward) {
      return <div className="bubble-text">¥{Number(rankMaxAward) / 100}元天天领</div>;
    }
    return <div className="bubble-text">红包天天领</div>;
  };

  const renderBbzEntryTipStatus = () => {
    return (
      <Fact
        c="bbzhong"
        d="bubble"
        expoLogkey="bbz_bubble_exposure"
        expoExtra={{
          icon_status: getFarmHelpPlantEntryStatusFact().icon_status || '',
          ...getFarmHelpPlantFactParams(),
        }}
      >
        {renderBbzTipsText()}
      </Fact>
    );
  };
  return (
    <Fragment>
      {app?.mainInfo?.userInfo?.ucFarmHasAuth && (
        <div className="head-comp">
          <div className="more-icon-wrapper">
            <img onClick={handleMore} className="icon" src={MoreIcon} alt="" />
            {
              showDoubleCardAwardTip && <div className="double-award-tip">膨胀肥料已发</div>
            }
          </div>
          <div onClick={harvest} className="harvest-div">
            <img className="harvest-img" src={harvestImg} alt="" />
            {!!app?.mainInfo?.gameInfo?.plantInfo?.exchangeCount && <div className="bubble">待兑</div>}
          </div>
          <DoubleCardEntry />
        </div>
      )}
      {/* 2025-04-11: 因帮帮种需求、暂时先下掉 合种 入口 */}
      {/* {app?.mainInfo?.gameInfo?.plantInfo?.teamPlant && (
        <div className="center-comp">
          <div className="combine-img">
            <img src={combineImg} alt="" />
            <div className="combine-title">{app?.mainInfo?.gameInfo?.teamInfo?.teamType}</div>
            <div className="combine-content">{app?.mainInfo?.gameInfo?.teamInfo?.teamName}</div>
          </div>
        </div>
      )} */}
      {app?.mainInfo?.userInfo?.ucFarmHasAuth && <VideoAdResource />}

      {/* 只有绑定登录才能参加活动 */}
      {app?.mainInfo?.userInfo?.ucFarmHasAuth && isBangBangZhongPeriod && (
        <div className="bbz-entry-comp" onClick={handleBbzEntry}>
          {Object.keys(farmHelpPlant?.helpPlantHome ?? {}).length > 0 && renderBbzEntryTipStatus()}
          <img src={BbzEntryIcon} className="bbz-icon" alt="" />
        </div>
      )}

      <div className="bottom-comp">
        {app?.mainInfo?.gameInfo?.plantInfo?.extMap?.waitExchangeUrl && (
          <Fact c="function" d="exchange" expoLogkey="exchange_exposure">
            <img onClick={tobeExchanged} className="tobe-exchanged" src={ToBeExchangedImg} alt="" />
          </Fact>
        )}
        <div className="back-tag-container">
          <BackTag />
        </div>
        {/* 福利入口 */}
        <img src={WelfareIcon} className="icon together-icon" onClick={toGoWelfare} alt="福利猪" />

        {isShowGuidance && (
          <div className="guidance">
            <img className="bubble" src={bubbleImg} alt="" />
            <div id="guidance-hand" />
          </div>
        )}
        {/* 静态施肥按钮 占位 */}
        <div className="static-water-button" style={{ visibility: showStaticBtn ? 'visible' : 'hidden' }}>
          <img src={WaterBtn} />
        </div>
        <div onClick={handleManure} className="manure-container">
          <img src={showIsDoubling ? ManureDoubleIcon : ManureIcon} className="icon manure-icon" alt="集肥料" />
          {/* {drawTimes === 0 && } */}
          {showIsDoubling && (
            <div className="bubble">
              <span>肥料膨胀中</span>
            </div>
          )}
        </div>
      </div>
      {shouldRender && <BubblePop onRef={bubbleRef} />}
    </Fragment>
  );
}
