.double-card-entry {
  position: absolute;
  right: 24rpx;
  top: 345rpx;
  z-index: 1;
  .double-card-img {
    width: 90rpx;
    height: 90rpx;
    background-size: cover;
    uc-perf-stat-ignore: image;
  }
  .red-dot {
    position: absolute;
    width: 16rpx;
    height: 16rpx;
    top: 8rpx;
    right: 4rpx;
    border-radius: 50%;
    background: #ff4c4c;
    border: 3rpx solid #ffffff;
  }
  .show {
    animation: double-bubble-animation 3.5s ease forwards;
  }
  .bubble {
    position: absolute;
    top: calc(100% + 7rpx);
    left: 50%;
    opacity: 0;
    background-color: white;
    background-size: contain;
    background-repeat: no-repeat;
    width: fit-content;
    max-width: 135rpx;
    padding: 0 8rpx;
    height: 34rpx;
    border-radius: 30rpx;
    transform: translateX(-50%);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 0 7rpx;
    font-family: PingFangSC-Medium;
    font-size: 20rpx;
    color: #8a4519;
    letter-spacing: 0;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    // 添加气泡箭头
    &::before {
      content: '';
      position: absolute;
      top: -3.8rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 5rpx solid transparent;
      border-right: 5rpx solid transparent;
      border-bottom: 5rpx solid white;
    }
    span {
      font-family: PingFangSC-Semibold;
      font-size: 20rpx;
      color: #fa6425;
      text-align: center;
      font-weight: 600;
    }
  }
}
@keyframes double-bubble-animation {
  0% {
    transform: translateY(40%) translateX(-50%) scale(0.5);
    opacity: 0;
  }

  5% {
    transform: translateY(0) translateX(-50%) scale(1);
    opacity: 1;
  }
  99% {
    transform: translateY(0) translateX(-50%) scale(1);
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
