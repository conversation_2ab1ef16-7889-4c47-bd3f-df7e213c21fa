import React, { Fragment, useEffect, useRef, useMemo } from 'react';
import './index.scss';
import dispatch from '@/logic/store';
import useMxState from '@/hooks/useMxState';
import doubleCardImg from '../images/double-icon.png';
import modals from '@/components/modals';
import { QueryCardInfoRes } from '@/api/doublePointsCard/typings';
import stat from '@/lib/stat';
import Fact from '@/components/Fact';
import { checkEnableDoubleCard } from '@/logic/store/models/app/new_year_time';
import { mx } from '@ali/pcom-iz-use';
import { MainAPI } from '@/logic/type/event';
import { EDoubleFactFromParam } from '@/components/modals/modal_double_card';

const enum EBubbleStatus {
  DOUBLING = 'DOUBLING', // 生效中
}
export default function DoubleCardEntry() {
  const [cardInfo] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { doubleNum, canReceiveDoubleAmount = 0 } = cardInfo?.todayCardInfo || {};
  const doubleBubbleRef = useRef<HTMLDivElement | null>(null);
  const doubleCardEnable = cardInfo && checkEnableDoubleCard();
  const isDoubling = cardInfo && doubleNum > 0; // 翻倍卡生效中
  const { drawChance, totalDrawTimes } = cardInfo?.drawInfo || {};
  const showing = useRef(false);
  const doubleBubbleStatus = useMemo(() => {
    // 当日未抽卡（前一日有奖励）
    if (!cardInfo) return null;
    if (isDoubling) return EBubbleStatus.DOUBLING;
    return null;
  }, [cardInfo]);

  useEffect(() => {
    mx.event.on(MainAPI.CLOSE_DOUBLE_CARD_POP, handleShowBubble);
    return () => {
      mx.event.off(MainAPI.CLOSE_DOUBLE_CARD_POP, handleShowBubble);
    };
  }, []);

  const handleShowBubble = () => {
    if (showing.current) return;
    const doubleBubble = doubleBubbleRef.current;
    if (doubleBubble) {
      doubleBubble.classList.add('show');
      showing.current = true;
      setTimeout(() => {
        doubleBubble.classList.remove('show');
        showing.current = false;
      }, 3500);
    }
  };

  const handleEntryLoad = () => {
    setTimeout(() => {
      if (!renderDoubleBubbleText()) return;
      handleShowBubble();
    }, 800);
  };
  const getIconStatus = () => {
    // 未参与
    if (totalDrawTimes === 0) {
      return 1;
    }
    // 翻倍生效中
    if (isDoubling) {
      return 3;
    }
    // 未抽中
    return 2;
  };
  const handleClickDoubleCard = () => {
    stat.click('fdcard_bubble_click', {
      c: 'fdcard',
      d: 'bubble',
      status: getIconStatus(),
    });
    dispatch.doublePointsCard.queryCardInfo();
    if (!isDoubling) {
      dispatch.farmHelpPlant.queryHelpPlantHome();
    }
    modals.openDoubleCardModal({
      from: EDoubleFactFromParam.BUBBLE,
    });
  };

  function formatWanNum(number: number): string {
    if (number >= 1e4) {
      const wanValue = Math.floor(number / 1000) / 10;
      let strValue = wanValue.toString();
      if (strValue.includes('.')) {
        strValue = strValue.replace(/\.?0+$/, '');
        if (strValue.endsWith('.')) {
          strValue = strValue.slice(0, -1);
        }
      }
      return `${strValue}万`;
    }
    return number.toString();
  }
  const renderDoubleBubbleText = () => {
    switch (doubleBubbleStatus) {
      case EBubbleStatus.DOUBLING:
        return (
          !!canReceiveDoubleAmount && (
            <Fragment>
              明日领<span>{formatWanNum(canReceiveDoubleAmount)}</span>
            </Fragment>
          )
        );
      default:
        return null;
    }
  };
  return (
    <Fragment>
      {doubleCardEnable && (
        <Fact
          c="fdcard"
          d="bubble"
          noUseClick
          expoLogkey="fdcard_bubble_exposure"
          expoExtra={{
            status: getIconStatus(),
          }}
          onClick={handleClickDoubleCard}
          className="double-card-entry"
        >
          <img onLoad={handleEntryLoad} className="double-card-img" alt="" src={doubleCardImg} />
          {!!drawChance && !isDoubling && <div className="red-dot" />}
          {renderDoubleBubbleText() && (
            <div ref={doubleBubbleRef} className="bubble">
              {renderDoubleBubbleText()}
            </div>
          )}
        </Fact>
      )}
    </Fragment>
  );
}
