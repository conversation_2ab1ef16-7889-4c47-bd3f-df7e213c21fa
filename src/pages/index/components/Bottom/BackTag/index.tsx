import React, { useEffect, useState, useRef } from 'react';
import './index.scss';
import stat from '@/lib/stat';
import Fact from '@/components/Fact';
import config from '@/config';
import mx from '@ali/pcom-mx';
import { getTaskAward, checkTaskFinished } from '@/pages/index/components/TaskPop/TaskList/util';
import { TASK_EVENT_TYPE, TASK_STATUS, TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { getParam } from '@/lib/qs';
import {handleCallApp} from "@/pages/index/components/TaskPop/TaskList/help";
import {geneTaskRequestId} from "@/logic/store/models/utils";
import dispatch from '@/logic/store';

interface taskContactEntryFace {
  tagName: string;
  showTime: string;
  taskId: string;
  entry: string;
}

const arrowIcon = 'https://img.alicdn.com/imgextra/i1/O1CN01hXdMsb1Q69qp5L1Br_!!6000000001926-55-tps-30-30.svg';

const BackTag = () => {
  const [flagTag, setFlagTag] = useState<boolean>(false);
  const [task, setTask] = useState<TaskInfo>({});
  const [taskContactEntry, setTaskContactEntry] = useState<taskContactEntryFace>({
    tagName: '',
    showTime: '',
    taskId: '',
    entry: '',
  });
  const entry = getParam('entry') || 'unknown';
  const resource = mx.store.get('resource');
  const preExpoTaskId = useRef(0);

  useEffect(() => {
    let taskList = resource?.multiResource?.[config.backTagResourceCode]?.taskList || [];
    let attributes = resource?.multiResource?.[config.backTagResourceCode]?.attributes || {};
    const resultEntryObj = attributes?.taskContactEntry?.find((item) => item?.entry === entry);
    
    if (resultEntryObj) {
      const resultTask = taskList?.find((item) => {
        return item?.event === TASK_EVENT_TYPE?.CLL_APP_NO_AWARD && String(item?.id) === resultEntryObj?.taskId;
      });
      if (resultTask && resultTask?.state === TASK_STATUS?.TASK_DOING) {
        setTask(resultTask);
        setTaskContactEntry(resultEntryObj);
        setFlagTag(true);
        if (resultEntryObj?.showTime) {
          const timer = setTimeout(() => {
            setFlagTag(false);
            clearTimeout(timer);
          }, resultEntryObj?.showTime);
        }
        return;
      }
    }
    setFlagTag(false);
  }, [resource]);

  useEffect(()=>{
    if (task?.id && task?.id !== preExpoTaskId.current) {
      preExpoTaskId.current = task?.id;
      dispatch.highValueTask.resourceExposure(task, 'EXPOSURE', config.backTagResourceCode);
    }
  }, [task]);

  const toBack = async () => {
    stat.click('resource_click', {
      c: 'label',
      d: 'label',
      resource_location: 'retain_label',
    });
    stat.click('task_click', {
      c: 'label',
      d: 'label',
      task_id: task?.id,
      task_name: task?.name,
      taskclassify: task?.taskClassify,
      groupcode: task?.groupCode,
      award_amount: getTaskAward(task),
      task_count: task?.dayTimes?.progress,
      isfinish: checkTaskFinished(task) ? 1 : 0,
      resource_location: 'retain_label',
    });
    handleCallApp(task, 'task-back-tag', geneTaskRequestId())
    setFlagTag(false);
  };

  return flagTag ? (
    <Fact
      c="label"
      d="label"
      expoLogkey="resource_exposure"
      expoExtra={{
        resource_location: 'retain_label',
      }}
      noUseClick
    >
      <Fact
        c="label"
        d="label"
        expoLogkey="task_exposure"
        expoExtra={{
          task_id: task?.id,
          task_name: task?.name,
          taskclassify: task?.taskClassify,
          groupcode: task?.groupCode,
          award_amount: getTaskAward(task),
          task_count: task?.dayTimes?.progress,
          isfinish: checkTaskFinished(task) ? 1 : 0,
          resource_location: 'retain_label',
        }}
        noUseClick
      >
        <div onClick={toBack} className="back-tag">
          <img className="back-tag-icon" src={arrowIcon} alt="" />
          {taskContactEntry?.tagName}
        </div>
      </Fact>
    </Fact>
  ) : null;
};

export default BackTag;
