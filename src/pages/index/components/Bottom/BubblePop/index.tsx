import React, { useState, useImperativeHandle, useEffect } from 'react';
import './index.scss';
import { openPage } from '@/lib/ucapi';
import wormholeData from "@/lib/wormhole-data";
import config from '@/config';
import stat from '@/lib/stat';
import { IAppState } from '@/logic/store/models/app/typings';
import useMxState from '@/hooks/useMxState';

import StrategyIcon from './images/strategy-icon.png';
import ManureDetailIcon from './images/manure-detail.png';
import ArrowIcon from './images/right-arrow.png';
import RedPacketIcon from './images/red-packet.png'

const link = wormholeData?.page?.['10010']?.link || {};

const bubble_content_list = [
  { key: 1, name: '攻略', icon: StrategyIcon, clickType: 'strategy'},
  { key: 2, name: '肥料明细', icon: ManureDetailIcon, clickType: 'detail' },
  { key: 3, name: '红包明细', icon: RedPacketIcon, clickType: 'redPacket'},
]

const IClickType = ['strategy', 'detail', 'redPacket'] as const;
export type IMenuClickType= (typeof IClickType)[number];

type IMenu = typeof bubble_content_list extends Array<infer U> ? U : never;
export type IMenuItem = Omit<IMenu, 'clickType'> & {
  clickType: IMenuClickType;
}

interface IProps{
  onRef: any;
  /** 点击item事件 */
  handleMenuItem?: (record: IMenuItem) => void;
  /** content类名 */
  contentClassName?: string;
  /** 需要隐藏的item-clickType */
  hiddenItems?: IMenuClickType[];
  /** 出现时触发的事件 */
  onVisible?: () => void;
}

const BubblePop = React.memo((props: IProps) => {
  const { contentClassName = '', hiddenItems = [] } = props;
  const [app] = useMxState<IAppState>('app');
  const [showBubble, setShowBubble] = useState(false);
  const frontData = app.mainInfo?.frontData; 

  const showMenuList = bubble_content_list.filter((item: IMenuItem) => !hiddenItems.includes(item.clickType));

  useEffect(() => {
    if (showBubble && props?.onVisible) {
      props.onVisible();
    }
  }, [showBubble])

  useImperativeHandle(props.onRef, () => ({
    handleShowBubble
  }))

  const handleShowBubble = () => {
    setShowBubble(true);
  }

  const handleCloseBubble = () => {
    setShowBubble(false);
  }

  const goToPage = (item) => {
    if (props?.handleMenuItem) {
      props.handleMenuItem(item);
    } else {
      stat.click('extra_click', {
        c: 'panel',
        d: 'extra',
        button: item?.clickType
      });
      const linkObj = {
        1: link?.strategyUrl || config?.strategyUrl,
        2: frontData?.detailLink,
        3: frontData?.redPacketLink,
      }
      openPage(linkObj[item?.key] || '');
    }
    handleCloseBubble();
  }

  return (
    <div className="bubble-pop-comp" style={{ display: showBubble ? 'block' : 'none' }}>
      <div className="bubble-mask" onClick={handleCloseBubble} />
      <div className={`bubble-content ${contentClassName}`}>
        {showMenuList?.map((item, index) => (
          <div className="bubble-list-item" key={item.key} onClick={() => goToPage(item)} >
            <img src={item.icon} className="item-icon" alt={item.name} />
            <div className={`item-name ${index === showMenuList?.length - 1 ? 'item-name-noline' : ''}`}>{item.name}</div>
            <img src={ArrowIcon} className="arrow-icon" alt="" />
          </div>
        ))}
        <div className="triangle" />
      </div>
    </div>
  )
})

export default BubblePop
