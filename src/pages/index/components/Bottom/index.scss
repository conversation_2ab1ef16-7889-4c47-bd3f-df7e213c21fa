.head-comp {
  .more-icon-wrapper {
    position: absolute;
    top: 95rpx;
    right: 24rpx;
    z-index: 1;
    width: 90rpx;
    height: 90rpx;
    background-size: cover;
    .icon {
      width: 100%;
      height: 100%;
    }
    .double-award-tip {
      position: absolute;
      top: 90rpx;
      right: -16rpx;
      width: 144rpx;
      height: auto;
      background-image: url('./images/award-tip.png');
      uc-perf-stat-ignore: image;
      background-size: contain;
      background-repeat: no-repeat;
      font-family: PingFangSC-Medium;
      font-size: 20rpx;
      color: #8A4519;
      letter-spacing: 0;
      text-align: center;
      line-height: 46rpx;
      font-weight: 500;
    }
  }
  .harvest-div {
    position: absolute;
    right: 24rpx;
    top: 220rpx;
    z-index: 1;
    .harvest-img {
      width: 90rpx;
      height: 90rpx;
      background-size: cover;
    }
    .bubble {
      box-sizing: border-box;
      position: absolute;
      top: -18rpx;
      left: 50%;
      width: 60rpx;
      height: 34rpx;
      background-image: linear-gradient(180deg, #ffb5b4 1%, #f7534f 99%);
      border: 2rpx solid #ffffff;
      border-radius: 30rpx 30rpx 30rpx 6rpx;
      font-family: PingFangSC-Medium;
      font-size: 20rpx;
      color: #ffffff;
      text-align: center;
      font-weight: 500;
      line-height: 34rpx;
    }
  }
}
.center-comp {
  .combine-img {
    position: absolute;
    top: 840rpx;
    left: 54rpx;
    img {
      width: 206rpx;
      height: 140rpx;
      background-size: cover;
    }
    .combine-title {
      width: 80rpx;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      position: absolute;
      top: 24rpx;
      left: 50%;
      transform: translateX(-50%);
      font-family: FZLANTY_CUK--GBK1-0;
      font-size: 20rpx;
      color: #955730;
      text-shadow: 0.5rpx 0.5rpx 0 rgba(255, 255, 255, 0.73);
      font-weight: 500;
      text-align: center;
    }
    .combine-content {
      width: 160rpx;
      position: absolute;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      left: 50%;
      transform: translateX(-50%);
      top: 52rpx;
      font-family: PingFangSC-Medium;
      font-size: 20rpx;
      color: #955730;
      text-align: center;
      line-height: 24rpx;
      text-shadow: 0 0.5rpx 0 rgba(255, 255, 255, 0.64);
    }
  }
}
.bottom-comp {
  width: 100%;
  position: absolute;
  //top: 1436rpx;
  .tobe-exchanged {
    position: absolute;
    top: -150rpx;
    width: 120rpx;
    height: 120rpx;
    background-size: cover;
    right: 42rpx;
    z-index: 1;
  }
  .icon {
    width: 110rpx;
    height: auto;
    z-index: 1;
  }
  .guidance {
    position: absolute;
    left: 258rpx;
    z-index: 1;
    width: 234rpx;
    height: 127rpx;
    background-color: transparent;
    pointer-events: none;
    .bubble {
      position: absolute;
      top: -140rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 550rpx;
      height: 130rpx;
      background-size: cover;
      animation: bubble-animation 1s ease;
    }
    #guidance-hand {
      position: absolute;
      top: 12rpx;
      left: 138rpx;
      width: 176rpx;
      height: 176rpx;
    }
  }
  .back-tag-container {
    position: absolute;
    left: 0;
    top: -98rpx;
    z-index: 9999;
  }
  .together-icon {
    position: absolute;
    left: 46rpx;
    uc-perf-stat-ignore: image;
  }
  .manure-container {
    position: absolute;
    z-index: 1;
    top: 0;
    right: 46rpx;
    .bubble {
      background-image: url('./images/double-bubble-bg.png');
      uc-perf-stat-ignore: image;
      background-size: contain;
      background-repeat: no-repeat;
      position: absolute;
      top: -37rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 140rpx;
      height: 44rpx;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC-Semibold;
      font-size: 20rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      span {
        margin-bottom: 6.5rpx;
      }
    }
  }
  .manure-icon {

  }
}
.static-water-button {
  position: absolute;
  top: -36rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 234rpx;
  visibility: visible;
  transition: visibility 1s;
  pointer-events: none;
  img {
    width: 100%;
  }
}

.bbz-entry-comp {
  z-index: 2;
  width: 206rpx;
  height: 206rpx;
  position: absolute;
  left: 18rpx;
  top: 788rpx;
  img {
    width: 100%;
    uc-perf-stat-ignore: image;
  }

  .bubble-text {
    width: 140rpx;
    height: 44rpx;
    line-height: 38rpx;
    background-image: url('./images/bubble-bg-short.png');
    uc-perf-stat-ignore: image;
    background-repeat: no-repeat;
    background-size: cover;
    box-sizing: border-box;
    font-family: PingFangSC-Semibold;
    font-size: 20rpx;
    color: #ffffff;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    uc-perf-stat-ignore: image;
  }

  .long-bg {
    width: 154rpx;
    background-image: url('./images/bubble-bg.png');
    uc-perf-stat-ignore: image;
  }
}

@keyframes bubble-animation {
  0% {
    transform: translateY(40%) translateX(-50%) scale(0.5);
    opacity: 0;
  }
  100% {
    transform: translateY(0) translateX(-50%) scale(1);
    opacity: 1;
  }
}

