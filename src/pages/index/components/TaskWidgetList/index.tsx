import React, {useEffect, useMemo, useState} from "react";
import useMxState from "@/hooks/useMxState";
import {ITaskState} from "@/logic/store/models/task/typing";
import config from '@/config';
import TaskWidget from './TaskWidget';
import mx from '@ali/pcom-mx';
import {TreeEventApi} from "@/logic/type/event";
import { getHighValueTaskList, ifShowTask } from "@/logic/store/models/task/helper";
import { TaskInfo } from "@/pages/index/components/TaskPop/TaskList/types";

const TaskWidgetList = () => {
  const [visible, setVisible] = useState(false);
  const [ucFarmHasAuth] = useMxState('app.mainInfo.userInfo.ucFarmHasAuth')
  const [multiResource] = useMxState<ITaskState>('resource.multiResource');
  const [recommendTaskList] = useMxState<TaskInfo[]>('task.recommendTaskList');
  const [preLoadAdTaskList] = useMxState<TaskInfo[]>('task.adTaskPreloadSuccessList');
  // const [showTaskList, setShowTaskList] = useState([]);
  const resourceData = multiResource?.[config.taskWidgetResourceCode];
  const resourceTaskList = resourceData?.taskList || [];
  const widgetTaskList = resourceData?.attributes?.widgetTaskList || [];
  const showTaskList = useMemo(() => {
    const highValueTaskList = getHighValueTaskList();
    let finalShowTaskList = resourceTaskList?.filter((curTask) => ifShowTask(curTask));
    finalShowTaskList = (finalShowTaskList ?? []).filter(curTask => {
      // 1、非高价值任务 2、通过任务列表下发 3、通过安装检测
      const isRecommend = recommendTaskList?.some((recommend: TaskInfo) => recommend.id === curTask.id)
      return !highValueTaskList.includes(String(curTask.id)) && isRecommend;
    });
    return finalShowTaskList;
  }, [recommendTaskList, resourceTaskList, preLoadAdTaskList]);

  useEffect(() => {
    mx.event.on(TreeEventApi.TREE_INIT_COMPLETE, (data) => {
      setVisible(true);
    });
  }, [ucFarmHasAuth])
  return (
    ucFarmHasAuth ? <TaskWidget visible={visible} widgetTaskList={widgetTaskList} taskList={showTaskList} /> : null
  )
}

export default TaskWidgetList
