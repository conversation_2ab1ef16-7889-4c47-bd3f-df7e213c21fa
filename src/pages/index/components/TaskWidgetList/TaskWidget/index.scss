.task-widget-comp {
  .extend-resource {
    position: absolute;
    text-align: center;
    z-index: 9;
    height: 160rpx;
    opacity: 0;
    animation: bubbleIn 0.5s forwards ease-out, bubbleFloat 2s infinite ease-in-out;
    animation-delay: calc(var(--delay) * 0.1s), calc(var(--delay) * 0.1s + 0.5s);
    animation-fill-mode: forwards;

    &.extend-resource-1 {
      top: 61.1vw;
      left: 402rpx;
    }

    &.extend-resource-2 {
      top: 61.1vw;
      left: 250rpx;
    }

    &.extend-resource-3 {
      top: 70.5vw;
      left: 554rpx;
    }

    &.fade-out {
      opacity: 0;
      animation: bubbleOut 0.3s forwards ease-in-out;
      animation-delay: 0s;
    }

    &.fade-in {
      opacity: 1;
      animation: bubbleIn 0.5s forwards ease-out;
      animation-delay: 2.5s;
    }

    &.no-float {
      animation: bubbleIn 0.5s forwards ease-out;
      animation-fill-mode: forwards;
    }

    .img-icon {
      width: 90rpx;
      height: 90rpx;
      uc-perf-stat-ignore: image;
    }
    .widget-amount {
      font-size: 24rpx;
      font-family: D-DIN-Bold;
      color: #7F370A;
      text-align: center;
      position: absolute;
      top: 45rpx;
      left: 50%;
      transform: translateX(-50%);
    }
    .red-bag-number{
      color: #fff;
    }
    .widget-name {
      margin-top: 5rpx;
      font-family: PingFangSC-Semibold;
      font-size: 22rpx;
      color: rgba(48,131,201,0.75);
      text-align: center;
      letter-spacing:0;
      font-weight: 600;
      &.nightColor{
        color: rgba(196,229,251,0.80);
     }
    }

    .award-count {
      font-family: PingFangSC-Semibold;
      font-size: 20rpx;
      opacity: 0.75;
      color: rgba(48,131,201,0.50);
      font-weight: 600;
      width: 100%;
      text-align: center;
      &.nightColor{
        color: rgba(196,229,251,0.65);
     }
    }

  }
}

@keyframes bubbleIn {
  0% {
    opacity: 0;
    transform: translateY(40rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(30rpx);
  }
}

@keyframes bubbleFloat {
  0% {
    transform: translateY(30rpx);
  }
  50% {
    transform: translateY(38rpx);
  }
  100% {
    transform: translateY(30rpx);
  }
}

@keyframes bubbleOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@media (max-width: 375px) {
  .task-widget-comp {
  .extend-resource {
    &.extend-resource-1 {
      top:54.1vw;
    }

    &.extend-resource-2 {
     top:54.1vw;
    }

    &.extend-resource-3 {
      top:63.5vw
    }
  }
}
}
