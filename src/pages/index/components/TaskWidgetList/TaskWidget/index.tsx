import React, { useEffect, useState, useRef, useCallback } from 'react';
import './index.scss';
import { checkTaskFinished, logToFinishTask, getTaskAward, getTaskCurDayTimeProcess, getTaskCurDayTimeTarget } from '../../TaskPop/TaskList/util';
import { dongfengTaskReport, taskActionHandler } from '../../TaskPop/TaskList/help';
import { TASK_STATUS, TASK_EVENT_TYPE, TASK_LOCATION } from '../../TaskPop/TaskList/types';
import { TaskInfo, WidgetTaskList } from "@/pages/index/components/TaskPop/TaskList/types";
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import dispatch from "@/logic/store";
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { mx } from '@ali/pcom-iz-use';
import { execWithLock } from '@/lib/utils/lock';
import BubbleIcon from '../assets/bubble.png';
import RedBagIcon from '../assets/red_bag.png';
import { getExtraInfo } from "@/logic/store/models/task/helper";
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import config from '@/config';


interface ITaskWidgetInfo extends TaskInfo {
  fadingIn: boolean;
  fadingOut: boolean;
  noFloat: any;
}
interface IProps {
  taskList: ITaskWidgetInfo[];
  widgetTaskList: WidgetTaskList[];
  visible: boolean;
}

export enum ICON_TYPE {
  CASH = 'cash',
  POINT = 'point',
}

export default function TaskWidget(props: IProps) {
  const { widgetTaskList, visible } = props;    
  const highValueTask = mx.store.get('highValueTask');
  const [task1, setTask1] = useState<ITaskWidgetInfo | null>(null);
  const [task2, setTask2] = useState<ITaskWidgetInfo | null>(null);
  const [task3, setTask3] = useState<ITaskWidgetInfo | null>(null);
  const [isNight, setIsNight] = useState(false);

  const initialSetup = useRef(true);
  const preExpoTaskId1 = useRef(0);
  const preExpoTaskId2 = useRef(0);
  const preExpoTaskId3 = useRef(0);
  const updateTaskIds = useRef<number[]>([]);

  const completeTask = (completedTask) => {
    const tasks = [task1, task2, task3];
    const setters = [setTask1, setTask2, setTask3];
    const index = tasks.findIndex(task => task?.id === completedTask.id);

    if (index === -1) return;

    // 更新当前任务为淡出状态
    const updatedTasks = [...tasks];
    // @ts-ignore
    updatedTasks[index] = { ...updatedTasks[index], fadingOut: true };
    setters[index](updatedTasks[index]);

    // 等待退场动画完成
    setTimeout(() => {
      // 移除已完成的任务
      updatedTasks[index] = null;
      setters[index](updatedTasks[index]);

      // 2秒后插入新任务
      setTimeout(() => {
        // 找到下一个可替换的任务，确保新任务不重复
        const nextTask = props.taskList.find(task => {
          return !tasks.some(t => t?.id === task.id) && task.state === TASK_STATUS.TASK_DOING && !updateTaskIds.current?.includes(task.id)
        });
        
        if (nextTask) {
          // 插入新任务到空位
          updatedTasks[index] = { ...nextTask, fadingIn: false };
          setters[index](updatedTasks[index]);
          // 存一下上次更新的任务, 避免有两任务任务同时更新时出现两个相同的任务
          if (!updateTaskIds.current?.includes(nextTask.id)) {
            updateTaskIds.current.push(nextTask?.id);
          }
        }
        // 2秒后置空,更新的任务没做, 后面还有可能会出现
        setTimeout(() => {
          updateTaskIds.current = [];
        }, 2000)
      }, 2000); // 2秒延迟后淡入新任务
    }, 500); // 等待 500 毫秒退场动画完成
  };

  useEffect(() => {
    const currentDayType = new Date().getHours() < 6 || (new Date()).getHours() >= 18
    setIsNight(currentDayType)
    const doingTasks = props.taskList.filter(task => task.state === TASK_STATUS.TASK_DOING).slice(0, 3);
    if (initialSetup.current) {
      if (doingTasks.length !== 0) {
        setTask1(doingTasks[0] || null);
        setTask2(doingTasks[1] || null);
        setTask3(doingTasks[2] || null);
        initialSetup.current = false;
      }
    } else {
      // 更新任务状态并处理任务完成后的任务列表变化
      const tasks = [task1, task2, task3];
      const setters = [setTask1, setTask2, setTask3];
      const usedTaskIds = new Set(tasks.filter(task => task).map(task => task?.id));
      tasks.forEach((task, index) => {
        if (task) {
          const updatedTask = props.taskList.find(t => t.id === task.id);
          if (updatedTask) {
            setters[index](updatedTask);
            usedTaskIds.add(updatedTask.id);
            if (checkTaskFinished(updatedTask)) {
              completeTask(updatedTask);
            }
          } else {
            // 当前位置的组件任务不在资源任务列表里
            // 则移除当前的组件任务, 放入下一个任务
            completeTask(task);
          }
        } else {
          // 处理 null 任务，确保不会重复使用已经设置的任务
          const availableTasks = doingTasks.filter(t => !usedTaskIds.has(t.id));
          if (availableTasks.length > 0) {
            setters[index](availableTasks[0]);
            usedTaskIds.add(availableTasks[0].id);
          }
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps 
  }, [props.taskList]); // 这里不能添加task1、task2、task3作为依赖项，在任务更新时会造成死循环
  
  useEffect(() => {
    // 某个组件任务完成后,会在原组件任务的位置出新的任务
    // 即同一位置任务任务发生变化时需重新曝光(不同任务)
    if (task1?.id && preExpoTaskId1.current !== task1?.id) {
      dispatch.highValueTask.resourceExposure(task1, 'EXPOSURE', config?.taskWidgetResourceCode);
      preExpoTaskId1.current = task1.id
    }

    if (task2?.id && preExpoTaskId2.current !== task2?.id) {
      dispatch.highValueTask.resourceExposure(task2, 'EXPOSURE', config?.taskWidgetResourceCode);
      preExpoTaskId2.current = task2.id
    }

    if (task3?.id && preExpoTaskId3.current !== task3?.id) {
      dispatch.highValueTask.resourceExposure(task3, 'EXPOSURE', config?.taskWidgetResourceCode);
      preExpoTaskId3.current = task3.id
    }
  }, [task1, task2, task3]);

  const handleTask = async (taskInfo) => {
    const requestId = geneTaskRequestId();

    // 东风点击上报
    dongfengTaskReport(taskInfo, 'click');

    stat.click('task_click', {
      c: `list`,
      d: 'bubble',
      task_id: taskInfo.id,
      task_name: taskInfo.name,
      award_amount: getTaskAward(taskInfo),
      award_type: getWidgetIconType(taskInfo),
      taskclassify: taskInfo.taskClassify,
      groupcode: taskInfo.groupCode,
      resource_location: 'bubble_task'
    });

    stat.click('resource_click', {
      c: `list`,
      d: 'bubble',
      resource_location: 'bubble_task'
    });

    const { state } = taskInfo;
    if (checkTaskFinished(taskInfo)) {
      completeTask(taskInfo);
      return;
    }

    if (highValueTask.currentTaskInfo?.id !== taskInfo.id) {
      const loginStatus = await dispatch.user.checkLoginAndBind(0, 7);
      if (!loginStatus && highValueTask.currentTaskInfo?.id !== taskInfo.id) {
        return;
      }
    }

    if (state === TASK_STATUS.TASK_NOT_COMPLETED && [TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD].includes(taskInfo.event)) {
      const result = await dispatch.task.checkAppDownloadFinish(taskInfo, highValueTask.currentTaskInfo?.id !== taskInfo.id);
      if (result) {
        return;
      }
    }

    if (state === TASK_STATUS.TASK_COMPLETED) {
      return receiveAward(taskInfo);
    }

    execWithLock('finish_task_lock', async () => {
      await toFinishTask(requestId, taskInfo);
    }, 2000);
  };

  const receiveAward = (taskInfo) => {
    dispatch.task.finishTask({
      taskId: taskInfo?.id,
      type: "award",
      useUtCompleteTask: !!taskInfo?.useUtCompleteTask,
      publishId: taskInfo.publishId,
    });
  };

  const toFinishTask = async (requestId: string, taskInfo: any) => {
    localStorage.setItem(LocalStorageKey.FINISH_TASK_FROM, 'taskBubble');
    // 去完成任务监控
    logToFinishTask(taskInfo, TASK_LOCATION.TASK_WIDGET);
    return taskActionHandler(taskInfo, requestId, {location: TASK_LOCATION.TASK_WIDGET});
  };

  const getWidgetIconType = (taskInfo: TaskInfo) => {
    return taskInfo.rewardItems?.[0]?.mark || ICON_TYPE.POINT;
  };

  const getWidgetName = (taskInfo: TaskInfo) => {
    const widget = widgetTaskList.find(item => +item.taskId === taskInfo.id);
    if (widget) {
      return widget.widgetName;
    }
    const extra = getExtraInfo(taskInfo);
    return extra.widgetName || taskInfo.name;
  };

  return (
    <div className="task-widget-comp" style={{visibility: visible ? 'visible' : 'hidden' }}>
      {[task1, task2, task3].map((task, index) => (
        task ? (
          <Fact
            key={task.id}
            c="list"
            d="bubble"
            expoLogkey="task_exposure"
            noUseClick
            expoExtra={{
              task_id: task.id,
              task_name: task.name,
              page_status: 1,
              taskclassify: task?.taskClassify,
              groupcode: task?.groupCode,
              award_amount: getTaskAward(task),
              award_type: getWidgetIconType(task),
              resource_location: 'bubble_task'
            }}
          >
            <Fact
            key={task.id}
            c="list"
            d="bubble"
            expoLogkey="resource_exposure"
            noUseClick
            expoExtra={{
              resource_location: 'bubble_task'
            }}
            >
              <div
                className={`extend-resource extend-resource-${index + 1} ${task.fadingOut ? 'fade-out' : ''} ${task.fadingIn ? 'fade-in' : ''} ${!task.fadingOut && task.noFloat ? 'no-float' : ''}`}
                style={{ '--delay': index }}
                onClick={() => handleTask(task)}
              >
                <img src={getWidgetIconType(task) === ICON_TYPE.CASH ? RedBagIcon : BubbleIcon} className="img-icon" alt="扩展资源位" />
                {
                  getWidgetIconType(task) === ICON_TYPE.CASH ?
                      <div className="widget-amount red-bag-number">{(getTaskAward(task) / 100).toFixed(2)}</div> :
                    <div className="widget-amount">{getTaskAward(task)}</div>
                }
                  <div className={`widget-name ${isNight ? 'nightColor' : ''}`}>{getWidgetName(task)}</div>
                  {task.taskType === 'everydayTimes' && <div className={`award-count ${isNight ? 'nightColor' : ''}`}>{`${getTaskCurDayTimeProcess(task)}/${getTaskCurDayTimeTarget(task)}`}</div>}
              </div>
            </Fact>
          </Fact>
        ) : null
      ))}
    </div>
  );
}
