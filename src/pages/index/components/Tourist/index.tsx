import React, { useEffect, useRef } from 'react';
import FertilizerButtonImg from './assets/fertilizer-button.png';
import dispatch from '@/logic/store';
import Lottie, { AnimationItem } from 'lottie-web';
import { isHighMode } from '@/gameObject/utils/screen';
import touristImg from './assets/touristimg.png';
import touristMiniImg from './assets/touristmini.png';
import './index.scss';
import { isUc } from '@/lib/universal-ua';
import stat from '@/lib/stat';
import toast from '@/lib/universal-toast/component/toast';
import { mx } from '@ali/pcom-iz-use';
import { newYearTouristimg, newYearTouristmini, checkPlantActivityCurActPeriod, inPeriod } from '@/logic/store/models/app/new_year_time';
import BbzTourist from './assets/bbz-tourist.png';
import BbzTouristMini from './assets/bbz-tourist-mini.png';
import DoubleTourist from './assets/double-tourist.png'
import DoubleTouristMini from './assets/double-tourist-mini.png'
import { getParam } from '@/lib/qs';
import { getIsBangBangMainActivity, getIsDoubleActivity } from '@/logic/store/models/utils';

const Index = () => {
  const app = mx.store.get('app');
  const handAnimation = useRef<AnimationItem | null>(null);
  useEffect(() => {
    handAnimation.current = Lottie.loadAnimation({
      name: 'hand',
      container: document.getElementById('guidance-hand') as HTMLElement,
      renderer: 'canvas',
      loop: true,
      autoplay: true,
      animationData: require('@/lib/animation/hand.json'),
      assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202405/6c4713/images/',
    });
    return () => {
      handAnimation.current?.destroy()
    };
  }, []);
  const touristClick = async () => {
    stat.click('manure_click', {
      c: 'function',
      d: 'manure',
    });
    if (!isUc) {
      toast.show('请使用UC浏览器打开');
      return;
    }
    await dispatch.user.checkLoginAndBind(0, 8);
  };

  const getTouristImg = () => {
    const inviteCode = getParam('inviteCode') || '';
    const isBangBangZhongActiviy = getIsBangBangMainActivity();
    const { actStartTime, actEndTime, curTime } = mx.store.get('doublePointsCard');
    const isDoubleCardActivity = getIsDoubleActivity() && inPeriod(actStartTime, actEndTime, curTime);
    // 普通助力场景
    if (inviteCode && !isBangBangZhongActiviy && !isDoubleCardActivity) {
      return isHighMode() ? touristImg : touristMiniImg
    }
    if (isDoubleCardActivity) {
      return isHighMode() ? DoubleTouristMini : DoubleTourist
    }
    if (isBangBangZhongActiviy || checkPlantActivityCurActPeriod()) {
      return isHighMode() ? BbzTourist : BbzTouristMini;
    }
    return isHighMode() ? touristImg : touristMiniImg
  }

  return (
    <div className="tourist">
      <div className="tourist-btn">
        {app?.isSpringPeriod ? (
          <img
            className={`${isHighMode() ? 'tourist-img' : 'tourist-mini-img'}`}
            src={isHighMode() ? newYearTouristimg : newYearTouristmini}
            alt=""
          />
        ) : (
          <img
            className={`${isHighMode() ? 'tourist-img' : 'tourist-mini-img'}`}
            src={getTouristImg()}
            alt=""
          />
        )}
        <img className="fertilizer-button" src={FertilizerButtonImg} alt="" />
        <div id="guidance-hand" />
        <div className="fertilizer-coating" onClick={touristClick} />
      </div>
    </div>
  );
};

export default Index;
