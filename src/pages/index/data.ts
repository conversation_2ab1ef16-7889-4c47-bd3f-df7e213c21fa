import { IFirstData } from '@/api/typings';
import { getUserInfo } from '@/lib/ucapi';
import { queryHomeData, getCmsData } from '@/api/home';
import { queryByMultiResource } from '@/api/resource';
import config from '@/config';

export type IFirstDataRes = IFirstData | null | {};

/* 通过data.ts暴露的方法实现首屏数据获取，响应值为
 * window.__INITIAL_PROPS__.initialProps值
 */
export async function getInitialData(kpsVal = ''): Promise<IFirstDataRes> {
  const userInfo: any = await getUserInfo();
  const kps = userInfo?.kps_wg || '';
  let data: IFirstDataRes = null;
  try {
    const {
      taskResourceCode,
      taskWidgetResourceCode,
      getMatryoshkaTaskCode,
      getBubbleTaskCode,
      getBackInterceptCode,
      backTagResourceCode,
      bbzResourceCode,
      homeAdResourceCode,
      wateringPopResourceCode,
    } = config;
    const multiResourceCode = [
      taskResourceCode,
      taskWidgetResourceCode,
      getMatryoshkaTaskCode,
      getBubbleTaskCode,
      getBackInterceptCode,
      backTagResourceCode,
      bbzResourceCode,
      homeAdResourceCode,
      wateringPopResourceCode,
    ];
    const [queryHomeRes, multiResource, cmsData] = await Promise.all([
      queryHomeData(kps),
      queryByMultiResource(multiResourceCode.join(','), kps),
      getCmsData(true),
    ]);
    data = {
      userInfo,
      mainInfo: queryHomeRes,
      multiResource,
      cmsData,
    };
  } catch (e) {
    return {
      userInfo,
      mainInfo: null,
      multiResource: null,
      cmsData: null,
      errCode: e.code || '',
    };
  }

  return data;
}

/**
 * 此处无需额外处理。
 * 并行ssr异步接口，获取ssr片段和首屏数据，只在开启了并行ssr时才有用
 * Broccoli应该是固定下来这样执行。（后续抽出来，不放在业务代码中）
 */
export async function getAsyncSsrResult() {
  let pathname;
  // @ts-ignore
  const broPackId = window?.__wh_data__?.packId;

  if (DEV) {
    pathname = `/api/v1/ssr/async-fetch${location.pathname}?wh_page_only=true`;
  } else {
    const broccoliPathRegExp = /\/apps\/(\S*)\/routes\/(\S*)/;
    const [_, appCode, routeCode] = window.location.pathname.match(broccoliPathRegExp) || [];
    if (appCode && routeCode) {
      pathname = `/api/v1/ssr/async-fetch/${appCode}/${routeCode}?uc_param_str=dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&webCompassApp=true&compass_params=name%3Aucbabafarm&$kps_info`;
      if (broPackId) {
        pathname += `&broPackId=${broPackId}`;
      }
    }
  }

  const customDocUrl = window.location.href.replace(/skeletonMode/g, 'skeletonBak');

  const pageOnlyData = await fetch(pathname, {
    headers: {
      'custom-doc-url': customDocUrl,
    },
  }).then((res) => res.json());

  if (!DEV && pageOnlyData?.data?.packId !== broPackId) return {};

  return pageOnlyData?.data;
}
