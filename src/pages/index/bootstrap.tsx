import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './index';
import { getInitialPropsFromDocument, perfMark } from '@ali/compass-app';

console.log('custom bootstrap!');
// 记录entry js 开始执行的时间
// @ts-ignore
perfMark('bootstrapExecute');
const initialProps = getInitialPropsFromDocument();
console.log("[=== DEBUG ===] hydrate's App props initialProps:", initialProps);

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(<App initialProps={initialProps} />);
