import { preloadImg, preloadFileWithFetch } from "@/lib/utils/preloadImg";

// 升级弹窗图片
import upgradeModalBg from '@/components/modals/modal_upgrade/assets/<EMAIL>';
// 新人弹窗背景
import newUserModalBg from '@/components/modals/modal_newbie_gift_pack/assets/gift.png';
// 助力弹窗
import helpBg from '@/components/modals/modal_help_success/images/modal-help-bg.jpg';
// 关闭按钮
import closeIcon from '@/components/modals/assets/modal-close.png';
// 任务浮层背景
import TaskPopBg from '@/pages/index/components/TaskPop/images/task-pop-bg.png';
// 任务浮层分割线
import TaskPopLine from '@/pages/index/components/TaskPop/images/task-pop-line.png';
// 任务浮出标题
import TaskPopTilt from '@/pages/index/components/TaskPop/images/task-pop-title.png';
// 任务浮出标题-翻倍卡
import DoubleHead from '@/pages/index/components/TaskPop/images/task-double-head.png';

// 春节
// import {newYearTouristimg, newYearTouristmini, newYearBindingBubbleImg, newYearLoginBubbleImg, newYearBingtaobaoImg, logoImg} from '@/logic/store/models/app/new_year_time';
// import NewYearGameBgDayImg from '@/pages/index/components/Game/assets/NewYear/<EMAIL>';
// import NewYearGameBgDayHighImg from '@/pages/index/components/Game/assets/NewYear/<EMAIL>';
// import NewYearGameBgNightImg from '@/pages/index/components/Game/assets/NewYear/<EMAIL>';
// import NewYearGameBgNightHightImg from '@/pages/index/components/Game/assets/NewYear/<EMAIL>';

export const imgsPreload = () => {
  preloadImg([
    newUserModalBg,
    upgradeModalBg,
    helpBg,
    closeIcon,
    TaskPopBg,
    TaskPopLine,
    TaskPopTilt,
    DoubleHead,
    // newYearTouristimg,
    // newYearTouristmini,
    // newYearBindingBubbleImg,
    // newYearLoginBubbleImg,
    // newYearBingtaobaoImg,
    // logoImg,
    // NewYearGameBgDayImg,
    // NewYearGameBgDayHighImg,
    // NewYearGameBgNightImg,
    // NewYearGameBgNightHightImg
  ]);
}
