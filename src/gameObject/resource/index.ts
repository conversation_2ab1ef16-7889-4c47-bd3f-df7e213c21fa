import { resource, TransformParams, RESOURCE_TYPE, GameObject } from '@eva/eva.js';
import prefResource from './prefResource';
import { Img } from '@eva/plugin-renderer-img';

export interface DisplayResType {
  name: string;
  type: string;
  width?: number;
  height?: number;
  src: {
    image: {
      url: string;
      type: string;
    };
  };
  preLoad: boolean;
}

/**
 *
 * @param 获取资源
 * @returns
 */
export function getResByName(name: string) {
  const resMap = resource.resourcesMap;
  return resMap[name];
}

/**
 * 添加图片资源
 * @param url
 * @returns
 */
export function addImgResource(url: string) {
  const resName = url;
  if (!getResByName(resName)) {
    resource.addResource([
      {
        name: resName,
        type: RESOURCE_TYPE.IMAGE,
        src: { image: { url, type: 'png' } },
      },
    ]);
  }
  return url;
}
/**
 * 添加资源列表
 * @param res
 */
export function addResourceList(res: DisplayResType[]) {
  resource.addResource(res as any);
}


export function addSpineResource(
  name: string,
  src: {
    ske: string;
    atlas: string;
    image: string;
  },
  scale?: number,
) {
  const resName = name;

  if (!getResByName(resName)) {
    resource.addResource([
      {
        name: resName,
        scale,
        type: 'SPINE',
        src: {
          ske: { url: src.ske, type: 'json' },
          atlas: { type: 'atlas', url: src.atlas },
          image: { type: 'png', url: src.image },
        },
      } as any,
    ]);
  }
  return resName;
}
/**
 * 添加预加载资源
 * @param resName 资源名称
 */

export function addPrefabResource(resName: string) {
  const info = prefResource.find((v) => {
    return v.name === resName;
  });
  if (info && !(resource as any).resourcesMap[resName]) {
    resource.addResource([{ ...(info as any) }]);
  }
}

/**
 * 用图片资源创建一个图片gameObject
 * @param resName
 * @param transform
 * @returns
 */
export function createImageGameObjectByUrl(resName: string, transform: TransformParams) {
  if (!getResByName(resName)) {
    resource.addResource([
      {
        name: resName,
        type: RESOURCE_TYPE.IMAGE,
        src: {
          image: {
            url: resName,
            type: 'png',
          },
        },
      },
    ]);
  }
  const newTransform = { ...transform };
  // 默认居中
  if (!newTransform.origin) {
    newTransform.origin = { x: 0.5, y: 0.5 };
  }
  const go = new GameObject(resName, transform);
  go.addComponent(Img, { resource: resName });
  return go;
}
/**
 * 读取资源详情
 * @param url
 * @returns
 */
export async function getImgResInfo(url: string): Promise<any> {
  return new Promise((resolve, reject) => {
    resource
      .getResource(url)
      .then((res: any) => {
        if (res?.data?.image) {
          resolve(res);
        } else {
          reject(new Error('image null'))
        }
      })
      .catch((e) => {
        reject(e)
      });
  })
}

export function addUrl(surl: string, width = 0) {
  const name = surl;
  // 屏蔽旧淘宝图片链接转换逻辑
  // const url = getCrossImage(surl, width, getSysCfg().ImageScale);
  const url = surl;
  if (!resource.resourcesMap[name]) {
    resource.addResource([
      {
        name,
        type: RESOURCE_TYPE.IMAGE,
        src: { image: { url, type: 'png' } },
      },
    ]);
  }
}
