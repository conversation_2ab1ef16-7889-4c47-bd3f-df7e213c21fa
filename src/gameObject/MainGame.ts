import { Game } from '@eva/eva.js';
import { createGame } from './utils/eva';
import { getScreenWidth } from '@/lib/utils/index';
import mx from '@ali/pcom-mx';
import { DisplayResType } from '@/gameObject/resource/index';
import prefResource from './resource/prefResource';
import { addResourceList } from '@/gameObject/resource';
import World from './uc/World';
import { SpriteTextSystem } from './SpriteText/index';
import { isNode } from 'universal-env';

function getRenderType() {
  const renderType = mx.store.get('renderType') || 0;
  if (renderType === 0 || renderType === 1 || renderType === 2) {
    return renderType;
  } else {
    return 0;
  }
}

function registerAutoRestoreContext(canvas) {
  const gl = canvas.getContext('webgl') || canvas.getContext('webgl2');
  if (gl) {
    gl.getExtension('WEBGL_lose_context');

    canvas.addEventListener('webglcontextlost', (event) => {
      setTimeout(() => {
        try {
          // store.restoreContext();
          // location.reload();
        } catch (e) {
          console.error('location reload error')
        }
      }, 1);
    });
  } else {
    console.error('gl not exist');
  }
}

function getPreLoadingRes(res: DisplayResType[]) {
  const preLoadList: DisplayResType[] = [];
  for (let i = 0; i < res.length; ++i) {
    if (res[i].preLoad) {
      preLoadList.push(res[i]);
    }
  }
  return preLoadList;
}

class MainGame {
  static instance = null;
  private _init: boolean;
  private _game: Game;

  constructor(canvas: HTMLCanvasElement, height) {
    this.initGame(canvas, height);
    this.initEvent();
  }
  createGame() {
    if (this._init) {
      return;
    }
    this._init = true;
    const world = World.createGameObject({}).gameObject;
    this._game.scene.addChild(world);
  }
  private getInitFrameRate() {
    const defaultFPS = 60;
    return defaultFPS;
  }

  private initEvent() {
    if (isNode) {
      return;
    }
    if (!this._game.getSystem(SpriteTextSystem)) {
      this._game.addSystem(new SpriteTextSystem());
    }
  }

  private initGame(canvas, height) {
    const options = {
      autoStart: true,
      frameRate: this.getInitFrameRate(),
      Render: {
        canvas,
        width: getScreenWidth(),
        height,
        transparent: true,
        resolution: window.devicePixelRatio / 2,
        renderType: getRenderType(),
      },
    };
    const game = createGame(options);
    this._game = game;
    (window as any).__EVA_GAME_INSTANCE__ = game;
    registerAutoRestoreContext(canvas);
    addResourceList(getPreLoadingRes(prefResource as any));
  }
}

export const createMainGame = (canvas: HTMLCanvasElement, height: number) => {
  if (MainGame.instance) {
    return MainGame.instance;
  }
  return new MainGame(canvas, height);
};
