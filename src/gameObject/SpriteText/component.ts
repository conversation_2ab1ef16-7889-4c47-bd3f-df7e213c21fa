import { Component } from '@eva/eva.js';
// import { type } from '@eva/inspector-decorator';

export interface Size {
  width: number,
  height: number
}

export interface Voter2 {
  x: number,
  y: number
}

export interface WordsWidthItem {
  // 使用该尺寸的字符集
  words: string,
  // 字符尺寸
  wordSize: Size
}

export interface TextStyle {
  letterSpacing?: number,
  verticalAlign?: 'middle' | 'top' | 'bottom'
}

export interface SpecialChar {
  word: string,
  spriteName: string
}



export interface SpriteTextParams {
  text: string,
  // 精灵图资源名
  spriteResource: string,
  // 字符sizemap
  wordsSizeMap: WordsWidthItem[] | Size
  // 当前字符精灵图名前缀
  spriteNamePrefix?: string,
  // 样式
  style?: TextStyle,
  specialCharMap?: SpecialChar[]
}

export default class SpriteText extends Component<SpriteTextParams> {
  static override componentName: string = 'SpriteText';
  // @type('string') spriteResource: string = '';
  // @type('string') text: string;
  // @type('string') spriteNamePrefix?: string = '';
  wordsSizeMap: WordsWidthItem[] | Size;
  style?: TextStyle = {
    letterSpacing: 0
  };
  specialCharMap?: SpecialChar[];

  override init(obj?: SpriteTextParams) {
    if (obj && obj.spriteResource) {
      Object.assign(this, obj);
    }
  }
}
