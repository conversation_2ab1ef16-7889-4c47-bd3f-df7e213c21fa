import { GameObject, decorators, resource, ComponentChanged, RESOURCE_TYPE, OBSERVER_TYPE } from '@eva/eva.js';
import { RendererManager, ContainerManager, RendererSystem, Renderer } from '@eva/plugin-renderer';

import SpriteComponent, { SpecialChar, WordsWidthItem } from './component';
import { Sprite as SpriteEngine } from '@eva/renderer-adapter';
import SpriteText, { Size } from './component';

function isType(val: any, type: string) {
  return Object.prototype.toString.call(val) === '[object ' + type + ']';
}

function isNumber(val: any) {
  return typeof val === 'number' && !isNaN(val);
}

function isHalfAngle(v: string) {
  return /[\x00-\xff]/g.test(v) ? true : false;
}

interface SpriteTextProprty {
  sprites: SpriteEngine[];
  instance: any;
  component: SpriteText;
  wrapWidth: number;
  wrapHeight: number;
}

const resourceKeySplit = '_s|r|c_'; // Notice: This key be used in ninepatch system.

@decorators.componentObserver({
  SpriteText: ['spriteResource', 'text', 'spriteNamePrefix', { prop: ['style'], deep: true }],
})
class SpriteTextSystem extends Renderer {
  static override systemName = 'SpriteText';
  override name: string = 'SpriteText';
  sprites: { [propName: number]: SpriteTextProprty } = {};
  renderSystem: RendererSystem;
  defaultWordSize: Size;
  override init() {
    this.renderSystem = this.game.getSystem(RendererSystem) as RendererSystem;
    this.renderSystem.rendererManager.register(this);
    this.defaultWordSize = { width: 30, height: 30 };
  }

  override async componentChanged(changed: ComponentChanged) {
    if (changed.componentName !== 'SpriteText') return;
    const gameObjectId = changed.gameObject.id;
    const component: SpriteComponent = changed.component as SpriteComponent;

    if (changed.type === OBSERVER_TYPE.ADD) {
      this.sprites[gameObjectId] = { sprites: [], instance: null, component, wrapHeight: 0, wrapWidth: 0 };
      // const asyncId = this.increaseAsyncId(gameObjectId);
      const { instance } = await resource.getResource(component.spriteResource);
      // if (!this.validateAsyncId(gameObjectId, asyncId)) return;
      if (!instance) {
        console.error(`GameObject:${changed.gameObject.name}'s SpriteText resource load error`);
        return;
      }
      this.sprites[gameObjectId].instance = instance;
      this.updateText(this.sprites[gameObjectId]);
      // this.setSize(changed);
    } else if (changed.type === OBSERVER_TYPE.REMOVE) {
      // this.increaseAsyncId(gameObjectId);
      const spriteText = this.sprites[gameObjectId];
      const container = this.containerManager.getContainer(gameObjectId);
      spriteText.sprites.forEach((s) => {
        container.removeChild(s.sprite);
        s.sprite.destroy({ children: true });
      });
      delete this.sprites[gameObjectId];
    } else {
      // const asyncId = this.increaseAsyncId(gameObjectId);
      const { instance } = await resource.getResource(component.spriteResource);
      // if (!this.validateAsyncId(gameObjectId, asyncId)) return;
      if (!instance) {
        console.error(`GameObject:${changed.gameObject.name}'s SpriteText resource load error`);
        return;
      }
      this.sprites[gameObjectId].instance = instance;
      this.updateText(this.sprites[gameObjectId]);
      // this.setSize(changed);
    }
  }

  private getTextHeight(wordsSizeMap: WordsWidthItem[] | Size) {
    if (wordsSizeMap) {
      if (isType(wordsSizeMap, 'Object')) {
        return (wordsSizeMap as Size).height;
      } else if (Array.isArray(wordsSizeMap)) {
        const sizeArr = wordsSizeMap.map((item) => item?.wordSize?.height).filter((ele) => isNumber(ele));
        if (sizeArr.length) {
          return Math.max(...sizeArr);
        }
      }
    }
    return this.defaultWordSize.height;
  }

  private getFontSize(v: string, wordsSizeMap: WordsWidthItem[] | Size): Size {
    if (wordsSizeMap) {
      if (isType(wordsSizeMap, 'Object')) {
        return wordsSizeMap as Size;
      } else if (Array.isArray(wordsSizeMap)) {
        const wordsCfg = wordsSizeMap?.find((item) => item.words?.includes(v));
        if (wordsCfg) {
          return wordsCfg.wordSize;
        }
      }
    }
    return this.defaultWordSize;
  }

  private getWordSuffix(word: string, specialCharMap: SpecialChar[], spriteNamePrefix: string = '') {
    const specialInfo = specialCharMap?.find((item) => item.word === word);
    if (specialInfo) {
      return specialInfo.spriteName;
    } else {
      return spriteNamePrefix + word;
    }
  }

  updateText(prop: SpriteTextProprty) {
    const container = this.containerManager.getContainer(prop.component.gameObject.id);
    const {
      text = '',
      spriteResource = '',
      spriteNamePrefix = '',
      wordsSizeMap = this.defaultWordSize,
      style = {},
      specialCharMap = [],
    } = prop.component;
    let k = 0,
      len = text.length;
    let offsetX = 0;
    let objHeight = this.getTextHeight(wordsSizeMap);
    const { verticalAlign = 'middle' } = style;
    for (; k < len; ++k) {
      const word = text[k];
      const curSize = this.getFontSize(word, wordsSizeMap);
      const spriteNameSuffix = this.getWordSuffix(word, specialCharMap, spriteNamePrefix);
      const offsetY =
        verticalAlign === 'middle'
          ? (objHeight - curSize.height) / 2
          : verticalAlign === 'bottom'
          ? objHeight - curSize.height
          : 0;
      if (!prop.sprites[k]) {
        const sprite = new SpriteEngine(null);
        sprite.image = prop.instance[spriteResource + resourceKeySplit + spriteNameSuffix];
        sprite.sprite.position.set(offsetX, offsetY);
        sprite.sprite.width = curSize.width;
        sprite.sprite.height = curSize.height;
        container.addChild(sprite.sprite);
        prop.sprites[k] = sprite;
      } else {
        prop.sprites[k].image = prop.instance[spriteResource + resourceKeySplit + spriteNameSuffix];
        prop.sprites[k].sprite.position.set(offsetX, offsetY);
        prop.sprites[k].sprite.width = curSize.width;
        prop.sprites[k].sprite.height = curSize.height;
      }
      offsetX += style.letterSpacing ? style.letterSpacing + curSize.width : curSize.width;
    }
    // 修改gameObject宽高
    prop.wrapHeight = objHeight;
    prop.wrapWidth = offsetX;
    const { transform } = prop.component.gameObject;
    if (transform) {
      transform.size.width = offsetX;
      transform.size.height = objHeight;
    }

    // destory多余
    if (k < prop.sprites.length) {
      for (let i = k; i < prop.sprites.length; ++i) {
        prop.sprites[i].sprite.destroy({ children: true });
      }
      prop.sprites.splice(k, prop.sprites.length - k);
    }
  }

  private setSize(changed: ComponentChanged) {
    const { transform } = changed.gameObject;
    if (!transform) return;
    transform.size.width = this.sprites[changed.gameObject.id].wrapWidth;
    transform.size.height = this.sprites[changed.gameObject.id].wrapHeight;
  }
}
export default SpriteTextSystem;
