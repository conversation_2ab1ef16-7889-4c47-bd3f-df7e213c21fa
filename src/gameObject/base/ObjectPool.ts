/**
 * 显示组件
 */
import { Component, GameObject } from '@eva/eva.js';
import { Pool } from '../utils/Pool';

import BaseComponent from './BaseComponent';

export interface ObjectPoolDelegate {
  onReuse: (params: any) => void;
  onUnuse?: () => void;
}
type InitParam<T> = T extends { init: (arg: infer R) => void } ? R : any;
type ReuseParam<T> = T extends { onReuse: (arg: infer R) => void } ? R : any;

export interface ObjectPoolParams<R extends Component & ObjectPoolDelegate> {
  // 初始数量
  initNum: number;
  compType: new (go: GameObject) => R;
  initParams?: InitParam<R>;
}

export class ObjectPool<T extends Component & ObjectPoolDelegate> extends BaseComponent {
  static override componentName = 'ObjectPool';

  private params: ObjectPoolParams<T>;
  private pool: Pool<T>;
  override init(params: ObjectPoolParams<T>) {
    this.params = params;
    this.pool = new Pool<T>();
    for (let i = 0; i < params.initNum; ++i) {
      this.pool.put((params.compType as any).createGameObject(this.params.initParams));
    }
  }
  protected clean() {
    this.pool.clean();
  }

  public getItemNum() {
    return this.pool.num;
  }

  public get(params: ReuseParam<T>): T {
    let obj: T = null;
    if (!this.pool.has()) {
      obj = (this.params.compType as any).createGameObject(this.params.initParams);
    } else {
      obj = this.pool.get();
    }
    obj.onReuse(params);
    return obj;
  }

  public put(obj: T) {
    if (obj.onUnuse) {
      obj.onUnuse();
    }

    obj.gameObject.remove();
    this.pool.put(obj);
  }
}
