import BaseComponent from '@/gameObject/base/BaseComponent';
import { GameObject } from '@eva/eva.js';
import { Graphics } from '@eva/plugin-renderer-graphics';

export interface GraphicParams {
  position: { x: number; y: number };
}

export class Graphic extends BaseComponent<GraphicParams> {
  static override componentName = 'Graphic';
  private _params: GraphicParams;
  private _tipsImg: GameObject;
  static inst: Graphic;
  gra: Graphics;
  private _drawParams: any;

  override init(params: GraphicParams) {
    Graphic.inst = this;
    this._params = params;
    this.gameObject.transform.position = this._params.position;
    this.gameObject.transform.size = { width: 150, height: 50 };
    this.gameObject.transform.origin = { x: 0.5, y: 0.5 };
    this.gameObject.transform.anchor = { x: 0.5, y: 0.5 };
    const outterGraphics = this.gameObject.addComponent(new Graphics());

    // const game = (window as any).mainGame?.game;
    // if (game) {
    //   game.canvas.addEventListener('webglcontextrestored', () => {
    //     this.gameObject.removeComponent('Graphics');
    //     setTimeout(() => {
    //       const outterGraphics = this.gameObject.addComponent(new Graphics());
    //       this.gra = outterGraphics;
    //       if (this._drawParams) {
    //         this.drawPop(this._drawParams);
    //       }
    //     }, 100);
    //   });
    // }
    this.gra = outterGraphics;
  }

  drawPop({
    width,
    height,
    color = '0xffffff',
    alpha = '1',
    radius = 12,
    borderWidth = 0,
    borderAlpha = 0.4,
    borderColor = 0x000000,
    arrowW = 8,
    arrowH = 8,
  }) {
    this._drawParams = arguments[0];
    const { graphics: _graphics } = this.gra;
    this.clear();
    const graphics: any = _graphics;
    graphics.lineStyle(borderWidth, borderColor, borderAlpha, 0, false);
    graphics.beginFill(color, alpha);
    graphics.moveTo(radius, 0);
    graphics.arcTo(width, 0, width, radius, radius);
    graphics.arcTo(width, height, width - radius, height, radius);
    graphics.lineTo(width / 2 + arrowW, height);
    // 下圆角
    graphics.arcTo(width / 2, height + arrowH, width / 2 - 5, height + 6, 3);
    graphics.lineTo(width / 2 - arrowW, height);
    graphics.arcTo(0, height, 0, 20, radius);
    graphics.lineTo(0, radius);
    graphics.arcTo(0, 0, radius, 0, radius);
    graphics.endFill();
  }
  clear() {
    (this.gra.graphics as any).clear();
  }
  updateBgWidth(w: number) {
    this.gameObject.transform.size.width = w;
  }
}
