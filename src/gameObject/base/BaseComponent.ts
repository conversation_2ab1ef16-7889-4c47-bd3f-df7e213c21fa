import { Component, ComponentParams, GameObject } from '@eva/eva.js';
import { register } from '../utils/plugins';
import { toGlobal } from '../utils/toGlobal';

export default class BaseComponent<T extends ComponentParams = {}> extends Component<T> {
  static override componentName = 'BaseComponent';
  static createGameObject(params: ComponentParams) {
    const go = new GameObject(this.componentName, params);
    go.addComponent(this, params);
    const comp: any = go.getComponent(this.componentName);
    return comp
  }

  constructor(params) {
    super(params);
    this.addGlobal(this.name);
  }

  override init(params: T) {
  }
  /**
   * @param key 关键字
   * 把key和obj位置加window上
   */
  addGlobal(key: string) {
    register(key, this.getPosition);
  }

  getPosition = () => {
    return toGlobal(this.gameObject.parent, this.gameObject.transform.position);
  };
}
