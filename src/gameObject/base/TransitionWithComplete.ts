import { AnimationStruct, ComponentParams } from '@eva/eva.js';
import { Transition } from '@eva/plugin-transition';
import { addComponent } from '../utils/addComponent';
import BaseComponent from './BaseComponent';


export default class TransitionWithComplete<T extends ComponentParams = {}> extends BaseComponent<T> {
  static override componentName = 'TransitionWithComplete';
  protected animation: Transition;
  private _onComplete: (() => void) | null;

  add(anim: string, values: AnimationStruct[]) {
    this.checkGroup(anim);
    this.animation.group[anim] = values;
  }

  async play(anim = 'default') {
    return new Promise<void>((resolve) => {
      if (!this.animation.gameObject) {
        return;
      }
      this.animation.play(anim, 1);
      this._onComplete = resolve;
    });
  }
  protected stop(anim = 'default') {
    if (!this.animation) {
      return;
    }
    this.animation.stop(anim);
  }

  protected stopAll() {
    if (!this.animation) {
      return;
    }
    this.animation.stop('');
  }

  protected playOnTimes(anim = 'default', times = 1) {
    return new Promise<void>((resolve) => {
      if (!this.animation.gameObject) {
        return;
      }
      this.animation.play(anim, times);
      this._onComplete = resolve;
    });
  }

  protected playLoop(anim = 'default') {
    this.animation.play(anim, -1);
  }

  private checkGroup(anim) {
    if (!this.animation) {
      this.animation = addComponent(this.gameObject, Transition);
      this.animation.group = {};

      this.animation.on('finish', (name) => {
        const cb = this._onComplete;
        if (cb) {
          this._onComplete = null;
          cb();
        }
      });
    } else if (this.animation.group[anim]) {
      (this.animation as any).animations[anim] = null;
    }
  }
}
