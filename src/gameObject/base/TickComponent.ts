/**
 * 显示组件
 */
import BaseComponent from './BaseComponent';

interface Tick {
  id: number;
  time: number;
  func: () => void;
}

/**
 * 处理降级方案，动画降到图片，只要刚开始初始化时用不同的resource就行
 */
export default class TickComponent extends BaseComponent {
  static override componentName = 'TickComponent';
  ticks: Tick[] = [];
  private _baseId = -1;
  setTimeout(time: number, func: () => void) {
    const id = this.genId();
    this.ticks.push({ time, func, id });
    return id;
  }
  clear() {
    this.ticks = [];
  }

  private genId() {
    return this._baseId++;
  }

  override update(params: { deltaTime: number }) {
    if (this.ticks.length === 0) {
      return;
    }
    const dt = params.deltaTime;
    const toRemove = [];

    this.ticks.forEach((t) => {
      t.time -= dt;
      if (t.time <= 0) {
        t.func();
        toRemove.push(t);
      }
    });

    toRemove.forEach((o) => {
      const idx = this.ticks.indexOf(o);
      if (idx !== -1) {
        this.ticks.splice(idx, 1);
      }
    });
  }
  clearTimeOut(id: number) {
    this.ticks = this.ticks.filter((v) => v.id !== id);
  }
}
