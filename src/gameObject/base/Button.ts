import { COMPONENT, GameObject } from '@eva/eva.js';
import { createText } from '../utils/eva';
import { Transition } from '@eva/plugin-transition';
import { Text } from '@eva/plugin-renderer-text';
import TransitionWithComplete from './TransitionWithComplete';
import { Img } from '@eva/plugin-renderer-img';
import mx from '@ali/pcom-mx';
import { addAnim, createFadeOutAnimParams, getWaterButtonAnim } from '../utils/anim';
import { addComponent } from '../utils/addComponent';
// import { delay } from "@/gameObject/utils/delay";
import { setRenderParams } from '../utils/setRenderParams';
import { getImgResInfo, addImgResource, getResByName, createImageGameObjectByUrl } from '@/gameObject/resource';
import { MainAPI } from '@/logic/type/event';
import {TOM_FERTILIZER_STORE} from "@/gameObject/uc/tomorrowFertilizer/common/constant";
import throttle from 'lodash.throttle';
import stat from "@/lib/stat";

export interface ButtonParams {
  resName?: string;
  spriteName?: string;
  imgUrl?: string;
  size?: { width: number; height: number };
}

/**
 * 点击触发逻辑
 * 可以设置一张图片
 */
export default class Button extends TransitionWithComplete {
  static override componentName = 'Button';
  private pressing: boolean;
  private img: Img;
  private text: Text;
  private textGameObject: GameObject;
  private imgGameObject: GameObject;
  // private imgObj: GameObject;
  // private textObj: GameObject;
  anim: Transition;
  // private _textAnim: Transition;
  // private _explainText: Text | null;
  // private _explainAnim: Transition | null;
  // private _needCarousel: boolean;
  private _imgUrl: string;

  override init(params: ButtonParams) {
    this.initGameObject();
    this.initButtonAni();
    this.initEvent()
    this.imgGameObject.addTouchHandler({
      tap: throttle(this.onTouch.bind(this), 300, { trailing: false }),
    });
    if (params.resName) {
      if (params.spriteName) {
        this.gameObject.addComponent(COMPONENT['Sprite'] as any, {
          resource: params.resName,
          spriteName: params.spriteName,
        });
      } else {
        this.img = this.gameObject.addComponent(Img, { resource: params.resName }) as Img;
        // @ts-ignore
        this.gameObject.transform.size = getResByName(params.resName);
        this.gameObject.transform.origin = { x: 0.5, y: 0.5 };
      }
    } else if (params.imgUrl) {
      this.updateImg(params.imgUrl);
    }
  }

  initButtonAni() {
    let anim = addComponent(this.gameObject, Transition);
    // @ts-ignore
    anim.group = getWaterButtonAnim(this.gameObject, 100);
    this.anim = anim;
  }

  initEvent() {
    // 按钮点击效果
    mx.event.on(MainAPI.Water, () => {
      if (this.pressing) {
        return;
      }
      this.pressing = true;
      this.anim.play('pressDown', 1);
      this.anim.once('finish', () => {
        this.anim.play('pressUp', 1);
        this.anim.once('finish', () => {
          this.pressing = false;
        });
      });
    });
  }

  initGameObject() {
    this.imgGameObject = new GameObject('imgGameObject');
    this.textGameObject = new GameObject('text');

    this.gameObject.addChild(this.imgGameObject);
    this.gameObject.addChild(this.textGameObject);
  }

  // 点击施肥按钮
  private onTouch() {
    stat.click('manure_click', {
      c: 'function',
      d: 'manure'
    })
    if (mx.store.get(TOM_FERTILIZER_STORE.IS_COLLECTING)) {
      return;
    }
    console.log('点击施肥')
    mx.event.emit(MainAPI.WaterGame);
  }

  // 更新施肥按钮图片
  // eslint-disable-next-line @typescript-eslint/member-ordering
  async updateImg(url: string) {
    if (!url) {
      if (this.img && this.img.gameObject.parent) {
        this.img.gameObject.remove();
      }
      return;
    }

    if (this._imgUrl === url) return;

    addImgResource(url);
    this._imgUrl = url;
    let resInfo = await getImgResInfo(url);
    let imgSize = { width: resInfo.data.image.width, height: resInfo.data.image.height };
    if (!this.img) {
      let imgGameObject = createImageGameObjectByUrl(url, {
        size: imgSize,
        origin: { x: 0.5, y: 1 },
      });
      this.imgGameObject.addChild(imgGameObject);
      this.img = imgGameObject.getComponentType(Img);
    }
    if (!this.img.gameObject.parent) {
      this.imgGameObject.addChild(this.img.gameObject);
    }
    // this.img.setUrl(url);
    this.img.resource = url;
    this.img.gameObject.transform.size = imgSize;
  }

  // 按钮文案 肥料数
  updateText(v: string, fill: string) {
    if (!v) {
      if (this.text && this.text.gameObject.parent) {
        this.text.gameObject.remove();
      }
    } else {
      if (!this.text) {
        const go = createText(
          'text',
          {
            position: { x: 0, y: -40 },
            origin: { x: 0.5, y: 0.5 },
          },
          {
            text: '',
            style: {
              fontFamily: 'D-DIN-Bold',
              fontSize: 26,
              // eslint-disable-next-line @iceworks/best-practices/recommend-add-line-height-unit
              lineHeight: 33,
              fontWeight: 400
            },
          },
        );
        // const anim = addAnim(go, 'fadeIn', [createFadeInAnimParams(go, 200, 0)]);
        addAnim(go, 'fadeOut', [createFadeOutAnimParams(go, 200)]);
        // this._textAnim = anim;
        this.textGameObject.addChild(go);
        this.text = go.getComponentType(Text);
      }
      if (!this.text.gameObject.parent) {
        this.textGameObject.addChild(this.text.gameObject);
      }
      if (fill && this.text.style) {
        // setRenderParams(this.text.gameObject,{alpha:alpha});
        this.text.style.fill = fill;
      }
      this.text.text = v;
    }
  }

  hideText() {
    if (this.text) {
      setRenderParams(this.text.gameObject, { alpha: 0 });
    }
  }

  showText() {
    if (this.text) {
      setRenderParams(this.text.gameObject, { alpha: 1 });
    }
  }

  // 按钮抖动效果
  playAddFeiliaoAnim(cb: () => void) {
    this.anim.play('addFeiliaoAnim');
    this.anim.once('finish', (name) => {
      cb();
    });
  }
}
