import { resource } from '@eva/eva.js';
import { NinePatch } from '@eva/plugin-renderer-nine-patch';
import BaseComponent from './BaseComponent';

export interface NineBgParams {
  resName: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  leftWidth: number;
  rightWidth: number;
  topHeight: number;
  bottomHeight: number;
}

export class NineBg extends BaseComponent {
  static override componentName = 'NineBg';
  private _params: NineBgParams;
  private _bg: NinePatch;

  override init(params: NineBgParams) {
    this._params = params;
    this.gameObject.transform.position = params.position;
    this.gameObject.transform.size = params.size;
    this.gameObject.transform.origin = { x: 0.5, y: 0.5 };
    this.gameObject.transform.anchor = { x: 0.5, y: 0.5 };
    resource
      .getResource(params.resName)
      .then(() => {
        this.gameObject.addComponent(NinePatch, {
          resource: params.resName,
          leftWidth: params.leftWidth,
          rightWidth: params.rightWidth,
          topHeight: params.topHeight,
          bottomHeight: params.bottomHeight,
        });
        this._bg = this.gameObject.getComponent(NinePatch) as NinePatch;
        const game = (window as any).__EVA_GAME_INSTANCE__?.game;
        if (game) {
          const sys = game.getSystem('NinePatch');
          game.canvas.addEventListener('webglcontextrestored', () => {
            sys.remove({
              type: 'REMOVE',
              gameObject: this.gameObject,
              component: this._bg,
              componentName: 'NinePatch',
            });
            setTimeout(() => {
              sys.add({
                type: 'ADD',
                gameObject: this.gameObject,
                component: this._bg,
                componentName: 'NinePatch',
              });
            }, 100);
          });
        }
      })
      .catch((error) => {
      });
  }

  updateBgRes(resName: string) {
    if (!this._bg) {
      return;
    }
    resource
      .getResource(resName)
      .then(() => {
        const { leftWidth, rightWidth, topHeight, bottomHeight } = this._params;
        this.gameObject.removeComponent(NinePatch);
        this._bg = this.gameObject.addComponent(NinePatch, {
          resource: resName,
          leftWidth,
          rightWidth,
          topHeight,
          bottomHeight,
        });
      })
      .catch((error) => {
      });
  }

  updateBgWidth(w: number) {
    if (w <= 0) {
      w = 0.0001;
    }
    this.gameObject.transform.size.width = w;
  }
}
