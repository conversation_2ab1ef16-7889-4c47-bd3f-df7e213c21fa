import BaseComponent from '../base/BaseComponent';
import { addPoint } from '../utils/point';
import { getScreenCenterBottom } from '../utils/screen';
import { StoreName } from '@/logic/type/store';
import mx from '@ali/pcom-mx';
import { addSpineResource } from '../resource'
import { Spine } from '@eva/plugin-renderer-spine';
import { GameObject } from '@eva/eva.js';

export interface DknBgAnimParams {
  resource: string;
  position: { x: number; y: number };
}

export default class DknBgAnim extends BaseComponent {
  static override componentName = 'DknBgAnim';
  private _anim: Spine | null;
  override init(params: DknBgAnimParams) {
    console.log('init dkbg');
    this.gameObject.transform.position = addPoint(getScreenCenterBottom(), {
      x: 0,
      // y: -542 - getStaticData().bgOffsetY,
      y: -542,
    });
    mx.store.on(
      StoreName.BgSpineImg,
      (v) => {
        if (!v) {
          return;
        }
        this.checkShowBgAnim();
        // mx.event.emit(MainAPI.ChangeBgAnim);
      },
      true,
    );
  }

  getSpineRes() {
    // let image = mx.store.get(StoreName.BgSpineImg);
    // let ske = mx.store.get(StoreName.BgSpineSke);
    // let atlas = mx.store.get(StoreName.BgSpineAtlas);
    const image = "https://gw.alicdn.com/imgextra/i4/O1CN01zUfN362860irahH5n_!!6000000007882-2-tps-750-1445.png";
    const ske = mx.store.get(StoreName.BgSpineSke);
    const atlas = mx.store.get(StoreName.BgSpineAtlas);
    return {
      image,
      atlas,
      ske,
    };
  }
  checkShowBgAnim() {
    const { image, atlas, ske } = this.getSpineRes();
    if (image && atlas && ske) {
      addSpineResource(image, {
        ske,
        atlas,
        image,
      });
      if (!this._anim) {
        const animObj = new GameObject('bganim', {});
        animObj.addComponent(Spine, {
          resource: image,
          animationName: 'animation',
          autoPlay: true,
        });
        this.gameObject.addChild(animObj);
        this._anim = animObj.getComponentType(Spine) as Spine;
      } else {
        if (this._anim.gameObject.parent) {
          this._anim.gameObject.remove();
        }
        this._anim.resource = image;
        this.gameObject.addChild(this._anim.gameObject);
      }
    } else if (this._anim && this._anim.gameObject.parent) {
      this._anim.gameObject.remove();
      this._anim = null;
    }
  }
}
