import mx from '@ali/pcom-mx';
import {getScreenCenterBottom, isHighMode} from "@/gameObject/utils/screen";
import { randomInRect } from '../utils/randomInRect';
import { delay, tick } from '../utils/delay';
import { addPoint, Point } from '../utils/point';
import BaseComponent from '../base/BaseComponent';
import { ObjectPool } from '../base/ObjectPool';
import { DailyFuqiDrop } from './DailyFuqiDrop';
import ButtonWatering from './ButtonWatering';
import {TOM_FERTILIZER_EVENT} from "@/gameObject/uc/tomorrowFertilizer/common/constant";

export interface DailyFuqiComParams { }

export default class DailyFuqiCom extends BaseComponent {
  static inst: DailyFuqiCom;
  static override componentName = 'DailyFuqiCom';
  // @ts-ignore
  private rainPool: ObjectPool<DailyFuqiDrop>;
  private defaultUrl = 'https://gw.alicdn.com/tfs/TB1pMKnHXT7gK0jSZFpXXaTkpXa-60-60.png';
  override init(params: DailyFuqiComParams) {
    DailyFuqiCom.inst = this;
    this.gameObject.transform.position = addPoint(getScreenCenterBottom(), { x: 150, y: isHighMode() ? -420 : -380 });
    this.putRain = this.putRain.bind(this);
    this.addEvt();
  }

  initPool() {
    let url = this.defaultUrl;
    // @ts-ignore
    this.rainPool = ObjectPool.createGameObject({
      initNum: 8,
      compType: DailyFuqiDrop,
      initParams: {
        resource: url,
      },
    });
    this.gameObject.addChild(this.rainPool.gameObject);
  }

  private getRain(pos?: Point) {
    if (this.rainPool.getItemNum() <= 0) {
      return null;
    }
    let url = this.defaultUrl;
    let startPos;
    if (pos) {
      startPos = randomInRect(pos.x, pos.y, 0, 0);
    } else {
      startPos = randomInRect(40, 120, 0, 0);
    }
    let rainDrop = this.rainPool.get({
      position: startPos,
      resource: url,
      size: { width: 50, height: 50 },
    });
    return rainDrop;
  }

  private putRain(rainDrop: DailyFuqiDrop) {
    this.rainPool.put(rainDrop);
  }

  // 肥料从明日肥料袋飞向按钮动画
  public playAnim(cb: () => void, pos?: Point) {
    if (!this.rainPool) {
      this.initPool();
    }

    let elpasedTime = 0;
    let intervalTime = 150;
    let durationTime = 1350;
    tick(intervalTime, () => {
      elpasedTime += intervalTime;
      let rainDrop = this.getRain(pos);
      if (elpasedTime >= durationTime || !rainDrop) {
        // this.endRain();
        cb();
        return false;
      }
      rainDrop.drop(this.putRain);
      // @ts-ignore
      this.gameObject.addChild(rainDrop.gameObject);
      return true;
    });
  }

  // 收明日肥料动画
  private addEvt() {
    mx.event.on(TOM_FERTILIZER_EVENT.FuqiColletedFlyAnim, (param) => {
      const { pos, cb = () => { } } = param || {};
      DailyFuqiCom.inst.playAnim(cb, pos);
      delay(400).then(() => {
        ButtonWatering.inst.playChatterAnim();
      });
    });
  }
}
