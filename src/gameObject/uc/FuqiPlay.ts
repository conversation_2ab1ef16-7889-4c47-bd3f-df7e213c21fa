import { createText } from '@/gameObject/utils/eva';
import { Text } from '@eva/plugin-renderer-text';
import mx from '@ali/pcom-mx';
import { StoreName } from "@/logic/type/store";
import { createFadeInAnimParams, createFadeOutAnimParams, createFuqiAnimParams } from '../utils/anim';
import { delay } from '../utils/delay';
import TransitionWithComplete from '../base/TransitionWithComplete';

export interface FuqiPlayParams {}

export interface FuqiPlayReuseParams {
  position: { x: number; y: number };
}

export class FuqiPlay extends TransitionWithComplete {
  static override componentName = 'FuqiPlay';
  private textComp: Text;
  override init(params: FuqiPlayParams) {
    this.gameObject.transform.origin = { x: 0.5, y: 0.5 };
    (this.gameObject as any).snapshotHidden = true;
    this.add('fuqifadein', [createFadeInAnimParams(this.gameObject, 300)]);
    this.add('fuqifadeout', [createFadeOutAnimParams(this.gameObject, 400)]);
  }

  fuqirise(v: number, onFinish: (FuqiPlay: FuqiPlay) => void, isFeiliao: boolean) {
    this.updateText(v, isFeiliao);
    this.add('fuqiupmove', [createFuqiAnimParams(this.gameObject, 0, { dt: 300, y: -300 }, { dt: 150, h: -20 })]);
    this.play('fuqifadein');
    this.play('fuqiupmove').then(() => {
      delay(1000).then(() => {
        this.play('fuqifadeout').then(() => {
          onFinish(this);
        });
      });
    });
  }

  private updateText(v: number, isFeiliao: boolean) {
    if (!this.textComp) {
      const go = createText(
        'text',
        {
          position: { x: 0, y: -45 },
          origin: { x: 0.5, y: 0.5 },
        },
        {
          text: '',
          style: {
            fontFamily: 'Heiti SC',
            fontSize: 30,
            fill: '#8A4A1E',
            stroke: '#FFF0C8',
            strokeThickness: 6,
            fontWeight: 'bold',
            // dropShadow:true,
            // dropShadowColor:"#76311c",
            // dropShadowAngle:-Math.PI/3,
          },
        },
      );
      this.gameObject.addChild(go);

      this.textComp = go.getComponentType(Text);
    }

    let costName = mx.store.get(StoreName.CostName);
    this.textComp.text = `浇灌${v}${costName}`;
  }

  onReuse(params: FuqiPlayReuseParams) {
    this.gameObject.transform.position = params.position;
  }
}
