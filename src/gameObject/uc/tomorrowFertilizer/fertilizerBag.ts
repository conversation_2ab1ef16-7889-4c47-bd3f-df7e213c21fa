import { GameObject } from '@eva/eva.js';
import { Img } from '@eva/plugin-renderer-img';
import { Text } from '@eva/plugin-renderer-text';
import { Render } from '@eva/plugin-renderer-render';
import { Event } from '@eva/plugin-renderer-event';
import BaseComponent from '../../base/BaseComponent';
import {
  addImageResource,
  getStringChineseLength,
  isEqual,
  playAnim,
  playOnce,
  stopAnim,
} from './common/utils';
import mx from '@ali/pcom-mx';
import { NinePatch } from '@eva/plugin-renderer-nine-patch';
import { Transition } from '@eva/plugin-transition';
import {
  createBtmInfoSwiperAnim,
  createFertilizerBagBeatingAnim,
  createFertiltzerBagAnim,
  createIncreaseTipAnim,
} from './common/anims';

import { RabbitType, TOM_FERTILIZER_EVENT, TOMORROW_BAG_BG } from './common/constant';
import { addPoint } from "@/gameObject/utils/point";
import { addSpineResource } from "@/gameObject/resource";
import {getScreenCenterBottom, isHighMode} from "@/gameObject/utils/screen";
import { Spine } from '@eva/plugin-renderer-spine';
import {ITomorrowGift} from "@/logic/store/models/app/typings";
import throttle from 'lodash.throttle';
import stat from "@/lib/stat";
import {delay} from "@/gameObject/utils/delay";

export default class FertilizerBag extends BaseComponent {
  static componentName = 'FertilizerBag';
  inst: FertilizerBag;
  private tomorrowGiftData: ITomorrowGift;
  // private inited = false;

  // 肥料袋
  private fertilizerBag: GameObject;
  private spine: Spine;
  private fertilizerBagAnim: Transition; // 整个明日肥料区域动画
  private bagNumBeatAnim: Transition; // 肥料袋and数值可领取时呼吸动画
  private isPlayBagBeatAnim = false; // 是否在播放可领取肥料袋呼吸动画

  // 立即领取按钮
  private receiveBtnRender: Render;
  // private receiveBtnText: Text;

  // 底部提示
  private btmTipRender: Render;
  private btmTipText: Text;
  private btmTipAnim: Transition;

  // 肥料数值
  private fertilizerText: Text;
  private fertilizerNumAnim: Transition;

  // 每次施肥+xx提示
  private increaseTipText: Text;
  private increaseTipAnim: Transition;

  init(params?: {}): void {
    this.inst = this;
    // 明日肥料袋位置
    this.fertilizerBag = new GameObject('fertilizerBag', {
      position: addPoint(getScreenCenterBottom(), { x: 284, y: isHighMode() ? -363 : -313}),
      size: { width: 160, height: 140 },
      origin: { x: 0.5, y: 0.5 },
    });
    // @ts-ignore
    // this.gameObject.snapshotHidden = true;
    this.gameObject.addChild(this.fertilizerBag);
    (this.gameObject as any).snapshotHidden = true;
    this.fertilizerBagAnim = createFertiltzerBagAnim(this.fertilizerBag);
    const evt = this.fertilizerBag.addComponent(new Event());
    // 监听点击事件
    evt.on('tap', throttle(this.onClickFertBag, 500));
    this.createFertilizerBag();
    this.createReceiveBtn();
    this.createBtmInfo();
    // mx.store.on('realExtraData.extraData.fertilizerPacket', this.dataChange, true);
    mx.event.on(TOM_FERTILIZER_EVENT.SHOW_BAG_CHATTER, this.showBagChatterAnim);
    mx.store.on('mainInfo.activityData.tomorrowGift', this.updateData)
  }

  onClickFertBag = () => {
    const {pointAmount, status, curTask } = this.tomorrowGiftData;
    let bag_status = 3
    switch (status) {
      case 'CAN_RECEIVE':
        bag_status = 0;
        break;
      case 'NOT_START':
        bag_status = 1;
        break;
      case 'ADVANCE_NOTICE':
        bag_status = 2;
        break;
      default:
    }

    stat.click('tomorrow_bag_click', {
      c: 'function',
      d: 'bag',
      bag_status,
      manure_quantity: pointAmount,
      taskclassify: curTask?.taskClassify,
      groupcode: curTask?.groupCode,
      task_id: curTask?.id,
      task_name: curTask?.name,
      award_amount: pointAmount,
    })
    mx.event.emit(TOM_FERTILIZER_EVENT.ClickTomorrowFertBag, this.tomorrowGiftData);
  }

  onDestroy(): void {
    mx.event.off(TOM_FERTILIZER_EVENT.SHOW_BAG_CHATTER, this.showBagChatterAnim);
  }

  // 播放收肥料抖动动画
  showBagChatterAnim = (v = 7) => {
    if (this.fertilizerBagAnim) {
      playAnim(this.fertilizerBagAnim, 'chatter', v);
      // chatter动画会放大，最后需要复原。
      delay(1200).then(() => {
        playAnim(this.fertilizerBagAnim, 'goback', 1)
      })
    }
  };

  // 数据改变状态变化
  updateData = async (data: ITomorrowGift) => {
    const isInit = !this.tomorrowGiftData
    if (!data || isEqual(this.tomorrowGiftData, data)) {
      return
    }
    const { status, pointAmount } = data;
    const isStatusChange = this.tomorrowGiftData && this.tomorrowGiftData.status !== status;
    this.tomorrowGiftData = Object.assign({}, data);
    // 状态切换
    if (isStatusChange) {
      await playOnce(this.fertilizerBagAnim, 'fadeOut');
    }
    this.spine.play('', false)
    if (status === 'NOT_START') {
      // const tomBtmTipText = `${relativeTimeFormat(receiveStartTime, curTime)}点可领`;
      const tomBtmTipText = `今日可领`;
      // const toDayBtmTipText = `${relativeTimeFormat(receiveStartTime, curTime)}点可领`;
      const toDayBtmTipText = `今日可领`;
      this.showBtmTip();
      const needAnim = !isStatusChange && this.btmTipText.text !== tomBtmTipText;
      this.setBtmTipText({
        tipText: toDayBtmTipText,
        needAppearAnim: needAnim,
      });
      this.setFertNumText(pointAmount, needAnim);

      // 停止肥料袋呼吸动效
      // this.stopFertBagBeatAnim();
    } else if (status === 'CAN_RECEIVE') {
      this.showReceiveBtn();
      this.setFertNumText(pointAmount);
      // 兔子挥手循环播放
      this.spine.play('', true)
    } else if (status === 'ADVANCE_NOTICE') {
      this.showBtmTip();
      this.setBtmTipText({
        // tipText: `${relativeTimeFormat(receiveStartTime, curTime)}点可领`,
        tipText: `明日可领取`,
      });
      this.setFertNumText(pointAmount);
      const { completeTimes, maxTimes } = this.tomorrowGiftData
      if (completeTimes < maxTimes) {
        this.showIncreaseTip(data.pointAmountPerTimes, false);
      } else {
        // 肥料袋满了的提示：进入页面不提示，施肥后提示。
        !isInit && this.showIncreaseTip(data.pointAmountPerTimes, true);
      }
    }

    if (isStatusChange) {
      await playOnce(this.fertilizerBagAnim, 'fadeIn');
    }
  }

  // 显示领取按钮
  showReceiveBtn() {
    this.receiveBtnRender.visible = true;
    this.btmTipRender.visible = false;
  }

  getFertNumFontSize(num) {
    return Number(num) < 100000 ? 29 : 24;
  }

  // 显示领取提示
  showBtmTip() {
    this.receiveBtnRender.visible = false;
    this.btmTipRender.visible = true;
  }

  // 设置肥料数字
  setFertNumText(fertNum: number | undefined, needAppearAnim?: boolean) {
    if (!fertNum) return;
    if (needAppearAnim) {
      this.animToAppearUp(this.fertilizerNumAnim, !!this.fertilizerText.text, () => {
        this.fertilizerText.text = String(fertNum);
      });
    } else {
      this.fertilizerText.text = String(fertNum);
    }
    // @ts-ignore
    this.fertilizerText.style.fontSize = this.getFertNumFontSize(fertNum);
  }

  // 设置底部提示
  setBtmTipText(params: { tipText: string; needAppearAnim?: boolean; btmTipTextFill?: string }) {
    const { tipText, needAppearAnim = false, btmTipTextFill = '#A7714D' } = params || {};
    if (!tipText) return;
    if (needAppearAnim) {
      this.animToAppearUp(this.btmTipAnim, !!this.btmTipText.text, () => {
        this.btmTipText.text = tipText;
      });
    } else {
      this.btmTipText.text = tipText;
    }
    // @ts-ignore
    this.btmTipText.style.fill = btmTipTextFill;
  }

  // 文字出现动画
  animToAppearUp(anim: Transition, fadeLast = true, onAppear?: () => void) {
    if (anim) {
      anim.off('finish');
      stopAnim(anim, ['appearUp', 'fadeOut']);
      if (fadeLast) {
        playOnce(anim, 'fadeOut');
        anim.once('finish', async (name) => {
          if (name === 'fadeOut') {
            onAppear && onAppear();
            playOnce(anim, 'appearUp');
          }
        });
      } else {
        playOnce(anim, 'appearUp');
        onAppear && onAppear();
      }
    }
  }

  getBagBg = () => {
    return TOMORROW_BAG_BG[`${RabbitType.WHITE}_day`];
  };

  // 创建肥料袋子
  async createFertilizerBag() {
    const fertilizerBag = new GameObject('fertilizerBag', {
      size: { width: 150, height: 103 },
      origin: { x: 0.5, y: 0 },
      position: { x: -10, y: -4 },
      anchor: { x: 0.5, y: 0 },
    });
    fertilizerBag.addComponent(
      new Render({
        sortableChildren: true,
      }),
    );

    // const resName = addImageResource(this.getBagBg());
    // (window as any).__evaSFSP && (window as any).__evaSFSP.addResource(resName);
    // const img = createImageFromUrl(resName, {
    //   size: { width: 150, height: 103 },
    //   position: { x: 18, y: 0 },
    // });
    // fertilizerBag.addComponent(new Img({ resource: resName }));
    this.bagNumBeatAnim = createFertilizerBagBeatingAnim(fertilizerBag);
    const fertilizerNum = new GameObject('fertilizerNum', {
      origin: { x: 0.5, y: 0.5 },
      anchor: { x: 0.5, y: 0 },
      position: { x: 0, y: 82 },
    });
    fertilizerNum.addComponent(new Render({ zIndex: 2 }));
    this.fertilizerText = fertilizerNum.addComponent(
      new Text({
        text: '',
        style: {
          fontFamily: 'Helvetica',
          fontSize: 28,
          fill: '#7F370A',
          fontWeight: 'bold',
        },
      }),
    );
    this.fertilizerNumAnim = createBtmInfoSwiperAnim(fertilizerNum);
    fertilizerBag.addChild(fertilizerNum);
    this.fertilizerBag.addChild(fertilizerBag);
    // 肥料袋背景
    const { image, atlas, ske } = this.getBagBg();
    const resource = addSpineResource(image, { ske, atlas, image });
    const rabbitGameObject = new GameObject('rabbit_spine', {
      origin: { x: 1, y: 0 },
      position: { x: 5, y: 34 },
      anchor: { x: 0.5, y: 1 },
    });
    rabbitGameObject.addComponent(new Render({ zIndex: 1 }));
    this.spine = new Spine({
      resource,
      animationName: 'animation',
      autoPlay: true,
    });
    rabbitGameObject.addComponent(this.spine);
    this.spine.once('loaded', () => {
      this.spine.setDefaultMix(0.1);
    });
    fertilizerBag.addChild(rabbitGameObject);
  }

  // 播放肥料袋子呼吸效果, 肥料袋子调起来，UI不需要
  playFertBagBeatAnim = () => {
    if (this.bagNumBeatAnim && !this.isPlayBagBeatAnim) {
      this.isPlayBagBeatAnim = true;
      playAnim(this.bagNumBeatAnim, 'fertilizerBagBeating', -1);
    }
  };

  stopFertBagBeatAnim = () => {
    if (this.bagNumBeatAnim && this.isPlayBagBeatAnim) {
      this.isPlayBagBeatAnim = false;
      stopAnim(this.bagNumBeatAnim, 'fertilizerBagBeating');
      this.bagNumBeatAnim.gameObject.transform.scale = { x: 1, y: 1 };
    }
  };

  // 创建收取按钮
  createReceiveBtn() {
    const receive_btn = new GameObject('receive_btn', {
      size: { width: 145, height: 47 },
      origin: { x: 0.5, y: 0 },
      position: { x: 0, y: 95 },
      anchor: { x: 0.5, y: 0 },
    });
    const btnImgUrl = addImageResource(
      'https://gw.alicdn.com/imgextra/i2/O1CN01VuNHO21zvuuDMRHYT_!!6000000006777-2-tps-145-47.png',
    );
    receive_btn.addComponent(new Img({ resource: btnImgUrl }));
    this.receiveBtnRender = receive_btn.addComponent(new Render({ visible: false }));
    this.fertilizerBag.addChild(receive_btn);

    const receive_btn_text = new GameObject('receive_btn_text', {
      origin: { x: 0.5, y: 0.5 },
      anchor: { x: 0.5, y: 0 },
      position: { x: 0, y: 20 },
    });
    receive_btn_text.addComponent(
      new Text({
        text: '点击领取',
        style: {
          fontFamily: 'PingFangSC-Medium',
          fontSize: 24,
          fill: '#fff',
        },
      }),
    );
    receive_btn.addChild(receive_btn_text);
  }

  // 创建底部提示
  createBtmInfo() {
    const fert_btm_info = new GameObject('fert_btm_info', {
      size: { width: 160, height: 52 },
      position: { x: 0, y: 88 },
    });
    const resName = addImageResource(
      'https://gw.alicdn.com/imgextra/i1/O1CN01ZczCVg20IMMoVJEAo_!!6000000006826-2-tps-160-52.png',
    );
    fert_btm_info.addComponent(new Img({ resource: resName }));
    this.btmTipRender = fert_btm_info.addComponent(new Render({ visible: false }));
    this.fertilizerBag.addChild(fert_btm_info);

    const btm_info_text = new GameObject('btm_info_text', {
      origin: { x: 0.5, y: 0.5 },
      anchor: { x: 0.5, y: 0.54 },
    });
    this.btmTipText = btm_info_text.addComponent(
      new Text({
        text: '',
        style: {
          fontFamily: 'PingFangSC-Medium',
          fontSize: 22,
          fill: '#A7714D',
        },
      }),
    );
    this.btmTipAnim = createBtmInfoSwiperAnim(btm_info_text);
    fert_btm_info.addChild(btm_info_text);
  }

  // 创建浇水出现的顶部tips
  createIncreaseTip() {
    if (this.increaseTipText) {
      return;
    }
    const bgResName = addImageResource(
      'https://gw.alicdn.com/imgextra/i4/O1CN01jkO1KJ1ZiTGbyh4hM_!!6000000003228-2-tps-168-50.png',
    );
    const increase_tip = new GameObject('increase_tip', {
      size: { width: 168, height: 50 },
      origin: { x: 0.5, y: 1 },
      anchor: { x: 0.5, y: 0 },
      position: { x: 0, y: 5 },
    });

    increase_tip.addComponent(
      new NinePatch({
        resource: bgResName,
        leftWidth: 10,
        rightWidth: 10,
        topHeight: 10,
        bottomHeight: 18,
      }),
    );
    increase_tip.addComponent(new Render({ alpha: 0 }));
    this.increaseTipAnim = createIncreaseTipAnim(increase_tip);
    this.fertilizerBag.addChild(increase_tip);

    const increase_tip_text = new GameObject('increase_tip_text', {
      origin: { x: 0.5, y: 0.5 },
      anchor: { x: 0.5, y: 0 },
      position: { x: 0, y: 22 },
    });
    this.increaseTipText = increase_tip_text.addComponent(
      new Text({
        text: '',
        style: {
          fontFamily: 'PingFangSC-Medium',
          fontSize: 22,
          fill: '#3F9A32',
        },
      }),
    );
    increase_tip.addChild(increase_tip_text);
  }

  private increaseTipTimer: number;

  // 播放顶部tips出现动画，修改tips宽度
  showIncreaseTip = (pointAmountPerTimes = 100, full: boolean) => {
    let text = full ? '肥料袋满咯' : `每次施肥+${pointAmountPerTimes}`
    const len = getStringChineseLength(text);
    const textWidth = len * 24;

    // eslint-disable-next-line no-negated-condition
    this.createIncreaseTip();
    this.increaseTipAnim.gameObject.transform.size.width = textWidth + 20;
    this.increaseTipText.text = text;
    // 展示当前
    if (!this.increaseTipTimer) {
      playOnce(this.increaseTipAnim, 'appear');
    }
    clearTimeout(this.increaseTipTimer);
    this.increaseTipTimer = window.setTimeout(() => {
      clearTimeout(this.increaseTipTimer);
      this.increaseTipTimer = 0;
      playOnce(this.increaseTipAnim, 'disappear');
    }, full ? 5000 : 3000);
  };
}
