import { useEffect, useRef } from 'react';
import mx from '@ali/pcom-mx';
import FertilizerBag from './fertilizerBag';
// @ts-ignore
import {clickFertilizerBag} from './action';
import { GameObject } from '@eva/eva.js';
import {TOM_FERTILIZER_EVENT} from "@/gameObject/uc/tomorrowFertilizer/common/constant";
import stat from '@/lib/stat';
import { IActivityData } from '@/logic/store/models/app/typings';

export default function TomorrowFertilizer() {
  const bagComponent = useRef<FertilizerBag>(null);

  const createGame = () => {
    // 获取完整状态树
    const fontExtendGame = mx.store.get('FontExtendGame');
    // const fertilizerPacket = mx.store.get('realExtraData.extraData.fertilizerPacket');
    const tomorrowGift: IActivityData['tomorrowGift'] = mx.store.get('mainInfo.activityData.tomorrowGift') || {};    
    if (!bagComponent.current && fontExtendGame && tomorrowGift?.status !== 'NONE') {
      const obj = new GameObject('fertilizerBag', {});
      // @ts-ignore
      bagComponent.current = obj.addComponent(new FertilizerBag({}));
      fontExtendGame.parent.addChild(obj);
      
      /** 明日肥料礼包曝光 */
      let bag_status = 3
      switch (tomorrowGift?.status) {
        case 'CAN_RECEIVE':
          bag_status = 0;
          break;
        case 'NOT_START':
          bag_status = 1;
          break;
        case 'ADVANCE_NOTICE':
          bag_status = 2;
          break;
        default:
      }
      stat.exposure('tomorrow_bag_exposure', {
        c: 'function',
        d: 'bag',
        bag_status,
        manure_quantity: tomorrowGift?.pointAmount,
        taskclassify: tomorrowGift?.curTask?.taskClassify,
        groupcode: tomorrowGift?.curTask?.groupCode,
        task_id: tomorrowGift?.curTask?.id,
        task_name: tomorrowGift?.curTask?.name,
        award_amount: tomorrowGift?.pointAmount
      })
    }
  };

  const onTomorrowGiftChange = (data) => {
    if (!data) return;
    if (!bagComponent.current) {
      createGame();
    }
  };

  useEffect(() => {
    mx.event.on(TOM_FERTILIZER_EVENT.ClickTomorrowFertBag, clickFertilizerBag);
    mx.store.on('mainInfo.activityData.tomorrowGift', onTomorrowGiftChange)
    // mx.store.on('FontExtendGame', createGame);
    return () => {
      mx.event.off(TOM_FERTILIZER_EVENT.ClickTomorrowFertBag, clickFertilizerBag);
      mx.store.off('mainInfo.activityData.tomorrowGift', onTomorrowGiftChange);
      // mx.store.off('FontExtendGame', createGame);
    };
  }, []);
  return null;
}
