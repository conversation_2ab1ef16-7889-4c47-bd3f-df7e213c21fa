import {
  // TOM_FERTILIZER_BIZ_CODE,
  TOM_FERTILIZER_EVENT,
  TOM_FERTILIZER_STORE,
} from '../common/constant';
// import { showModal } from './modal';
import mx from '@ali/pcom-mx';
import dispatch from "@/logic/store";
// import { delay } from '@strategies/game/utils/helpers';
// import { tomFertilizerRequest } from '../services/service';
import toast from "@/lib/universal-toast/component/toast";
import baseModal from '@/lib/modal';
import {MODAL_ID} from "@/components/modals/ModalAll";
import {ITomorrowGift} from "@/logic/store/models/app/typings";
import {delay} from "@/gameObject/utils/delay";
import { TasklistSource } from '@/pages/index/utils';
import { formatTimestamp } from '@/lib/utils/date';
import { MainAPI } from '@/logic/type/event';

let fertClkIsDoing = false;

// 点击明日肥料袋
export async function clickFertilizerBag(fertData: ITomorrowGift) {
  try {
    // 动画效果
    // mx.event.emit(TOM_FERTILIZER_EVENT.FuqiColletedFlyAnim, {
    //   pos: { x: 40, y: 120 },
    // });
    // return

    const loginStatus = await dispatch.user.checkLoginAndBind(100, 1)
    if (!loginStatus) return
    if (fertClkIsDoing) return;
    if (fertData.status === 'NOT_START' || fertData.status === 'ADVANCE_NOTICE') {
      setTimeout(() => {
        toast.show(fertData.status === 'ADVANCE_NOTICE'
          ? '今日肥料包已领，去做任务赚更多吧'
          : `${formatTimestamp(fertData?.receiveStartTime, 'hh:mm')}后才能领，先去做任务赚更多吧`);
        mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: TasklistSource.tomorrow_fertilizer });
        // baseModal.open(MODAL_ID.RECEIVE_FERTILIZER, {
        //   receiveTime: fertData.receiveStartTime,
        //   pointAmount: fertData.pointAmount,
        //   status: fertData.status
        // })
      }, 80);
      return;
    }
    if (fertData.status === "CAN_RECEIVE") {
      fertClkIsDoing = true;
      mx.store.update(TOM_FERTILIZER_STORE.IS_COLLECTING, true);
      const drawRes = await dispatch.task.drawCustomTaskAward('TOMORROW_GIFT')
      if (drawRes) {
        // // 收肥料动画
        // mx.event.emit(TOM_FERTILIZER_EVENT.FuqiColletedFlyAnim, {
        //   pos: { x: 40, y: 120 },
        // });
        // // 肥料袋子抖动动画
        // mx.event.emit(TOM_FERTILIZER_EVENT.SHOW_BAG_CHATTER);
        toast.show(`已获得${drawRes?.pointAmount}肥料，做任务能赚更多哟`);
        mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: TasklistSource.tomorrow_fertilizer });
        await delay(1300).then(async () => {
          await dispatch.app.updateHomeData()
          fertClkIsDoing = false;
        })
      } else {
        fertClkIsDoing = false;
      }
    }
  } catch (error) {
    console.log(error);
    fertClkIsDoing = false;
    toast.show('网络异常，请稍后再试~');
  }
  mx.store.update(TOM_FERTILIZER_STORE.IS_COLLECTING, false);
}
