import { GameObject, resource, RESOURCE_TYPE } from '@eva/eva.js';
import { Img } from '@eva/plugin-renderer-img';
import { Transition } from '@eva/plugin-transition';

export function isTrue(val: any) {
  return String(val) === 'true';
}

export function isNumEqual(val1: any, val2: any) {
  if (isNaN(Number(val1)) || isNaN(Number(val2))) return false;
  return String(val1) === String(val2);
}

export function completion(val: number) {
  return +val >= 10 ? val : `0${val}`;
}

export function formatTime(time: number) {
  const days = Math.floor(time / 1000 / 60 / 60 / 24);
  const hours = Math.floor((time % 86400000) / 3600000);
  const minutes = Math.floor((time % 3600000) / 60000);
  const seconds = Math.floor((time % 60000) / 1000);
  return `${days ? `${days}天` : ''}${completion(hours)}:${completion(minutes)}:${completion(seconds)}`;
}

export function safeDate(datetime): Date | null {
  if (!datetime) {
    return null;
  }
  let date;
  if (datetime instanceof Date) {
    date = datetime;
  } else if (typeof datetime === 'number') {
    date = new Date(datetime);
  } else if (typeof datetime === 'string') {
    if (isNaN(+datetime)) {
      // 兼容部分iOS机器上new Date('2020-01-15 16:00:00')为NaN
      date = new Date(datetime);

      if (date == 'Invalid Date') {
        date = new Date(datetime.replace(/-/g, '/'));
      }
    } else {
      date = new Date(+datetime);
    }
  }

  if (date == 'Invalid Date') {
    return null;
  }
  return date;
}

export function toLocaleDateString(date: Date) {
  if (date instanceof Date) {
    return `${date.getFullYear()}-${completion(date.getMonth() + 1)}-${completion(date.getDate())}`;
  }
  return null;
}

export function relativeTimeFormat(targetTime: number, currentTime: number, isNeedMin?: boolean) {
  const targetDate = safeDate(targetTime);
  const currentDate = safeDate(currentTime);
  if (!targetDate || !currentDate) return '';
  const targetLocaleDate = safeDate(toLocaleDateString(targetDate));
  const currentLocaleDate = safeDate(toLocaleDateString(currentDate));
  if (!targetLocaleDate || !currentLocaleDate) return '';
  const timeDiff = targetLocaleDate.getTime() - currentLocaleDate.getTime();
  let dayDiff = Math.floor(timeDiff / 86400000);
  let targetHour = targetDate.getHours();
  if (targetHour === 0) {
    targetHour = 24;
    dayDiff = Math.max(dayDiff - 1, 0);
  }
  const targetMin = completion(targetDate.getMinutes());

  if (dayDiff === 0) {
    return isNeedMin ? `今日${targetHour}:${targetMin}` : `今日${targetHour}`;
  } else if (dayDiff === 1) {
    return isNeedMin ? `明日${targetHour}:${targetMin}` : `明日${targetHour}`;
  } else if (dayDiff > 0) {
    return isNeedMin ? `${dayDiff}日${targetHour}:${targetMin}` : `${dayDiff}日${targetHour}`;
  } else {
    return isNeedMin ? `--:--` : '--';
  }
}

export function addImageResource(img: string) {
  const url = img;
  // @ts-ignore
  if (resource.resourcesMap[img]) {
    return img;
  }
  resource.addResource([
    {
      type: RESOURCE_TYPE.IMAGE,
      name: img,
      src: {
        image: {
          type: 'png',
          url: url,
        },
      },
    },
  ]);
  return img;
}

export function addImageFromUrl(obj: GameObject, url: string) {
  addImageResource(url);
  obj.addComponent(new Img({ resource: url }));
}

export function playOnce(anim: Transition, animName: string) {
  return new Promise((resolve, reject) => {
    if (!anim?.gameObject) {
      return;
    }
    anim.play(animName, 1);
    anim.once('finish', (a) => {
      resolve(true);
    });
  });
}

// 兼容stop
export function stopAnim(anim: Transition, animName: string | string[]) {
  if (typeof animName === 'string') {
    // @ts-ignore
    if (anim?.animations?.[animName]) {
      anim.stop(animName);
    }
  } else if (Array.isArray(animName)) {
    for (const nameItem of animName) {
      // @ts-ignore
      if (anim?.animations?.[nameItem]) {
        anim.stop(nameItem);
      }
    }
  }
}

export function playAnim(anim: Transition, animName: string, times = 1) {
  if (anim?.gameObject && anim?.group[animName]) {
    stopAnim(anim, animName);
    anim.play(animName, times);
  }
}

// 判断汉字 真实长度
export function getStringChineseLength(str: string) {
  if (!str) {
    return 0;
  }
  // const reg = /[\u4e00-\u9fa5]/g;
  const reg = /[^\x00-\xff]/g;
  // @ts-ignore
  const _chineseCharNum = ~~(str.match(reg) && str.match(reg).length);
  const realLength = _chineseCharNum + Math.ceil((str.length - _chineseCharNum) / 2);
  return realLength;
}

export function isType(val: any, type: string) {
  return Object.prototype.toString.call(val) === `[object ${type}]`;
}

/**
 *
 * @param value 值
 * @param other 对比值
 * @returns boolean 是否相对
 */
export function isEqual(value: any, other: any) {
  if (value === other) return true;
  if (Object.prototype.toString.call(value) !== Object.prototype.toString.call(other)) return false;
  if (typeof value === 'object' || typeof other === 'object') {
    let valueProps = Object.keys(value);
    let otherProps = Object.keys(other);
    if (valueProps.length !== otherProps.length) {
      return false;
    }
    for (const key of valueProps) {
      if (Object.prototype.hasOwnProperty.call(other, key)) {
        if (!isEqual(value[key], other[key])) {
          return false;
        }
      } else {
        return false;
      }
    }
    return true;
  }
  return false;
}
