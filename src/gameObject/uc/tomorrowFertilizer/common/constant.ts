export const TOM_FERTILIZER_BIZ_CODE = 'fertilizerPacket'; // extar接口参数

export const TOM_FERTILIZER_EXTRA_FROM = 'fertilizerPacketRefresh'; // extra接口入参用

export const AlibabaSans102Font =
  'https://g.alicdn.com/eva-assets/1be25eeff944c7df570668ce2e28af52/0.0.1/tmp/f9265c2/a2f95c84-904d-450d-b211-01e81b8cd60a.otf';
export const FZLANTY_CUK_GBK1_FONT =
  'https://g.alicdn.com/eva-assets/601d3a11b8a00d0ae8efd059bcd367bf/0.0.1/tmp/b18528d/314c9711-a039-4007-9875-e3c4424274ce.otf';

const TOM_FERTILIZER = `secondary-strategy.tomorrowFertiltzer`;

export const TOM_FERTILIZER_EVENT = {
  SHOW_INCREASE_TIP: `${TOM_FERTILIZER}.showIncreaseTip`,
  SHOW_BAG_CHATTER: `${TOM_FERTILIZER}.showBagChatterAnim`,
  // 游戏区飞肥料逻辑
  FuqiColletedFlyAnim: 'showFuqiColletedFlyAnim',
  RefreshAccountData: 'toBiz_refreshAccountData', // 刷新主按钮肥料数值
  ClickTomorrowFertBag: 'clickTomorrowFertBag', // 点击肥料袋子
  DelayActivityTimeOut: 'DelayActivityTimeOut', // 延时活动倒计时结束
  DelayActivitySuccess: 'DelayActivitySuccess', // 领取肥料成功
};

export const TOM_FERTILIZER_STORE = {
  IS_COLLECTING: 'tomorrowFertilezer.isCollecting',
};
export enum RabbitType {
  /** 白兔子 */
  WHITE = 'WHITE',
}

export const TOMORROW_BAG_BG = {
  [`${RabbitType.WHITE}_day`]: {
    image: 'https://gw.alicdn.com/imgextra/i2/O1CN01Zoqkbi1J5yB8gqlIs_!!************8-2-tps-141-141.png',
    ske: 'https://g.alicdn.com/eva-assets/0448409863dc4a6623a251dc5af2892a/0.0.1/tmp/eb118bc/64c5bfd4-8899-47fc-99c9-dc986519a523.json',
    atlas: 'https://g.alicdn.com/eva-assets/9a28a2bb302b592ccdfd867ee0496622/0.0.1/tmp/2165a60/d4952088-d0a2-43db-9fe9-a293989f11d5.atlas',
  },
  [`${RabbitType.WHITE}_night`]: {
    image: 'https://gw.alicdn.com/imgextra/i2/O1CN01Zoqkbi1J5yB8gqlIs_!!************8-2-tps-141-141.png',
    ske: 'https://g.alicdn.com/eva-assets/0448409863dc4a6623a251dc5af2892a/0.0.1/tmp/eb118bc/64c5bfd4-8899-47fc-99c9-dc986519a523.json',
    atlas: 'https://g.alicdn.com/eva-assets/9a28a2bb302b592ccdfd867ee0496622/0.0.1/tmp/2165a60/d4952088-d0a2-43db-9fe9-a293989f11d5.atlas',
  },
  default: {
    image: 'https://gw.alicdn.com/imgextra/i2/O1CN01Zoqkbi1J5yB8gqlIs_!!************8-2-tps-141-141.png',
    ske: 'https://g.alicdn.com/eva-assets/0448409863dc4a6623a251dc5af2892a/0.0.1/tmp/eb118bc/64c5bfd4-8899-47fc-99c9-dc986519a523.json',
    atlas: 'https://g.alicdn.com/eva-assets/9a28a2bb302b592ccdfd867ee0496622/0.0.1/tmp/2165a60/d4952088-d0a2-43db-9fe9-a293989f11d5.atlas',
  }
};
