let cbs = [];

let singleInterval;

export const myInterval = (cb: Function, interval: number[] | number = [2500, 5000], preDeal?: boolean) => {
  let lastTime;
  let flag = 0;
  let curInterval = typeof interval === 'number' ? interval : interval[0];
  let requestAnimationFrameid = null;
  const cancel = () => {
    if (requestAnimationFrameid) {
      cancelAnimationFrame(requestAnimationFrameid);
      requestAnimationFrameid = null;
    }
  };
  const update = () => {
    // @ts-ignore
    requestAnimationFrameid = requestAnimationFrame(t => {
      if (!lastTime) {
        lastTime = t;
      }
      if (t - lastTime >= curInterval) {
        lastTime = t;
        flag++;
        curInterval = typeof interval === 'number' ? interval : interval[flag % interval.length];
        cb && cb(curInterval);
      }
      update();
    });
  };
  if (preDeal) {
    cb && cb(curInterval);
  }
  update();

  return cancel;
};

function intervalCb() {
  // @ts-ignore
  cbs.forEach(cb => cb());
}

interface IntervalOptions {
  leading?: boolean;
}

const myIntervalSingle = (cb: Function, time: number[] | number, options?: IntervalOptions) => {
  // @ts-ignore
  cbs.push(cb);
  if (options?.leading) {
    cb && cb();
  }

  if (!singleInterval) {
    singleInterval = myInterval(intervalCb, time);
  }
  return () => {
    cbs = cbs.filter(item => item !== cb);
    if (cbs.length === 0) {
      singleInterval();
      singleInterval = null;
    }
  };
};
export default myIntervalSingle;
