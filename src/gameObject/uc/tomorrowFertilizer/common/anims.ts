import { GameObject } from '@eva/eva.js';
import { Render } from '@eva/plugin-renderer-render';
import { Transition } from '@eva/plugin-transition';

export function createIncreaseTipAnim(node: GameObject, duration: number = 3000) {
  let anim = node.getComponent(Transition);
  if (!anim) {
    anim = node.addComponent(new Transition());
  }
  let render = node.getComponent(Render);
  if (!render) {
    render = node.addComponent(new Render());
  }
  const { transform } = node;
  const originY = transform.position.y;
  anim.group = {
    show: [
      {
        name: 'position.y',
        component: transform,
        values: [
          {
            time: 0,
            value: 30,
            tween: 'linear',
          },
          {
            time: 300,
            value: originY,
            tween: 'linear',
          },
          {
            time: duration,
            value: originY,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 0,
            tween: 'linear',
          },
          {
            time: 300,
            value: 1,
            tween: 'linear',
          },
          {
            time: duration - 200,
            value: 1,
            tween: 'linear',
          },
          {
            time: duration,
            value: 0,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'visible',
        component: render,
        values: [
          {
            time: 0,
            value: true,
            tween: 'linear',
          },
          {
            time: duration,
            value: false,
            tween: 'linear',
          },
        ],
      },
    ],
    appear: [
      {
        name: 'position.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originY + 10,
            tween: 'linear',
          },
          {
            time: 300,
            value: originY,
            tween: 'linear',
          }
        ],
      },
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 0,
            tween: 'linear',
          },
          {
            time: 300,
            value: 1,
            tween: 'linear',
          },
        ],
      }
    ],
    disappear: [
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 200,
            value: 0,
            tween: 'linear',
          },
        ],
      }
    ]
  };

  return anim;
}


export function createBtmInfoSwiperAnim(node: GameObject) {
  let anim = node.getComponent(Transition);
  if (!anim) {
    anim = node.addComponent(new Transition());
  }
  let render = node.getComponent(Render);
  if (!render) {
    render = node.addComponent(new Render());
  }
  const { transform } = node;
  const originY = transform.position.y;
  anim.group = {
    fadeOut: [
      {
        name: 'position.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originY,
            tween: 'linear',
          },
          {
            time: 300,
            value: originY - 5,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 300,
            value: 0,
            tween: 'linear',
          },
        ],
      },
    ],
    appearUp: [
      {
        name: 'position.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originY + 5,
            tween: 'linear',
          },
          {
            time: 300,
            value: originY,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 0,
            tween: 'linear',
          },
          {
            time: 300,
            value: 1,
            tween: 'linear',
          },
        ],
      },
    ],
  };

  return anim;
}

export function createFertiltzerBagAnim(node: GameObject) {
  let anim = node.getComponent(Transition);
  if (!anim) {
    anim = node.addComponent(new Transition());
  }
  let render = node.getComponent(Render);
  if (!render) {
    render = node.addComponent(new Render());
  }
  const { transform } = node;
  const originScale = transform.scale;
  const originPos = transform.position;
  anim.group = {
    chatter: [
      {
        name: 'scale.x',
        component: transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 80,
            value: 0.98,
            tween: 'ease-out',
          },
          {
            time: 160,
            value: 1.1,
            tween: 'ease-out',
          },
        ],
      },
      {
        name: 'scale.y',
        component: transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 80,
            value: 0.98,
            tween: 'ease-out',
          },
          {
            time: 160,
            value: 1.1,
            tween: 'ease-out',
          },
        ],
      },
    ],
    goback: [
      {
        name: 'scale.x',
        component: transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 60,
            value: 1,
            tween: 'ease-out',
          },
        ],
      },
      {
        name: 'scale.y',
        component: transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 60,
            value: 1,
            tween: 'ease-out',
          },
        ],
      },
    ],
    fadeOutIn: [
      {
        name: 'scale.x',
        component: transform,
        values: [
          {
            time: 0,
            value: originScale.x,
            tween: 'linear',
          },
          {
            time: 150,
            value: originScale.x / 2,
            tween: 'linear',
          },
          {
            time: 300,
            value: originScale.x,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'scale.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originScale.y,
            tween: 'linear',
          },
          {
            time: 150,
            value: originScale.y / 2,
            tween: 'linear',
          },
          {
            time: 300,
            value: originScale.y,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 150,
            value: 0,
            tween: 'linear',
          },
          {
            time: 300,
            value: 1,
            tween: 'linear',
          },
        ],
      },
    ],
    fadeOut: [
      {
        name: 'scale.x',
        component: transform,
        values: [
          {
            time: 0,
            value: originScale.x,
            tween: 'linear',
          },
          {
            time: 300,
            value: originScale.x * 0.9,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'scale.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originScale.y,
            tween: 'linear',
          },
          {
            time: 300,
            value: originScale.y * 0.9,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 180,
            value: 0,
            tween: 'linear',
          },
        ],
      },
    ],
    fadeIn: [
      {
        name: 'position.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originPos.y - 10,
            tween: 'linear',
          },
          {
            time: 230,
            value: originPos.y + 5,
            tween: 'linear',
          },
          {
            time: 370,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 470,
            value: originPos.y,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'scale.x',
        component: transform,
        values: [
          {
            time: 0,
            value: originScale.x * 0.8,
            tween: 'linear',
          },
          {
            time: 230,
            value: originScale.x,
            tween: 'linear',
          },
          {
            time: 370,
            value: originScale.x * 0.95,
            tween: 'linear',
          },
          {
            time: 370,
            value: originScale.x,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'scale.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originScale.y * 0.8,
            tween: 'linear',
          },
          {
            time: 230,
            value: originScale.y,
            tween: 'linear',
          },
          {
            time: 370,
            value: originScale.y * 1.05,
            tween: 'linear',
          },
          {
            time: 470,
            value: originScale.y,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'alpha',
        component: render,
        values: [
          {
            time: 0,
            value: 0,
            tween: 'linear',
          },
          {
            time: 230,
            value: 1,
            tween: 'linear',
          },
        ],
      },
    ]
  };

  return anim;
}

export function createFertilizerBagBeatingAnim(node: GameObject) {
  let anim = node.getComponent(Transition);
  if (!anim) {
    anim = node.addComponent(new Transition());
  }
  const { transform } = node;
  const originPos = transform.position;
  anim.group = {
    fertilizerBagBeating: [
      {
        name: 'position.y',
        component: transform,
        values: [
          {
            time: 0,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 4000,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 4133,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 4300,
            value: originPos.y-2,
            tween: 'linear',
          },
          {
            time: 4467,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 4600,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 4767,
            value: originPos.y-2,
            tween: 'linear',
          },
          {
            time: 4900,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 5033,
            value: originPos.y,
            tween: 'linear',
          },
          {
            time: 5167,
            value: originPos.y,
            tween: 'linear',
          }
        ],
      },
      {
        name: 'scale.y',
        component: transform,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 4000,
            value: 1,
            tween: 'linear',
          },
          {
            time: 4133,
            value: 0.98,
            tween: 'linear',
          },
          {
            time: 4300,
            value: 1.05,
            tween: 'linear',
          },
          {
            time: 4467,
            value: 1,
            tween: 'linear',
          },
          {
            time: 4600,
            value: 0.97,
            tween: 'linear',
          },
          {
            time: 4767,
            value: 1.03,
            tween: 'linear',
          },
          {
            time: 4900,
            value: 1.03,
            tween: 'linear',
          },
          {
            time: 5033,
            value: 0.99,
            tween: 'linear',
          },
          {
            time: 5167,
            value: 1,
            tween: 'linear',
          }
        ],
      },
      {
        name: 'scale.x',
        component: transform,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 4000,
            value: 1,
            tween: 'linear',
          },
          {
            time: 4133,
            value: 1.05,
            tween: 'linear',
          },
          {
            time: 4300,
            value: 0.94,
            tween: 'linear',
          },
          {
            time: 4467,
            value: 1,
            tween: 'linear',
          },
          {
            time: 4600,
            value: 1.06,
            tween: 'linear',
          },
          {
            time: 4767,
            value: 0.97,
            tween: 'linear',
          },
          {
            time: 4900,
            value: 0.97,
            tween: 'linear',
          },
          {
            time: 5033,
            value: 1.05,
            tween: 'linear',
          },
          {
            time: 5167,
            value: 1,
            tween: 'linear',
          }
        ],
      },
    ]
  };

  return anim;
}

