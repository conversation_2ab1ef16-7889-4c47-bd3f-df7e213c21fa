export interface CountDownParam {
  time: number;
  interval?: number;
  preDeal?: boolean;
  finishCb?: () => void;
  intervalCb?: (time: number) => void;
}

export function countDown(params: CountDownParam) {
  const { time, interval = 1000, preDeal = false, intervalCb, finishCb } = params;
  let requestAnimationFrameId: any;
  let lastTime: number;
  let dt = 0;
  let leftTime = +time;
  if (typeof leftTime !== 'number' || leftTime <= 0 || isNaN(leftTime)) {
    intervalCb?.(0);
    return
  }
  if (preDeal) {
    intervalCb && intervalCb(leftTime);
  }
  const update = () => {
    requestAnimationFrameId = requestAnimationFrame(t => {
      if (!lastTime) {
        lastTime = t;
      }
      dt += t - lastTime;
      lastTime = t;
      if (dt >= interval) {
        dt -= interval;
        leftTime -= interval;
        if (leftTime <= 0) {
          intervalCb && intervalCb(0);
          finishCb && finishCb();
          return;
        }
        intervalCb && intervalCb(leftTime);
      }
      update();
    });
  };
  update();
  return () => {
    requestAnimationFrameId && cancelAnimationFrame(requestAnimationFrameId);
  };
}
