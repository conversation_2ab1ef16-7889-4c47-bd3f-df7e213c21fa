import { GameObject } from '@eva/eva.js';
import { Text } from '@eva/plugin-renderer-text';
import { Transition } from '@eva/plugin-transition';
import { Img } from '@eva/plugin-renderer-img';
import throttle from 'lodash.throttle';
import TransitionWithComplete from '@/gameObject/base/TransitionWithComplete';
import { addPoint } from '@/gameObject/utils/point';
import { isHighMode, getScreenCenterBottom } from '@/gameObject/utils/screen';
import { createFadeInAnimParams, createFadeOutAnimParams, getShakingAnim } from '@/gameObject/utils/anim';
import { setRenderParams } from '@/gameObject/utils/setRenderParams';
import { Graphic } from '@/gameObject/base/Graphic';
import { addPrefabResource, addUrl, createImageGameObjectByUrl } from '@/gameObject/resource';
import { createImageFromUrl } from '@/gameObject/utils/createImageFromUrl';
import shortNick from '@/gameObject/utils/shortNick';
import { EMultiOpenGiftStatus, IActivityData } from '@/logic/store/models/app/typings';
import { IRewardData } from './typings';
import { isCanOpen, isWaitOpen } from './help';
import { Render } from '@eva/plugin-renderer-render';
import mx from '@ali/pcom-mx';
import { StoreName } from '@/logic/type/store';
import stat from '@/lib/stat';

export interface TipsParams {
  // position: { x: number; y: number };
  afterInitCallback?: () => void;
  clickCallback?: () => void;
}

// 红包气泡
export default class RewardTipsPlusNew extends TransitionWithComplete<TipsParams> {
  static override componentName = 'RewardTipsPlusNew';
  private graphic: Graphic;
  private conObj: GameObject;
  private textCom: Text;
  private bubbleIconImg: Img;
  // private arrowCom: Img;
  private bubbleIconTransition: any;
  private imgObj: GameObject;
  private gameObjectRender: Render;
  private bubbleIconAniIsPlay: boolean;
  // private _curIdx: number;
  // private _playing: boolean;
  // private _time: number;
  // private _interval = 4000;
  private _tipsWidth: number;
  // 更新轮播数组
  // private _times: any;
  // 初始化动画
  private isInitAnim: boolean;
  // 气泡icon
  private _bubbleIcon: string;
  private _bubbleIconSize: number;
  private _rewardStateData: IRewardData | null;
  private _text: string;
  private clickCallback: () => void;
  private progressExposureFlag = true;
  override init(params: TipsParams) {
    (this.gameObject as any).snapshotHidden = true;
    this._bubbleIcon = 'https://img.alicdn.com/imgextra/i4/O1CN01vAR9Ar1e2PA37bxuh_!!6000000003813-2-tps-96-96.png';
    this._bubbleIconSize = 30;

    this.gameObject.transform.position = addPoint(getScreenCenterBottom(), {
      x: 0,
      y: isHighMode() ? -380 : -330,
    });
    this.showCurInfo = this.showCurInfo.bind(this);

    this.gameObject.addTouchHandler({
      tap: throttle(this.onTouch.bind(this), 200, {
        trailing: false,
      }),
    });
    this.initAnimation();
    this.gameObjectRender = this.gameObject.getComponent('Render');
    params.clickCallback && (this.clickCallback = params.clickCallback);
    params.afterInitCallback?.();
  }

  initAnimation() {
    this.add('fadeIn', [createFadeInAnimParams(this.gameObject, 500, 0)]);
    this.add('fadeOut', [createFadeOutAnimParams(this.gameObject, 500)]);
  }

  toggleVisible(visible: boolean) {
    this.gameObjectRender && (this.gameObjectRender.visible = visible);
  }

  updateStateData(data: IRewardData) {
    this._rewardStateData = data;
  }

  onDataChangeRewardBubbles() {
    this.updateData();
  }

  onDataProgressBarVisibles(visible: boolean) {
    if (!visible) {
      this.hideTips();
    } else {
      this.updateData();
    }
  }

  override update() {
    if (this.textCom) {
      const txtW = this.textCom.gameObject.transform.size.width;
      let imgW = 0;
      let rightPadding = 20;
      if (this.bubbleIconImg && this.bubbleIconImg.gameObject.parent) {
        imgW = this.bubbleIconImg.gameObject.transform.size.width;
        rightPadding = 8;
        this.textCom.gameObject.transform.position.x = imgW / 2;
      } else {
        this.textCom.gameObject.transform.position.x = 0;
      }
      // conObj.x + 图片w/2 + 文字w + 箭头到文字间距(2) + 箭头w + 右边距
      const totalW = 27 + imgW / 2 + txtW + 2 + 12 + rightPadding;

      if (this._tipsWidth !== totalW) {
        this._tipsWidth = totalW;
        this.graphic.drawPop({
          width: totalW,
          height: 47,
          radius: 12,
          color: '0xffffff',
          alpha: '1',
          borderWidth: 2,
          borderColor: 0xffffff,
        });
        this.graphic.updateBgWidth(totalW);
      }
    }
  }

  private updateData() {
    const { progressBarVisible, status, canExchange, seedCode } = this._rewardStateData || {};
    // 进度条隐藏 || 没有任务 || 可兑换 || 未选种，隐藏拆包气泡
    if (!progressBarVisible || status === EMultiOpenGiftStatus.NONE || canExchange || !seedCode) {
      this.hideTips();
      return;
    }
    this.playAnim();
  }

  private playAnim() {
    const text = this.getTipText();
    if (this._text === text) return;
    if (!this._text && text) {
      // 拆包气泡无->有
      this._text = text;
      this.showCurInfo(text);
      setRenderParams(this.gameObject, { alpha: 1 });
    } else if (!text) {
      this._text = text;
      setRenderParams(this.gameObject, { alpha: 0 });
    } else {
      // 拆包气泡文字变化
      this._text = text;
      this.showCurInfo(text);
      setRenderParams(this.gameObject, { alpha: 1 });
    }
  }

  private createShakingAnim() {
    if (this.isInitAnim) {
      return;
    }
    this.isInitAnim = true;
    this.bubbleIconTransition.group = {
      shaking: getShakingAnim(this.imgObj, 0.7),
    };
  }

  private getTipText() {
    let text = '';
    if (!this._rewardStateData) return '';
    const { needTimes, completeTimes } = this._rewardStateData;
    if (isCanOpen(this._rewardStateData)) {
      text = '立即领';
    } else if (isWaitOpen(this._rewardStateData)) {
      text = `再施${needTimes - completeTimes}次领礼包`;
    }
    return text;
  }

  // 展示气泡数据
  private showCurInfo(text: string) {
    this.createGraphicComponent();
    this.createTextComponent();
    this.showImgIcon();
    // this.updateBubbleSize();
    this.textCom.text = shortNick(text, 17, '');
    if (this._rewardStateData) {
      if (isCanOpen(this._rewardStateData)) {
        if (this.textCom.style) this.textCom.style.fill = '#FA6425';
        this.createArrowComponent('wateringPinkBubbleArrowImg');
      } else if (isWaitOpen(this._rewardStateData)) {
        if (this.textCom.style) this.textCom.style.fill = '#8A4519'
        this.createArrowComponent('wateringBubbleArrowImg');
      }
    }
    // 曝光埋点回掉
    if (this.progressExposureFlag) {
      this.progressExposureFlag = false;
      const multiOpenGiftData: IActivityData['multiOpenGift'] = mx.store.get(StoreName.MultiOpenGift) || {};
      if (!multiOpenGiftData?.status) return;
      if (multiOpenGiftData?.status === EMultiOpenGiftStatus.CAN_RECEIVE) {
        stat.exposure('progress_exposure', {
          c: 'function',
          d: 'progress',
          bubble_state: 'award',
          task_turn: multiOpenGiftData?.groupKey,
          round_turn: multiOpenGiftData?.round,
          taskclassify: multiOpenGiftData?.curTask?.taskClassify,
          groupcode: multiOpenGiftData?.curTask?.groupCode,
          task_id: multiOpenGiftData?.curTask?.id,
          task_name: multiOpenGiftData?.curTask?.name,
        });
      } else if (
        multiOpenGiftData?.status === EMultiOpenGiftStatus.INIT &&
        Number.isInteger(multiOpenGiftData?.needTimes) &&
        Number.isInteger(multiOpenGiftData?.completeTimes) &&
        multiOpenGiftData?.needTimes > multiOpenGiftData?.completeTimes
      ) {
        stat.exposure('progress_exposure', {
          c: 'function',
          d: 'progress',
          bubble_state: 'pending',
          task_turn: multiOpenGiftData?.groupKey,
          round_turn: multiOpenGiftData?.round,
          taskclassify: multiOpenGiftData?.curTask?.taskClassify,
          groupcode: multiOpenGiftData?.curTask?.groupCode,
          task_id: multiOpenGiftData?.curTask?.id,
          task_name: multiOpenGiftData?.curTask?.name,
        });
      }
    }
  }

  // 处理气泡icon图片
  private showImgIcon() {
    if (!this._bubbleIcon && this.bubbleIconImg && this.bubbleIconImg.gameObject.parent) {
      this.bubbleIconImg.gameObject.remove();
      return;
    }
    addUrl(this._bubbleIcon);

    if (this.bubbleIconImg) {
      this.bubbleIconImg.resource = this._bubbleIcon;
      this.bubbleIconImg.gameObject.transform.size.width = this._bubbleIconSize;
      this.bubbleIconImg.gameObject.transform.size.height = this._bubbleIconSize;
      this.bubbleIconImg.gameObject.transform.position.y = 12 + (this._bubbleIconSize - 30) / 2;
    } else {
      this.createImgIconComponent();
    }
    if (this.imgObj && this._rewardStateData?.status === EMultiOpenGiftStatus.CAN_RECEIVE) {
      if (!this.bubbleIconAniIsPlay) {
        this.bubbleIconAniIsPlay = true;
        this.bubbleIconTransition.play('shaking', -1);
      }
    } else {
      this.bubbleIconAniIsPlay = false;
      this.imgObj.transform.rotation = 0;
      if (this.bubbleIconTransition.animations && this.bubbleIconTransition.animations.shaking) {
        this.bubbleIconTransition.animations.shaking.stop();
      }
    }
  }

  private createImgIconComponent() {
    if (!this.bubbleIconImg) {
      const size = this._bubbleIconSize;
      this.imgObj = createImageFromUrl(this._bubbleIcon, {
        size: { width: size, height: size },
        origin: { x: 0.5, y: 1 },
        position: { x: -5, y: 12 + (size - 30) / 2 },
      });

      this.bubbleIconImg = this.imgObj.getComponent(Img.componentName);
      // con对象添加一个mask子对象
      this.conObj.addChild(this.imgObj);
      this.bubbleIconTransition = this.imgObj.addComponent(new Transition());
      this.createShakingAnim();
    }

    if (!this.bubbleIconImg.gameObject.parent) {
      this.conObj.addChild(this.bubbleIconImg.gameObject);
    }
  }

  private createGraphicComponent() {
    if (!this.graphic) {
      const graphicGameObject = new GameObject(Graphic.componentName, {});
      this.graphic = new Graphic({
        position: { x: 0, y: 0 },
      });
      graphicGameObject.addComponent(this.graphic);

      this.gameObject.addChild(this.graphic.gameObject);
      // 创建一个con对象，添加到graphic对象当中
      this.conObj = new GameObject('conObj', {
        origin: { x: 0.5, y: 0.5 },
        position: { x: 27, y: 2 },
        anchor: { x: 0, y: 0.5 },
      });
      this.graphic.gameObject.addChild(this.conObj);
    }
    if (!this.graphic.gameObject.parent) {
      this.gameObject.addChild(this.graphic.gameObject);
    }
  }

  private createTextComponent() {
    if (this.textCom) return;
    const textGameObject = new GameObject(Text.componentName);
    this.textCom = new Text({
      text: '',
      style: {
        fontFamily: 'PingFangSC-Medium',
        fontSize: 24,
        fill: '#8A4519',
        // eslint-disable-next-line @iceworks/best-practices/recommend-add-line-height-unit
        lineHeight: 25,
      },
    });
    textGameObject.addComponent(this.textCom);
    // this.textCom.gameObject.transform.origin = { x: 0, y: 0 };
    this.textCom.gameObject.transform.position.y = -18;
    // con对象添加一个text子对象
    this.conObj.addChild(this.textCom.gameObject);
  }

  private createArrowComponent(resource) {
    addPrefabResource(resource);
    const imgObj = createImageGameObjectByUrl(resource, {
      size: { width: 8, height: 12 },
      origin: { x: 0, y: 0.5 },
      anchor: { x: 1, y: 0.5 },
      position: { x: 2, y: 0 },
    });
    // this.arrowCom = imgObj.getComponentType(Img);
    this.textCom.gameObject.addChild(imgObj);
  }

  // 点击
  private onTouch() {
    this.clickCallback?.();
  }

  // 清除数据
  private hideTips() {
    // this._curInfo = null;
    // this._playing = false;
    this._rewardStateData = null;
    this.stopAll();
    setRenderParams(this.gameObject, { alpha: 0 });
    if (this.graphic && this.graphic.gameObject.parent) {
      this.graphic.gameObject.remove();
    }
  }
}
