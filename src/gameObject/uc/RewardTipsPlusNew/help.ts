import { EMultiOpenGiftStatus } from '@/logic/store/models/app/typings';
import { IRewardData } from './typings';

export const isCanOpen = (data: IRewardData) => {
  const { status } = data || {};
  return status === EMultiOpenGiftStatus.CAN_RECEIVE;
};

export const isWaitOpen = (data: IRewardData) => {
  const { status, needTimes, completeTimes } = data || {};
  return (
    status === EMultiOpenGiftStatus.INIT &&
    Number.isInteger(needTimes) &&
    Number.isInteger(completeTimes) &&
    needTimes > completeTimes
  );
};
