import mx from '@ali/pcom-mx';
import RewardTipsPlusNew from './RewardTipsPlusNew';
import { StoreName } from '@/logic/type/store';
import { IRewardData } from './typings';
import { EMultiOpenGiftStatus, IActivityData } from '@/logic/store/models/app/typings';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import dispatch from '@/logic/store';
import { EnumTaskType, DRAW_CUSTOM_TASK_ERROR_CODE_TEXT_MAP } from '@/logic/store/models/task/typing';
import toast from '@/lib/universal-toast/component/toast';
import { isCanOpen, isWaitOpen } from './help';
import stat from '@/lib/stat';
import { TasklistSource } from '@/pages/index/utils';
import { MainAPI } from '@/logic/type/event';

export default class FuqiEventBind extends RewardTipsPlusNew {
  static override componentName = 'RewardTipsPlusNew';
  private triggleLoading = false;

  constructor() {
    super({
      afterInitCallback: () => {
        this.updateStateData(this.getRewardData());
        this.initMxData();
        this.initMxEvent();
      },
      clickCallback: () => {
        this.clickEvent();
        stat.click('gift_click', {
          c: 'gift',
          d: '0',
        });
      },
    });
  }

  private clickEvent() {
    const rewardData = this.getRewardData() || {};
    const { needTimes, completeTimes, groupKey, round, curTask } = rewardData;    
    if (isCanOpen(rewardData)) {
      stat.click('progress_click', {
        c: 'function',
        d: 'progress',
        task_turn: groupKey,
        round_turn: round,
        bubble_state: 'award',
        taskclassify: curTask?.taskClassify,
        groupcode: curTask?.groupCode,
        task_id: curTask?.id,
        task_name: curTask?.name,
      });
      this.triggleRewardTask();
    } else if (isWaitOpen(rewardData)) {
      stat.click('progress_click', {
        c: 'function',
        d: 'progress',
        task_turn: groupKey,
        round_turn: round,
        bubble_state: 'pending',
        taskclassify: curTask?.taskClassify,
        groupcode: curTask?.groupCode,
        task_id: curTask?.id,
        task_name: curTask?.name,
      });
      setTimeout(() => {
        const HappyPoint = mx.store.get(StoreName.HappyPoint);
        const wateringCost = mx.store.get(StoreName.WateringCost);
        if (HappyPoint < wateringCost) {
          toast.show('肥料不够了，去做任务赚更多吧');
          mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: TasklistSource.fertilizer_gift });
        } else {
          baseModal.open(MODAL_ID.PROGRESS_BUBBLE, { isReward: false, needTimes: needTimes - completeTimes, modalType: 'progress' });
        }
      }, 80);
    }
  }

  private async triggleRewardTask() {
    // baseModal.open(MODAL_ID.PROGRESS_BUBBLE, { isReward: true, pointAmount: 100 })
    if (this.triggleLoading) return;
    this.triggleLoading = true;
    try {
      const res = await dispatch.task.drawCustomTaskAward(EnumTaskType.MULTI_OPEN_GIFT);
      if (res?.pointAmount && res.pointAmount > 0) {
        const HappyPoint = mx.store.get(StoreName.HappyPoint);
        const wateringCost = mx.store.get(StoreName.WateringCost);
        if (HappyPoint < wateringCost) {
          toast.show(`已获得${res.pointAmount}肥料，去做任务赚更多吧`);
          mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: TasklistSource.fertilizer_gift });
        } else {
          // 发奖成功
          await dispatch.resource.queryResource({});
          baseModal.open(MODAL_ID.PROGRESS_BUBBLE, { isReward: true, pointAmount: res.pointAmount, modalType: 'progress' });
        }
      } else {
        toast.show(DRAW_CUSTOM_TASK_ERROR_CODE_TEXT_MAP.others);
      }
      dispatch.app.updateHomeData();
      this.triggleLoading = false;
    } catch (error) {
      const errorText =
        DRAW_CUSTOM_TASK_ERROR_CODE_TEXT_MAP[error?.code] || DRAW_CUSTOM_TASK_ERROR_CODE_TEXT_MAP.others;
      toast.show(errorText);
      // 异常场景刷新主接口更新页面数据，避免状态无法更新
      dispatch.app.updateHomeData();
      console.warn('[triggleRewardTask error]:', JSON.stringify(error), error?.message);
      this.triggleLoading = false;
    }
  }

  private initMxData() {
    // 监听核心数据变化
    const dataChangeEvents = [
      StoreName.CanExchange,
      StoreName.MultiOpenGiftStatus,
      StoreName.MultiOpenGiftCompleteTimes,
      StoreName.MultiOpenGiftNeedTimes,
    ];
    const updateData = () => {
      this.updateStateData(this.getRewardData());
      this.onDataChangeRewardBubbles();
    }
    dataChangeEvents.forEach((event) => {
      mx.store.on(event, updateData, true);
    });
  }

  private initMxEvent() {
    mx.store.on(
      StoreName.MultiOpenGift,
      () => {
        this.updateStateData(this.getRewardData());
        this.onDataChangeRewardBubbles();
      },
      false,
    );
  }

  /**
   * 获取关联数据
   * @returns IRewardData
   */
  private getRewardData(): IRewardData {
    const multiOpenGiftData: IActivityData['multiOpenGift'] = mx.store.get(StoreName.MultiOpenGift) || {};
    const data = {
      ...multiOpenGiftData,
      seedCode: mx.store.get(StoreName.SeedCode),
      canExchange: mx.store.get(StoreName.CanExchange),
      progressBarVisible: true,
      status: mx.store.get(StoreName.MultiOpenGiftStatus) || EMultiOpenGiftStatus.INIT, // NONE 不满足任务条件 INIT 任务已触发未可领 CAN_RECEIVE 可以领取
      // completeTimes: mx.store.get(StoreName.MultiOpenGiftCompleteTimes), // 已完成次数
      // needTimes: mx.store.get(StoreName.MultiOpenGiftNeedTimes), // 需要完成次数
      // round: mx.store.get(StoreName.Round), // 第几轮
      // groupKey: mx.store.get(StoreName.GroupKey), // ab实验结果
    };
    return data;
  }
}
