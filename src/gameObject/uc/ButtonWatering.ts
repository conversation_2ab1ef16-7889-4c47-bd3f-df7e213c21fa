import { GameObject } from '@eva/eva.js';
import { createText } from '../utils/eva';
import { Text } from '@eva/plugin-renderer-text';
import { Transition } from '@eva/plugin-transition';
import mx from '@ali/pcom-mx';
import { Spine } from '@eva/plugin-renderer-spine';
import Button from '../base/Button';
import { NineBg } from '../base/NineBg';
import { GameInfo } from '@/logic/type/user';
import { MainAPI } from '@/logic/type/event';
import { addPoint } from '../utils/point';
import { toInt } from '../utils/toInt';
import throttle from 'lodash.throttle';
import {
  addPrefabResource,
  addSpineResource,
} from '../resource';
import {getScreenCenterBottom, isHighMode} from '../utils/screen';
import { delay, tick } from '../utils/delay';
import { setRenderParams } from '../utils/setRenderParams';
import { StoreName } from '@/logic/type/store';
import BaseComponent from '../base/BaseComponent';
import FuqiCom from "@/gameObject/uc/FuqiCom";

export interface ButtonWateringParams {
  position: { x: number; y: number };
  size: { width: number; height: number };
}

// 施肥按钮
export default class ButtonWatering extends BaseComponent {
  static override componentName = 'ButtonWatering';
  static inst: ButtonWatering;
  // private params: ButtonWateringParams;
  private buttonCom: Button;
  private banAlpha = 0.5;
  private allowAlpha = 1;
  private chatterAnim: Transition;
  private costParent: GameObject; // 右上角tits
  private costBg: NineBg;
  private costText: Text;
  private _textParent: GameObject;
  textGameObject: GameObject;
  private _curHappyPoint: number;
  private _wateringBtnText: string;
  private _wateringSpine: Spine;
  private _isWatering: boolean;
  private _spineAnimObj: GameObject;

  override init(params: ButtonWateringParams) {
    ButtonWatering.inst = this;
    // this.params = params;
    this.gameObject.transform.position = addPoint(getScreenCenterBottom(), { x: 0, y: 12 });
    this.gameObject.transform.origin = {x: 0.5, y: 1};
    (this.gameObject as any).snapshotHidden = true;
    this._spineAnimObj = new GameObject('waterAnim', {});
    this.costParent = new GameObject('costParent', {
      position: {x: 90, y: -108},
      origin: {x: 0.5, y: 0.5},
    });
    this.createWaterSpine(); // 施肥动画
    this.initEventBind();
  }

  initEventBind() {
    const dataChangeEvents = [StoreName.HappyPoint, StoreName.SeedImg, StoreName.WateringCost];
    const updateView = throttle(() => {
      this.updateText();
      this.updateCostText();
    }, 500)
    dataChangeEvents.forEach((event) => {
      mx.store.on(event, updateView);
    });
    mx.store.on(
      StoreName.SeedImg,
      (v) => {
        this.createAndUpdateButton();
      },
      true,
    );
    // 收集肥料时播放按钮动效
    mx.event.on(MainAPI.FeiLiaoDropAnim, (msg) => {
      console.log('playAddFeiliaoAnim:', msg)
      this.playAddFeiliaoAnim(msg);
    });

    // 施肥完成监听，播放施肥动画
    mx.event.on(MainAPI.AfterWaterResult, () => {
      FuqiCom.inst.fuqistart();
      ButtonWatering.inst.playWaterAnim()
      delay(1000).then(() => {
        mx.event.emit(MainAPI.AfterWaterAni)
      })
    })
  }

  // 按钮抖动动画
  playChatterAnim() {
    if (!this.chatterAnim) {
      return;
    }
    this.chatterAnim.play('chatter', 6);
    delay(820).then(() => {
      this.chatterAnim.play('goback', 1);
    });
  }

  // 新建按钮组件
  private createButtonCom(url: string) {
    // @ts-ignore
    this.buttonCom = Button.createGameObject({
      imgUrl: url,
      size: { width: 412, height: 159 }
    });
    this.gameObject.addChild(this.buttonCom.gameObject);
    this.buttonCom.gameObject.addChild(this.costParent);
    this.gameObject.addChild(this._spineAnimObj);
    this.chatterAnim = this.buttonCom.anim;
    mx.event.emit(MainAPI.CreateButtonGO);
  }

  // 施肥按钮图片
  private getWaterBtnImg() {
    // return 'https://gw.alicdn.com/imgextra/i2/O1CN01hLd7Oj1CjApgVE6Jg_!!6000000000116-2-tps-234-152.png'
    return 'https://gw.alicdn.com/imgextra/i3/O1CN013YK8o71PiLBPirzpI_!!6000000001874-2-tps-234-152.png'
  }

  private _hander: any;
  private _hasSeed: boolean;
  private _canExchange: boolean;

  createAndUpdateButton() {
    clearTimeout(this._hander);
    this._hander = setTimeout(() => {
      let url = this.getWaterBtnImg();
      if (!url) {
        if (this.buttonCom && this.buttonCom.gameObject.parent) {
          this.buttonCom.gameObject.remove();
        }
        return;
      }

      if (!this.buttonCom) {
        this.createButtonCom(url);
      } else {
        this.buttonCom.updateImg(url)
      }

      if (!this.buttonCom.gameObject.parent) {
        this.gameObject.addChild(this.buttonCom.gameObject);
      }

      this.updateText();
      this._hasSeed = !!mx.store.get(StoreName.SeedImg);
      this._canExchange = mx.store.get(StoreName.CanExchange);

      const disabled = !this._hasSeed || this._canExchange;
      const alpha: number = disabled ? this.banAlpha : this.allowAlpha;
      setRenderParams(this.buttonCom.gameObject, { alpha: alpha });
      this.checkWaterSpineVisable();
    }, 50);
  }

  private updateText() {
    try {
      let gameInfo: GameInfo = mx.store.get(StoreName.GameInfo);
      if (!gameInfo) {
        return;
      }
      let fill = '#a34c01';
      if (!this.buttonCom) {
        return;
      }
      let wateringBtnText = '肥料';
      let text = `${wateringBtnText} ${gameInfo.accountInfo?.happyPoint || 0}`;
      this._curHappyPoint = toInt(gameInfo.accountInfo?.happyPoint);
      this._wateringBtnText = wateringBtnText;
      this.buttonCom.updateText(text, fill);
      // @ts-ignore
      // if (+gameInfo.accountInfo?.happyPoint < 0) {
      //   this.buttonCom.beginCarousel();
      // } else {
      //   this.buttonCom.stopCarousel();
      // }
    } catch (error) {
      console.log(error);
    }
  }

  updateTextAnim(toNum: number) {
    let elpasedTime = 0;
    let roolSpeed = 2;
    let maxRollTime = 1000;
    let intervalTime = 30;
    let startNum = this._curHappyPoint;
    let needChangeNum = toNum - startNum;
    let curNum = startNum;
    let roolTime = Math.min(Math.abs((toNum - startNum) / roolSpeed), maxRollTime);
    if (startNum >= toNum) {
      return;
    }

    tick(intervalTime, () => {
      elpasedTime += intervalTime;
      curNum = startNum + Math.floor((elpasedTime / roolTime) * needChangeNum);
      curNum = Math.min(curNum, toNum);
      let text = `${this._wateringBtnText} ${curNum}`;
      this.buttonCom.updateText(text, '');
      return curNum < toNum;
    });
  }

  createWateringCost() {
    setRenderParams(this.costParent, { alpha: 1 });
    addPrefabResource('waterBadgeBg');
    // @ts-ignore
    this.costBg = NineBg.createGameObject({
      resName: 'waterBadgeBg',
      position: { x: 0, y: 0 },
      size: { width: 36, height: 36 },
      leftWidth: 18,
      rightWidth: 18,
      topHeight: 4,
      bottomHeight: 4,
    }) as NineBg;
    this.costParent.addChild(this.costBg.gameObject);
    this._textParent = new GameObject('textParent', {
      origin: { x: 0.5, y: 0.5 },
    });
    this.costParent.addChild(this._textParent);
  }

  // 剩余施肥次数
  updateCostText() {
    if (!this.costBg) {
      this.createWateringCost();
    }
    let happyPoint = toInt(mx.store.get(StoreName.HappyPoint));
    happyPoint = isNaN(happyPoint) ? 0 : happyPoint;
    let wateringCost = mx.store.get(StoreName.WateringCost) || 600;
    let cost = Math.floor(happyPoint / wateringCost);
    if (cost < 0) {
      cost = 0
    }
    if (!this.costText) {
      this.textGameObject = createText(
        'text',
        {
          position: { x: 0, y: 0 },
          origin: { x: 0.5, y: 0.5 },
        },
        {
          text: '',
          style: {
            fontFamily: 'PingFangSC-Semibold',
            fontSize: 24,
            fill: '#ffffff',
          },
        },
      );
      this.costText = this.textGameObject.getComponentType(Text);
      this._textParent.addChild(this.textGameObject);
    }
    this.costText.text = String(cost);
    delay(50).then(() => {
      this.updateCostWidth();
    });
  }

  updateCostWidth() {
    let textW = this.costText.gameObject.transform.size.width;
    let bgW = Math.max(36, textW + 26);
    this.costBg.updateBgWidth(bgW);
  }


  playAddFeiliaoAnim(msg: { afterRefund: number; refundAmount: number }) {
    let toNum = toInt(msg.afterRefund);
    this.buttonCom?.playAddFeiliaoAnim(() => {
      this.updateTextAnim(toNum);
    });
  }

  private checkWaterSpineVisable() {
    if (!this._wateringSpine) {
      return;
    }
    if (!this._hasSeed || this._canExchange) {
      setRenderParams(this._wateringSpine.gameObject, { alpha: 0 });
    } else {
      setRenderParams(this._wateringSpine.gameObject, { alpha: 1 });
    }
  }

  // 施肥铲子动画
  private createWaterSpine() {
    const {
      waterAnimSke = 'https://g.alicdn.com/eva-assets/3cb796e2e69f28cd0646dadca35a8c0a/0.0.1/tmp/51478d2/13fc3125-4d64-45c8-b433-ae7abf2ca16e.json',
      waterAnimAtlas = 'https://g.alicdn.com/eva-assets/d37f785daa8d53c2623360edb777264c/0.0.1/tmp/fe15086/8967ea01-91bc-40ac-aa1a-e035d652356c.atlas',
      waterAnimPic = 'https://gw.alicdn.com/imgextra/i4/O1CN018QVTFk1WerG68rAnJ_!!6000000002814-2-tps-74-74.png',
    } = mx.store.get(StoreName.WaterAnim) || {};
    if (!waterAnimSke || !waterAnimAtlas || !waterAnimPic) {
      return;
    }

    function addSpine() {
      addSpineResource(waterAnimPic, {
        ske: waterAnimSke,
        atlas: waterAnimAtlas,
        image: waterAnimPic,
      });
    }
    if (!this._wateringSpine) {
      addSpine();
      let animObj = new GameObject('animobj', {
        position: { x: 0, y: isHighMode() ? -150 : -100 },
        origin: { x: 0.5, y: 0.5 },
      });
      animObj.addComponent(Spine, {
        resource: waterAnimPic,
        animationName: 'standby1',
        autoPlay: false,
      });
      this._spineAnimObj.addChild(animObj);
      let anim = animObj.getComponentType(Spine) as Spine;
      this._wateringSpine = anim;
      anim.on('complete', (data) => {
        if (data.name === 'standby2') {
          this._isWatering = false;
        }
      });
    } else {
      addSpine();
      this._wateringSpine.resource = waterAnimPic;
    }
  }

  playWaterAnim() {
    if (!this._wateringSpine || this._isWatering) {
      return;
    }
    this._isWatering = true;
    console.log('[ButtonWatering]playWaterAnim')
    this._wateringSpine.play('standby2', false);
  }
}
