import mx from '@ali/pcom-mx';
import { StoreName } from "@/logic/type/store";
import { getScreenCenterBottom } from "@/gameObject/utils/screen";
import { addPoint } from '../utils/point';
import BaseComponent from '../base/BaseComponent';
import { ObjectPool } from '../base/ObjectPool';
import { FuqiPlay } from './FuqiPlay';

export interface FuqiComParams {
  position: { x: number; y: number };
  wateringCost: number;
}

export interface FuqiComReuseParams {}

export default class FuqiCom extends BaseComponent {
  static override componentName = 'FuqiCom';
  static inst: FuqiCom;
  // @ts-ignore
  private FuqiPool: ObjectPool<FuqiPlay>;
  private isFeiliao: boolean;
  override init(params: FuqiComParams) {
    FuqiCom.inst = this;
    this.gameObject.transform.position = addPoint(getScreenCenterBottom(), { x: 0, y: -100 });
    (this.gameObject as any).snapshotHidden = true;
    this.onFuqiFinish = this.onFuqiFinish.bind(this);
  }

  private initPool() {
    // @ts-ignore
    this.FuqiPool = ObjectPool.createGameObject({
      initNum: 2,
      compType: FuqiPlay,
    });
    this.gameObject.addChild(this.FuqiPool.gameObject);
  }

  // 施肥提示动画 "浇灌xx肥料"
  public fuqistart() {
    let wateringCost = mx.store.get(StoreName.WateringCost);
    if (!wateringCost) {
      return;
    }
    if (!this.FuqiPool) {
      this.initPool();
    }
    let fuqiObj = this.FuqiPool.get({
      position: { x: 0, y: 0 },
    });
    // @ts-ignore
    this.gameObject.addChild(fuqiObj.gameObject);
    fuqiObj.fuqirise(wateringCost, this.onFuqiFinish, this.isFeiliao);
  }

  private onFuqiFinish(fuqi: FuqiPlay) {
    this.FuqiPool.put(fuqi);
  }
}
