import { Transform } from '@eva/eva.js';
import {
  createdailyfuqiballfadeInAnimParams,
  createEvenMoveXAnimParams,
  createEvenMoveYAnimParams,
} from '../utils/anim';
import { addImgResource, createImageGameObjectByUrl } from "@/gameObject/resource";
import { delay } from '../utils/delay';
import TransitionWithComplete from '../base/TransitionWithComplete';
import { Img } from '@eva/plugin-renderer-img';
import {isHighMode} from "@/gameObject/utils/screen";

export interface DailyFuqiDropParams {
  resource: string;
}

export interface DailyFuqiDropReuseParams {
  resource: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
}

export class DailyFuqiDrop extends TransitionWithComplete {
  static override componentName = 'DailyFuqiDrop';
  private imgComp: Img;
  private imgTransform: Transform;
  override init(params: DailyFuqiDropParams) {
    const imgObj = createImageGameObjectByUrl(params.resource, {
      size: { width: 50, height: 50 },
    });
    this.imgComp = imgObj.getComponentType(Img);
    this.imgTransform = imgObj.transform;

    this.gameObject.addChild(imgObj);
    this.add('fadein', [createdailyfuqiballfadeInAnimParams(this.gameObject, 450, 0)]);
  }

  drop(onFinish: (DailyFuqiDrop: DailyFuqiDrop) => void) {
    this.add('movey', [createEvenMoveYAnimParams(this.gameObject, 0, 450, isHighMode() ? 270 : 220)]);
    this.add('movex', [createEvenMoveXAnimParams(this.gameObject, 0, 450, Math.random() * -120 - 100)]);

    this.play('fadein');
    this.play('movex');
    this.play('movey');
    delay(450).then(() => {
      onFinish(this);
    });
  }

  onReuse(params: DailyFuqiDropReuseParams) {
    addImgResource(params.resource);
    this.gameObject.transform.position = params.position;
    this.imgComp.resource = params.resource;
    this.imgTransform.size = params.size;
  }
  onUnuse() {}
}
