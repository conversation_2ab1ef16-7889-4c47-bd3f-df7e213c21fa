import { StoreName } from '@/logic/type/store';
import mx from '@ali/pcom-mx';
import {MainAPI} from '@/logic/type/event';
import { TreeLiveCycle, TransitionWithComplete, TreeArea, TreeData, ProgressData, addPoint, TreeEvent, TreeStatus } from '@ali/farm-game-object';
import { getScreenCenterBottom, isHighMode } from '@/gameObject/utils/screen';
import { TAKE_CANVAS_SNAPSHOT } from '@ali/farm-utils';
import stat from '@/lib/stat';
import tracker from '@/lib/tracker';
import { getParam } from '@/lib/qs';
import dispatch from '@/logic/store';

export default class TreeContainer extends TransitionWithComplete {
  static override componentName = 'TreeContainer';
  private treeArea: TreeArea;
  override init() {
    console.log('[=== TreeContainer init ===]');
    const treeArea = TreeArea.createGameObject({
      position: addPoint(getScreenCenterBottom(), { x: 0, y: isHighMode() ? -352 : -302 }),
      treeConfig: {
        treeData: null,
        onTreeClick: () => {
          console.log('tree click!');
        },
      },
      progressConfig: {
        data: null,
      },
    });
    // 树点击事件
    treeArea.bindEvent(TreeEvent.ON_TREE_CLICK, (data) => {
      console.log('ON_TREE_CLICK', data);
      if (data.treeStatus === TreeStatus.ExChangeBox) {
        mx.event.emit('sxchange_gift', { isNeedModal: false, exchangeUrl: mx.store.get(StoreName.ExchangeUrl)});
      }
      if (data.treeStatus === TreeStatus.PaintBoard) {
        const inviteCode = getParam('inviteCode') || '';
        dispatch.app.showSelectSeedModal({
          isAssist: Boolean(inviteCode),
          status: inviteCode ? 'assist_user' : 'harvest',
          needToast: false,
        })
      }
    });
    // 树升级时开始展示时机事件
    treeArea.bindEvent(TreeEvent.ON_TREE_UPGRADE_FADE_IN, (data) => {
      console.log('ON_TREE_UPGRADE_FADE_IN', data);
    });
    // 树升级时消失时机事件
    treeArea.bindEvent(TreeEvent.ON_TREE_UPGRADE_FADE_OUT, (data) => {
      console.log('ON_TREE__UPGRADE_FADE_OUT', data);
    });
    // 树升级时展示动画播放完成
    treeArea.bindEvent(TreeEvent.ON_TREE_UPGRADE_FADE_END, (data) => {
      // 截取静态图
      mx.event.emit(TAKE_CANVAS_SNAPSHOT, {
        stageLevel: mx.store.get(StoreName.StageLevel),
        seedImg: mx.store.get(StoreName.SeedImg)
      })
      console.log('ON_TREE__UPGRADE_FADE_END', data);
    });
    // 树可兑换时动画播放完成时机
    treeArea.bindEvent(TreeEvent.ON_TREE_EXCHANGE_ANIM_COMPLETE, (data) => {
      console.log('ON_TREE_EXCHANGE_ANIM_COMPLETE', data);
    });
    // 树种下播放完成时机
    treeArea.bindEvent(TreeEvent.ON_TREE_PLANT_ANIM_END, (data) => {
      console.log('ON_TREE_PLANT_ANIM_END', data);
    });
    treeArea.bindEvent(TreeEvent.ON_TREE_FPS_DETECT, data => {
      console.log('ON_TREE_FPS_DETECT', data);
      if (data.type === 'treeComplete') {
        mx.event.emit('TREE_INIT_COMPLETE', {
          now: Date.now(),
        })
      }
      tracker.log({
        msg: data.type,
        category: 121,
        wl_avgv1: data.maxFps,
        wl_avgv2: data.minFps,
        wl_avgv3: data.delta,
        wl_avgv4: data.rate,
        wl_avgv5: data.avg,
        wl_avgv6: data.detail,
      });
    });
    // 出现种树木牌
    treeArea.bindEvent(TreeLiveCycle.TREE_BOARD_EXPOSURE, () => {
      console.log('TREE_BOARD_EXPOSURE');
      stat.exposure('multiseed_billboard_exposure', {
        c: 'billboard',
        d: 'multiseed',
      });
    });
    treeArea.bindEvent(TreeLiveCycle.TREE_INIT_COMPLETE, (data) => {
      // mx.event.emit('TREE_INIT_COMPLETE', {
      //   now: Date.now(),
      // })
      const info = {
        msg: data.treeType,
        category: 120,
        wl_avgv1: data.now,
        wl_avgv2: data.cost
      };
      console.log('TREE_INIT_COMPLETE:', info);
      tracker.log(info);
    });
    this.treeArea = treeArea;
    this.gameObject.addChild(treeArea.gameObject);
    this.addEventListener();

    // 施肥结束，播放树抖动动画
    mx.event.on(MainAPI.AfterWaterAni, () => {
      treeArea.playNormalWaterAnim();
    });
  }
  private addEventListener() {
    // 监听数据变化
    // const dataChangeEvents = [
    //   StoreName.SeedImg,
    //   StoreName.CanExchange,
    //   StoreName.SceneCode,
    //   StoreName.CurrentProgress,
    //   StoreName.TotalProgress,
    //   StoreName.CanExchange,
    //   StoreName.StageLevel,
    // ];
    // 主接口更新时，更新树状态。
    mx.store.on(StoreName.MainInfo, () => {
      this.treeArea.updateData({
        treeData: this.getTreeData(),
        progressData: this.getProgressData(),
      });
    });
  }
  /**
   * 获取关联数据
   * @returns
   */
  private getTreeData(): TreeData {
    const treeData = {
      downgrade: getParam('force_downgrade') === '1',
      seedImg: mx.store.get(StoreName.SeedImg),
      treeSpine: {
        animImg: mx.store.get(StoreName.AnimImg),
        animAtalas: mx.store.get(StoreName.AnimAtalas),
        animSkeleton: mx.store.get(StoreName.AnimSkeleton),
      },
      canExchange: mx.store.get(StoreName.CanExchange),
      goPlantTreeImg: 'https://gw.alicdn.com/imgextra/i4/O1CN01USjVGD22qN8wv5A5M_!!6000000007171-2-tps-438-490.png',
      exchangeBoxImg: 'https://gw.alicdn.com/imgextra/i2/O1CN01aNM9mw25fKRSOt7qL_!!6000000007553-2-tps-306-270.png',
      status: mx.store.get(StoreName.Status),
      stageLevel: mx.store.get(StoreName.StageLevel),
      sceneCode: mx.store.get(StoreName.SceneCode),
    };
    return treeData;
  }
  private getProgressData(): ProgressData {
    const data = {
      sceneCode: mx.store.get(StoreName.SceneCode),
      currentProgress: mx.store.get(StoreName.CurrentProgress),
      totalProgress: mx.store.get(StoreName.TotalProgress),
      canExchange: mx.store.get(StoreName.CanExchange),
      stageLevel: mx.store.get(StoreName.StageLevel),
      stageText: mx.store.get(StoreName.StageText),
      stageTextColor: '#000000', // 「可选」自定义文案颜色
      levReward: {
        treeStateInfo: {
          seedName: mx.store.get(StoreName.SeedName),
          subTitle: mx.store.get(StoreName.SeedSubTitle),
          seedPic: mx.store.get(StoreName.GrownUpImg),
        },
      },
    };
    return data;
  }
}
