import TransitionWithComplete from '../base/TransitionWithComplete';
import { GameObject } from '@eva/eva.js';
import mx from '@ali/pcom-mx';
import TreeContainer from './TreeContainer';
import ButtonWatering from "./ButtonWatering";
import FuqiCom from "@/gameObject/uc/FuqiCom";
import DailyFuqiCom from "@/gameObject/uc/DailyFuqiCom";
import RewardTipsPlusNew from './RewardTipsPlusNew/EventBind';
import LimitTaskPedants from './LimitTaskPedants';

function getWorldParentPosition() {
  // TODO，补充不同浏览器的偏移量
  return {
    x: 0,
    y: -16
  }
}

export default class World extends TransitionWithComplete {
  static override componentName = 'World';
  /**
   * 当前游戏实例
   */
  static inst: World;
  /**
   * 布局组件
   */
  private parentObj: GameObject;
  /**
   * 脚本组件初始化方法
   */
  override init() {
    World.inst = this;
    this.gameObject = new GameObject('World');
    // 控制游戏整体布局
    this.parentObj = new GameObject('parentObj', {
      position: getWorldParentPosition(),
    });
    this.gameObject.addChild(this.parentObj);

    /**
     * 创建在树后面的object
     */
    this.createTreeBackObj();

    /**
     * 创建果树区域相关节点
     */
    this.createTreeObject();
    /**
     * 施肥相关节点
     */
    this.createWaterObject();
    /**
     * 创建树前面的节点,用于插入动画元素
     */
    this.createTreeFrontObj();
  }
  /**
   * 游戏动态背景
   */
  private createTreeBackObj() {
    // 背景动画
    // @ts-ignore
    // this.gameObject.addChild(DknBgAnim.createGameObject({}).gameObject);
  }
  /**
   * 果树区域，双端对齐
   */
  private createTreeObject() {
    // 树
    // @ts-ignore
    this.parentObj.addChild(TreeContainer.createGameObject({}).gameObject);
  }
  /**
   * 施肥相关组件
   */
  private createWaterObject() {
    // 施肥相关组件写到这里
    // @ts-ignore
    this.parentObj.addChild(ButtonWatering.createGameObject({}).gameObject)
  }
  /**
   * 果树前面视图，用于渲染动态内容
   */
  private createTreeFrontObj() {
    // 比如果树的前面挂一些自定义挂件
    let treeFrontObj = new GameObject('treeFrontObj', {});
    this.parentObj.addChild(treeFrontObj);
    this.parentObj.addChild(RewardTipsPlusNew.createGameObject({}).gameObject);
    // 浇水冒福气
    // @ts-ignore
    this.parentObj.addChild(FuqiCom.createGameObject({}).gameObject);
    // 明日福气动画
    // @ts-ignore
    this.parentObj.addChild(DailyFuqiCom.createGameObject({}).gameObject);

    // 限时玩法挂件
    this.parentObj.addChild(LimitTaskPedants.createGameObject({}).gameObject);

    mx.store.update('FontExtendGame', treeFrontObj);
  }
}
