import { getScreenLeftTop, getScreenCenterCenter } from '@/gameObject/utils/screen';
import { addPoint } from '../utils/point';
import { GameObject, resource, RESOURCE_TYPE } from '@eva/eva.js';
import mx from '@ali/pcom-mx';
import TransitionWithComplete from '../base/TransitionWithComplete';
import { MainAPI } from '@/logic/type/event';
import { Render } from '@eva/plugin-renderer-render';
import baseModal from '@/lib/modal';
import { Event } from '@eva/plugin-renderer-event';
import { MODAL_ID } from '@/components/modals/types';
import { Lottie } from '@eva/plugin-renderer-lottie';
import { StoreName } from '@/logic/type/store';
import LimitGiftPendant from '@/assets/<EMAIL>';
import { addImageResource } from './tomorrowFertilizer/common/utils';
import { Img } from '@eva/plugin-renderer-img';
import stat from '@/lib/stat';

export interface LimitTaskPedantsParams {}

export default class LimitTaskPedants extends TransitionWithComplete {
  static inst: LimitTaskPedants;
  static override componentName = 'LimitTaskPedants';

  private limitTaskPedants: GameObject;
  private limitTaskPedantsRender: Render;
  private limitTaskPedantsLottie: any;
  private hasInit: boolean;

  override init(params: LimitTaskPedantsParams) {
    LimitTaskPedants.inst = this;
    this.limitTaskPedants = new GameObject('limitTaskPedants', {
      position: addPoint(getScreenLeftTop(), { x: 0, y: 0 }),
    });
    this.hasInit = false;
    this.gameObject.addChild(this.limitTaskPedants);
  }

  override update() {
    if (!this.hasInit) {
      const lp = mx.store.get(StoreName.DeviceLevel);
      lp === '0.0' ? this.initImagePedant() : this.initLottie();
      this.initPendant();
      this.initPendantEvent();
    }
  }

  initPendant() {
    this.limitTaskPedantsRender = this.limitTaskPedants.addComponent(new Render({ visible: false }));
    this.hasInit = true;
  }

  initPendantEvent() {
    mx.event.on(MainAPI.LimitTaskPedantsShow, () => {
      this.limitTaskPedantsRender.visible = true;

      if (this.limitTaskPedantsLottie) {
        this.limitTaskPedantsLottie.play([20, 30]);
        this.limitTaskPedantsLottie.on('complete', () => {
          this.limitTaskPedantsLottie.play([31, 86], { repeats: Infinity });
        });
      }
      stat.exposure("award_task_pendant_exposure", {
        c: 'panel',
        d: 'task'
      });
    });

    mx.event.on(MainAPI.LimitTaskPedantsHide, () => {
      this.limitTaskPedantsRender.visible = false;
    });
    const event = this.limitTaskPedants.addComponent(new Event());
    event.on('tap', () => {
      stat.click("award_task_pendant_click", {
        c: 'panel',
        d: 'task'
      });
      // baseModal.open(MODAL_ID.LIMITED_TIME_BENEFITS_TRIGGER);
      baseModal.open(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
    });
  }

  initLottie() {
    resource.addResource([
      {
        name: 'limit-pendant',
        // @ts-ignore
        type: 'LOTTIE',
        src: {
          json: {
            type: 'json',
            url: 'https://image.uc.cn/s/uae/g/1y/animate/202409/335a16/data.json',
          },
        },
      },
    ]);
    this.limitTaskPedantsLottie = new Lottie({ resource: 'limit-pendant' });

    const limitPendantLottie = new GameObject('limit-pendant-lottie', {
      position: { x: -60, y: 40 },
      size: { width: 120, height: 100 },
      origin: { x: 0, y: 0 },
      anchor: { x: 0, y: 0 },
    });
    limitPendantLottie.addComponent(this.limitTaskPedantsLottie);
    this.limitTaskPedants.addChild(limitPendantLottie);
  }

  initImagePedant() {
    const imgUrl = addImageResource(LimitGiftPendant);
    const limitTip = new GameObject('LimitGiftPendant', {
      position: addPoint(getScreenCenterCenter(), { x: -180, y: 0 }),
      size: { width: 104, height: 122 },
      origin: { x: 0, y: 0 },
      anchor: { x: 0, y: 0 },
    });
    limitTip.addComponent(
      new Img({
        resource: imgUrl,
      }),
    );
    this.limitTaskPedants.addChild(limitTip);
  }
}
