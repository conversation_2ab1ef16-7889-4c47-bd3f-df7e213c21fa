import * as Evac from '@eva/eva.js';
import { Game, GameObject } from '@eva/eva.js';
import { Transition, TransitionSystem } from '@eva/plugin-transition';
import { Text, TextSystem } from '@eva/plugin-renderer-text';
import { Img, ImgSystem } from '@eva/plugin-renderer-img';
import { Render, RenderSystem } from '@eva/plugin-renderer-render';
import { RendererSystem } from '@eva/plugin-renderer';
import { Event, EventSystem } from '@eva/plugin-renderer-event';
import { NinePatchSystem } from '@eva/plugin-renderer-nine-patch';
import { Mask, MaskSystem } from '@eva/plugin-renderer-mask';
import { SpriteAnimation, SpriteAnimationSystem } from '@eva/plugin-renderer-sprite-animation';
import { Spine, SpineSystem } from '@eva/plugin-renderer-spine';
import { Graphics, GraphicsSystem } from '@eva/plugin-renderer-graphics';
import { LottieSystem } from '@eva/plugin-renderer-lottie'

import { Sound, SoundSystem } from '@eva/plugin-sound';
import { A11y } from '@eva/plugin-a11y';
import { isNode } from 'universal-env';

patchGameObject();

function patchGameObject() {
  const Eva = Evac;
  if (!isNode) {
    Eva[Event.componentName] = Event;
    Eva[Img.componentName] = Img;
    Eva[SpriteAnimation.componentName] = SpriteAnimation;
    Eva[Text.componentName] = Text;
    Eva[Mask.componentName] = Mask;
    Eva[Render.componentName] = Render;
    Eva[Transition.componentName] = Transition;
    Eva[Spine.componentName] = Spine;
    Eva[Graphics.componentName] = Graphics;
    Eva[Sound.componentName] = Sound;
    Eva[A11y.componentName] = A11y;
  }

  const oldGetComp = Eva.GameObject.prototype.getComponent;
  Eva.GameObject.prototype.getComponent = function (compType) {
    if (typeof compType !== 'string') {
      compType = compType.componentName;
    }
    return oldGetComp.call(this, compType);
  };

  Eva.GameObject.prototype.addComponentType = function (compType, initParams) {
    return this.addComponent(compType.componentName, initParams);
  };

  Eva.GameObject.prototype.getComponentType = function (compType) {
    return this.getComponent(compType.componentName);
  };

  Eva.GameObject.prototype.setRenderParams = function (v) {
    let render = this.getComponent('Render');
    if (!render) {
      render = this.addComponent(Render);
    }
    return Object.assign(render, v);
  };

  Eva.GameObject.prototype.getCenterPosition = function () {
    const pos = this.transform.position;
    const { size } = this.transform;
    const { origin } = this.transform;
    return {
      x: pos.x + size.width * (origin.x - 0.5),
      y: pos.y + size.height * (0.5 - origin.y),
    };
  };

  Eva.GameObject.prototype.addTouchHandler = function (params) {
    let eventComp = this.getComponent('Event');
    if (!eventComp) {
      eventComp = this.addComponent(Event);
    }
    let lastTime = new Date().getTime();
    params.touchend &&
    eventComp.on('touchend', (obj) => {
      obj.stopPropagation();
      params.touchend(obj.data.position);
    });
    params.tap &&
    eventComp.on('tap', (obj) => {
      const now = new Date().getTime();
      if (now - lastTime < 500) {
        return;
      }
      lastTime = now;
      obj.stopPropagation();
      params.tap(obj.data.position);
    });
    params.touchstart &&
    eventComp.on('touchstart', (obj) => {
      const now = new Date().getTime();
      if (now - lastTime < 500) {
        return;
      }
      lastTime = now;
      obj.stopPropagation();
      params.touchstart(obj.data.position);
    });
    params.touchmove &&
    eventComp.on('touchmove', (obj) => {
      // obj.stopPropagation();
      params.touchmove(obj.data.position);
    });
    params.touchcancel &&
    eventComp.on('touchcancel', (obj) => {
      obj.stopPropagation();
      params.touchcancel(obj.data.position);
    });
  };
}
export function createGame(options) {
  const rendererSystem = new RendererSystem(options.Render);

  const game = new Game({
    autoStart: options.autoStart, // 可选
    frameRate: options.frameRate,
    systems: [
      rendererSystem,
      new RenderSystem(),
      new TextSystem(),
      new EventSystem(),
      new ImgSystem(),
      new NinePatchSystem(),
      new SpriteAnimationSystem(),
      new MaskSystem(),
      new SpineSystem(),
      new GraphicsSystem(),
      new TransitionSystem(),
      new SoundSystem(),
      new LottieSystem()
      // new SpriteTextSystem()
    ],
  });

  return game;
}


export function createImg(res, trans, params) {
  const go = new GameObject(res, trans);
  go.addComponent(Img, params);
  return go;
}

export function createText(res, trans, params) {
  const go = new GameObject(res, trans);
  go.addComponent(Text, params);
  return go;
}

