export interface Point {
  x: number;
  y: number;
}

export function addPoint(pt1: Point, pt2: Point) {
  return {
    x: pt1.x + pt2.x,
    y: pt1.y + pt2.y,
  };
}

export function newPoint(pt: Point): Point {
  return { x: pt.x, y: pt.y };
}

export function subPoint(pt1: Point, pt2: Point) {
  return {
    x: pt1.x - pt2.x,
    y: pt1.y - pt2.y,
  };
}
export function lerp(from: number, to: number, dt: number) {
  return from + (to - from) * dt;
}
export function lerpPoint(from: Point, to: Point, dt: number) {
  return { x: lerp(from.x, to.x, dt), y: lerp(from.y, to.y, dt) };
}

// 计算单位向量
export function sepVector(src: Point, dst: Point): Point {
  const pt = { x: dst.x - src.x, y: dst.y - src.y };
  const max = Math.sqrt(pt.x * pt.x + pt.y * pt.y);
  return { x: pt.x / max, y: pt.y / max };
}

// 把向量单位化
export function normalizeSelf(pt: Point): Point {
  const max = Math.sqrt(pt.x * pt.x + pt.y * pt.y);
  return { x: pt.x / max, y: pt.y / max };
}

export function getRadiansFromVector(pt: Point): number {
  return Math.atan(pt.y / pt.x);
}

export function mulSelf(pt: Point, mul: number): Point {
  return { x: pt.x * mul, y: pt.y * mul };
}

// 计算从src往dst走dist走到的坐标点
export function getMoveDstPosition(src: Point, dst: Point, dist: number): Point {
  let vec = sepVector(src, dst);
  vec.x = vec.x * dist + src.x;
  vec.y = vec.y * dist + src.y;
  return vec;
}

export function distance(pt1: Point, pt2: Point): number {
  const dx = pt2.x - pt1.x;
  const dy = pt2.y - pt1.y;

  return Math.sqrt(dx * dx + dy * dy);
}
