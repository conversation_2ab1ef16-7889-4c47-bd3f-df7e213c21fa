let list: { [key: string]: Function } = {};
function getTargetObjectClientRect(key: string) {
  try {
    if (list[key]) {
      return list[key]();
    }
  } catch (error) {
    console.log(error);
  }
  return null;
}

export function register(key: string, fun: Function) {
  let win = window as any;
  if (!win.gameUtil) {
    win.gameUtil = {
      allGameObjectKey: [],
      getTargetObjectClientRect,
    };
  }
  win.gameUtil.allGameObjectKey.push(key);
  list[key] = fun;
}
