export class Pool<T> {
  private _objs: T[] = [];
  private _cur = -1;

  get num() {
    return this._cur + 1;
  }

  has() {
    return this._cur >= 0;
  }
  get(): T | null {
    if (this._cur < 0) {
      return null;
    }
    const obj = this._objs[this._cur];
    this._cur--;
    return obj;
  }

  contain(v: T) {
    for (let i = 0; i <= this._cur; ++i) {
      if (this._objs[i] === v) {
        return true;
      }
    }
    return false;
  }

  put(v: T) {
    // if(this.contain(v)){
    //     console.log("重复put",v);
    // }
    this._cur++;
    this._objs[this._cur] = v;
  }

  clean() {
    this._objs = [];
    this._cur = -1;
  }
}
