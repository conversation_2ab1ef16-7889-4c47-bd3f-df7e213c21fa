import { resource, RESOURCE_TYPE, TransformParams } from '@eva/eva.js';
import { getResByName } from '../resource';
import { createImg } from './eva';

export function createImageFromUrl(url: string, transform: TransformParams) {
  const resName = url;

  if (!getResByName(resName)) {
    resource.addResource([
      {
        name: resName,
        type: RESOURCE_TYPE.IMAGE,
        src: {
          image: {
            url,
            type: 'png',
          },
        },
      },
    ]);
  }

  return createImg(resName, transform, { resource: resName });
}
