// import World from '../uc/World';
// import { TbWorld } from '../business/gameObject/TbWorld';

let _delayHandles = [];

export async function delay(dt: number) {
  return new Promise((resolve) => {
    let handler = setTimeout(() => {
      // @ts-ignore
      let idx = _delayHandles.indexOf(handler);
      if (idx !== -1) {
        _delayHandles.splice(idx, 1);
      }
      resolve(true);
    }, dt);
    // @ts-ignore
    _delayHandles.push(handler);
  });
}

export function tick(dt: number, func: () => boolean) {
  delay(dt).then(() => {
    if (func()) {
      tick(dt, func);
    }
  });
}

export function cancelAllDelay() {
  _delayHandles.forEach((h) => {
    clearTimeout(h);
  });
  _delayHandles = [];
}
