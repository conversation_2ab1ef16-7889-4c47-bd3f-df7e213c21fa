const MaxHeight = 1420;
const MinHeight = 1300;

// 长屏幕判断方法 长宽比大于2且主文档高度大于760
export function isHighMode() {
  const screenWidth = window.screen.width
  const screenHeight = window.screen.height
  const ratio = screenHeight / screenWidth;
  return ratio > 2 && screenHeight > 780
}

export function getGameHeight() {
  if (isHighMode()) {
    return MaxHeight;
  }
  return MinHeight;
}

export function getScreenTopY() {
  return 0;
}

export function getScreenBottomY() {
  return getScreenHeight();
}

export function getScreenCenterY() {
  return getScreenHeight() * 0.5;
}

export function getScreenLeftX() {
  return 0;
}

export function getScreenRightX() {
  return getScreenWidth();
}

export function getScreenCenterX() {
  return getScreenWidth() * 0.5;
}

export function getScreenWidth() {
  return 750;
}


export function getScreenHeight() {
  return getGameHeight();
}

export function getScreenLeftTop() {
  return { x: getScreenLeftX(), y: getScreenTopY() };
}

export function getScreenRightTop() {
  return { x: getScreenRightX(), y: getScreenTopY() };
}

export function getScreenCenterTop() {
  return { x: getScreenCenterX(), y: getScreenTopY() };
}

export function getScreenLeftCenter() {
  return { x: getScreenLeftX(), y: getScreenCenterY() };
}

export function getScreenRightCenter() {
  return { x: getScreenRightX(), y: getScreenCenterY() };
}

export function getScreenCenterCenter() {
  return { x: getScreenCenterX(), y: getScreenCenterY() };
}

export function getScreenLeftBottom() {
  return { x: getScreenLeftX(), y: getScreenBottomY() };
}

export function getScreenRightBottom() {
  return { x: getScreenRightX(), y: getScreenBottomY() };
}

export function getScreenCenterBottom() {
  return { x: getScreenCenterX(), y: getScreenBottomY() };
}

export function getScreenLeftCenterX() {
  return getScreenCenterX() * 0.5;
}

export function getScreenRightCenterX() {
  return getScreenCenterX() * 1.5;
}

export function getOriginCenterBottom() {
  return { x: 0.5, y: 1 };
}

export const pageViewHeight = window.innerHeight || document.documentElement.clientHeight;
export const pageViewWidth = window.innerWidth || document.documentElement.clientWidth;

/**
 * 游戏canvas元素尺寸自适应计算
 * @param num 设计稿尺寸
 * @param isXAxis 相对x轴方向
 * @param scale 尺寸设计稿倍数，默认2
 * @returns 自适应尺寸
 */
export const gamePosCalc = ({
  num,
  heightOffsetDirect = 1,
  isXAxis = false,
}: {
  num: number;
  heightOffsetDirect?: 1 | -1;
  isXAxis?: boolean;
}): number => {
  const xRatio = pageViewWidth / 375;
  const yRatio = pageViewHeight / 812;
  let result = num;

  if (isXAxis) {
    result *= xRatio;
  } else {
    const finalNum = result;
    result = (finalNum * yRatio) / xRatio;
  }
  return result;
};
