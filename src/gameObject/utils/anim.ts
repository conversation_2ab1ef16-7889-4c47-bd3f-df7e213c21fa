import { COMPONENT, GameObject } from '@eva/eva.js';
// import { getScreenHeight } from '@ali/multimod-tmfarm__shared';
import { Render } from '@eva/plugin-renderer-render';
import { Transition } from '@eva/plugin-transition';

export function addAnim(gameObject: GameObject, animName: string, values: any[]): Transition {
  let transition = gameObject.getComponent(Transition);
  if (!transition) {
    transition = gameObject.addComponent(Transition);
    transition.group = {};
  }
  if (transition.group[animName]) {
    (transition as any).animations[animName] = null;
    transition.group[animName] = null;
  }
  transition.group[animName] = values;
  return transition;
}

export function playOnce(anim, animName) {
  return new Promise((resolve, reject) => {
    if (!anim.gameObject) {
      return;
    }
    anim.play(animName, 1);
    anim.once('finish', (a) => {
      resolve(true);
    });
  });
}
export let frameTime = 1000 / 30;

export function getDropRedBagAnim(obj: GameObject) {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  return [
    {
      name: 'alpha',
      component: obj.getComponent(Render.componentName),
      values: [
        {
          time: 0,
          value: 0,
          tween: 'ease-in',
        },
        {
          time: 22 * frameTime,
          value: 0,
          tween: 'ease-in',
        },
        {
          time: 23 * frameTime,
          value: 0.5,
          tween: 'ease-in',
        },
        {
          time: 32 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 103 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 107 * frameTime,
          value: 1,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'position.y',
      component: obj.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 22 * frameTime,
          value: -350,
          tween: 'linear',
        },
        {
          time: 25 * frameTime,
          value: -540,
          tween: 'linear',
        },
        {
          time: 53 * frameTime,
          value: 0,
          tween: 'linear',
        },
        {
          time: 60 * frameTime,
          value: -17,
          tween: 'linear',
        },
        {
          time: 62 * frameTime,
          value: -19,
          tween: 'linear',
        },
        {
          time: 67 * frameTime,
          value: 0,
          tween: 'linear',
        },
        {
          time: 70 * frameTime,
          value: 0,
          tween: 'linear',
        },
        {
          time: 72 * frameTime,
          value: -5,
          tween: 'linear',
        },
        {
          time: 74 * frameTime,
          value: -12,
          tween: 'linear',
        },
        {
          time: 76 * frameTime,
          value: -12,
          tween: 'linear',
        },
        {
          time: 80 * frameTime,
          value: 0,
          tween: 'linear',
        },
        {
          time: 107 * frameTime,
          value: 0,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'position.x',
      component: obj.transform,
      values: [
        {
          time: 0,
          value: 0,
          tween: 'ease-in',
        },
        //   {
        //     time: 22*frameTime,
        //     value: 0,
        //     tween:"linear"
        //   },
        {
          time: 53 * frameTime,
          value: -130,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'rotation',
      component: obj.getComponent('Transform'),
      values: [
        {
          time: 0,
          value: 0,
          tween: 'linear',
        },
        {
          time: 53 * frameTime,
          value: 2 * Math.PI,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'scale.y',
      component: obj.transform,
      values: [
        {
          time: 0,
          value: 0,
          tween: 'ease-in',
        },
        {
          time: 22 * frameTime,
          value: 0,
          tween: 'linear',
        },
        {
          time: 23 * frameTime,
          value: 0.5,
          tween: 'linear',
        },
        {
          time: 53 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 56 * frameTime,
          value: 0.9,
          tween: 'linear',
        },
        {
          time: 58 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 60 * frameTime,
          value: 1.1,
          tween: 'linear',
        },
        {
          time: 62 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 67 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 70 * frameTime,
          value: 0.9,
          tween: 'linear',
        },
        {
          time: 72 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 74 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 76 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 80 * frameTime,
          value: 0.9,
          tween: 'linear',
        },
        {
          time: 83 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 107 * frameTime,
          value: 1,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'scale.x',
      component: obj.transform,
      values: [
        {
          time: 0,
          value: 0,
          tween: 'ease-in',
        },
        {
          time: 22 * frameTime,
          value: 0,
          tween: 'linear',
        },
        {
          time: 23 * frameTime,
          value: 0.5,
          tween: 'linear',
        },
        {
          time: 53 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 56 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 58 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 60 * frameTime,
          value: 0.9,
          tween: 'linear',
        },
        {
          time: 62 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 67 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 70 * frameTime,
          value: 1.1,
          tween: 'linear',
        },
        {
          time: 72 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 74 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 76 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 80 * frameTime,
          value: 1.1,
          tween: 'linear',
        },
        {
          time: 83 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 107 * frameTime,
          value: 1,
          tween: 'linear',
        },
      ],
    },
  ];
}

export function getDropRedBagTextAnim(obj) {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  return [
    {
      name: 'alpha',
      component: obj.getComponent(Render.componentName),
      values: [
        {
          time: 0,
          value: 0,
          tween: 'ease-in',
        },
        {
          time: 83 * frameTime,
          value: 0,
          tween: 'linear',
        },
        {
          time: 93 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 103 * frameTime,
          value: 1,
          tween: 'linear',
        },
        {
          time: 107 * frameTime,
          value: 1,
          tween: 'linear',
        },
      ],
    },
  ];
}

export function createFeiliaoDropAnimParams(obj: GameObject, y: number) {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  const tran = obj.getComponent('Transform') as any;
  const startY = tran.position.y;
  const destY = startY + y;
  return [
    {
      name: 'scale.x',
      component: tran,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'linear',
        },
        {
          time: 2 * frameTime,
          value: 0.8,
          tween: 'linear',
        },
        {
          time: 7 * frameTime,
          value: 1.2,
          tween: 'linear',
        },
        {
          time: 13 * frameTime,
          value: 1.2,
          tween: 'linear',
        },
        {
          time: 29 * frameTime,
          value: 0.5,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'scale.y',
      component: tran,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'linear',
        },
        {
          time: 2 * frameTime,
          value: 0.8,
          tween: 'linear',
        },
        {
          time: 7 * frameTime,
          value: 1.2,
          tween: 'linear',
        },
        {
          time: 13 * frameTime,
          value: 1.2,
          tween: 'linear',
        },
        {
          time: 29 * frameTime,
          value: 0.5,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'alpha',
      component: obj.getComponent(Render.componentName),
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 26 * frameTime,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 29 * frameTime,
          value: 0,
          tween: 'ease-out',
        },
      ],
    },
    {
      name: 'position.y',
      component: tran,
      values: [
        {
          time: 0,
          value: startY,
          tween: 'ease-out',
        },
        {
          time: 13 * frameTime,
          value: startY,
          tween: 'ease-in',
        },
        {
          time: 29 * frameTime,
          value: destY,
          tween: 'ease-in',
        },
      ],
    },
  ];
}

export function getShakingAnim(imgObj: GameObject, param: number) {
  return [
    {
      name: 'rotation',
      component: imgObj.transform,
      values: [
        {
          time: 0,
          value: 0,
          tween: 'linear',
        },
        {
          time: 100 * param,
          value: (1 / 15) * Math.PI,
          tween: 'linear',
        },
        {
          time: 200 * param,
          value: (1 / 10) * Math.PI,
          tween: 'linear',
        },
        {
          time: 350 * param,
          value: (-1 / 15) * Math.PI,
          tween: 'linear',
        },
        {
          time: 500 * param,
          value: (1 / 20) * Math.PI,
          tween: 'linear',
        },
        {
          time: 550 * param,
          value: (1 / 15) * Math.PI,
          tween: 'linear',
        },
        {
          time: 700 * param,
          value: (-1 / 15) * Math.PI,
          tween: 'linear',
        },
        {
          time: 850 * param,
          value: (1 / 15) * Math.PI,
          tween: 'linear',
        },
        {
          time: 1000 * param,
          value: (-1 / 15) * Math.PI,
          tween: 'linear',
        },
        {
          time: 1100 * param,
          value: 0,
          tween: 'linear',
        },
        {
          time: 3000,
          value: 0,
          tween: 'linear',
        },
      ],
    },
  ];
}
export function createHouseTipsAnim(obj: GameObject) {
  const tran = obj.getComponent('Transform') as any;
  const startY = tran.position.y;
  return {
    name: 'position.y',
    component: tran,
    values: [
      {
        time: 0,
        value: startY,
        tween: 'linear',
      },
      {
        time: 1000,
        value: startY - 10,
        tween: 'linear',
      },
      {
        time: 2000,
        value: startY,
        tween: 'linear',
      },
    ],
  };
}
/**
 * 上下匀速移动
 * @param obj
 * @param dt 毫秒
 */
export function createMoveYLinearAnimParams(obj: GameObject, dt: number, y: number) {
  const tran = obj.getComponent('Transform') as any;
  const startY = tran.position.y;
  const destY = startY + y;
  return {
    name: 'position.y',
    component: tran,
    values: [
      {
        time: 0,
        value: startY,
        tween: 'linear',
      },
      {
        time: dt,
        value: destY,
        tween: 'linear',
      },
      {
        time: dt * 2,
        value: startY,
        tween: 'linear',
      },
    ],
  };
}

/**
 * 渐变进入
 * @param obj
 * @param dt 毫秒
 */
export function createFadeInAnimParams(obj: GameObject, dt: number, waitTime = 0, tween = 'linear') {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  return {
    name: 'alpha',
    component: obj.getComponent(Render.componentName),
    values: [
      {
        time: 0,
        value: 0,
        tween: tween,
      },
      {
        time: waitTime,
        value: 0,
        tween: tween,
      },
      {
        time: waitTime + dt,
        value: 1,
      },
    ],
  };
}
/**
 * 背包一键施肥图片动画
 */
export function createPackSackImgAnimGroup(node: GameObject, startPosY: number, endPosY: number) {
  let render = node.getComponent(Render.componentName);
  if (!render) {
    render = node.addComponent(Render);
  }
  return [
    {
      name: 'alpha',
      component: render,
      values: [
        {
          time: 0,
          value: 0,
          tween: 'linear',
        },
        {
          time: 420,
          value: 1,
          tween: 'linear',
        },
        {
          time: 900,
          value: 1,
          tween: 'linear',
        },
        {
          time: 1200,
          value: 0,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'position.y',
      component: node.transform,
      values: [
        {
          time: 0,
          value: startPosY,
          tween: 'ease-out',
        },
        {
          time: 1200,
          value: endPosY,
          tween: 'ease-out',
        },
      ],
    },
  ];
}

// export function createfuqiballfadeInAnimParams(obj: GameObject, dt: number, waitTime = 0) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return {
//     name: 'alpha',
//     component: obj.getComponent(Render.componentName),
//     values: [
//       {
//         time: 0,
//         value: 0.3,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime,
//         value: 0.3,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime + dt,
//         value: 1,
//       },
//     ],
//   };
// }

export function createdailyfuqiballfadeInAnimParams(obj: GameObject, dt: number, waitTime: number) {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  return {
    name: 'alpha',
    component: obj.getComponent(Render.componentName),
    values: [
      {
        time: 0,
        value: 0,
        tween: 'linear',
      },
      {
        time: waitTime,
        value: 0,
        tween: 'linear',
      },
      {
        time: (waitTime + dt) * 0.2,
        value: 1,
        tween: 'linear',
      },
      {
        time: (waitTime + dt) * 0.6,
        value: 1,
        tween: 'linear',
      },
      {
        time: waitTime + dt,
        value: 0,
        tween: 'linear',
      },
    ],
  };
}

export function createStarfadeInAnimParams(obj: GameObject, dt: number, waitTime = 0) {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  return {
    name: 'alpha',
    component: obj.getComponent(Render.componentName),
    values: [
      {
        time: 0,
        value: 0.4,
        tween: 'ease-out',
      },
      {
        time: waitTime,
        value: 0.4,
        tween: 'ease-out',
      },
      {
        time: waitTime + dt,
        value: 1,
      },
    ],
  };
}

/**
 * 渐变消失
 * @param obj
 * @param dt 毫秒
 */
export function createFadeOutAnimParams(obj: GameObject, dt: number, tween = 'linear') {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  return {
    name: 'alpha',
    component: obj.getComponent(Render.componentName),
    values: [
      {
        time: 0,
        value: 1,
        tween: tween,
      },
      {
        time: dt,
        value: 0,
        tween: tween,
      },
    ],
  };
}

export function createStarfadeOutAnimParams(obj: GameObject, dt: number) {
  if (!obj.getComponent(Render.componentName)) {
    obj.addComponent(Render);
  }
  return {
    name: 'alpha',
    component: obj.getComponent(Render.componentName),
    values: [
      {
        time: 0,
        value: 1,
        tween: 'ease-out',
      },
      {
        time: dt,
        value: 0.4,
      },
    ],
  };
}

// export function createStarEndfadeOutAnimParams(obj: GameObject, dt: number) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return {
//     name: 'alpha',
//     component: obj.getComponent(Render.componentName),
//     values: [
//       {
//         time: 0,
//         value: 0.4,
//         tween: 'ease-out',
//       },
//       {
//         time: dt,
//         value: 0,
//       },
//     ],
//   };
// }

// export function createBallfadeOutAnimParams(obj: GameObject, dt: number) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return {
//     name: 'alpha',
//     component: obj.getComponent(Render.componentName),
//     values: [
//       {
//         time: 0,
//         value: 1,
//         tween: 'ease-out',
//       },
//       {
//         time: dt,
//         value: 0.2,
//       },
//     ],
//   };
// }

// 铲子消失
// export function createChanzifadeOutAnimParams(obj: GameObject, dt: number) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return {
//     name: 'alpha',
//     component: obj.getComponent(Render.componentName),
//     values: [
//       {
//         time: 0,
//         value: 1,
//         tween: 'ease-out',
//       },
//       {
//         time: dt,
//         value: 0.0,
//       },
//     ],
//   };
// }

// 明日福气渐隐渐出
// export function createDailyFuQifadeAnimParams(obj: GameObject, dt: number, waitTime: number) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return {
//     name: 'alpha',
//     component: obj.getComponent(Render.componentName),
//     values: [
//       {
//         time: 0,
//         value: 1,
//         tween: 'ease-out',
//       },
//       {
//         time: dt,
//         value: 0,
//         tween: 'ease-in',
//       },
//       {
//         time: dt + waitTime + 450,
//         value: 1,
//         tween: 'ease-out',
//       },
//     ],
//   };
// }
/**
 * 落地效果
 * @param obj
 * @param dt 毫秒
 */
// export function createDropOnLandAnimParams(obj: GameObject, waitTime: number, dt: number, ratio = 0.8) {
//   return {
//     name: 'scale.y',
//     component: obj.getComponent('Transform'),
//     values: [
//       {
//         time: 0,
//         value: 1,
//         tween: 'ease-out',
//       },
//       {
//         time: waitTime,
//         value: 0.9,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime + dt * ratio,
//         value: 1.2,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime + dt,
//         value: 1,
//       },
//     ],
//   };
// }

/**
 * 树动画
 * @param obj
 * @param dt 毫秒
 */

export function createFuqiAnimParams(
  obj: GameObject,
  waitTime: number,
  upMove: { dt: number; y: number },
  downMove: { dt: number; h: number },
) {
  let tran = obj.getComponent('Transform') as any;
  let startY = tran.position.y;
  let upDestY = upMove.y - startY;
  let targetY = upDestY - downMove.h;
  return {
    name: 'position.y',
    component: tran,
    values: [
      {
        time: 0,
        value: startY,
        tween: 'linear',
      },
      {
        time: waitTime,
        value: startY,
        tween: 'linear',
      },
      {
        time: waitTime + upMove.dt,
        value: upDestY,
        tween: 'linear',
      },
      {
        time: waitTime + upMove.dt + downMove.dt,
        value: targetY,
        tween: 'linear',
      },
    ],
  };
}

// export function createJumpAnimParams(
//   obj: GameObject,
//   waitTime: number,
//   upMove: { dt: number; h: number },
//   downMove: { dt: number; y: number },
// ) {
//   let tran = obj.getComponent('Transform') as any;
//   let startY = tran.position.y;
//   let upDestY = startY + upMove.h;
//   let targetY = downMove.y;
//   return {
//     name: 'position.y',
//     component: tran,
//     values: [
//       {
//         time: 0,
//         value: startY,
//         tween: 'ease-out',
//       },
//       {
//         time: waitTime,
//         value: startY,
//         tween: 'ease-out',
//       },
//       {
//         time: waitTime + upMove.dt,
//         value: upDestY,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime + upMove.dt + downMove.dt,
//         value: targetY,
//         tween: 'ease-in',
//       },
//     ],
//   };
// }

// export function createFuqiJumpAnimParams(
//   obj: GameObject,
//   waitTime: number,
//   upMove: { dt: number; y: number },
//   downMove: { dt: number; h: number },
//   newupMove: { dt: number; h: number },
// ) {
//   let tran = obj.getComponent('Transform') as any;
//   let startY = tran.position.y;
//   let upDestY = upMove.y - startY;
//   let targetY = upDestY + downMove.h;
//   let newtargetY = targetY - newupMove.h;
//   return {
//     name: 'position.y',
//     component: tran,
//     values: [
//       {
//         time: 0,
//         value: startY,
//         tween: 'ease-out',
//       },
//       {
//         time: 0,
//         value: startY,
//         tween: 'ease-out',
//       },
//       {
//         time: waitTime + upMove.dt,
//         value: upDestY,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime + upMove.dt + downMove.dt,
//         value: targetY,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime + upMove.dt + downMove.dt + newupMove.dt,
//         value: newtargetY,
//         tween: 'ease-in',
//       },
//     ],
//   };
// }

// 提示框上下浮动
// export function createPointMoveYLinearAnimParams(obj: GameObject, dt: number, h: number) {
//   let tran = obj.getComponent('Transform') as any;
//   let startY = tran.position.y;
//   let destY = startY + h;
//   return {
//     name: 'position.y',
//     component: tran,
//     values: [
//       {
//         time: 0,
//         value: startY,
//         tween: 'linear',
//       },
//       {
//         time: dt,
//         value: destY,
//         tween: 'linear',
//       },
//       {
//         time: dt * 2,
//         value: startY,
//         tween: 'linear',
//       },
//     ],
//   };
// }

/**
 * 加速上移
 * @param obj
 * @param dt 毫秒
 */
// export function createMoveYAnimParams(obj: GameObject, waitTime: number, dt: number, y: number) {
//   let tran = obj.getComponent('Transform') as any;
//   let startY = tran.position.y;
//   let destY = startY + y;
//   return {
//     name: 'position.y',
//     component: tran,
//     values: [
//       {
//         time: 0,
//         value: startY,
//         tween: 'ease-out',
//       },
//       {
//         time: waitTime,
//         value: startY,
//         tween: 'ease-in',
//       },
//       {
//         time: waitTime + dt,
//         value: destY,
//         tween: 'ease-in',
//       },
//     ],
//   };
// }

/**
 * 在dt时间内移动到toPos
 * @param obj
 * @param toPos
 * @param dt
 */
// export function createMoveToAnim(obj: GameObject, toPos: { x: number; y: number }, dt: number) {}

/**
 * 水平方向匀速平移
 * @param obj
 * @param dt 毫秒
 */
export function createEvenMoveXAnimParams(obj: GameObject, waitTime: number, dt: number, targetX: number) {
  let tran = obj.getComponent('Transform') as any;
  let startX = tran.position.x;

  return {
    name: 'position.x',
    component: tran,
    values: [
      {
        time: 0,
        value: startX,
        tween: 'ease-out',
      },
      {
        time: waitTime,
        value: startX,
        tween: 'linear',
      },
      {
        time: waitTime + dt,
        value: targetX,
        tween: 'linear',
      },
    ],
  };
}

export function createEvenMoveYAnimParams(obj: GameObject, waitTime: number, dt: number, targetY: number) {
  let tran = obj.getComponent('Transform') as any;
  let startY = tran.position.y;

  return {
    name: 'position.y',
    component: tran,
    values: [
      {
        time: 0,
        value: startY,
        tween: 'linear',
      },
      {
        time: waitTime,
        value: startY,
        tween: 'linear',
      },
      {
        time: waitTime + dt,
        value: targetY,
        tween: 'linear',
      },
    ],
  };
}

export function createStarMoveXAnimParams(obj: GameObject, waitTime: number, dt: number) {
  let tran = obj.getComponent('Transform') as any;
  let startX = tran.position.x;
  let offsetX = -30;
  return {
    name: 'position.x',
    component: tran,
    values: [
      {
        time: 0,
        value: startX,
        tween: 'linear',
      },
      {
        time: waitTime,
        value: startX,
        tween: 'linear',
      },
      {
        time: waitTime + (dt * 1) / 2,
        value: startX + offsetX,
        tween: 'linear',
      },
      {
        time: waitTime + dt,
        value: startX,
        tween: 'linear',
      },
    ],
  };
}
/**
 * 加速平移
 * @param obj
 * @param dt 毫秒
 */
export function createMoveXAnimParams(obj: GameObject, waitTime: number, dt: number, x: number) {
  let tran = obj.getComponent('Transform') as any;
  let startX = tran.position.x;
  let destX = startX + x;
  return {
    name: 'position.x',
    component: tran,
    values: [
      {
        time: 0,
        value: startX,
        tween: 'ease-out',
      },
      {
        time: waitTime,
        value: startX,
        tween: 'ease-in',
      },
      {
        time: waitTime + dt,
        value: destX,
        tween: 'ease-in',
      },
    ],
  };
}

// export function createRotateElasticAnim(obj: GameObject, dt: number, from: number, to: number) {
//   return {
//     name: 'rotation',
//     component: obj.getComponent('Transform'),
//     values: [
//       {
//         time: 0,
//         value: from,
//         tween: 'ease-in',
//       },
//       {
//         time: dt,
//         value: to,
//         tween: 'ease-out',
//       },
//     ],
//   };
// }

export function createScaleXAnimParams(obj: GameObject, dt: number, from: number, to: number, waitTime = 0) {
  return {
    name: 'scale.x',
    component: obj.getComponent('Transform'),
    values: [
      {
        time: 0,
        value: from,
        tween: 'ease-out',
      },
      {
        time: waitTime,
        value: to,
        tween: 'ease-in',
      },
      {
        time: dt + waitTime,
        value: to,
        tween: 'ease-out',
      },
    ],
  };
}

export function createScaleYAnimParams(obj: GameObject, dt: number, from: number, to: number, waitTime = 0) {
  return {
    name: 'scale.y',
    component: obj.getComponent('Transform'),
    values: [
      {
        time: 0,
        value: from,
        tween: 'ease-out',
      },
      {
        time: waitTime,
        value: to,
        tween: 'ease-in',
      },
      {
        time: dt + waitTime,
        value: to,
        tween: 'ease-out',
      },
    ],
  };
}

// export function createbtmwaterchatter(obj: GameObject, waitTime: number) {
//   return [
//     {
//       name: 'scale.y',
//       component: obj.transform,
//       values: [
//         {
//           time: waitTime,
//           value: 1.1,
//           tween: 'ease-in',
//         },
//         {
//           time: waitTime + 60,
//           value: 0.98,
//           tween: 'ease-in',
//         },
//         {
//           time: waitTime + 120,
//           value: 1.1,
//           tween: 'ease-in',
//         },
//       ],
//     },
//     {
//       name: 'scale.x',
//       component: obj.transform,
//       values: [
//         {
//           time: waitTime,
//           value: 1.1,
//           tween: 'ease-in',
//         },
//         {
//           time: waitTime + 60,
//           value: 0.98,
//           tween: 'ease-in',
//         },
//         {
//           time: waitTime + 120,
//           value: 1.1,
//           tween: 'ease-in',
//         },
//       ],
//     },
//   ];
// }

// export function createScaleXOnTweenAnimParams(
//   obj: GameObject,
//   dt: number,
//   from: number,
//   to: number,
//   waitTime = 0,
//   ease?: string,
// ) {
//   ease = ease || 'linear';
//   return {
//     name: 'scale.x',
//     component: obj.getComponent('Transform'),
//     values: [
//       {
//         time: 0,
//         value: from,
//         tween: 'ease-out',
//       },
//       {
//         time: waitTime,
//         value: from,
//         tween: ease,
//       },
//       {
//         time: dt + waitTime,
//         value: to,
//         tween: 'ease-out',
//       },
//     ],
//   };
// }
//
// export function createScaleYOnTweenAnimParams(
//   obj: GameObject,
//   dt: number,
//   from: number,
//   to: number,
//   waitTime = 0,
//   ease?: string,
// ) {
//   ease = ease || 'linear';
//   return {
//     name: 'scale.y',
//     component: obj.getComponent('Transform'),
//     values: [
//       {
//         time: 0,
//         value: from,
//         tween: 'ease-out',
//       },
//       {
//         time: waitTime,
//         value: from,
//         tween: ease,
//       },
//       {
//         time: dt + waitTime,
//         value: to,
//         tween: 'ease-out',
//       },
//     ],
//   };
// }

export function getTreeClickAnim(gameObject: GameObject) {
  return [
    {
      name: 'scale.y',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 90,
          value: 1.02,
          tween: 'ease-in',
        },
        {
          time: 240,
          value: 0.98,
          tween: 'ease-in',
        },
        {
          time: 390,
          value: 1.02,
          tween: 'ease-in',
        },
        {
          time: 480,
          value: 0.99,
          tween: 'ease-in',
        },
        {
          time: 570,
          value: 0.99,
          tween: 'ease-in',
        },
        {
          time: 630,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
    {
      name: 'scale.x',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 90,
          value: 0.98,
          tween: 'ease-in',
        },
        {
          time: 240,
          value: 1.02,
          tween: 'ease-in',
        },
        {
          time: 390,
          value: 0.99,
          tween: 'ease-in',
        },
        {
          time: 480,
          value: 1.005,
          tween: 'ease-in',
        },
        {
          time: 570,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 630,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
  ];
}

export function getTreeHuXiAnim(gameObject: GameObject) {
  return [
    {
      name: 'scale.y',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 40 * frameTime,
          value: 1.01,
          tween: 'ease-in',
        },
        {
          time: 80 * frameTime,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
    {
      name: 'scale.x',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 40 * frameTime,
          value: 0.99,
          tween: 'ease-in',
        },
        {
          time: 80 * frameTime,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
  ];
}

export function getTreeCanExchangeFadeInAnim(gameObject: GameObject) {
  return [
    {
      name: 'alpha',
      component: gameObject.getComponent(Render.componentName),
      values: [
        {
          time: 0,
          value: 0,
          tween: 'ease-in',
        },
        {
          time: 22 * frameTime,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
    {
      name: 'scale.x',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-out',
        },
        {
          time: 6 * frameTime,
          value: 0.98,
          tween: 'ease-out',
        },
        {
          time: 9 * frameTime,
          value: 1.02,
          tween: 'ease-out',
        },
        {
          time: 12 * frameTime,
          value: 0.99,
          tween: 'ease-out',
        },
        {
          time: 15 * frameTime,
          value: 1,
          tween: 'ease-out',
        },
      ],
    },
    {
      name: 'scale.y',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-out',
        },
        {
          time: 8 * frameTime,
          value: 1.02,
          tween: 'ease-out',
        },
        {
          time: 9 * frameTime,
          value: 0.98,
          tween: 'ease-out',
        },
        {
          time: 12 * frameTime,
          value: 1.01,
          tween: 'ease-out',
        },
        {
          time: 15 * frameTime,
          value: 1,
          tween: 'ease-out',
        },
      ],
    },
  ];
}

export function getTreeCanExchangeFadeOutAnim(gameObject: GameObject) {
  return [
    {
      name: 'alpha',
      component: gameObject.getComponent(Render.componentName),
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 10 * frameTime,
          value: 1,
          tween: 'ease-out',
        },
        {
          time: 23 * frameTime,
          value: 0,
          tween: 'ease-in',
        },
      ],
    },
    {
      name: 'scale.x',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-out',
        },
        {
          time: 3 * frameTime,
          value: 1.02,
          tween: 'ease-out',
        },
        {
          time: 7 * frameTime,
          value: 0.98,
          tween: 'ease-out',
        },
        {
          time: 10 * frameTime,
          value: 1,
          tween: 'ease-out',
        },
      ],
    },
    {
      name: 'scale.y',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 3 * frameTime,
          value: 0.98,
          tween: 'ease-in',
        },
        {
          time: 7 * frameTime,
          value: 1.02,
          tween: 'ease-in',
        },
        {
          time: 10 * frameTime,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
  ];
}

export function getTreeScaleAnim(gameObject: GameObject) {
  return [
    {
      name: 'scale.y',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-out',
        },
        {
          time: 90,
          value: 1.01,
          tween: 'ease-out',
        },
        {
          time: 210,
          value: 0.99,
          tween: 'ease-out',
        },
        {
          time: 300,
          value: 1,
          tween: 'ease-out',
        },
      ],
    },
    {
      name: 'scale.x',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 90,
          value: 0.99,
          tween: 'ease-in',
        },
        {
          time: 210,
          value: 1.01,
          tween: 'ease-in',
        },
        {
          time: 300,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
  ];
}
//
// export function getHuanAnim(gameObject: GameObject) {
//   return [
//     {
//       name: 'alpha',
//       component: gameObject.getComponent(COMPONENT['Render']),
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'ease-in',
//         },
//         {
//           time: 12 * frameTime,
//           value: 0,
//           tween: 'ease-in',
//         },
//         {
//           time: 21 * frameTime,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 29 * frameTime,
//           value: 0,
//           tween: 'ease-out',
//         },
//       ],
//     },
//
//     {
//       name: 'scale.y',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 10 * frameTime,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 29 * frameTime,
//           value: 2,
//           tween: 'ease-in',
//         },
//       ],
//     },
//     {
//       name: 'scale.x',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 10 * frameTime,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 29 * frameTime,
//           value: 2,
//           tween: 'ease-in',
//         },
//       ],
//     },
//   ];
// }
//
// export function getLightAnim(gameObject: GameObject, waitTime: number) {
//   return [
//     {
//       name: 'alpha',
//       component: gameObject.getComponent(COMPONENT['Render']),
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'ease-in',
//         },
//         {
//           time: waitTime,
//           value: 0,
//           tween: 'ease-in',
//         },
//         {
//           time: 5 * frameTime + waitTime,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 15 * frameTime + waitTime,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 20 * frameTime + waitTime,
//           value: 0,
//           tween: 'ease-in',
//         },
//       ],
//     },
//
//     {
//       name: 'scale.y',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: 0.2,
//           tween: 'ease-in',
//         },
//         {
//           time: waitTime,
//           value: 0.2,
//           tween: 'ease-in',
//         },
//         {
//           time: 20 * frameTime + waitTime,
//           value: 2,
//           tween: 'ease-in',
//         },
//       ],
//     },
//     {
//       name: 'scale.x',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: 0.2,
//           tween: 'ease-in',
//         },
//         {
//           time: waitTime,
//           value: 0.2,
//           tween: 'ease-in',
//         },
//         {
//           time: 20 * frameTime + waitTime,
//           value: 2,
//           tween: 'ease-in',
//         },
//       ],
//     },
//   ];
// }
//
// export function getHandMoveAnim(gameObject: GameObject) {
//   let pos = gameObject.transform.position;
//   return [
//     {
//       name: 'position.y',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: pos.y,
//           tween: 'ease-in',
//         },
//         {
//           time: 250,
//           value: pos.y + 17,
//           tween: 'ease-out',
//         },
//         {
//           time: 500,
//           value: pos.y,
//           tween: 'ease-in',
//         },
//       ],
//     },
//     {
//       name: 'position.x',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: pos.x,
//           tween: 'ease-in',
//         },
//         {
//           time: 250,
//           value: pos.x + 17,
//           tween: 'ease-out',
//         },
//         {
//           time: 500,
//           value: pos.x,
//           tween: 'ease-in',
//         },
//       ],
//     },
//   ];
// }
//
// export function createMoveToTargetAnimParams(
//   obj: GameObject,
//   waitTime: number,
//   dt: number,
//   targetY: number,
//   tween = 'linear',
// ) {
//   let tran = obj.getComponent('Transform') as any;
//   let startY = tran.position.y;
//   return {
//     name: 'position.y',
//     component: tran,
//     values: [
//       {
//         time: 0,
//         value: startY,
//         tween: tween,
//       },
//       {
//         time: waitTime,
//         value: startY,
//         tween: tween,
//       },
//       {
//         time: waitTime + dt,
//         value: targetY,
//         tween: tween,
//       },
//     ],
//   };
// }

export function getTreeClickDropAnim(gameObject: GameObject) {
  return [
    {
      name: 'scale.y',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 5 * frameTime,
          value: 0.95,
          tween: 'ease-in',
        },
        {
          time: 10 * frameTime,
          value: 1.03,
          tween: 'ease-in',
        },
        {
          time: 16 * frameTime,
          value: 0.97,
          tween: 'ease-in',
        },
        {
          time: 21 * frameTime,
          value: 1.02,
          tween: 'ease-in',
        },
        {
          time: 26,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
    {
      name: 'scale.x',
      component: gameObject.transform,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'ease-in',
        },
        {
          time: 5 * frameTime,
          value: 1.05,
          tween: 'ease-in',
        },
        {
          time: 10 * frameTime,
          value: 0.97,
          tween: 'ease-in',
        },
        {
          time: 16 * frameTime,
          value: 1.03,
          tween: 'ease-in',
        },
        {
          time: 21 * frameTime,
          value: 0.98,
          tween: 'ease-in',
        },
        {
          time: 26,
          value: 1,
          tween: 'ease-in',
        },
      ],
    },
  ];
}
//
// export function createChanziMoveAnimParams(obj: GameObject) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return [
//     {
//       name: 'position.x',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: -105,
//           tween: 'linear',
//         },
//         {
//           time: 21 * frameTime,
//           value: -70,
//           tween: 'linear',
//         },
//         {
//           time: 26 * frameTime,
//           value: -70,
//           tween: 'linear',
//         },
//         {
//           time: 51 * frameTime,
//           value: 42,
//           tween: 'linear',
//         },
//         {
//           time: 57 * frameTime,
//           value: 42,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'position.y',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: 21 * frameTime,
//           value: -300,
//           tween: 'linear',
//         },
//         {
//           time: 57 * frameTime,
//           value: -300,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'alpha',
//       component: obj.getComponent(Render.componentName),
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 46 * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 65 * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'rotation',
//       component: obj.getComponent('Transform'),
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: 21 * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: 36 * frameTime,
//           value: (1 / 15) * Math.PI,
//           tween: 'linear',
//         },
//         {
//           time: 57 * frameTime,
//           value: (1 / 15) * Math.PI,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'scale.x',
//       component: obj.getComponent('Transform'),
//       values: [
//         {
//           time: 0,
//           value: 0.8,
//           tween: 'linear',
//         },
//         {
//           time: 21 * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 57 * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'scale.y',
//       component: obj.getComponent('Transform'),
//       values: [
//         {
//           time: 0,
//           value: 0.8,
//           tween: 'linear',
//         },
//         {
//           time: 21 * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 57 * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function fertilizerDropAnim(obj: GameObject, dropInfo) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return [
//     {
//       name: 'alpha',
//       component: obj.getComponent(Render.componentName),
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.alphaFrames[0] * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.alphaFrames[1] * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.alphaFrames[2] * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.alphaFrames[3] * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.alphaFrames[4] * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'position.x',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: dropInfo.startPos.x,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.frames[0] * frameTime,
//           value: dropInfo.startPos.x,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.frames[1] * frameTime,
//           value: dropInfo.endPos.x,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.frames[2] * frameTime,
//           value: dropInfo.endPos.x,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'position.y',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: dropInfo.startPos.y,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.frames[0] * frameTime,
//           value: dropInfo.startPos.y,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.frames[1] * frameTime,
//           value: dropInfo.endPos.y,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.frames[2] * frameTime,
//           value: dropInfo.endPos.y,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'scale.x',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.scaleFrames[0] * frameTime,
//           value: 1.8,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.scaleFrames[1] * frameTime,
//           value: 1.8,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.scaleFrames[2] * frameTime,
//           value: 1.8,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'scale.y',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.scaleFrames[0] * frameTime,
//           value: 1.8,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.scaleFrames[1] * frameTime,
//           value: 1.8,
//           tween: 'linear',
//         },
//         {
//           time: dropInfo.scaleFrames[2] * frameTime,
//           value: 1.8,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function getTextShowAnim(obj: GameObject, startY: number, time = 500, fadeTime = 100) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return [
//     {
//       name: 'alpha',
//       component: obj.getComponent(Render.componentName),
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'ease-in',
//         },
//         {
//           time: fadeTime,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: fadeTime + time,
//           value: 1,
//           tween: 'ease-in',
//         },
//       ],
//     },
//     {
//       name: 'position.y',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: startY,
//           tween: 'linear',
//         },
//         {
//           time: fadeTime + time,
//           value: 0,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function getTextHideAnim(obj: GameObject, endY: number, time = 500, fadeTime = 100) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   return [
//     {
//       name: 'alpha',
//       component: obj.getComponent(Render.componentName),
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: fadeTime,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: time + fadeTime,
//           value: 0,
//           tween: 'ease-out',
//         },
//       ],
//     },
//     {
//       name: 'position.y',
//       component: obj.transform,
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: fadeTime + time,
//           value: endY,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function createFeiliaoYuAnimParams(obj: GameObject, dt: number, y: number) {
//   if (!obj.getComponent(Render.componentName)) {
//     obj.addComponent(Render);
//   }
//   dt = Math.max(dt, 600);
//   let tran = obj.getComponent('Transform') as any;
//   let startY = tran.position.y;
//   let destY = startY + y;
//   return [
//     {
//       name: 'alpha',
//       component: obj.getComponent(Render.componentName),
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: 300,
//           value: 1,
//           tween: 'ease-in',
//         },
//         {
//           time: dt - 300,
//           value: 1,
//           tween: 'ease-out',
//         },
//         {
//           time: dt,
//           value: 0,
//           tween: 'ease-out',
//         },
//       ],
//     },
//     {
//       name: 'position.y',
//       component: tran,
//       values: [
//         {
//           time: 0,
//           value: startY,
//           tween: 'ease-out',
//         },
//         {
//           time: dt,
//           value: destY,
//           tween: 'ease-in',
//         },
//       ],
//     },
//   ];
// }

export function createFeiliaoDropOfWaterButtonAnimParams(obj: GameObject) {
  let tran = obj.getComponent('Transform') as any;
  return [
    {
      name: 'scale.x',
      component: tran,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'linear',
        },
        {
          time: 3 * frameTime,
          value: 0.9,
          tween: 'linear',
        },
        {
          time: 6 * frameTime,
          value: 1.1,
          tween: 'linear',
        },
        {
          time: 9 * frameTime,
          value: 0.95,
          tween: 'linear',
        },
        {
          time: 12 * frameTime,
          value: 1.05,
          tween: 'linear',
        },
        {
          time: 15 * frameTime,
          value: 1,
          tween: 'linear',
        },
      ],
    },
    {
      name: 'scale.y',
      component: tran,
      values: [
        {
          time: 0,
          value: 1,
          tween: 'linear',
        },
        {
          time: 3 * frameTime,
          value: 0.9,
          tween: 'linear',
        },
        {
          time: 6 * frameTime,
          value: 1.1,
          tween: 'linear',
        },
        {
          time: 9 * frameTime,
          value: 0.95,
          tween: 'linear',
        },
        {
          time: 12 * frameTime,
          value: 1.05,
          tween: 'linear',
        },
        {
          time: 15 * frameTime,
          value: 1,
          tween: 'linear',
        },
      ],
    },
  ];
}
//
// export function createAccTipsGuangAnim(gameObject: GameObject, dist: number, dt: number, waitTime: number) {
//   let startX = gameObject.transform.position.x;
//   return [
//     {
//       name: 'position.x',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startX,
//           tween: 'linear',
//         },
//         {
//           time: dt,
//           value: startX + dist,
//           tween: 'linear',
//         },
//         {
//           time: waitTime + dt,
//           value: startX + dist,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function createGuangAnim(gameObject: GameObject, dist: number, dt: number, waitTime: number) {
//   let startX = gameObject.transform.position.x;
//   return [
//     {
//       name: 'position.x',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startX,
//           tween: 'linear',
//         },
//         {
//           time: dt,
//           value: startX + dist,
//           tween: 'linear',
//         },
//         {
//           time: waitTime + dt,
//           value: startX + dist,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function createAccTipsTxtAnim(obj: GameObject) {
//   let tran = obj.getComponent('Transform') as any;
//   return [
//     {
//       name: 'scale.x',
//       component: tran,
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 800,
//           value: 0.8,
//           tween: 'linear',
//         },
//         {
//           time: 1600,
//           value: 1,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'scale.y',
//       component: tran,
//       values: [
//         {
//           time: 0,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 800,
//           value: 0.8,
//           tween: 'linear',
//         },
//         {
//           time: 1600,
//           value: 1,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function createLeftHeartUpAnim(
//   gameObject: GameObject,
//   endPos: { x: number; y: number },
//   startRotation: number,
//   endRotation: number,
// ) {
//   let render = gameObject.getComponent(Render.componentName);
//   if (!render) {
//     render = gameObject.addComponent(Render);
//   }
//   let startPos = gameObject.transform.position;
//   return [
//     {
//       name: 'position.x',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startPos.x,
//           tween: 'linear',
//         },
//         {
//           time: 30 * frameTime,
//           value: startPos.x + 6,
//           tween: 'linear',
//         },
//         {
//           time: 60 * frameTime,
//           value: endPos.x,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: endPos.x,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'position.y',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startPos.y,
//           tween: 'linear',
//         },
//         {
//           time: 30 * frameTime,
//           value: startPos.y - 15,
//           tween: 'linear',
//         },
//         {
//           time: 60 * frameTime,
//           value: endPos.y,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: endPos.y,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'alpha',
//       component: render,
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: 30 * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 60 * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'rotation',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startRotation,
//           tween: 'linear',
//         },
//         {
//           time: 60 * frameTime,
//           value: endRotation,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: endRotation,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function createRightHeartUpAnim(
//   gameObject: GameObject,
//   endPos: { x: number; y: number },
//   startRotation: number,
//   endRotation: number,
// ) {
//   let render = gameObject.getComponent(Render.componentName);
//   if (!render) {
//     render = gameObject.addComponent(Render);
//   }
//   let startPos = gameObject.transform.position;
//   return [
//     {
//       name: 'position.x',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startPos.x,
//           tween: 'linear',
//         },
//         {
//           time: 15 * frameTime,
//           value: startPos.x,
//           tween: 'linear',
//         },
//         {
//           time: 45 * frameTime,
//           value: startPos.x - 6,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: endPos.x,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'position.y',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startPos.y,
//           tween: 'linear',
//         },
//         {
//           time: 15 * frameTime,
//           value: startPos.y,
//           tween: 'linear',
//         },
//         {
//           time: 45 * frameTime,
//           value: (endPos.y - startPos.y) / 2,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: endPos.y,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'alpha',
//       component: render,
//       values: [
//         {
//           time: 0,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: 15 * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//         {
//           time: 45 * frameTime,
//           value: 1,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: 0,
//           tween: 'linear',
//         },
//       ],
//     },
//     {
//       name: 'rotation',
//       component: gameObject.transform,
//       values: [
//         {
//           time: 0,
//           value: startRotation,
//           tween: 'linear',
//         },
//         {
//           time: 15 * frameTime,
//           value: startRotation,
//           tween: 'linear',
//         },
//         {
//           time: 75 * frameTime,
//           value: endRotation,
//           tween: 'linear',
//         },
//       ],
//     },
//   ];
// }
//
// export function getTomorrowClickAnim(gameObject: GameObject, originPos: { x: number; y: number }) {
//   return {
//     pressDown: [
//       {
//         name: 'scale.x',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//           {
//             time: 150,
//             value: 0.87,
//             tween: 'ease-out',
//           },
//         ],
//       },
//       {
//         name: 'scale.y',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//           {
//             time: 150,
//             value: 0.87,
//             tween: 'ease-out',
//           },
//         ],
//       },
//     ],
//     pressUp: [
//       {
//         name: 'scale.x',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 0.87,
//             tween: 'ease-out',
//           },
//           {
//             time: 90,
//             value: 1.03,
//             tween: 'ease-out',
//           },
//         ],
//       },
//       {
//         name: 'scale.y',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 0.87,
//             tween: 'ease-out',
//           },
//           {
//             time: 90,
//             value: 1.03,
//             tween: 'ease-out',
//           },
//         ],
//       },
//     ],
//
//     chatter: [
//       {
//         name: 'scale.x',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//           {
//             time: 60,
//             value: 1.03,
//             tween: 'ease-out',
//           },
//           {
//             time: 120,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//         ],
//       },
//       {
//         name: 'scale.y',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//           {
//             time: 60,
//             value: 1.03,
//             tween: 'ease-out',
//           },
//           {
//             time: 120,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//         ],
//       },
//     ],
//     fadepress: [
//       {
//         name: 'scale.x',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//           {
//             time: 350,
//             value: 0.7,
//             tween: 'ease-out',
//           },
//         ],
//       },
//       {
//         name: 'scale.y',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1.1,
//             tween: 'ease-out',
//           },
//           {
//             time: 350,
//             value: 0.7,
//             tween: 'ease-out',
//           },
//         ],
//       },
//     ],
//     goback: [
//       {
//         name: 'scale.x',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 0.7,
//             tween: 'ease-out',
//           },
//           {
//             time: 90,
//             value: 1,
//             tween: 'ease-out',
//           },
//         ],
//       },
//       {
//         name: 'scale.y',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 0.7,
//             tween: 'ease-out',
//           },
//           {
//             time: 90,
//             value: 1,
//             tween: 'ease-out',
//           },
//         ],
//       },
//     ],
//     fertilizerBagBeating: [
//       {
//         name: 'position.y',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: originPos.y,
//             tween: 'linear',
//           },
//           {
//             time: 133,
//             value: originPos.y,
//             tween: 'linear',
//           },
//           {
//             time: 300,
//             value: originPos.y - 6,
//             tween: 'linear',
//           },
//           {
//             time: 467,
//             value: originPos.y,
//             tween: 'linear',
//           },
//           {
//             time: 600,
//             value: originPos.y,
//             tween: 'linear',
//           },
//           {
//             time: 767,
//             value: originPos.y - 6,
//             tween: 'linear',
//           },
//           {
//             time: 900,
//             value: originPos.y,
//             tween: 'linear',
//           },
//           {
//             time: 1033,
//             value: originPos.y,
//             tween: 'linear',
//           },
//           {
//             time: 1167,
//             value: originPos.y,
//             tween: 'linear',
//           },
//           {
//             time: 5000,
//             value: originPos.y,
//             tween: 'linear',
//           },
//         ],
//       },
//       {
//         name: 'scale.y',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1,
//             tween: 'linear',
//           },
//           {
//             time: 133,
//             value: 0.93,
//             tween: 'linear',
//           },
//           {
//             time: 300,
//             value: 1.06,
//             tween: 'linear',
//           },
//           {
//             time: 467,
//             value: 1,
//             tween: 'linear',
//           },
//           {
//             time: 600,
//             value: 0.92,
//             tween: 'linear',
//           },
//           {
//             time: 767,
//             value: 1.03,
//             tween: 'linear',
//           },
//           {
//             time: 900,
//             value: 1.03,
//             tween: 'linear',
//           },
//           {
//             time: 1033,
//             value: 0.95,
//             tween: 'linear',
//           },
//           {
//             time: 1167,
//             value: 1,
//             tween: 'linear',
//           },
//           {
//             time: 5000,
//             value: 1,
//             tween: 'linear',
//           },
//         ],
//       },
//       {
//         name: 'scale.x',
//         component: gameObject.transform,
//         values: [
//           {
//             time: 0,
//             value: 1,
//             tween: 'linear',
//           },
//           {
//             time: 133,
//             value: 1.07,
//             tween: 'linear',
//           },
//           {
//             time: 300,
//             value: 0.94,
//             tween: 'linear',
//           },
//           {
//             time: 467,
//             value: 1,
//             tween: 'linear',
//           },
//           {
//             time: 600,
//             value: 1.08,
//             tween: 'linear',
//           },
//           {
//             time: 767,
//             value: 0.97,
//             tween: 'linear',
//           },
//           {
//             time: 900,
//             value: 0.97,
//             tween: 'linear',
//           },
//           {
//             time: 1033,
//             value: 1.05,
//             tween: 'linear',
//           },
//           {
//             time: 1167,
//             value: 1,
//             tween: 'linear',
//           },
//           {
//             time: 5000,
//             value: 1,
//             tween: 'linear',
//           },
//         ],
//       },
//     ],
//   };
// }

// 施肥按钮点击动画
export function getWaterButtonAnim(gameObject: GameObject, scaleTime: number) {
  let pos = gameObject.transform.position;
  return {
    pressDown: [
      {
        name: 'scale.x',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'ease-out',
          },
          {
            time: scaleTime,
            value: 0.8,
            tween: 'ease-out',
          },
        ],
      },
      {
        name: 'scale.y',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'ease-out',
          },
          {
            time: scaleTime,
            value: 0.8,
            tween: 'ease-out',
          },
        ],
      },
    ],
    pressUp: [
      {
        name: 'scale.x',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 0.8,
            tween: 'ease-out',
          },
          {
            time: scaleTime,
            value: 1,
            tween: 'ease-out',
          },
        ],
      },
      {
        name: 'scale.y',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 0.8,
            tween: 'ease-out',
          },
          {
            time: scaleTime,
            value: 1,
            tween: 'ease-out',
          },
        ],
      },
    ],
    moveOut: [
      {
        name: 'position.y',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: pos.y,
            tween: 'ease-in',
          },
          {
            time: 200,
            // value: gameObject.transform.size.height + getScreenHeight(),
            tween: 'ease-out',
          },
        ],
      },
    ],
    chatter: [
      {
        name: 'scale.x',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 60,
            value: 0.98,
            tween: 'ease-out',
          },
          {
            time: 120,
            value: 1.1,
            tween: 'ease-out',
          },
        ],
      },
      {
        name: 'scale.y',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 60,
            value: 0.98,
            tween: 'ease-out',
          },
          {
            time: 120,
            value: 1.1,
            tween: 'ease-out',
          },
        ],
      },
    ],
    goback: [
      {
        name: 'scale.x',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 60,
            value: 1,
            tween: 'ease-out',
          },
        ],
      },
      {
        name: 'scale.y',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1.1,
            tween: 'ease-out',
          },
          {
            time: 60,
            value: 1,
            tween: 'ease-out',
          },
        ],
      },
    ],
    breath: [
      {
        name: 'scale.x',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 800,
            value: 0.9,
            tween: 'linear',
          },
          {
            time: 1600,
            value: 1,
            tween: 'linear',
          },
        ],
      },
      {
        name: 'scale.y',
        component: gameObject.transform,
        values: [
          {
            time: 0,
            value: 1,
            tween: 'linear',
          },
          {
            time: 800,
            value: 0.9,
            tween: 'linear',
          },
          {
            time: 1600,
            value: 1,
            tween: 'linear',
          },
        ],
      },
    ],
    addFeiliaoAnim: createFeiliaoDropOfWaterButtonAnimParams(gameObject),
  };
}

export function ceateTextCarouselAnim(node: GameObject, traTime: number) {
  return {
    // 中 -> 上
    ver_init: [
      {
        name: 'position.y',
        component: node.transform,
        values: [
          {
            time: 0,
            value: 0,
            tween: 'ease-out',
          },
          {
            time: traTime,
            value: -26,
            tween: 'ease-out',
          },
        ],
      },
    ],
    // 下 -> 中
    ver_next: [
      {
        name: 'position.y',
        component: node.transform,
        values: [
          {
            time: 0,
            value: 26,
            tween: 'ease-out',
          },
          {
            time: traTime,
            value: 0,
            tween: 'ease-out',
          },
        ],
      },
    ],
  };
}
