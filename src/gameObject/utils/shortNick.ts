/* eslint-disable */
/**
 * @memberOf mu
 * @name shortNick
 * @function
 * @description 昵称处理
 * @param { String } str 原始字符串
 * @param { String } n 中文字符数
 * @param { String } suffix 补全英文字符
 * @returns { String } 展示内容宽度为（n + suffix / 2）个中文字
 * @example
import { shortNick } from '@ali/pcom-mu'
console.log(shortNick('迪丽热巴', 3, '..')) // 迪丽热巴
console.log(shortNick('阿塞拜疆默罕默德', 3, '..')) // 阿塞拜..
console.log(shortNick('Angelababy', 3, '..')) // Angela..
 */
const shortNick = function (str, n, suffix) {
  if (n === void 0) {
    n = 0;
  }
  if (suffix === void 0) {
    suffix = '';
  }
  if (!str || !n) return str;
  const reg = /[^\x00-\xff]/g;
  const tplCharLen = suffix.replace(reg, '**').length / 2;
  if (str.replace(reg, '**').length <= 2 * (n + tplCharLen)) {
    return str;
  }
  const strLen = str.length;
  let curCharLen = 0;
  let curStr = '';
  let i;
  for (i = 0; i < strLen; i++) {
    const char = str[i];
    if (char.replace(reg, '') === '') {
      curCharLen += 1;
    } else {
      curCharLen += 0.5;
    }
    curStr += char;
    if (curCharLen >= n) {
      break;
    }
  }
  return curStr + suffix;
};

export default shortNick;
