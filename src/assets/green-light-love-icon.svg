<?xml version="1.0" encoding="UTF-8"?>
<svg width="22px" height="22px" viewBox="0 0 22 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/助力值备份-爱心箭头</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="22" height="22"></rect>
        <linearGradient x1="50%" y1="2.17696651%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#FF5C03" offset="0%"></stop>
            <stop stop-color="#FF6D05" offset="18.6690751%"></stop>
            <stop stop-color="#FF4600" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="2.17696651%" x2="50%" y2="100%" id="linearGradient-4">
            <stop stop-color="#2AC638" offset="0%"></stop>
            <stop stop-color="#2AC638" offset="18.6690751%"></stop>
            <stop stop-color="#2AC638" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="icon/助力值备份-爱心箭头" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="编组-3">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="矩形"></g>
            <g id="编组" mask="url(#mask-2)" fill-rule="nonzero">
                <g transform="translate(0.1393, 2.1024)">
                    <path d="M15.1750127,1.76427037 L10.8357789,6.83822072 L10.7814016,6.90541811 C10.5610163,7.23031496 10.8010907,7.69852488 11.2163618,7.67948486 L14.0240635,7.67948486 L14.0055557,8.03644973 C13.6665578,11.9213552 11.0408792,15.2868193 8.74241313,16.7298764 C6.07817287,15.4144652 0.505010609,12.5023282 0.0241407355,6.95484449 C-0.239392703,3.9145814 1.68698936,1.55622635 4.22452931,1.33215452 C5.79780408,1.19323 7.26028281,1.93939324 8.18143113,2.73333694 C8.95198933,1.79001086 10.2638744,0.798863592 11.837149,0.659937574 C13.0856452,0.549692624 14.2635728,0.962231616 15.1750127,1.76427037 Z" id="形状结合" fill="url(#linearGradient-4)"></path>
                    <path d="M16.9782772,0.849115807 L13.0696617,6.08310854 C13.0070435,6.16696004 13.0242565,6.2856972 13.1081079,6.34831541 C13.1460418,6.37664346 13.1932059,6.38974506 13.2403156,6.38504104 L14.8626012,6.22305188 L14.8626012,6.22305188 C15.1506482,6.19448744 15.4054824,6.40074548 15.4545303,6.68216704 L15.4630211,6.77853906 C15.4804577,11.1288406 13.9356458,14.5019125 11.6277716,17.0434481 L11.4773473,17.2032508 L11.6274498,17.1523465 C16.0578886,15.5781158 18.7928275,12.2917947 19.5985879,7.14061319 L19.6536877,6.76034277 C19.6888289,6.49720225 19.9014999,6.29786634 20.1610948,6.28475162 L21.3475048,6.27148091 C21.4521506,6.27031038 21.5360339,6.1845293 21.5348634,6.07988349 C21.5344202,6.04026182 21.5215674,6.00177591 21.4981128,5.96983912 L17.7396623,0.852157065 C17.5847964,0.64128443 17.2883068,0.59588199 17.0774341,0.750747876 C17.0397031,0.778457764 17.0062876,0.811607295 16.9782772,0.849115807 Z" id="路径" fill="#2AC638" transform="translate(16.597, 8.7699) rotate(-356) translate(-16.597, -8.7699)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>