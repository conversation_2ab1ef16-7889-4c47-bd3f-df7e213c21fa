module.exports = {
  prefix: '/',
  routes: [
    {
      /** 天马模块规范 */
      name: 'pages/index/index', // 必填，构建过程中的标识
      path: '/', // 必填，本地开发测试时的访问路径
      source: 'pages/index/index.tsx', // 必填，页面渲染地址
      config: { // 针对页面的配置，可拓展，非必填
        asyncSsrCache: true, // 是否开启并行SSR html片段缓存
        asyncSsrCacheExpiredTime: 24 * 60 * 60 * 1000, // ms
      },
      data: 'pages/index/data.ts', // 非必填，默认会取source同级目录内容的data.ts
      // document: 'pages/index/document.tsx', // 非必填，默认会取document/index.tsx
      /**
       * 非必填，用于定制工程中的client入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      entryClient: 'pages/index/entry-client.ts',
      /**
       * 非必填，用于定制工程中的Server入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryServer: 'pages/index/entry-server.tsx',
    },
    {
      /** 天马模块规范 */
      name: 'pages/manureDetail/index', // 必填，构建过程中的标识
      path: '/manureDetail', // 必填，本地开发测试时的访问路径
      source: 'pages/manureDetail/index.tsx', // 必填，页面渲染地址
      config: { // 针对页面的配置，可拓展，非必填
        asyncSsrCache: true, // 是否开启并行SSR html片段缓存
        asyncSsrCacheExpiredTime: 24 * 60 * 60 * 1000, // ms
      },
      data: 'pages/manureDetail/data.ts', // 非必填，默认会取source同级目录内容的data.ts
      // document: 'pages/index/document.tsx', // 非必填，默认会取document/index.tsx
      /**
       * 非必填，用于定制工程中的client入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryClient: 'pages/index/entry-client.tsx',
      /**
       * 非必填，用于定制工程中的Server入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryServer: 'pages/index/entry-server.tsx',
    },
    {
      /** 天马模块规范 */
      name: 'pages/redPacketDetail/index', // 必填，构建过程中的标识
      path: '/redPacketDetail', // 必填，本地开发测试时的访问路径
      source: 'pages/redPacketDetail/index.tsx', // 必填，页面渲染地址
      config: { // 针对页面的配置，可拓展，非必填
        asyncSsrCache: true, // 是否开启并行SSR html片段缓存
        asyncSsrCacheExpiredTime: 24 * 60 * 60 * 1000, // ms
      },
      data: 'pages/redPacketDetail/data.ts', // 非必填，默认会取source同级目录内容的data.ts
      // document: 'pages/index/document.tsx', // 非必填，默认会取document/index.tsx
      /**
       * 非必填，用于定制工程中的client入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryClient: 'pages/index/entry-client.tsx',
      /**
       * 非必填，用于定制工程中的Server入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryServer: 'pages/index/entry-server.tsx',
    },
    {
      /** 天马模块规范 */
      name: 'pages/helpRanking/index', // 必填，构建过程中的标识
      path: '/helpRanking', // 必填，本地开发测试时的访问路径
      source: 'pages/helpRanking/index.tsx', // 必填，页面渲染地址
      config: { // 针对页面的配置，可拓展，非必填
        asyncSsrCache: true, // 是否开启并行SSR html片段缓存
        asyncSsrCacheExpiredTime: 24 * 60 * 60 * 1000, // ms
      },
      data: 'pages/helpRanking/data.ts', // 非必填，默认会取source同级目录内容的data.ts
      // document: 'pages/index/document.tsx', // 非必填，默认会取document/index.tsx
      /**
       * 非必填，用于定制工程中的client入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryClient: 'pages/index/entry-client.tsx',
      /**
       * 非必填，用于定制工程中的Server入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryServer: 'pages/index/entry-server.tsx',
    },
    {
      /** 天马模块规范 */
      name: 'pages/helpPlant/index', // 必填，构建过程中的标识
      path: '/helpPlant', // 必填，本地开发测试时的访问路径
      source: 'pages/helpPlant/index.tsx', // 必填，页面渲染地址
      config: { // 针对页面的配置，可拓展，非必填
        asyncSsrCache: true, // 是否开启并行SSR html片段缓存
        asyncSsrCacheExpiredTime: 24 * 60 * 60 * 1000, // ms
      },
      data: 'pages/helpPlant/data.ts', // 非必填，默认会取source同级目录内容的data.ts
      // document: 'pages/index/document.tsx', // 非必填，默认会取document/index.tsx
      /**
       * 非必填，用于定制工程中的client入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      entryClient: 'pages/helpPlant/entry-client.ts',
      /**
       * 非必填，用于定制工程中的Server入口文件初始流程，
       * 详看后续的『完全开放entryLoader模板定制』
       */
      // entryServer: 'pages/index/entry-server.tsx',
    },
  ],
  reactSnapShot: {
    enable: false,
    version: '18.2.0',
  },
};
