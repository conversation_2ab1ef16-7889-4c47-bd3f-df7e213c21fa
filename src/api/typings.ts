import { IQueryResourceInfo } from '@/logic/store/models/highTask/types';

export interface IFirstData {
  userInfo: IGetUserInfoRes;
  mainInfo: any;
  multiResource?: any;
  cmsData?: any;
}

export interface IGetUserInfoRes {
  kps_wg: string;
  loginStatus: boolean;
  service_ticket: string;
  utdId: string;
  uId: string;
  sign_wg: string;
  nickname: string;
  avatar_url: string;
  vCode: string;
  [key: string]: any;
}

export interface IQueryHomeParams {
  appId: string;
  kps: string;
  requestId: string;
}

export interface IGameInfo {
  accountInfo?: IAccountInfo;
  plantInfo: IPlantInfo;
  sceneInfo?: ISceneInfo;
  teamInfo?: ITeamPlantInfo;
  frontData?: {
    diamondRecommendTaskPlan: any[];
  };
}

export interface ISceneInfo {
  main?: boolean; // 是否主场景
  sceneCode?: string;
  sceneFresher?: boolean;
  sceneName?: string;
  floorId?: string; // feeds流id
  desc?: string; // 选种子秒速
  status?: number; // 场景状态，0：进行中，1：预热，2：敬请期待
  leftTime?: number; // 如果场景有时间限制，则存在该字段，毫秒
  finished?: boolean; // 当前场景是否已种成，能否继续种
  enableTeamPlant?: boolean; // 该场景是否开启合种
  finishInfo?: {
    seedName?: string; // 种子名称
    seedPic?: string; // 种子图片
    desc?: string; // 描述
  };
  hasRefund?: boolean; // finishInfo 存在，则显示种植成功相关信息，如果不存在在判断hasRefund字段，如果true则显示refundInfo相关退福气信息
  refundInfo?: {
    // refundInfo相关退福气信息
    title?: string;
    desc?: string;
    pic?: string;
    subTitle?: string;
  };
  showReward?: boolean; // 是否展示权益库存
  totalReward?: number; // 总奖品数量
  rewardNum?: number; // 已领取奖品数量
}

export interface IAccountInfo {
  happyPoint: number;
  wateringCost: number;
  wateringLeftTimes: number;
  sunAmount: number; // 阳光数量
  hasMigrate: boolean; // 是否完成切换
  randomSeed: boolean; // 代表是否进行随机选种，出新手字段
}

export interface IPlantInfo {
  canExchange?: boolean;
  teamPlant?: boolean;
  contribution?: number;
  exchangeCount?: number;
  seasonSeed?: boolean;
  seedTips?: string;
  preDismiss?: boolean;
  seedStage?: ISeedStage;
  completeFruit?: number;
  extMap?: {
    waitExchangeUrl?: string;
    activityPreEndTipTitle: string;
    activityPreEndTipText: string;
  };
}
export interface ISeedStage {
  seedCode?: string;
  seedName?: string;
  currentProgress?: number;
  totalProgress?: number;
  stageLevel?: number;
  stageName?: string;
  redpacket?: boolean;
  seedImg?: string;
  seedSubTitle?: string;
  stageText?: string;
  // grownUpImg?: string;
  // upgradeText?: string;
  // upgradeImg?: string;
  // description?: string;
  animImg?: string;
  animAtalas?: string;
  animSkeleton?: string;
  tipsHeight?: number; // 轮播气泡高度
  // bubblePositionX?: number; // 树上奖励气泡游戏区左边距离
  // bubblePositionY?: number; // 树上奖励气泡距离游戏区底部
  unit?: string;
  totalValue?: number;
}

export interface ITeamPlantInfo {
  teamType: string; // 合种类型
  teamName: string; // 队伍名称
  teamSize: string; // 队伍人数
  teamTypeCode: string; // 队伍类型
  // 队伍
  partnerList: Array<{
    hasCallBack: string;
    userAvatar: string;
  }>;
}


export interface IQueryDetailParams extends IQueryHomeParams{
  pageNum: number;
  pageSize: number;
}

export interface IQueryByMultiResource {
  [key: string]: IQueryResourceInfo;
}

export interface IAssetsGatherInfo {
  yesterdayTotalAmount: number; // 昨日收肥数
  yesterdayDoubleAmount: number; // 肥料翻倍奖励
  doubleNum: number; // 翻倍卡倍数
}
