import network from '@/lib/network';
import config from '@/config';
import { isNode } from 'universal-env';
import { getQueryUCParamsObj } from '@/lib/qs';
import { geneTaskRequestId } from '@/api/utils';
import { isMock, mockData } from '@/mockData';
import { IInviteScore } from './typings';
import { IQueryRankHistory } from '@/mockData/queryRankHistory';
import { IHelpPlantHomeRes } from '../helpPlant/typings';

const requestId = geneTaskRequestId();
const ucParamsQueryObj = isNode ? getQueryUCParamsObj() : {};

interface IQueryListParams {
  kps: string;
  pageNum: number;
  pageSize?: number;
}
interface IQueryScoreListParams extends IQueryListParams {
  queryCutOffTime: number;
}
interface IQueryRankHistoryParams extends IQueryListParams {
  queryDate: string;
}
export const queryScoreList = async ({
  kps,
  pageNum,
  pageSize,
  queryCutOffTime,
}: IQueryScoreListParams): Promise<IInviteScore[]> => {
  if (isMock()) {
    return mockData.queryScoreList;
  }
  // return mockData.queryScoreList;
  const { farmHost, appId } = config;
  return network
    .get(`${farmHost}/help/queryScoreList`, {
      appId,
      kps,
      requestId,
      pageNum: pageNum || 1,
      pageSize: pageSize || 20,
      queryCutOffTime: queryCutOffTime || 0,
      ...ucParamsQueryObj,
    })
    .catch((err) => {
      return Promise.reject(err);
    });
};
export const queryRankHistory = async ({
  kps,
  pageNum,
  pageSize = 20,
  queryDate,
}: IQueryRankHistoryParams): Promise<IQueryRankHistory> => {

  if (isMock()) {
    return mockData.queryRankHistory;
  }
  const { farmHost, appId } = config;
  return network
    .get(`${farmHost}/help/queryRankHistory`, {
      appId,
      kps,
      requestId,
      pageNum: pageNum || 1,
      pageSize: pageSize || 20,
      queryDate: queryDate || undefined,
      ...ucParamsQueryObj,
    })
    .catch((err) => {
      return Promise.reject(err);
    });
};

export const queryHelpRankingHome = async ({ kps }) => {
  try {
    const res: IHelpPlantHomeRes = await network.get(`${config.farmHost}/help/queryHome`, {
      appId: config.appId,
      kps,
      requestId,
    });
    return res;
  } catch (error) {
    console.log(error);
    return null;
  }
};
