export interface IInviteScore {
  id: number; // 流水id
  appId: string; // 业务方appId
  amount: number; // 虚拟币数量（钱包单位分）
  title: string; // 标题
  state: EInviteScoreState; // 状态 预收入=INCOME_PRE, 收入确认INCOME_CONFIRM,收入取消=INCOME_CANCEL, 预支出=PAYOUT_PRE,支出确认=PAYOUT_CONFIRM,支出取消=PAYOUT_CANCEL;
  expireAmount: number;
  validTime: number;
  expireTime: number;
  createTime: number;
  updateTime: number;
  extra: {
    title_icon: string; // 图标
    display_title: string; // 文案
    related_uid: string; // 对应的uid
    title_type: EInviteTitleType; // ASSIST 助力的 INVITE 邀请的
  };
}

export const enum EInviteScoreState {
  INCOME_CONFIRM = 'INCOME_CONFIRM', // 收入确认
  INCOME_PRE = 'INCOME_PRE', // 预收入
  INCOME_CANCEL = 'INCOME_CANCEL', // 收入取消
  PAYOUT_PRE = 'PAYOUT_PRE', // 预支出
  PAYOUT_CONFIRM = 'PAYOUT_CONFIRM', // 支出确认
  PAYOUT_CANCEL = 'PAYOUT_CANCEL', // 支出取消
}
export const enum EInviteTitleType {
  ASSIST = 'ASSIST', // 助力的
  INVITE = 'INVITE', // 邀请的
}
