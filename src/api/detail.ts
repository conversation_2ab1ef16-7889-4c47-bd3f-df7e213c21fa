import network from '@/lib/network';
import config from '@/config';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import {IQueryDetailParams} from "@/api/typings";
import {IDetail} from "@/logic/store/models/app/typings";
import { getUserInfo } from '@/lib/ucapi';


export const queryDetailData = async (pageNum: number): Promise<IDetail[]> => {
  const userInfo: any = await getUserInfo();  
  const kps = userInfo?.kps_wg || '';
  if (!kps) {
    // @ts-ignore
    return []
  }
  const { farmHost, appId} = config;
  const requestId = geneTaskRequestId();
  const query: IQueryDetailParams = {
    appId,
    kps,
    requestId,
    pageNum,
    pageSize: 20,
  };
  return network.get(`${farmHost}/manureHis`, query);
};
