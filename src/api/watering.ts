import network from '@/lib/network';
import config from '@/config';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { miniwua } from "@/lib/ucapi";
import mx from '@ali/pcom-mx';


const getSecurityHeader = async () => {
  try {
    const miniwuaRes = await miniwua({
      // text如果不encode， iOS会崩溃
      text: encodeURIComponent(mx.store.get('user.utdId')),
      api: '/watering'
    })
    if (miniwuaRes?.output) {
      return {
        'x-mini-wua': encodeURIComponent(miniwuaRes?.output?.['x-mini-wua'] || ''),
        'x-umt': encodeURIComponent(miniwuaRes?.output?.['x-umt'] || ''),
        'Content-Type': 'application/json'
      }
    } else {
      return {}
    }
  } catch (e) {
    return {}
  }
}


// 施肥接口：https://leaf.uc.cn/projects/yes-farm/editor/Vt1ipOlZ3f
export const wateringAPI = async (kps?: string, times = 1): Promise<IWateringResData> => {
  const { farmHost, appId } = config;
  const data: IWateringParams = {
    appId,
    kps: kps || '',
    requestId: geneTaskRequestId(),
    waterTimes: times,
  };
  const securityHeader = await getSecurityHeader()
  return network.post(`${farmHost}/watering`, data, {
    headers: securityHeader
  })
};

export interface IWateringParams {
  appId: string;
  kps: string;
  requestId: string;
  waterTimes: number;
}

export interface IWateringResData {
  canExchange: boolean; // 是否可以兑换
  captain: boolean;
  currentCost: number; // 消耗肥料
  currentStage: ICurrentStage;
  eventPops: [];
  exchangeUrl: string; // 兑换链接
  helpMode: boolean;
  leftHappyPoint: number; // 剩余肥料
  resourceInfo: {};
  speedUp: boolean;
  statistics: {
    dailyAppWateringCount: number;
  };
  upgrade: boolean; // 是否升级
  completeFruit: number; // 种成树的次数
}

export interface ICurrentStage {
  "seedId": number; // 种子ID
  "seedCode": string; // 种子编码
  "seedName": string; // 种子名称
  "seedSubTitle": string; // 种子子标题
  "currentProgress": number; // 当前进度 随着用户浇水从0到1w
  "totalProgress": number; // 总升级进度 1w
  "stageLevel": number; // 当前阶段等级
  "stageName": string; // 当前阶段名称
  "stageText": string; // 阶段文案
  "seedImg": string; // 种子图片
  "grownUpImg": string; // 成熟图片
  "redpacket": boolean; // 是否红包
  "upgradeText": string; // 升级展示文案
  "upgradeImg": string; // 升级展示图片
  "description": string; // 描述
  "animImg": string; // 动画图片
  "animAtalas": string; // 动画图集信息
  "animSkeleton": string; // 动画骨骼信息
  "stackImg": string; // 种植开始时间
  "sunFarmImg": string; // 果实图片
  "tipsHeight": string; // tips高度
  "category": string; // 类目
  "unit": string; // 单位
  "totalValue": number; // 用户目前赚取的总经验值 当currentProgress是762 stageLevel是13 totalValue就是120762=(13-1)*10000+762
  "levelCategory": string; // 等级目录
  "bubbleImg": string; // 气泡图片
  "type": number; // 兑换类型
  "seedImgTexture": string; // 种子图片纹理
  "animImgTexture": string; // 动画图片纹理
  "seedValue": string;
}
