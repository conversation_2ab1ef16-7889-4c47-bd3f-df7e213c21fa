import network from '@/lib/network';
import config from '@/config';
import { IQueryResourceInfo } from '@/logic/store/models/highTask/types';
import { isMock, mockData} from '@/mockData';
import { geneTaskRequestId, getPublishVersion } from './utils';
import { getParam, getQueryUCParamsObj } from '@/lib/qs';
import {IQueryByMultiResource} from "@/api/typings";
import { isNode } from 'universal-env';


export const queryResourceInfo = async (code: string, kps: string): Promise<IQueryResourceInfo> => {
  // if (isMock()) {
  //   // @ts-ignore
  //   return mockData.resources.data
  // }
  const { farmHost, appId} = config;
  const requestId = geneTaskRequestId();
  const entry = getParam('entry') || 'unknown';
  const ucParamsQueryObj = isNode ? getQueryUCParamsObj() : {};
  const fve = getPublishVersion();

  const query = {
    code,
    kps,
    requestId,
    entry,
    appId,
    fve,
    activeUser: 1,
    prioritySize: 1,
    ...ucParamsQueryObj
  };
  return network.get(`${farmHost}/task/queryByResource`, query).catch((err) => {
    return null
  });
};

export const queryByMultiResource = async (codes: string, kps: string): Promise<IQueryByMultiResource | null> => {
  if (isMock()) {
    // @ts-ignore
    return mockData.multiResourceData
  }
  const { farmHost, appId} = config;
  const ucParamsQueryObj = isNode ? getQueryUCParamsObj() : {};
  const requestId = geneTaskRequestId();
  const entry = getParam('entry') || 'unknown'
  const fve = getPublishVersion();
  const params = {
    codes,
    kps,
    requestId,
    entry,
    appId,
    activeUser: 1, // 活跃用户类型，1-福利场内活跃用户
    prioritySize: 1,
    fve,
    ...ucParamsQueryObj
  };
  return network.get(`${farmHost}/task/queryByMultiResource`, params).catch((err) => {
    return null
  });
};
