// 翻倍卡信息
export interface QueryCardInfoRes {
  curTime: number; // 当前毫秒时间戳
  actStartTime: number; // 活动开始时间
  actEndTime: number; // 活动结束时间
  frontData: {
    shareConfig: {
      ucShareModuleId: string; // 主端分享模块
      ucLiteShareModuleId: string; // 极速版分享模块
    };
    titleImgUrl: string; // 翻倍首抽弹窗标题图片
    ruleLink?: string; // 规则链接
    guidePopText?: string; // 宣传弹窗文案
    guideDisplayNum?: number; // 宣传弹窗显示次数
    doubleCardDirectActiveDays?: number; // 膨胀卡直送持续天数
    countdownNum?: number; // 首抽倒计时时间
  };
  inviteInfo: {
    "dailyAssistMaxNum": number; // 每日可助力次数
    "needAssistNum": number; // 需要助力的人数
    "inviteCode": string; // 邀请码
  };
  yesterdayCardInfo: {
    received: boolean; // 是否已领奖励
    doubleNum: number; // 卡倍数，122表示1.22倍 --昨日 --未领取才查询
    totalAmount: number; // 总肥料  --昨日 --未领取才查询
    doubleAmountLimit: number; // 肥料翻倍限额
    canReceiveDoubleAmount: number; // 可领翻倍奖励--昨日
  };
  todayCardInfo: CardInfo;
  drawInfo: {
    drawChance: number; // 抽奖机会
    luckyTimes: number; // 没卡抽奖必中次数
    drawTimes: number; // 当前状态的已抽奖次数 --没卡变成有卡，次数从0重算算
    totalDrawTimes: number; // 总抽奖次数 --今日
    refreshTimes: number; // 刷新倍数次数
  };
}

export interface CardInfo {
  doubleNum: number; // 卡倍数，122表示1.22倍 --今日
  doubleNumLimit: number; // 卡最大倍数 --今日
  totalAmount: number; // 今天总肥料 --今日
  doubleAmountLimit: number; // 肥料翻倍限额
  canReceiveDoubleAmount: number; // 可领翻倍奖励--今日
}
