import network from '@/lib/network';
import config from '@/config';
import { isNode } from 'universal-env';
import { getQueryUCParamsObj } from '@/lib/qs';
import { geneTaskRequestId } from "@/api/utils";
import { isMock, mockData } from '@/mockData';
import { IQueryRankingRes, IHelpPlantHomeRes } from './typings';

const requestId = geneTaskRequestId();
const ucParamsQueryObj = isNode ? getQueryUCParamsObj() : {};

export const queryHelpPlantHome = async (kps: string): Promise<IHelpPlantHomeRes> => {
  if (isMock()) {
    return mockData.helpPlantHomeData;
  }  
  const { farmHost, appId} = config;
  return network.get(`${farmHost}/help/queryHome`, {
    appId,
    kps,
    requestId,
    ...ucParamsQueryObj
  }).catch((err) => {
    return null;
  });
}

export const queryRankingList = async (kps: string, pageSize: number): Promise<IQueryRankingRes> => {
  if (isMock()) {
    return mockData.rankListData;
  }
  const { farmHost, appId} = config;
  return network.get(`${farmHost}/help/queryRankingList`, {
    appId,
    kps,
    requestId,
    pageNum: 1,
    pageSize,
    ...ucParamsQueryObj
  }).catch((err) => {
    return null;
  });
}
