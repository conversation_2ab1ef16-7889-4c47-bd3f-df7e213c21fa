import { IHintItem } from '@/logic/store/models/farmHelpPlant/type';

export interface IQueryRankingRes {
  curTime: number;
  title: string;
  description: string;
  date: string;
  tag: string;
  displayStartTime: number;
  startTime: number;
  endTime: number;
  status: string;
  totalNumber: number;
  needMinScore: number; // 上榜要求的最小助力值
  teamMembers: TeamMembers[];
  myInfo: MyInfo;
  rankings: Rankings[];
  displayRankNum: number; // 排名显示数量
}

export interface TeamMembers {
  name: string;
  avatar: string;
  own: boolean;
  score: number;
}

export interface MyInfo {
  nickname: string;
  avatar: string;
  newestScore: number;
  score: number;
  ranking: number;
  own: boolean;
  rate: number;
  rewardStatus: string;
  label: string;
  honor: string;
  wsgUid: string;
  userKey: string;
  prizeItems: PrizeItems[];
}

export interface PrizeItems {
  prizeName: string;
  prizeIcon: string;
  prizeMark: string;
  prizeAmount: number;
  prizeType: string;
}

export interface Rankings {
  label: string;
  honor: string;
  wsgUid: string;
  nickname: string;
  avatar: string;
  ranking: number;
  score: number;
  own: boolean;
  rewardStatus: string;
  prizeItems: PrizeItems[];
}

export enum AssistUserType {
  ALL_USER = 'ALL_USER',
  BIZ_NEW = 'BIZ_NEW',
  BIZ_RECALL_USER = 'BIZ_RECALL_USER',
}

export interface IAssistInfo {
  score: number; // 助力值
  inviterScore: number; // 分享人助力值
  assistUserType: string; // 助力用户类型，ALL_USER 普通用户  BIZ_NEW 业务新用户 ，BIZ_RECALL_USER 业务回流用户
}

export interface IHelpPlantFrontData {
  ucFarmLink: string;
  ruleLink: string; // 规则
  historyRankingLink: string; // 历史榜单
  detailLink: string; // 肥料明细
  redPacketLink: string; // 红包明细
  guideList: Array<{
    title: string; // 标题
    subTitle: string; // 副标题
    imgUrl: string; // 图片配置
  }>;
  shareConfig: {
    ucShareModuleId: string; // 主端分享模块
    ucLiteShareModuleId: string; // 极速版分享模块
  };
  riskRule: string; // 风控链接
  redBookConfig: {
    highest: number; // 领 最高金额
    lowest: number; // 最低金额
    timeRange: number; // 笔记需公开保留 >= xx(timeRange)小时
    serviceLink: string; // 客服链接
  };
  paramsEntry: string[]; // 返回去农场的entry
  noticeConfig: {
    title: string; // 标题
    subtitle: string; // 副标题
    prizeConfig?: {
      manure?: number; // 肥料
      cash?: number; // 现金红包
    };
  };
  [key: string]: any;
}

export interface IHelpPlantHomeRes {
  curTime: number;
  accountUnusual: boolean; // 是否账号异常
  ucFarmHasAuth: boolean; // 是否已认证UC农场
  frontData: IHelpPlantFrontData;
  inviteInfo: {
    inviteCode: string; // 邀请码
    defaultScore: number; // 默认助力值
    defaultInviterScore: number; // 默认分享人助力值
    assistInfo: Array<{
      score: number; // 助力值
      inviterScore: number; // 分享人助力值
      assistUserType: string; // 助力用户类型，ALL_USER 普通用户  BIZ_NEW 业务新用户 ，BIZ_RECALL_USER 业务回流用户
    }>;
    needAssistNum: number; // 每天被助力次数
    dailyAssistMaxNum: number; // 每天可助力次数
  };
  rankRewardList: Array<{
    tag: string;
    status: string; // 状态：NONE 未获取
    type: string; // 奖励分类'; 奖励分类'; RANKING 排行榜类
    typeValue: string; // 奖励分类值，格式：排名最小值-排名最大值，1-3表示第1名到第3名都是这个奖品
    prizeName: string; // 奖励名称 --前端显示多个奖品名，约定英文逗号分隔
    prizeMark: string; // 奖励标识
    prizeIcon: string; // 奖励图标
    prizeValue: string; // 奖励配置值
    prizeActualValue: string; // 奖励实际值
    rewardSource: string; // 奖励来源：CURRENCY 资产，AWARD 权益
  }>;
  /** 弹窗提示列表 */
  hintList: IHintItem[];
  /** 排名显示数量 */
  displayRankNum: number;
}
