import config from "@/config";

/**
 * 单独拆分处理，避utils被合并打包
 */
export function geneTaskRequestId() {
  function s4() {
    // eslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
}

export const getPublishVersion = () => {
  // 测试环境PUBLISH_VERSION 格式为1.20.0-beta.1244142421, 去掉多余的字符
  const version = (PUBLISH_VERSION || '').replace(/-.+/, '')
  if (+version.replace(/\./g, '') === 0) {
    return config.fve
  }
  return version
}
