import network from '@/lib/network';
import config from '@/config';
import { IShareConfig } from '@ali/act-base-share';
import { getParam } from '@/lib/qs';
import { QueryShareInfoListResponseResult } from '@/logic/store/models/share/typing';
import { geneTaskRequestId, getPublishVersion } from '@/logic/store/models/utils';
import { isMock } from '@/mockData';

const publishEnv: any = PUBLISH_ENV || 'local';

/**
 * 获取分享配置
 */
export const getShareDetail = async (): Promise<IShareConfig> => {
  const { shareHost, sharePlanId} = config;
  let query = {
    plan_id: sharePlanId
  };

  const pub_id = getParam('sharePubId')
  if (publishEnv !== 'prod' && pub_id) {
    query['pub_id'] = pub_id;
  }
  return network.get(`${shareHost}/wechat/v1/query_share`, query);
};

/**
 * 获取分享邀请码
 */
export const getQueryInviteInfoList = async (kps: string): Promise<QueryShareInfoListResponseResult['data']> => {
  if (isMock()) {
    // @ts-ignore
    return mockData.queryShareInfoList.data;
  }
  if (!kps) {
    // @ts-ignore
    return {}
  }
  const requestId = geneTaskRequestId();
  return network.get(`${config.farmHost}/task/queryShareInfoList`, {
    appId: config.appId,
    fve: getPublishVersion(),
    kps,
    requestId,
  });
};
