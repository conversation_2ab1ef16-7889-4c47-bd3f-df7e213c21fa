import network from '@/lib/network';
import config from '@/config';
import { IQueryHomeParams, IAssetsGatherInfo } from "@/api/typings";
import {IMainInfo} from "@/logic/store/models/app/typings";
import { isMock, mockData } from '@/mockData';
import cmsRes from '@ali/cms-res';
import { getParam, getQueryUCParamsObj } from '@/lib/qs';
import { RES_CODES } from '@/logic/store/models/cms/typings';
import { isNode } from 'universal-env';
import { geneTaskRequestId } from "@/api/utils";

// 查询首屏数据：https://leaf.uc.cn/projects/yes-farm/editor/R1gDXTlSAV
// 返回null时会在客户端进行重试，兼容node端拿不到kps的场景。-暂不考虑这种场景
export const queryHomeData = async (kps: string): Promise<IMainInfo | null> => {
  if (isMock()) {
    // @ts-ignore
    return mockData.homeData;
  }
  if (!kps) {
    // @ts-ignore
    return {}
  }
  const { farmHost, appId} = config;
  const requestId = geneTaskRequestId();
  const query: IQueryHomeParams = {
    appId,
    kps,
    requestId,
  };
  return network.get(`${farmHost}/home`, query).catch((err) => {
    return err;
  });
};

// 请求昨日收肥数
export const queryAssetGatherInfo = async (kps: string): Promise<IAssetsGatherInfo> => {
  const { farmHost, appId} = config;
  const api = `${farmHost}/task/queryAssetGatherInfo`
  const requestId = geneTaskRequestId();
  const query: IQueryHomeParams = {
    appId,
    kps,
    requestId,
  };
  return network.get(api, query);
}

// 单独请求diamond配置
export const queryDiamondConf = async () => {
  const api = `${config.coralHost}/aggregation/home`
  const reqParams = [{ module: 'diamond', key: `${config.diamondKey}`}]
  const req_services = JSON.stringify(reqParams);
  return network.get(api, {
    req_services,
  }).then(res => {
    return res?.[0]?.data
  }).catch(() => {
    return null
  })
}

export async function getCmsData(firstInit = false) {
  try {
    const ucParamsQueryObj = isNode ? getQueryUCParamsObj() : {};
    console.log('[getCmsData] ut', getParam('ut'));
    console.log('[getCmsData] ds', getParam('ds'));
    const cmsResData = await cmsRes.init({
      // 请求域名
      host: config.cmsUrl,
      // 合作方ID（必填)
      partner_id: 'farm-server',
      // 分组编码(必填)
      group: 'uc-farm-server',
      // 自定义公参串 (如果留空，sdk请求时 会默认加上 uc_param_str=cgligimiosntwilasspijbnwdnnifrpfbivecpchbtbmprpvstsvgddsudkt )
      uc_param_str: getParam('uc_param_str') || 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf',
      // 数据缓存类型(默认: LS). 可选值 WX | LS | SS | UNI | CUSTOM | NONE. NONE 表示数据不缓存
      cacheType: 'NONE',
      // 自定义额外参数
      params: {
        ver: '200000',
        ve: '18.0.0',
        user_type: 'utdid',
        entry: getParam('entry') ?? '',
        apply_res: Object.values(RES_CODES).join(','),
        ...ucParamsQueryObj
      },
      timeout: firstInit ? 12e3 : 60e3
    });
    return cmsResData;
  } catch (err) {
    return null;
  }
}
