import network from '@/lib/network';
import config from '@/config';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { getUserInfo } from '@/lib/ucapi';
import { IRedPacketDetail } from '@/logic/store/models/app/typings';
import { isMock, mockData } from '@/mockData';


export const queryDetailData = async (pageNum: number): Promise<IRedPacketDetail[]> => {
  const userInfo = await getUserInfo();

  if (isMock()) {
    return mockData.queryFlowList;
  }
  
  const kps = userInfo?.kps_wg || '';
  if (!kps) {
    // @ts-ignore
    return []
  }

 
  const { coralHost, appId, redPacketmoduleCode} = config;
  const requestId = geneTaskRequestId();
  const query = {
    appId,
    kps,
    moduleCode: redPacketmoduleCode,
    requestId,
    stateList: 'INCOME_CONFIRM,INCOME_EXPIRE,PAYOUT_PRE,PAYOUT_CONFIRM,PAYOUT_CANCEL,INCOME_USED,INCOME_USING',
    pageNum,
    pageSize: 20,
    queryCutOffTime: 0,
  };
  return network.get(`${coralHost}/currency/v1/queryFlowList`, query);
};
