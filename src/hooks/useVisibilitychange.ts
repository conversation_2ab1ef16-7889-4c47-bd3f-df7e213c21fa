import { useEffect, useRef, useState } from 'react';
import { isIOS } from '@/lib/universal-ua';


/**
 * @description webCompassApp模式下visible变更相关触发事件
 * 参考url: https://aliyuque.antfin.com/web-compass/manuals/szddxh
 */
const webCompassAppEvents = [
  'pageappear', // webCompassApp模式下的事件
  'pagedisappear', // webCompassApp模式下的事件
  'pageforeground', // webCompassApp模式下的事件, 能监听到
  'pagebackground'// webCompassApp模式下的事件, 能监听到
];

/**
 * @description global compass, 临时兼容IOS webCompassApp模式下半屏覆盖半屏未触发visible的问题
 * 限制: 主站 & 版本>=16.5.9
 * url: https://jas.alibaba-inc.com/jsapi/66695e7d018dd90124765380
 */
const Global_CompassPageStateChanged = 'UCEVT_Global_CompassPageStateChanged';

type PageVisibilityChangeListener = (visible: boolean) => void;
const pageVisibilityChangeListeners: PageVisibilityChangeListener[] = [];

export function addListenerForPageVisibilityChange(listener: PageVisibilityChangeListener) {
  // visibility change
  let isVisible = true;
  // leave/entry state
  let isLeave = false;

  function doCallback(visible: boolean) {
    pageVisibilityChangeListeners.forEach((c) => {
      c(visible);
    });
  }

  // 页面回来的时候，加一个双重判断, 切换后台的时候，也会触发compass事件
  function getPageVisibility() {
    return window?.compass?.lifecycle?.visibilityState === 'visible' || document.visibilityState === 'visible';
  }

  function leave() {
    // console.log('[page visible]leave');
    // console.log('[compass visibilityState]', window?.compass?.lifecycle?.visibilityState);
    isLeave = true;
    doCallback(false);
  }

  function enter() {
    // console.log('[page visible]enter');
    // console.log('[compass visibilityState]', window?.compass?.lifecycle?.visibilityState);
    isLeave = false;
    doCallback(getPageVisibility());
  }

  function foreground() {
    // console.log('[page visible]foreground');
    // console.log('[compass visibilityState]', window?.compass?.lifecycle?.visibilityState);
    if (isVisible) {
      doCallback(getPageVisibility());
    }
  }

  function background() {
    // console.log('[page visible]background');
    // console.log('[compass visibilityState]', window?.compass?.lifecycle?.visibilityState);
    doCallback(false);
  }
  // state: 'open' 新开容器，底部容器变更为 hidden,  'close': 容器关闭，底部容器变更为 hidden,
  function globalCompassEvent(event: {detail?: {'state': 'open' | 'close'; url: string}}) {
    // console.log('[page visible]Global_CompassPageStateChanged', event);
    // console.log('event state:', event.detail?.state);
    if (event.detail?.state === 'open') {
      leave();
    } else {
      enter();
    }
  }

  function onVisibilityChange() {
    // console.log(`[page visible]visibilityState:${document.visibilityState}`);
    // console.log(`[page visible]isLeave:${isLeave}`);
    const visible = document.visibilityState === 'visible';
    if (isLeave) {
      // console.log('[page visible]visibility change after leave');
      return;
    }
    // console.log(`[page visible]isLeave:${visible}`);
    isVisible = visible;
    doCallback(visible);
  }

  // 做一个简单的防止连点
  // 存在边界情况，快速双击进行路由跳转，但是这个时候还没有跳转完成
  // 会触发这里的 click，导致 visibility 判断失效
  let lastClick = 0;

  function onDocumentClick() {
    if (lastClick + 500 > Date.now()) {
      lastClick = Date.now();
      return;
    }
    lastClick = Date.now();
    // 作为页面可见性的兜底处理
    // 如果用户点击了，说明页面可见，但如果目前页面状态仍为不可见，则需要额外触发一个可见回调
    if (!isVisible) {
      // console.log('[page visible]click');
      doCallback(true);
      isVisible = true;
    }
  }

  // 不用重复添加同一个 callback
  if (!pageVisibilityChangeListeners.includes(listener)) {
    pageVisibilityChangeListeners.push(listener);
  }

  // 只用在第一次添加回调时，注册对应的监听事件
  if (pageVisibilityChangeListeners.length === 1) {
    document.addEventListener('touchstart', onDocumentClick);


    // compass 模式下
    if (window && window?.compass) {
      // 这里暂时先区分监听
      document.addEventListener('pageappear', enter);
      document.addEventListener('pagedisappear', leave);
      document.addEventListener('pageforeground', foreground);
      document.addEventListener('pagebackground', background);

      // 兼容处理，IOS下半屏叠加半屏未触发visible
      if (isIOS) {
        // @ts-ignore
        document.addEventListener(Global_CompassPageStateChanged, globalCompassEvent);
      }
      window.addEventListener('visibilitychange', onVisibilityChange);
    }
  } else {
    window.addEventListener('visibilitychange', onVisibilityChange);
  }

  return function removeEventListener() {
    // 移除 callback
    const idx = pageVisibilityChangeListeners.indexOf(listener);
    if (idx > -1) {
      pageVisibilityChangeListeners.splice(idx, 1);
    }

    // 如果还存在监听回调，则不用移除所有的监听
    if (pageVisibilityChangeListeners.length > 0) {
      return;
    }

    // 以下需要移除所有监听
    document.removeEventListener('touchstart', onDocumentClick);
    // compass 模式下
    if (window && window?.compass) {
      // 这里暂时先区分监听
      document.removeEventListener('pageappear', enter);
      document.removeEventListener('pagedisappear', leave);
      document.removeEventListener('pageforeground', foreground);
      document.removeEventListener('pagebackground', background);

      // 兼容处理，IOS下叠加半屏未触发visible
      if (isIOS) {
        // @ts-ignore
        document.removeEventListener(Global_CompassPageStateChanged, globalCompassEvent)
      }
    } else {
      window.removeEventListener('visibilitychange', onVisibilityChange);
    }
  };
}


/**
 *
 * @param listener 监听的事件
 */
export const usePageVisibilityListener = (listener: PageVisibilityChangeListener) => {
  const [pageVisible, setPageVisible] = useState<boolean>(document.visibilityState === 'visible' || window?.compass?.lifecycle?.visibilityState === 'visible');
  const [hasInit, setHasInit] = useState<boolean>(false);

  useEffect(() => {
    if (hasInit) {
      return;
    }
    addListenerForPageVisibilityChange((val) => {
      setPageVisible(val);
      setHasInit(true);
    });
  }, [hasInit]);

  // 执行回调
  useEffect(() => {
    listener(pageVisible);
  }, [pageVisible]);
}
