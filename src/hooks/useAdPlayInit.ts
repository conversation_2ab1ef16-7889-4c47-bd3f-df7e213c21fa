import { IUserState } from '@/logic/store/models/user/typings';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { useEffect } from 'react';
import useMxState from '@/hooks/useMxState';
import { IAppState } from '@/logic/store/models/app/typings';
import dispatch from '@/logic/store';
import { isOpenQueryAward } from '@/logic/store/models/utils';
import { ITaskState } from '@/logic/store/models/task/typing';
import { getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import { browseAdPlayerInstance } from '@/lib/adPlayer/ad_video';
import config from '@/config';
import { dealWithPreloadSuccessAdTask, ifShowTask } from '@/logic/store/models/task/helper';

interface InitOption {
  taskList: TaskInfo[];
  onPreloadSuccess?: (adTask: TaskInfo) => void;
  onPreloadEnd?: () => void;
  onPreloadError?: (adTask: TaskInfo, error: Error) => void;
}
export const useAdPlayInit = ({ taskList, onPreloadSuccess, onPreloadEnd }: InitOption) => {
  const [user] = useMxState<IUserState>('user');
  const [app] = useMxState<IAppState>('app');
  const [task] = useMxState<ITaskState>('task');
  useEffect(() => {
    if (taskList?.length && user.bindTaobao) {
      adPlayInit();
    }
  }, [taskList, user.bindTaobao]);

  console.log(taskList,'taskList')
  const adPlayInit = async () => {
    const isLite = app?.pr === 'UCLite';
    const appVersion = await dispatch.app.getAppVersion();
    const isOpenQueryReward = isOpenQueryAward(appVersion, isLite);
    const preloadAdTaskMap = task.preloadAdTaskMap;
    taskList &&
      taskList?.length &&
      taskList?.forEach((adTask, index) => {
        const isLast =
          index === taskList.length - 1 &&
          taskList.findIndex((item) => item?.id === adTask?.id) === taskList.length - 1;
        if (preloadAdTaskMap.has(`${adTask?.id}`)) {
          return;
        }
        const adData = getIncentiveAdSlotData(adTask);
        if (adData?.slotKey) {
          preloadAdTaskMap.set(`${adTask?.id}`, adTask?.id);
          browseAdPlayerInstance.init({
            task: adTask,
            slotKey: adData?.slotKey,
            appId: adData?.appId,
            coralAppId: config?.appId,
            enableAsyncQueryReward: isOpenQueryReward,
            finishPreload: (res) => {
              // 加载成功
              if (res) {
                dealWithPreloadSuccessAdTask(adTask);
                onPreloadSuccess && onPreloadSuccess(adTask);
                isLast && onPreloadEnd && onPreloadEnd();
              }
            },
          });
        } else {
          isLast && onPreloadEnd && onPreloadEnd();
        }
      });
  };
  return {
    taskList: taskList.filter((item) => ifShowTask(item)),
  };
};
