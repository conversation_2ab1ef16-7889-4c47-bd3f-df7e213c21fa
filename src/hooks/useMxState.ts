import { useState, useRef } from 'react';
import mx from '@ali/pcom-mx';
import { useOnce } from './useOnce';

const isPlainObject = (val) => typeof val === 'object' && {}.toString.call(val) === '[object Object]';

const isArray = (val) => typeof val === 'object' && {}.toString.call(val) === '[object Array]';

const isDef = (val) =>
  val !== undefined && val !== null && val !== 'undefined' && val !== 'null' && String(val) !== 'NaN';

const useMxState = <T>(location = '', defaultData?: any): [T, (T) => void] => {
  const initValue = isDef(mx.store.get(location)) ? mx.store.get(location) : defaultData;
  const initialState = useRef(initValue);
  const [mxState, setMxState] = useState<T>(initialState.current);
  const updateMxState = useRef((newVal) => {
    mx.store.update(location, newVal);
  });

  const stateChangeListener = useRef((state) => {
    setMxState(() => {
      if (isPlainObject(state)) {
        return { ...state };
      } else if (isArray(state)) {
        return state.slice();
      } else {
        return state;
      }
    });
  });

  useOnce(() => {
    mx.store.on(location, stateChangeListener.current);
    return () => {
      mx.store.off(location, stateChangeListener.current);
    };
  });

  return [mxState, updateMxState.current];
};

export default useMxState;
