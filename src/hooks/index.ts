import { useCallback, useEffect, useRef, useState } from 'react';

const useLatest = <T>(value: T) => {
  const ref = useRef(value);
  ref.current = value;

  return ref;
};

const useMount = (fn: () => void) => {
  useEffect(() => {
    fn?.();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
};

const useUnmount = (fn: () => void) => {
  const { current } = useLatest(fn);
  useEffect(
    () => () => {
      current?.();
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );
};

const useUpdate = () => {
  const [, setState] = useState({});

  return useCallback(() => setState({}), []);
};

export { useLatest, useMount, useUnmount, useUpdate };

export const useDebounce = (fn, delay, dep = []) => {
  const { current } = useRef<{ fn: any; timer: any }>({ fn, timer: null });
  useEffect(() => {
    current.fn = fn;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fn]);

  return useCallback(function f(this: any, ...args) {
    if (current.timer) {
      clearTimeout(current.timer);
    }
    current.timer = setTimeout(() => {
      current.fn.call(this, ...args);
    }, delay);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dep);
};

export const useThrottle = (callback, delay) => {
  const [timeoutId, setTimeoutId] = useState(null);
  const [lastTime, setLastTime] = useState(0);

  const throttled = () => {
    const currentTime = Date.now();
    if (currentTime - lastTime >= delay) {
      callback();
      setLastTime(currentTime);
      // 设置超时，确保节流效果
      const id = setTimeout(() => {
        setTimeoutId(null);
      }, delay);
      setTimeoutId(id);
    }
  };

  useEffect(() => {
    return () => {
      if (timeoutId !== null) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  return throttled;
};


/*
 * 防抖，添加至少执行次数参数
 * @params fn 待执行函数
 * @params delay 防抖时间
 * @params dep 返回useCallback依赖
 * @params atleast 多少毫秒内至少执行一次
 */
export const useDebounceAtleast = (fn, delay, dep = [], atleast = 0) => {
  const { current } = useRef<{ fn: any; timer: any; atleast: number; preview: any }>({
    fn,
    timer: null,
    atleast,
    preview: null,
  });
  useEffect(() => {
    current.fn = fn;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fn]);

  return useCallback(function f(this: any, ...args) {
    const debounce = () => {
      if (current.timer) {
        clearTimeout(current.timer);
      }
      current.timer = setTimeout(() => {
        current.fn.call(this, ...args);
      }, delay);
    };
    if (current.atleast) {
      const now = Number(new Date());
      !current.preview && (current.preview = now);
      if (now - current.preview > atleast) {
        current.fn.call(this, ...args);
        current.preview = now;
      } else {
        debounce();
      }
    } else {
      debounce();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dep);
};
