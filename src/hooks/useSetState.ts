import { useCallback, useEffect, useRef, useState } from 'react';
import type { Dispatch, SetStateAction, MutableRefObject } from 'react';

type StateType<T> = T | (() => T);

export function useRefState<T>(initialState: StateType<T>): [T, Dispatch<SetStateAction<T>>, MutableRefObject<T>] {
  const [state, setState] = useState<T>(initialState);
  const ref = useRef(state);
  const setRafState = useCallback(
    (patch) => {
      setState((prevState) => {
        // eslint-disable-next-line no-return-assign
        return (ref.current = typeof patch === 'function' ? patch(prevState) : patch);
      });
    },
    [state],
  );
  return [state, setRafState, ref];
}

export const useUnmountedRef = () => {
  const unmountedRef = useRef(false);
  useEffect(() => {
    unmountedRef.current = false;

    return () => {
      unmountedRef.current = true;
    };
  }, []);
  return unmountedRef;
};

const useSetState = <T extends object>(
  initialState: T,
): [T, (patch: Partial<T> | ((prevState: T) => Partial<T>)) => void, MutableRefObject<T>] => {
  const unmountedRef = useUnmountedRef();
  const [state, setState, ref] = useRefState<T>(initialState);

  const setMergeState = useCallback((patch) => {
    if (unmountedRef.current) return;
    setState((prevState) => ({
      ...prevState,
      ...(typeof patch === 'function' ? patch(prevState) : patch),
    }));
  }, []);

  return [state, setMergeState, ref];
};

export default useSetState;
