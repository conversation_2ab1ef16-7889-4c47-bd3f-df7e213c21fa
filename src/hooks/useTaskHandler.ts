import { TaskInfo, TASK_EVENT_TYPE, TASK_STATUS } from '@/pages/index/components/TaskPop/TaskList/types';
import { IAppState, TaobaoRtaConfig } from '@/logic/store/models/app/typings';
import { IUserState } from '@/logic/store/models/user/typings';
import { IHighValueTaskState } from '@/logic/store/models/highTask/types';
import { checkTaskFinished, canContinueGoLinkTask, logToFinishTask, getTaskAward } from '@/pages/index/components/TaskPop/TaskList/util';
import { dongfengTaskReport, taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { execWithLock } from '@/lib/utils/lock';
import stat from '@/lib/stat';
import dispatch from '@/logic/store';
import useMxState from '@/hooks/useMxState';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';

interface UseTaskHandlerProps {
  currentTask: TaskInfo | null;
  source: 'bubble' | 'list';
}

/**
 * 任务处理：只关注任务的处理逻辑
 * @param props 
 * source: 来源，埋点使用
 * @returns 
 */
export const useTaskHandler = ({
  currentTask,
  source,
}: UseTaskHandlerProps) => {
  const [user] = useMxState<IUserState>('user');
  const [app] = useMxState<IAppState>('app');
  const [highValueTask] = useMxState<IHighValueTaskState>('highValueTask');
  const [taobaoRtaConfig] = useMxState<TaobaoRtaConfig>('app.frontData.taobaoRtaConfig');

  const getCommonTrackParams = (task: TaskInfo | null) => {
    if (!task) return {};
    
    const taobaoRtaInfo = app.taobaoRtaInfo;
    const isTaobaoRta = [
      TASK_EVENT_TYPE.RTA_CALL_TAOBAO, 
      TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, 
      TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD
    ].includes(task.event) && taobaoRtaInfo;

    return {
      task_id: task.id,
      task_name: task.name,
      isfinish: checkTaskFinished(task) ? 1 : 0,
      task_count: task?.dayTimes?.progress,
      taskclassify: task.taskClassify,
      groupcode: task.groupCode,
      award_amount: getTaskAward(task),
      task_progress: task?.dayTimes?.progress || '',
      tasklist_source: source === 'bubble' ? 'resourceNiche' : source,
      ...(isTaobaoRta ? {
        taobao_rta_type: taobaoRtaInfo.category,
        sid: taobaoRtaInfo.adInfo?.sid,
        rta_price: taobaoRtaInfo.adInfo?.price
      } : {})
    };
  };

  const toFinishTask = async (task: TaskInfo, requestId: string) => {
    localStorage.setItem(LocalStorageKey.FINISH_TASK_FROM, 'taskBubble');
    logToFinishTask(task, source);
    // note: 具体的处理逻辑
    return taskActionHandler(task, requestId);
  };

  const receiveAward = (task: TaskInfo) => {
    dispatch.task.finishTask({
      taskId: task.id,
      type: "award",
      useUtCompleteTask: !!task.useUtCompleteTask,
      publishId: task.publishId
    });
  };

  const handleTask = async () => {
    if (!currentTask) return;
    
    try {
      const requestId = geneTaskRequestId();
      dongfengTaskReport(currentTask, 'click');

      const { state, id } = currentTask;
      
      stat.click('task_click', {
        c: source === 'bubble' ? 'card' : source,
        d: 'task',
        pop_source: source === 'bubble' ? 'balloon' : source,
        page_status: user?.bindTaobao ? 1 : 0,
        requestId,
        scene: taobaoRtaConfig?.sceneId,
        ...getCommonTrackParams(currentTask)
      });

      if (checkTaskFinished(currentTask)) {
        canContinueGoLinkTask(currentTask);
        return;
      }

      if (highValueTask.currentTaskInfo?.id !== id) {
        const loginStatus = await dispatch.user.checkLoginAndBind(0, 7);
        if (!loginStatus && highValueTask.currentTaskInfo?.id !== currentTask.id) {
          return;
        }
      }

      if (state === TASK_STATUS.TASK_NOT_COMPLETED && 
          [TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD].includes(currentTask.event)) {
        const result = await dispatch.task.checkAppDownloadFinish(currentTask, highValueTask.currentTaskInfo?.id !== id);
        if (result) {
          return;
        }
      }

      if (state === TASK_STATUS.TASK_COMPLETED) {
        return receiveAward(currentTask);
      }

      execWithLock('finish_task_lock', async () => {
        await toFinishTask(currentTask, requestId);
      }, 2000);
    } catch (error) {
      console.error('Task handling failed:', error);
    }
  };

  return {
    handleTask,
    getCommonTrackParams
  };
}; 
