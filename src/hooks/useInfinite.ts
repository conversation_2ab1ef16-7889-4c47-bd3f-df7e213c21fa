import { useEffect, useRef, useState } from 'react';

interface IBaseProps<T> {
  fetchNextPage: (params: { pageNum: number; pageSize: number }) => Promise<T[]>;
  initPageNum: number;
  initData: T[];
  initPageSize: number;
  maxRange?: number;
  needCheckVersion?: boolean;
  total?: number;
  onLoadMore?: () => void;
}

interface IVersionAtrribute<T> {
  version?: number;
  buildVersionKeyFn?: (data: T[]) => number;
}

type IProps<T> = IBaseProps<T> & IVersionAtrribute<T>;
export const useInfinite = <T>(props: IProps<T>) => {
  const {
    initPageNum,
    initData,
    fetchNextPage,
    initPageSize,
    version,
    buildVersionKeyFn,
    maxRange,
    needCheckVersion = false,
    total,
    onLoadMore
  } = props;
  // const [data, setData, dataRef] = useSetState(initData || []);
  const [data, setData] = useState<T[]>(initData);
  const [loading, setLoading] = useState(false);
  const pageNum = useRef(initPageNum);
  // const [pageNum, setPageNum] = useState(initPageNum);
  const hasMoreRef = useRef(true);
  const versionRef = useRef(version || 0);

  useEffect(() => {
    setData(initData);
  }, [initData]);
  const isOverRange = (arr: T[]) => {
    if (maxRange) {
      return arr.length >= maxRange;
    }
    if (typeof total === 'number' && arr.length >= total) {
      return true;
    }
    return false;
  };

  const checkVersionUpdate = async (curPageNum: number) => {
    const res = await fetchNextPage({
      pageSize: curPageNum * initPageSize,
      pageNum: 1,
    });
    // reset(res);
    if (buildVersionKeyFn) {
      versionRef.current = buildVersionKeyFn(res)
    }
    setData(res || []);
    if (isOverRange(res)) {
      hasMoreRef.current = false;
    }
  };
  const fetchNext = async (isRetry?: boolean) => {
    if (!hasMoreRef.current) return;
    if (isOverRange(data)) {
      hasMoreRef.current = false;
      return;
    }
    // versionRef.current = version || 0;
    const curPageNum = isRetry ? pageNum.current - 1 : pageNum.current;
    const res = await fetchNextPage({ pageNum: curPageNum, pageSize: initPageSize });
    if (!res.length) {
      hasMoreRef.current = false;
      return;
    }
    if (needCheckVersion && buildVersionKeyFn && versionRef.current && buildVersionKeyFn(res) !== versionRef.current) {
      console.log(buildVersionKeyFn(res), 'buildVersionKeyFn(res)');
      console.log(versionRef.current, 'versionRef.current');
      // 新旧版本号不一致，数据有更新
      await checkVersionUpdate(curPageNum);
    } else {
      if (buildVersionKeyFn) {
        versionRef.current = buildVersionKeyFn(res)
      }
      setData((prev) => {
        let newRankings = [...prev, ...res];
        if (isOverRange(newRankings)) {
          hasMoreRef.current = false;
          newRankings = [...newRankings]?.slice(0, maxRange)
        }
        return newRankings;
      });
      // if (isOverRange(dataRef.current)) {
      //   hasMoreRef.current = false;
      //   return;
      // }
    }

    !isRetry && pageNum.current++;
  };
  const loadMore = async (isRetry?: boolean) => {
    onLoadMore && onLoadMore()
    try {
      setLoading(true);
      await fetchNext(isRetry);
    } finally {
      setLoading(false);
    }
  };

  const reset = (newData: T[]) => {
    setData(newData || []);
    pageNum.current = 2;
    hasMoreRef.current = true;
  };
  const update = (newData: T[]) => {
    setData(newData);
    pageNum.current = 2;
    hasMoreRef.current = true;
  };
  return {
    data,
    loading,
    loadMore,
    hasMore: hasMoreRef.current,
    reset,
    update,
  };
};
