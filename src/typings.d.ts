import * as store from '@ali/pcom-mx/lib/base/store' // 继承

declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.json' {
  const value: any;
  export default value;
}


declare module '*.svg'
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.bmp'
declare module '*.tiff'

declare let __wh_data__: any;

declare let MAIN_VERSION: string; // 业务代码版本号
declare let BUILD_UUID: string; // 构建唯一id
declare let PUBLISH_ENV: 'prod' | 'pre' | 'daily' | never; // 云端环境
declare let PUBLISH_VERSION: string;
declare let DEV: boolean;

declare interface Window {
  ucapi?: any;
  IS_WEEX: boolean;
  _ucEvent?: Event & {
    used?: boolean;
    detail: Record<string, any>;
  };
  __wh_data__: any;
  ucweb: any;
  __lp_disable: string;
  __itracecfg__: {
    wv_traceid: string;
  };
}

// 递归深度
type Prev = [never, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

type PathImpl<T, D extends number = 10> = [D] extends [never]
  ? never
  : T extends object
    ? {
      [K in keyof T]: T[K] extends any[]
        ? K
        : T[K] extends object
          ? K | `${K & string}.${PathImpl<T[K], Prev[D]> & string}`
          : K;
    }[keyof T]
    : never;

type Path<T> = PathImpl<T>;

type PathValue<T, P extends string> = P extends `${infer Key}.${infer Rest}`
  ? Key extends keyof T
    ? PathValue<T[Key], Rest>
    : never
  : P extends keyof T
    ? T[P]
    : never;

declare module '@ali/pcom-mx/lib/base/store' {
  export interface Store {
    getStore(): import('@/logic/store').StoreState;
    get<TPath extends Path<import('@/logic/store').StoreState>>(
      path: TPath
    ): PathValue<import('@/logic/store').StoreState, TPath>;
  }
}
