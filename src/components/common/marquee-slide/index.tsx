import React, { useEffect, useRef, useState, useCallback } from 'react';
import './index.scss';

export type SlideDirection = 'row' | 'col';
export type SlidePosition = 'start' | 'center' | 'end';

export interface MarqueeSlideProps<T> {
  /**
   * 文案列表
   */
  list: T[];
  /**
   * 滑动时间
   */
  durning?: number;
  /**
   * 停留时间
   */
  delay?: number;
  /**
   * 对齐位置 'start' | 'center' | 'end'
   */
  position?: SlidePosition;
  /**
   * 方向 'row' | 'col'
   */
  direction?: SlideDirection;
  /**
   * 边缘淡出遮罩
   */
  mask?: boolean;
  /**
   * 自定义渲染项
   */
  renderItem?: ({ data, index }: { data: T; index: number }) => React.ReactNode;
  /**
   * 动画停止时的回调函数
   */
  onStop?: (index: number) => void;
}

export default function BaseMarqueeSlide<T>(props: MarqueeSlideProps<T>) {
  const {
    list = [],
    durning = 1000,
    delay = 1000,
    position = 'start',
    direction = 'col',
    mask = true,
    renderItem,
    onStop,
  } = props;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [nextIndex, setNextIndex] = useState(1);
  const [beforeAnimate, setBeforeAnimate] = useState(false);
  const [animating, setAnimating] = useState(false);
  const [marqueeHeight, setMarqueeHeight] = useState(0);

  const currentItem = list[currentIndex];
  const nextItem = list[nextIndex];

  const itemRef = useRef<HTMLElement | null>(null);
  const timerRef = useRef<number>();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const animate = () => {
    setBeforeAnimate(true);
    setAnimating(true);
    timerRef.current = window.setTimeout(() => {
      setBeforeAnimate(false);
      setAnimating(false);

      setCurrentIndex(nextIndex);
      setNextIndex(nextIndex >= list.length - 1 ? 0 : nextIndex + 1);
    }, durning! + delay!);
  };

  useEffect(() => {
    if (list.length > 1) {
      const t = setTimeout(() => {
        animate();
        clearTimeout(t);
      }, delay);
    }
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [list, delay, animate]);

  useEffect(() => {
    if (itemRef.current) {
      setMarqueeHeight(itemRef.current.offsetHeight);
    }
  }, [itemRef.current]);

  const stop = () => {
    if (onStop) {
      onStop(nextIndex);
    }
  };

  const marqueeHeightStyle = marqueeHeight > 0 ? { height: `${marqueeHeight}rpx` } : {};
  const animateDuration = animating ? { transitionDuration: `${durning}ms` } : {};
  const withMask = mask === true ? 'with-mask' : '';

  return (
    <div
      className={`base-marquee-list marquee-${position} marquee-${direction} ${withMask}`}
      style={marqueeHeightStyle}
    >
      <div
        ref={itemRef}
        className={`marquee-item ${beforeAnimate ? 'animate' : ''} ${animating ? 'animating' : ''}`}
        style={animateDuration}
        onTransitionEnd={stop}
      >
        { !!list?.length && renderItem && renderItem({ data: currentItem, index: currentIndex })}
      </div>
      <div
        className={`marquee-item ${beforeAnimate ? 'animate' : ''} ${animating ? 'animating' : ''}`}
        style={animateDuration}
      >
        {!!list?.length && renderItem && renderItem({ data: nextItem, index: nextIndex })}
      </div>
    </div>
  );
}
