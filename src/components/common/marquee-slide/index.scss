.base-marquee-list {
  display: flex;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  font-size: 1em;
  min-height: 1em;

  &.marquee-col {
    flex-direction: column;
  }

  &.marquee-col.with-mask {
    -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0), #000 20%, #000 80%, rgba(0, 0, 0, 0) 100%);
  }

  &.marquee-col.marquee-center {
    align-items: center;
  }

  &.marquee-col.marquee-end {
    align-items: flex-end;
  }

  &.marquee-row.with-mask {
    -webkit-mask-image: linear-gradient(to right, rgba(0, 0, 0, 0), #000 10%, #000 90%, rgba(0, 0, 0, 0) 100%);
  }

  &.marquee-row .marquee-item {
    width: 100%;
  }


  &.marquee-row.marquee-center .marquee-item {
    justify-content: center;
  }

  &.marquee-row.marquee-end .marquee-item {
    justify-content: flex-end;
  }

  .marquee-item {
    position: relative;
    display: flex;
    align-items: center;
    min-width: 0;
    flex-basis: 100%;
    flex-shrink: 0;
  }

  .marquee-item.animate {
    transition: transform linear 1s;
  }

  .marquee-item.animating {
    transform: translate(0, -100%);
  }

  &.marquee-row .marquee-item {
    flex: 0 0 100%;
  }

  &.marquee-row .marquee-item.animating {
    transform: translate(-100%, 0);
  }

}
