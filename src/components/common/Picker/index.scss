.pop-date-picker {
  $--picker-default-wh: 598rpx;
  $--picker-content-h: 498rpx;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.6);
  z-index: 10;
  .pop-mask {
    width: 100%;
    height: 100%;
    transform: translateZ(0);
  }
  .pop-content-wrap {
    position: absolute;
    width: 100vw;
    left: 0;
    height: $--picker-default-wh;
    background: #ffffff;
    border-radius: 41.67rpx 41.67rpx 0 0;
    bottom: 0;
    .pop-header {
      height: 100rpx;
      padding: 0 36rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
      font-family: PingFangSC-Medium;
      font-weight: 500;
      border-bottom: 0.5rpx solid #e0e5e899;
      .pop-header-close {
        color: #12161a;
        &:active {
          opacity: 0.5;
        }
      }
      .pop-header-confirm {
        color: #2696ff;
        &:active {
          opacity: 0.5;
        }
      }
    }
    .pop-content {
      position: relative;
      width: 100%;
      height: $--picker-content-h;
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow-y: scroll;
      overscroll-behavior: contain;

      .picker-column {
        flex: 1;
        overflow: hidden;
        .picker-column-wrapper {
          transition-timing-function: cubic-bezier(0.23, 1, 0.68, 1);
        }
        .selected {
          .pop-content-item {
            color: #000 !important;
            font-family: PingFangSC-Medium !important;
          }
        }
        .picker-column-item {
          display: flex;
          align-items: center;
          justify-content: center;
          list-style: none;
          margin: 0;
          &--disabled {
            cursor: not-allowed;
            opacity: 0.6;
          }
          .pop-content-item {
            width: 670rpx;
            min-height: 80rpx;
            max-height: 80rpx;
            display: flex;
            font-family: PingFangSC-Regular;
            font-size: 32rpx;
            color: #859199;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            cursor: grab;
          }
        }
      }
      .picker-footer {
        width: 100%;
        height: 67rpx;
        background-color: white;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .pop-footer-indicator {
          background-color: #000;
          border-radius:99rpx;
          height: 7rpx;
          width: 250rpx;
          margin-top: 40rpx;
          margin-left: auto;
          margin-right: auto;
        }
      }
    }

    .picker-frame {
      position: absolute;
      top: 31.5%;
      z-index: 2;
      width: 100%;
      height: 80rpx;
      transform: translateY(-50%);
      pointer-events: none;
      border-bottom: 0.5rpx solid #e0e5e899;
      border-top: 0.5rpx solid #e0e5e899;
    }
  }
  .pop-appear {
    animation: bottom-appear 0.3s ease-in-out;
  }
  .pop-disappear {
    animation: bottom-disappear 0.3s ease-in-out;
  }
  .pop-content {
  }

  @keyframes bottom-appear {
    from { bottom: -598rpx; }
    to { bottom: 0; }
  }
  @keyframes bottom-disappear {
    from { bottom: 0; }
    to { bottom: -598rpx; }
  }
}
