
export type PickerColumnOption = {
  text?: React.ReactNode;
  value?: string;
  children?: PickerColumnOption[];
  disabled?: boolean;
  textKey?: string;
  valueKey?: string;
} & Record<string, any>

export interface BaseTypeProps {
  style?: React.CSSProperties | any;
  className?: string;
  children?: React.ReactNode;
}

export interface PickerColumnProps extends BaseTypeProps {
  /** column 列索引 */
  index?: number;
  readOnly?: boolean;
  itemHeight: string; // rpx;
  value?: string;
  /** 提示用户选择的第一项 */
  placeholder?: React.ReactNode;
  /** 惯性滑动的时间 */
  swipeDuration?: number | string;
  /** 可见的选项个数 */
  visibleItemCount?: number | string;
  options?: PickerColumnOption[];
  optionRender?: (option: any) => React.ReactNode;
  onSelect: (
    value: PickerColumnOption | undefined,
    index: number,
    ignoreChange?: boolean
  ) => void;
  renderFooter?: () => React.ReactNode;
}
export interface PickerSingleProps<T> extends IPickerProps<T> {
  /** 选中项 */
  value?: string;
  /** 默认选中项 */
  defaultValue?: string;
  /** 选项改变时触发 */
  onChange?: (value: string, selectedRow: T, index?: number) => void;
}

export interface IPickerProps<T> {
  loading?: boolean;
  loadingContent?: React.ReactNode | null | JSX.Element;
  visible: boolean;
  onClose?: () => void;
  onSelect?: (value: any) => void;
  /** 点击完成按钮时触发 */
  onConfirm?: (value: string, selectedRow: T, index: number) => void;
  value?: string;
  columns?: PickerColumnOption[];
  renderFooter?: () => React.ReactNode;
}
