import React, { useMemo, useRef, forwardRef, memo, useImperativeHandle } from 'react';
import { PickerColumnOption, PickerColumnProps } from './types';
import useSetState from '@/hooks/useSetState';
import { useIsomorphicLayoutEffect } from 'ahooks';
import useTouch from '@/hooks/useTouch';
import classNames from 'classnames';
import { getScreenWidth } from '@/lib/utils';

const DEFAULT_DURATION = 200;

// 在手指离开屏幕时，如果和上一次 move 时的间隔小于 `MOMENTUM_LIMIT_TIME` 且 move
// 距离大于 `MOMENTUM_LIMIT_DISTANCE` 时，执行惯性滑动
const MOMENTUM_LIMIT_TIME = 300;
const MOMENTUM_LIMIT_DISTANCE = 15;

function range(num: number, min: number, max: number): number {
  return Math.min(Math.max(num, min), max);
}

function getElementTranslateY(element) {
  const style = window.getComputedStyle(element);
  const transform = style.transform || style.webkitTransform;
  const translateY = transform.slice(7, transform.length - 1).split(', ')[5];

  return Number(translateY);
}

function convertRpx(value: string): number {
  if (typeof window === 'undefined') return 40;
  const val = value.replace(/rpx/g, '');
  return ((+val / getScreenWidth()) * 100 * window.innerWidth) / 100;
}


const PickerColumn = memo<PickerColumnProps & { ref?: React.ForwardedRef<{ stopMomentum: () => void }> }>(
  forwardRef<{ stopMomentum: () => void }, PickerColumnProps>((props, ref) => {
    const { itemHeight: _itemHeight, visibleItemCount, placeholder, value, renderFooter } = props;

    const itemHeight = useMemo(() => {
      // itemHeight 限制输入为rpx，最终会转成vw，将vw转成px再进行计算
      return typeof _itemHeight === 'string' ? convertRpx(_itemHeight) : 40;
    }, [_itemHeight]);

    const options = useMemo(() => {
      if (Array.isArray(props.options) && !props.options.length) return [];
      if (placeholder) {
        const DEFAULT_OPTION = {
          value: undefined,
          valueKey: undefined,
          text: String(placeholder || ''),
          textKey: String(placeholder || ''),
          disabled: false
        };
        return [DEFAULT_OPTION, ...(props.options || [])];
      }
      return props.options || [];
    }, [props.options]);

    const wrapper = useRef(null);
    const moving = useRef(false);
    const startOffset = useRef(0);
    const transitionEndTrigger = useRef<(() => void) | null>(null);
    const touchStartTime = useRef(0);
    const momentumOffset = useRef(0);

    const [state, updateState, stateRef] = useSetState({
      offset: 0,
      duration: 0,
    });

    const touch = useTouch();

    const baseOffset = useMemo(() => {
      // 默认转入第一个选项的位置
      return (itemHeight * (+(visibleItemCount || 0) - 1)) / 2;
    }, [itemHeight, visibleItemCount]);

    const adjustIndex = (index: number) => {
      index = range(index, 0, options.length);

      for (let i = index; i < options.length; i += 1) {
        if (!options[i]?.disabled) return i;
      }
      for (let i = index - 1; i >= 0; i -= 1) {
        if (!options[i]?.disabled) return i;
      }

      return null;
    };

    const onSelect = (val: PickerColumnOption | undefined) => {
      props.onSelect(val, props.index as number);
    };

    const setIndex = (index: number) => {
      index = adjustIndex(index) || 0;
      const offset = -index * itemHeight;
      const trigger = () => {
        if (options[index]?.valueKey !== value) {
          onSelect(options[index]);
        }
      };

      // 触发选中事件
      if (moving.current && offset !== stateRef.current.offset) {
        transitionEndTrigger.current = trigger;
      } else {
        trigger();
      }
      updateState({ offset });
    };

    const animate = (index: number) => {
      index = adjustIndex(index) || 0;
      const offset = -index * itemHeight;
      updateState({ offset });
    };

    useIsomorphicLayoutEffect(() => {
      if (options.length === 0) {
        if (value !== undefined) {
          onSelect(undefined);
        }
      } else {
        let targetIndex = options.findIndex((item) => item.valueKey === value);
        if (targetIndex < 0) {
          targetIndex = 0;
          onSelect(options[0]);
        }
        animate(targetIndex);
      }
    }, [value, JSON.stringify(options)]);

    const onClickItem = (index: number) => {
      if (moving.current || props.readOnly) {
        return;
      }
      transitionEndTrigger.current = null;
      updateState({ duration: DEFAULT_DURATION });
      setIndex(index);
    };

    const getIndexByOffset = (offset: number) => range(Math.round(-offset / itemHeight), 0, options.length - 1);

    const momentum = (distance: number, _duration: number) => {
      const speed = Math.abs(distance / _duration);

      distance = stateRef.current.offset + (speed / 0.003) * (distance < 0 ? -1 : 1);
      const index = getIndexByOffset(distance);
      updateState({ duration: +(props.swipeDuration || 300) });
      setIndex(index);
    };

    const stopMomentum = () => {
      moving.current = false;
      updateState({ duration: 0 });

      if (transitionEndTrigger.current) {
        transitionEndTrigger.current();
        transitionEndTrigger.current = null;
      }
    };

    const onTouchStart = (event) => {
      if (props.readOnly) {
        return;
      }

      touch.start(event);
      let { offset } = state;

      if (moving.current) {
        const translateY = getElementTranslateY(wrapper.current);
        offset = Math.min(0, translateY - baseOffset);
        startOffset.current = offset;
      } else {
        startOffset.current = offset;
      }

      updateState({ duration: 0, offset });
      touchStartTime.current = Date.now();
      momentumOffset.current = startOffset.current;
      transitionEndTrigger.current = null;
    };

    const onTouchMove = (event: TouchEvent | React.TouchEvent) => {
      if (props.readOnly) {
        return;
      }

      touch.move(event as TouchEvent);

      if (touch.isVertical()) {
        moving.current = true;
      }

      const offset = range(startOffset.current + touch.deltaY.current, -(options.length * itemHeight), itemHeight);

      updateState({
        offset,
      });

      const now = Date.now();
      if (now - touchStartTime.current > MOMENTUM_LIMIT_TIME) {
        touchStartTime.current = now;
        momentumOffset.current = offset;
      }
    };

    const onTouchEnd = () => {
      if (props.readOnly || !moving.current) {
        console.log('no moving.current,start momentum')
        return;
      }
      const distance = stateRef.current.offset - momentumOffset.current;
      const duration = Date.now() - touchStartTime.current;

      const allowMomentum = duration < MOMENTUM_LIMIT_TIME && Math.abs(distance) > MOMENTUM_LIMIT_DISTANCE;

      if (allowMomentum) {
        momentum(distance, duration);
        return;
      }
      const index = getIndexByOffset(stateRef.current.offset);
      updateState({ duration: DEFAULT_DURATION });
      setIndex(index);

      // compatible with desktop scenario
      // use setTimeout to skip the click event triggered after touchstart
      setTimeout(() => {
        moving.current = false;
      }, 0);
    };

    const renderOptions = () => {
      const optionStyle = {
        height: `${props.itemHeight}`,
      };

      return options.map((option, index: number) => {
        const { disabled } = option;

        const data = {
          role: 'button',
          style: optionStyle,
          tabIndex: disabled ? -1 : 0,
          className: classNames('picker-column-item', {
            disabled,
            selected: option.valueKey === value,
          }),
          onClick: () => {
            onClickItem(index);
          },
        };

        const childData = {
          className: 'ellipsis',
          children: option.textKey,
        };

        return (
          <div {...data} key={index}>
            {props.optionRender ? props.optionRender(option) : <div {...childData} />}
          </div>
        );
      });
    };

    useImperativeHandle(ref, () => ({
      stopMomentum,
    }));

    return (
      <div
        className={classNames('picker-column', props.className)}
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
        onTouchCancel={onTouchEnd}
      >
        <div
          ref={wrapper}
          style={{
            transform: `translate3d(0, ${state.offset + baseOffset}px, 0)`,
            transitionDuration: `${state.duration}ms`,
            transitionProperty: state.duration ? 'all' : 'none',
          }}
          className={classNames(('picker-column-wrapper'))}
          onTransitionEnd={stopMomentum}
        >
          {renderOptions()}
          {renderFooter && renderFooter()}
        </div>
      </div>
    );
  }),
(prev, next) => {
  if (prev.index !== next.index) return false;
  if (prev.value !== next.value) {
    return false;
  }
  if (prev.onSelect !== next.onSelect) return false;
  if (JSON.stringify(prev.options) !== JSON.stringify(next.options)) {
    return false;
  }
  return true;
},);

export default PickerColumn;
