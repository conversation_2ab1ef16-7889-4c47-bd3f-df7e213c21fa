import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import './index.scss';
import PickerColumn from './column';
import { IPickerProps, PickerColumnOption } from './types';

export interface PickerRef {
  close: () => void;
}

const Picker = forwardRef<PickerRef, IPickerProps<PickerColumnOption>>((props, ref) => {
  const { value, columns, loading, loadingContent, visible, onClose, onConfirm, onSelect, renderFooter } = props;
  const [innerValue, setInnerValue] = useState<string>(value || '');
  // const [visible, setVisible] = useState(_visible || false);

  useImperativeHandle(ref, () => ({
    close: () => {
      handleClose();
    },
  }));

  useEffect(() => {
    if (value === undefined) return; // Uncontrolled mode
    if (innerValue === value) return;
    setInnerValue(value);
  }, [value]);
  useEffect(() => {
    if (!visible) return;
    const originalOverflow = document.body.style.overflow;
    document.body.style.overflow = 'hidden';

    return () => {
      // 恢复原始样式
      document.body.style.overflow = originalOverflow;
    };
  }, [visible]);

  const handleSelect = (val: PickerColumnOption, index: number) => {
    onSelect && onSelect(val);
    setInnerValue(val?.valueKey || '');
  };

  const handleClose = () => {
    onClose && onClose();
    // setVisible(false);
  };
  const handleConfirm = () => {
    const selectRowIndex = (columns || []).findIndex((item) => item.valueKey === innerValue);
    // if (selectRowIndex === -1) return;
    onConfirm && onConfirm(innerValue, (columns || [])[selectRowIndex], selectRowIndex);
    // setVisible(false);
  };
  const renderHeader = () => {
    return (
      <div className="pop-header">
        <div className="pop-header-close" onClick={handleClose}>
          取消
        </div>
        <div className="pop-header-confirm" onClick={handleConfirm}>
          确定
        </div>
      </div>
    );
  };
  const renderColumnItems = () => {
    return (
      <PickerColumn
        renderFooter={renderFooter}
        onSelect={handleSelect}
        swipeDuration={300}
        optionRender={(val) => {
          return (
            <div className="pop-content-item" key={val.value}>
              {val.text}
            </div>
          );
        }}
        value={innerValue}
        itemHeight={'80rpx'}
        // index={columnIndex}
        visibleItemCount={4}
        options={columns}
      />
    );
  };
  const renderPickerFrame = () => {
    return !!columns?.length && <div className="picker-frame" />;
  };
  const renderFooterIndicator = () => {
    return (
      <div className="picker-footer">
        <div className="pop-footer-indicator" />
      </div>
    );
  };
  return (
    visible && (
      <div className={`pop-date-picker`} style={{ visibility: visible ? 'visible' : 'hidden' }}>
        <div className="pop-mask" onClick={handleClose} />
        <div className={`pop-content-wrap ${visible ? 'pop-appear' : 'pop-disappear'}`}>
          {renderHeader()}
          {loading ? (
            loadingContent
          ) : (
            <div className="pop-content">
              {renderColumnItems()}
              {renderPickerFrame()}
              {renderFooterIndicator()}
            </div>
          )}
        </div>
      </div>
    )
  );
});

export default Picker;
