import React, { useEffect, useImperativeHandle, useState } from 'react';
import mx from '@ali/pcom-mx';
import { MainAPI } from '@/logic/type/event';
import dispatch from '@/logic/store';
import Lottie from 'lottie-web';
import './index.scss';
import { isHighMode } from "@/gameObject/utils/screen";

const Index = (props) => {
  const { refProps } = props;
  const [isShow, setIsShow] = useState<boolean>(false);
  useImperativeHandle(refProps, () => ({
    playFertilizer,
  }));
  const playFertilizer = () => {
    setIsShow(true);
  };
  useEffect(() => {
    if (!isShow) return;
    const ani = Lottie.loadAnimation({
      name: 'collect-fertilizer',
      container: document.getElementById('collect-fertilizer') as HTMLElement,
      renderer: 'canvas',
      loop: false,
      autoplay: true,
      animationData: require('@/lib/animation/fertilizer.json'),
      assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202404/eae4be/images/',
    });
    ani.addEventListener('complete', () => {
      setIsShow(false);
      dispatch.app.updateHomeData();
      mx.event.emit(MainAPI.FeiLiaoDropAnim, {
        afterRefund: 0,
        refundAmount: 0,
      });
    });
  }, [isShow]);
  return isShow ? <div id="collect-fertilizer" className="collect-fertilizer" style={{top: isHighMode() ? `${610 / 7.5}vw` : `${490 / 7.5}vw`}} /> : null;
};

export default Index;
