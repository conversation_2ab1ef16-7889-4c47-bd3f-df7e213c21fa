import React, { Fragment, useMemo } from 'react';
import Hand from './assets/hand.png';

import './index.scss';

interface GuideHandProps {
  /**
   * 手势方向
   */
  reverse?: boolean;
  /**
   * 重复次数，不传无限循环
   */
  repeat?: number;
  /**
   * 播放动效
   */
  play?: boolean;
  /**
   * 对齐标准点
   * @values 'edge' | 'center'
   */
  alignPoint?: 'edge' | 'center';
  /**
   * 手势图片
   */
  src?: string;

  /**
   * 是否显示光圈
   */
  hideShine?: boolean;

  className?: string;
  animationEndEvent?: () => void;
  handClick?: (e: MouseEvent) => void;
}

const BaseGuide: React.FC<GuideHandProps> = ({
  reverse = false,
  play = true,
  alignPoint = 'edge',
  src = Hand,
  hideShine = false,
  ...props
}) => {
  const animationRepeat = useMemo(() => {
    const repeat = props.repeat ?? -1;
    return repeat >= 0
      ? {
        animationIterationCount: repeat * 2,
      }
      : undefined;
  }, [props.repeat]);

  const handStatus = [reverse ? 'reverse' : '', play ? 'is-play' : ''];
  const handAlign = alignPoint === 'edge' ? 'is-edge' : 'is-center';

  function handleClick(event) {
    props.handClick?.(event);
  }

  function animationEndEvent() {
    props.animationEndEvent?.();
  }

  return (
    <div className={`base-guide-hand ${handStatus.join(' ')} ${handAlign} ${props.className}`}>
      {!hideShine && (
        <Fragment>
          <div className="shine" style={animationRepeat} />
          <div className="shine-in" style={animationRepeat} />
        </Fragment>
      )}
      <img
        src={src}
        className="hand-main"
        style={animationRepeat}
        alt=""
        onClick={(event) => handleClick(event)}
        onAnimationEnd={() => animationEndEvent()}
      />
    </div>
  );
};

export default BaseGuide;
