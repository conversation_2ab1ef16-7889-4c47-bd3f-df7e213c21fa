$shine-color: #ffffff;

.base-guide-hand {
  position: absolute;
  width: 182rpx;
  height: 182rpx;
  z-index: 1;

  &.is-edge {
    margin-left: -91rpx;
    margin-top: -91rpx;
  }

  .shine {
    position: absolute;
    top: 68rpx;
    left: 68rpx;
    border-radius: 100%;
    width: 40rpx;
    height: 40rpx;
    border: 1.5rpx solid rgba($shine-color, 0.3);
    background: rgba($shine-color, 0.2);
    box-shadow: inset 0 0 8rpx rgba($shine-color, 0.5);
  }

  .shine-in {
    position: absolute;
    top: 76rpx;
    left: 76rpx;
    border-radius: 100%;
    width: 25rpx;
    height: 25rpx;
    border: 1rpx solid rgba($shine-color, 0.7);
    box-shadow:
      inset 0 0 4rpx 2rpx rgba($shine-color, 0.6),
      inset 0 0 15rpx rgba($shine-color, 0.5);
  }

  .hand-main {
    position: absolute;
    left: 88rpx;
    top: 72rpx;
    width: 120rpx;
    height: 120rpx;
    transform-origin: 0 0;
  }

}

.base-guide-hand.is-play {
  .hand-main {
    animation: click 500ms ease-in infinite alternate;
  }

  .shine {
    animation: shine 500ms cubic-bezier(0.8, 0.5, 0.2, 1.1) infinite alternate;
  }

  .shine-in {
    animation: shine-in 500ms ease-in-out 10ms infinite alternate;
  }
}

.base-guide-hand.reverse {
  transform: scaleX(-1);
}

@keyframes click {
  100% {
    transform: translate(-10rpx, -4rpx) rotate(-6deg) scale(0.99);
  }
}

@keyframes shine {
  80% {
    transform: scale(1.3);
    opacity: 0.7;
  }

  100% {
    transform: scale(1.4);
    opacity: 0.6;
  }
}

@keyframes shine-in {
  100% {
    transform: scale(0.87);
  }
}
