import React, { useState, useEffect } from 'react';
import { MainAPI } from '@/logic/type/event';
import mx from '@ali/pcom-mx';
import stat from '@/lib/stat';
import './index.scss';
import closeImg from '@/components/common/LoginProtocol/images/close.png';
import { openPage, openTaobaoLoginWindow } from '@/lib/ucapi';

// 登录协议
const Index = () => {
  const [showPanle, setShowPanel] = useState(false);
  const app = mx.store.get('app');
  useEffect(() => {
    mx.event.on(MainAPI.OpenLoginProtocolPanel, openPanel);
    mx.event.on(MainAPI.HideLoginProtocolPanel, closePanel);
  }, []);
  const openPanel = () => {
    stat.exposure('privacy_pop_show', {
      c: 'privacy',
      d: 'pop',
      source: 'log_pop',
    })
    setShowPanel(true);
  };
  const closePanel = (position) => {
    stat.click('privacy_pop_click', {
      c: 'privacy',
      d: 'pop',
      source: 'log_pop',
      click_position: position || '',
    })
    setShowPanel(false);
  };
  const userAgreement = () => {
    const { userAgreement } = app?.frontData || {};
    if (!userAgreement) return;
    openPage(userAgreement);
  };
  const privacyPolicy = () => {
    const { privacyPolicy } = app?.frontData || {};
    if (!privacyPolicy) return;
    openPage(privacyPolicy);
  };
  const submitLogin = () => {
    stat.click('privacy_pop_click', {
      c: 'privacy',
      d: 'pop',
      source: 'log_pop',
      click_position: 'agree',
    })
    openTaobaoLoginWindow({loginType: 'taobao', agreeLicense: 1})
    setShowPanel(false);
  };
  return (
    <div className="login-panel-comp" style={{ display: showPanle ? 'block' : 'none' }}>
      <div onClick={() => closePanel('close')} className="panel-mask" />
      <div className={`operation-content ${showPanle ? 'panel-appear' : ''}`}>
        <div className="close">
          <img onClick={() => closePanel('close')} src={closeImg} alt="" />
        </div>
        <div className="title">请先同意用户协议与隐私条款</div>
        <div className="protocol">
          <div>为了保障你的合法权益，请你先阅读并同意 </div>
          <div>
            <span onClick={userAgreement}>《用户协议》</span>
            <span onClick={privacyPolicy}>《隐私政策》</span>
          </div>
        </div>
        <div onClick={submitLogin} className="button agree">
          同意并登录
        </div>
        <div onClick={() => closePanel('disagree')} className="button disagree">
          不同意
        </div>
      </div>
    </div>
  );
};

export default Index;
