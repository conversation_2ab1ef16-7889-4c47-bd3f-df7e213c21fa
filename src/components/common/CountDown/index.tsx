import React, { useEffect, useState, useRef } from 'react';

interface IProps{
  diff: number;
  onComplete?: () => void;
  formatFunc: (time: number) => React.ReactNode;
}

export default function Index(props: IProps) {
  const { diff, onComplete, formatFunc } = props;
  const [leftTime, setLeftTime] = useState(diff);
  const timer = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (timer.current) {
      clearInterval(timer.current)
    }
    timer.current = setInterval(() => {
      setLeftTime((prevSeconds) => prevSeconds - 1);
    }, 1000);

    // 组件卸载时清除定时器
    return () => {
      if (timer.current) {
        clearInterval(timer.current)
      }
    };
  }, [diff]);

  useEffect(() => {
    if (leftTime === 0) {
      if (timer.current) {
        clearInterval(timer.current)
      }
      if (onComplete) {
        onComplete()
      }
    }
  }, [leftTime]);

  return (
    <span>
      {formatFunc(leftTime)}
    </span>
  );
}
