import React, { useState, useRef, useEffect } from 'react';
import './index.scss';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';

const MessageScroll = ({ messages, interval = 3000, renderItem }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(true);
  const containerRef = useRef<number>(0);
  const intervalRef = useRef<number>(0);
  const startScrolling = () => {
    intervalRef.current = setInterval(() => {
      setIsTransitioning(true);
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % (messages.length + 1);
        if (nextIndex === messages.length) {
          setTimeout(() => {
            setIsTransitioning(false);
            setCurrentIndex(0);
          }, 500);
        }
        return nextIndex;
      });
    }, interval);
  };
  const stopScrolling = () => {
    clearInterval(intervalRef.current);
  };
  usePageVisibilityListener((visible: boolean) => {
    if (visible) {
      if (messages?.length > 1) {
        startScrolling();
      }
    } else {
      stopScrolling();
    }
  });

  return (
    <div className="message-scroll-container">
      <div
        ref={containerRef}
        className="message-scroll-content"
        style={{
          transform: messages?.length > 1 ? `translateY(-${currentIndex * (100 / (messages?.length + 1))}%)` : 'none',
          transition: isTransitioning ? 'transform 0.5s linear' : 'none',
        }}
      >
        {messages.map((message, index) => renderItem(message, index))}
        {messages?.length > 1 && renderItem(messages?.[0])}
      </div>
    </div>
  );
};

export default MessageScroll;
