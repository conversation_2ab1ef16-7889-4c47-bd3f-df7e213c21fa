import React, { useEffect, useRef, useState } from 'react';
import './index.scss';

import EmptyImg from './images/list-empty-img.png';
import { useLockFn } from 'ahooks';

interface IProps {
  /** 渲染列表的item */
  renderItem: (any, index: number) => React.ReactNode;
  /** 列表数据 */
  list: any[];
  /** 容器类名 */
  wrapClassName: string;
  /** 加载更多函数 */
  loadMore?: (isRetry?: boolean) => Promise<void> | void;
  /** 初始展示条数 */
  initNum?: number;
  /** 是否正在加载中 */
  loading?: boolean;
  /** 是否为空数据 */
  isEmpty?: boolean;
  /** 空数据图片 */
  emptyImg?: string;
  /** 空数据文案 */
  emptyText?: string;
  /** 是否还有更多 */
  hasMore: boolean;
  noMoreText?: string;
  /** 渲染列表的item */
  renderEmpty?: () => React.ReactNode;
  /** 触摸滑动事件 */
  onTouchScroll?: () => void;
}

export default function Index(props: IProps) {
  const {
    wrapClassName,
    renderItem,
    list = [],
    loadMore,
    initNum = 0,
    loading = false,
    isEmpty = false,
    emptyImg,
    emptyText = '还未有记录哦，明日再来看吧',
    noMoreText = '- 没有更多啦 -',
    hasMore = true,
    renderEmpty,
    onTouchScroll
  } = props;
  const [failed, setFailed] = useState(false);
  const doLoadMore = useLockFn(async (isRetry?: boolean) => {
    try {
      loadMore && (await loadMore(isRetry));
    } catch (e) {
      setFailed(true);
      throw e;
    }
  });
  const triggeredRef = useRef(false);
  const listFooterRef = useRef<HTMLDivElement>(null);
  const listContainerRef = useRef<HTMLDivElement>(null);
  const isScrolled = useRef<boolean>(false);
  const [touchY, setTouchY] = useState(0);
  const [showAll, setShowAll] = useState(true);

  const showList = showAll ? list : list?.slice(0, initNum);
  const isScrolling = useRef(false)
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (list.length && initNum && !isScrolled.current && list.length > initNum) {
      setShowAll(false);
    }
  }, [list, initNum]);

  useEffect(() => {
    // eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(async (entry) => {
          if (entry.isIntersecting && doLoadMore && !loading && hasMore) {
            await doLoadMore();
          }
        });
      },
      {
        root: listContainerRef?.current,
        rootMargin: '30%',
        threshold: 0,
      },
    );
    if (listFooterRef?.current) {
      observer.observe(listFooterRef?.current);
    }
  }, [showAll, list, loading, hasMore]);

  useEffect(() => {
    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, []);

  const handleScroll = () => {
    isScrolling.current = true; // 标记为正在滚动

    // 每次滚动时清除之前的定时器
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    // 设置新的定时器检测滚动停止
    scrollTimeout.current = setTimeout(() => {
      isScrolling.current = false;
    }, 150); // 150ms 内无新滚动视为停止
  };

  async function retry() {
    setFailed(false);
    try {
      await doLoadMore(true);
    } catch (e) { /* empty */ }
  }

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isScrolled.current) {
      const touch = e.touches[0];
      setTouchY(touch.clientY);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    const delta = touchY - touch.clientY;
    if (!isScrolling.current && !triggeredRef.current) {
      if (Math.abs(delta) > 10) {
        onTouchScroll && onTouchScroll()
        triggeredRef.current = true; // 标记已触发
      }
    }
    if (!showAll && !isScrolled.current) {
      if (delta > 10) {
        setShowAll(true);
        isScrolled.current = true;
      }
    }
  };

  const renderRetry = () => {
    if (hasMore && failed && !loading) {
      return (
        <div className="retry-wrap" onClick={retry}>
          加载失败，点击重试
        </div>
      );
    }
    return null;
  };
  const renderListFooter = () => {
    if (loading && hasMore) {
      return (
        <div ref={listFooterRef} className="list-footer">
          加载中...
        </div>
      );
    }
    return (
      <div ref={listFooterRef} className="list-footer">
        {noMoreText}
      </div>
    );
  };

  const handleTouchEnd = () => {
    triggeredRef.current = false; // 重置触发标记
  };

  return (
    <div className={`scroll-list-comp ${wrapClassName}`}>
      {isEmpty ? (
        <div className="empty-wrap">
          {renderEmpty ? renderEmpty() : (
            <div className="empty-content">
              <img src={emptyImg || EmptyImg} className="empty-img" alt="" />
              <div className="empty-text">{emptyText}</div>
            </div>
          )}
        </div>
      ) : (
        <div className="list-content" onScroll={handleScroll} onTouchEnd={handleTouchEnd} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove}>
          {showList?.map((item, index) => renderItem(item, index))}
          {!!list?.length && showAll && !failed && renderListFooter()}
          {list?.length > initNum && !showAll && !failed && <div className="list-footer">- 滑动展示更多 -</div>}
          {renderRetry()}
        </div>
      )}
    </div>
  );
}
