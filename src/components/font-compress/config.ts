import { dirname, resolve } from 'path';
import { fileURLToPath } from 'url';

// eslint-disable-next-line
const __filename = fileURLToPath(import.meta.url);
// eslint-disable-next-line
const __dirname = dirname(__filename);

export interface GenConfig {
  // 生成的字体name。css文件名、font-family啥的都是用这个
  name: string;
  // 精简字库，默认使用pet同目录的
  text: string;
  // 源字库
  source: string;
  // 生成的字体目录
  fontDir: string;
  // 生成的css目录
  cssDir: string;
  // 生成的类型，支持ttf，默认ttf。ttf\woff区别不大，woff2兼容性没那么好，慎重选择
  target?: 'ttf' | 'woff' | 'woff2';
}

export const configs: Record<string, GenConfig> = {
  FZLanTingYuanS: {
    // 生成的字体name。css文件名、font-family啥的都是用这个
    name: 'FZLanTingYuanS-Bold',
    // 精简字库，默认使用pet同目录的
    text: resolve(__dirname, './FZLanTingYuanS/text'),
    // 源字库
    source: resolve(__dirname, './FZLanTingYuanS/FZLanTingYuanS.ttf'),
    // 生成的字体目录
    fontDir: resolve(__dirname, './FZLanTingYuanS/'),
    // 生成的css目录
    cssDir: resolve(__dirname, './FZLanTingYuanS/'),
  },
  Gilroy: {
    // 生成的字体name。css文件名、font-family啥的都是用这个
    name: 'Gilroy-Bold',
    // 精简字库，默认使用pet同目录的
    text: resolve(__dirname, './Gilroy/text'),
    // 源字库
    source: resolve(__dirname, './Gilroy/Gilroy-Bold.ttf'),
    // 生成的字体目录
    fontDir: resolve(__dirname, './Gilroy/'),
    // 生成的css目录
    cssDir: resolve(__dirname, './Gilroy/'),
  },
  FZLanTingYuanSEB: {
    // 生成的字体name。css文件名、font-family啥的都是用这个
    name: 'FZLanTingYuanS-Extra-Bold',
    // 精简字库，默认使用pet同目录的
    text: resolve(__dirname, './FZLanTingYuanS-EB/text'),
    // 源字库
    source: resolve(__dirname, './FZLanTingYuanS-EB/FZLanTingYuanS-Extra-Bold.ttf'),
    // 生成的字体目录
    fontDir: resolve(__dirname, './FZLanTingYuanS-EB/'),
    // 生成的css目录
    cssDir: resolve(__dirname, './FZLanTingYuanS-EB/'),
  },
  GilroyExtraBoldItalic: {
    // 生成的字体name。css文件名、font-family啥的都是用这个
    name: 'Gilroy-ExtraBoldItalic',
    // 精简字库，默认使用pet同目录的
    text: resolve(__dirname, './Gilroy-ExtraBoldItalic/text'),
    // 源字库
    source: resolve(__dirname, './Gilroy-ExtraBoldItalic/Gilroy-ExtraBoldItalic.otf'),
    // 生成的字体目录
    fontDir: resolve(__dirname, './Gilroy-ExtraBoldItalic/'),
    // 生成的css目录
    cssDir: resolve(__dirname, './Gilroy-ExtraBoldItalic/'),
  },
};
