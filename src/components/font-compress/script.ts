import Fontmin from 'fontmin';
import { readFileSync, statSync, writeFileSync } from 'fs';
import { dirname, extname, relative } from 'path';

import { getCssFilePath, getFontFilePath } from './utils/path';
import { configs, type GenConfig } from './config';

const DEFAULT_TARGET = 'ttf';

function getConfig(key: string) {
  return configs[key];
}

function getFonts(config: GenConfig) {
  return readFileSync(config.text, 'utf8').replace(/\n/g, '');
}
async function genFont(config: GenConfig) {
  return new Promise<void>((res, rej) => {
    const text = getFonts(config);

    const sourceType = extname(config.source);
    const targetType = config.target ?? DEFAULT_TARGET;

    const fontmin = new Fontmin().src(config.source);

    switch (sourceType) {
      case '.ttf':
        break;
      case '.otf':
        fontmin.use(Fontmin.otf2ttf());
        break;
      default:
        rej(`unsupported source font type ${sourceType}`);
        return;
    }

    fontmin.use(
      Fontmin.glyph({
        text,
        hinting: false, // keep ttf hint info (fpgm, prep, cvt). default = true
      }),
    );

    switch (targetType) {
      case DEFAULT_TARGET:
        break;
      case 'woff':
        fontmin.use(Fontmin.ttf2woff());
        break;
      case 'woff2':
        fontmin.use(Fontmin.ttf2woff2());
        break;
      default:
        // eslint-disable-next-line
                rej(`unsupported target font type ${targetType}`);
        return;
    }

    fontmin.run((err, files) => {
      if (err != null) {
        rej(err);
        return;
      }
      /* eslint-disable */
            // @ts-expect-error
            writeFileSync(`${getFontFilePath(config, DEFAULT_TARGET)}`, files[0]!._contents as unknown as Buffer);
            /* eslint-enable */
      res();
    });
  });
}

async function genCss(config: GenConfig) {
  //
  writeFileSync(
    `${config.cssDir}/${config.name}.css`,
    `@font-face {
    font-family: ${config.name};
    src: url('${relative(dirname(getCssFilePath(config)), getFontFilePath(config, DEFAULT_TARGET))}');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
`,
  );
}

async function genFile(key: string) {
  console.log(`--------------------------------`);
  console.log(`run font compress@${key}...`);
  const config = getConfig(key);
  if (!config) {
    console.error(`config[${key}] not found`);
    process.exit(1);
    return;
  }
  const t1 = Date.now();

  console.log('start fontmin...');
  await genFont(config);
  await genCss(config);

  // 啊啊啊啊我要chalk
  console.log(`--------------------------------`);
  console.log(`font compress done@config: ${key}, cost: ${Date.now() - t1}ms`);
  console.log(
    `source: ${statSync(config.source).size / 1024}KB -> `,
    `target: ${statSync(getFontFilePath(config, DEFAULT_TARGET)).size / 1024}KB |`,
    `${getFonts(config).length} fonts`,
  );
  console.log(`dist:`);
  console.log('-', getFontFilePath(config, DEFAULT_TARGET));
  console.log('-', getCssFilePath(config));
}

// 串行吧，看起来顺眼一点，error了也好看一点
async function run(genKeys: string[]) {
  while (genKeys.length > 0) {
    await genFile(genKeys.shift()!);
  }
}

// 如果传参数就只build参数对应的key，否则全部build一遍
run(process.argv.length > 2 ? process.argv.slice(2) : Object.keys(configs));
