/**
 * 字体展示的serve
 */

import { readFileSync } from 'fs';
import { createServer, type IncomingMessage, type ServerResponse } from 'http';
import { dirname, relative, resolve } from 'path';
import { fileURLToPath } from 'url';

import { getCssFilePath, getFontFilePath } from './utils/path';
import { configs } from './config';

// eslint-disable-next-line
const __filename = fileURLToPath(import.meta.url);
// eslint-disable-next-line
const __dirname = dirname(__filename);

function buildTemplate(): string {
  const renderConfig = Object.keys(configs).map((genKey) => {
    return {
      ...configs[genKey],
      textList: readFileSync(configs[genKey]!.text, 'utf8'),
    };
  });

  const styles = Object.keys(configs)
    .map((genKey) => getCssFilePath(configs[genKey]!))
    .map((cssPath) => readFileSync(cssPath, 'utf8'))
    .join('\n');

  const htmlTemplate = readFileSync(resolve(__dirname, './utils/template.html'), 'utf8')
    .replace('<%=config%>', () => JSON.stringify(renderConfig))
    .replace('<%=style%>', () => styles);
  return htmlTemplate;
}

const template = buildTemplate();

// eslint-disable-next-line @iceworks/best-practices/recommend-polyfill
const fontNameMap = Object.fromEntries(
  Object.keys(configs).map((key) => {
    const filePath = getFontFilePath(configs[key]!);
    return [`/${relative(dirname(getCssFilePath(configs[key]!)), filePath)}`, readFileSync(filePath)];
  }),
);

const server = createServer((req: IncomingMessage, res: ServerResponse) => {
  const url = req.url === '/' ? '/index.html' : (req.url as string);

  if (url === '/index.html') {
    // root html
    res.setHeader('Content-Type', 'text/html');
    res.statusCode = 200;
    res.end(template);
    return;
  }
  if (fontNameMap[url]) {
    // font
    res.setHeader('Content-Type', 'application/octet-stream');
    res.statusCode = 200;
    res.end(fontNameMap[url]);
    return;
  }

  res.statusCode = 404;
  res.end('File not found');
});

// 启动服务器
const port = process.env.PORT ?? 8989;
server.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});
