<!doctype html>
<html>
    <head>
        <meta charset="UTF-8" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="referrer" content="no-referrer" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover"
        />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <title>字体预览</title>
        <style>
            body {
                margin: 0;
            }
            ul{
                position: fixed;
                top: 0;
                left: 0;
                display: flex;
                margin: 0;
                padding: 0;
                width: 100vw;
                border-bottom: 1px solid #dedede;
                overflow-x: auto;
                background: #fff;
            }
            li{
                margin: 10px 0;
                flex: 1;
                text-align: center;
                list-style: none;
            }
            .text{
                margin-top: 50px;
                display: none;
            }
            .size-handler{
                position: fixed;
                bottom: 0;
                box-sizing: border-box;
                width: 100vw;
                margin: 0;
                padding: 4px 20px 4px 12px;
                background: #fff;
            }
            .font-size{
                position: fixed;
                bottom: 0;
                right:0;
                font-size: 20px;
            }

            <%=style%>
        </style>
    </head>
    <body>
        <ul id="$tabs"></ul>
        <div id="$content"></div>
        <input class="size-handler" type="range" id="$fontSize" min="4" max="100" />
        <div id="$fontSizeValue" class="font-size"></div>
        <script>
            const config = <%=config%>;

            $tabs.innerHTML = config.map(data => `<li data-name="${data.name}">${data.name}</li>`).join('\n');
            $content.innerHTML = config.map(data => `<div class="text" data-list="${data.name}" style="font-family:${data.name};">${data.textList.replace(/\n/g, '<br />')}</div>`).join('\n');

            document.body.addEventListener('click', e => {
                if(e.target.dataset.name){
                    document.querySelectorAll('[data-list]').forEach(dom => {
                        dom.style.display = ''
                    });
                    document.querySelector(`[data-list="${e.target.dataset.name}"]`).style.display = 'block'
                }
            });

            $fontSize.addEventListener('input', e => {
                $content.style['font-size'] = `${$fontSize.value}px`;
                $fontSizeValue.textContent = $fontSize.value;
            });

            if(document.querySelector('[data-list]')){
                document.querySelector('[data-list]').style.display = 'block';
            }
        </script>
    </body>
</html>
