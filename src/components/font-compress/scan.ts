import fs from 'fs';
import path from 'path';

// gpt生成的
function scan(dir: string, exts: string[]): string[] {
  const charSet: Set<string> = new Set();

  function scanDir(directory: string): void {
    const files = fs.readdirSync(directory);

    for (const file of files) {
      const filePath = path.join(directory, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        scanDir(filePath); // 递归扫描子目录
      } else if (stat.isFile() && exts.includes(path.extname(file))) {
        const content = fs.readFileSync(filePath, 'utf-8');
        const charRegex = /[\u4e00-\u9fa5]/g;
        const charMatches = content.match(charRegex);

        if (charMatches) {
          charMatches.forEach((char) => {
            charSet.add(char);
          });
        }
      }
    }
  }

  scanDir(dir);

  return Array.from(charSet);
}

const scanPath = process.argv[2];

if (!scanPath) {
  console.error('请提供扫描路径');
  process.exit(1);
}

const extensions = ['.ts', '.vue', '.css'];

console.log(`开始扫描 ${path.resolve(scanPath)}`);

const charList = scan(scanPath, extensions);

console.log(`共找到 ${charList.length} 字`);
console.log('------------------------------------------------');
console.log(charList.join(''));
