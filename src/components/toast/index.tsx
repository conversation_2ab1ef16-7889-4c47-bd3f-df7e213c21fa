import React, { CSSProperties, useEffect, useState } from 'react';
import ModalToast from '../modals/modal_toast';
import './index.scss';

export const enum ToastElementType {
  SHARE = 'share',
}

// 组件映射
export const EToastCompMap = {
  [ToastElementType.SHARE]: ModalToast,
} as const;

// 根据组件类型获取对应props
export type ToastComponentProps<T extends ToastElementType> =
  T extends keyof typeof EToastCompMap
    ? React.ComponentProps<typeof EToastCompMap[T]>
    : never;

interface BaseToastProps {
  children?: React.ReactNode;
  duration?: number;
  onClose?: () => void;
  style?: CSSProperties;
}

export type ToastProps =
  | (BaseToastProps & { type: ToastElementType; props: ToastComponentProps<ToastElementType> })
  | (BaseToastProps & { type?: undefined; props?: undefined });

const Toast: React.FC<ToastProps> = ({
  children,
  duration = 4000,
  onClose,
  type,
  style,
  props,
}) => {
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLeaving(true);
    }, duration - 300); // 提前300ms开始离场动画

    return () => clearTimeout(timer);
  }, [duration]);

  const handleAnimationEnd = (e: React.AnimationEvent) => {
    if (e?.animationName?.includes('toastLeave')) {
      onClose?.();
    }
  };

  if (type && type in EToastCompMap) {
    const Component = EToastCompMap[type];
    return (
      <div style={style} className={`global-toast ${isLeaving ? 'toast-leave' : 'toast-enter'}`} onAnimationEnd={handleAnimationEnd}>
        <div className="toast-el-content">
          <Component {...(props)} />
        </div>
      </div>
    );
  }

  return (
    <div style={style} className={`global-toast ${isLeaving ? 'toast-leave' : 'toast-enter'}`} onAnimationEnd={handleAnimationEnd}>
      <div className="toast-el-content">{children}</div>
    </div>
  );
};

export default React.memo(Toast);
