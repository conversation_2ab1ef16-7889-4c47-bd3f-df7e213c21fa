import React from 'react';
import ToastManager from '@/components/toast/manager';
import { ToastElementType, ToastComponentProps } from '@/components/toast';

interface IShowParam<T> {
  content: T extends ToastElementType ? undefined | null : React.ReactNode;
  options: T extends ToastElementType ? { type: T; props: ToastComponentProps<T> } : undefined | null;
  duration?: number;
  style?: React.CSSProperties;
}

const toastElement = {
  show: (params: IShowParam<ToastElementType> | IShowParam<undefined>) => {
    const { content, options, duration, style } = params;
    ToastManager.show(content, options as any, duration, style);
  },
};

export default toastElement;
