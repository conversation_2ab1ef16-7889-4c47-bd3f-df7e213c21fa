.toast-container {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
}

.global-toast {
  position: fixed;
  left: 50%;
  bottom: 255rpx;
  transform: translateX(-50%);
  z-index: 1000;
  will-change: transform, opacity;
  pointer-events: none;
  backface-visibility: hidden; // 触发硬件加速
  -webkit-font-smoothing: antialiased;

  &.toast-enter {
    animation: toastEnter 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  &.toast-leave {
    animation: toastLeave 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .toast-el-content {
    pointer-events: auto; // 实际内容接收事件
    box-shadow: 0 0 48rpx 0 rgba(0,0,0,0.10);
  }
  .toast-content {
    top: 0 !important;
    box-shadow: 0 0 48rpx 0 rgba(0,0,0,0.10);
  }
}

@keyframes toastEnter {
  from {
    opacity: 0;
    transform: translate(-50%, -40%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes toastLeave {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -60%) scale(0.8);
  }
}
