import React, { Fragment } from 'react';
import { createRoot } from 'react-dom/client';
import Toast, { ToastElementType, ToastProps, ToastComponentProps } from '@/components/toast';

interface ToastItemBase {
  id: number;
  duration?: number;
  style?: React.CSSProperties;
}

type ToastItem = ToastItemBase & (
  | { content?: React.ReactNode; options?: never }
  | { content?: never | null; options: { type: ToastElementType; props: ToastComponentProps<ToastElementType> } }
);

class ToastManager {
  private container: HTMLDivElement | null = null;
  private queue: ToastItem[] = [];
  private activeToasts: Set<number> = new Set();
  private counter = 0;
  private readonly maxToasts: number = 5;
  private root: ReturnType<typeof createRoot> | null = null;
  private isInitialized = false;

  show(
    content?: React.ReactNode,
    options?: { type: ToastElementType; props: ToastComponentProps<ToastElementType> },
    duration = 4000,
    style?: React.CSSProperties,
  ) {
    if (typeof window === 'undefined') return;

    this.initialize();

    if (!this.container || !this.isInitialized) return;

    const id = ++this.counter;
    const toastItem: ToastItem = options
      ? { id, options, duration, style }
      : { id, content, duration, style };

    this.queue.push(toastItem);

    while (this.activeToasts.size >= this.maxToasts) {
      const oldestToast = this.queue.find((t) => this.activeToasts.has(t.id));
      if (oldestToast) {
        this.removeToast(oldestToast.id);
      }
    }

    this.activeToasts.add(id);
    this.renderToasts();
  }

  private initialize() {
    if (this.isInitialized || typeof window === 'undefined') return;

    try {
      this.container = document.createElement('div');
      this.container.className = 'toast-container';
      document.body.appendChild(this.container);
      this.root = createRoot(this.container);
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize ToastManager:', error);
    }
  }

  private cleanup() {
    if (this.root) {
      this.root.unmount();
      this.root = null;
    }
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
    }
    this.isInitialized = false;
  }

  private renderToasts() {
    if (!this.root) return;

    if (this.activeToasts.size === 0) {
      this.cleanup();
      return;
    }

    this.root.render(
      <Fragment>
        {Array.from(this.queue)
          .filter((toast) => this.activeToasts.has(toast.id))
          .map((toast) => {
            const {duration, options, id, content, style} = toast
            const props: ToastProps = options
              ? {
                duration: duration,
                onClose: () => this.removeToast(id),
                type: options?.type,
                props: options?.props,
                style,
              }
              : {
                duration: duration,
                onClose: () => this.removeToast(id),
                children: content,
                style,
              };

            return <Toast key={toast.id} {...props} />;
          })}
      </Fragment>,
    );
  }

  private removeToast(id: number) {
    this.activeToasts.delete(id);
    this.queue = this.queue.filter((toast) => toast.id !== id);
    this.renderToasts();
  }
}

export default new ToastManager();
