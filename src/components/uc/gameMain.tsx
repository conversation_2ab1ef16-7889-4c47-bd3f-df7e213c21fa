import React, { useEffect} from 'react';
import { createMainGame } from '@/gameObject/MainGame';
import { getGameHeight } from "@/gameObject/utils/screen";

const GAME_ID = 'uc-main-game';

const UCGameMain = (props: { showGameMain: boolean }) => {
  useEffect(() => {
    const gameEl = document.getElementById(GAME_ID) as HTMLCanvasElement;
    const game = createMainGame(gameEl, getGameHeight());
    game.createGame();
  }, []);

  return (
    <div className="canvas-wrapper" style={{visibility: props.showGameMain ? 'visible' : 'hidden'}}>
      <canvas className="game-canvas-fuguo" id={GAME_ID} />
    </div>
  );
};

export default UCGameMain;
