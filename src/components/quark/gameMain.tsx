import React, { useEffect } from 'react';
import { createMainGame } from '@/gameObject/MainGame';
import { getGameHeight } from '@/lib/utils/index';

const GAME_ID = 'quark-main-game';

function UCGameMain() {
  useEffect(() => {
    const gameEl = document.getElementById(GAME_ID) as HTMLCanvasElement;
    const game = createMainGame(gameEl, getGameHeight());
    game.createGame();

  }, []);

  return (
    <div>

      <canvas id={GAME_ID}></canvas>
    </div>
  );
}

export default UCGameMain;
