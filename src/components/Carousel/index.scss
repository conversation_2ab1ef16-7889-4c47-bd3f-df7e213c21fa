.carousel {
  position: relative;
  width: 100%;
  // overflow: hidden;

  &-container {
    width: 100%;
    position: relative;
    overflow: hidden;
    min-height: 300rpx;
    transition: height 0.3s ease-out;
    border-radius: 44rpx;
  }

  &-wrapper {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: flex-start;
  }

  &-item {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-image {
    display: block;
    width: 100% !important;
    height: auto !important;
    max-height: 50vh;
    object-fit: cover;
  }

  &-indicators {
    margin-top: 16rpx;
    display: flex;
    flex-direction: row;
    justify-content: center;
    z-index: 999;
  }

  .indicator {
    width: 10rpx;
    height: 10rpx;
    border-radius: 50%;
    background-color: #D4DADE;
    margin-right: 10rpx;
    &:last-child {
      margin-right: 0;
    }
    cursor: pointer;
    &.active {
      background-color: #859199;
    }
  }

  &-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;

    &-left {
      left: 10rpx;
      .arrow-icon {
       width: 60rpx;
       height: 60rpx;
       background-size: cover;
      }
    }

    &-right {
      right: 10rpx;
      .arrow-icon {
        width: 60rpx;
        height: 60rpx;
        background-size: cover;
      }
    }
  }
} 