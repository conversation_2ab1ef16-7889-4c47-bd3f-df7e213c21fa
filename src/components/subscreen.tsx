import React, { useEffect, useState } from 'react';
import TaskPop from '@/pages/index/components/TaskPop';
import BindTaobaoPop from '@/pages/index/components/BindTaobaoPop';
import ModalAll from './modals/ModalAll';
// import SharePanel from '../pages/index/components/SharePanel';
import LoginProtocol from '@/components/common/LoginProtocol'
import FertilizerNotify from "@/pages/index/components/FertilizerNotify";
import { ShareBase, ShareComponent } from '@ali/act-base-share';
import stat from '@/lib/stat';
import tracker from '@/lib/tracker';
import useMxState from '@/hooks/useMxState';
import { IShareState } from '@/logic/store/models/share';
import '@ali/act-base-share/lib/css/index.css';

export default function SubScreen() {
  // 使用state来控制延迟组件的渲染
  const [shouldRender, setShouldRender] = useState(false);
  const [shouldDelayRender, setShouldDelayRender] = useState(false);
  const [share] = useMxState<IShareState>('share');
  useEffect(() => {
    // 延迟200ms, 
    const timer = setTimeout(() => {
      setShouldRender(true);
    }, 200);
    
    const timerAction = setTimeout(() => {
      setShouldDelayRender(true);
    }, 600);

    return () => {
      clearTimeout(timer);
      clearTimeout(timerAction);
    };
  }, []);

  useEffect(() => {
    ShareBase.init({
      sharePlanId: 'motr0ca5x0xm',
      installFunction: { stat, tracker, trackerCategory: 113 },
    });
  }, []);

  useEffect(() => {
    if (share.shareConfig) {
      ShareBase.updateConfig(share.shareConfig);
    }
  }, [share.shareConfig]);

  return (
    <React.Fragment>
      <ModalAll />
      {/* 按照触发的时机和内容区分，而不是整块区域延迟渲染 */}
      {shouldRender && (
        <React.Fragment>
          <BindTaobaoPop />
          <LoginProtocol />
          <FertilizerNotify />
        </React.Fragment>
      )}
      {/* 延迟渲染, 任务&分享面板可延迟渲染, 分享使用时机主要在任务面板点击分享任务 */}
      {shouldDelayRender && (
        <React.Fragment>
          <TaskPop />
          {/* <SharePanel /> */}
        </React.Fragment>
      )}
      <ShareComponent />
    </React.Fragment>
  );
}
