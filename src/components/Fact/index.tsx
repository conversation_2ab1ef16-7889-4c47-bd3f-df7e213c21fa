import React, {FC, PropsWithChildren} from 'react';

interface FactProps extends PropsWithChildren {
  /** C位 */
  c: string;
  /** D位 */
  d: string;
  /** 点击事件名称arg1 */
  logkey?: string;
  /** 曝光事件名称arg1 */
  expoLogkey?: string;
  /** 点击扩展参数对象 */
  ckExtra?: object;
  /** 曝光扩展参数对象 */
  expoExtra?: object;
  /** 不发送自动点击统计 */
  noUseClick?: boolean;
  /** 不发送自动曝光统计 */
  noUseExpos?: boolean;
  className?: string;
  onClick?: (e) => void;
}

const Fact: FC<FactProps> = (props) => {
  const {
    c, d, logkey, expoLogkey, ckExtra, expoExtra,
    noUseClick, noUseExpos,
    children, ...res
  } = props;
  const factData: any = {};
  if (c) {
    factData['data-c'] = c;
    factData['data-d'] = d || c;
  }
  if (logkey) factData['data-click-arg1'] = logkey;
  if (expoLogkey) factData['data-exposure-arg1'] = expoLogkey;
  if (ckExtra) factData['data-click-extra'] = JSON.stringify(ckExtra);
  if (expoExtra) factData['data-exposure-extra'] = JSON.stringify(expoExtra);
  if (noUseClick) factData['data-fact-notuseclick'] = noUseClick;
  if ((expoLogkey && !logkey) || noUseClick) {
    factData['data-fact-notuseclick'] = true;
  }
  if (noUseExpos === undefined && !expoLogkey) {
    factData['data-fact-notuseexposure'] = true;
  } else if (noUseExpos) factData['data-fact-notuseexposure'] = noUseExpos;

  return (
    <div {...factData} {...res}>{ children }</div>
  );
}

export default Fact;
