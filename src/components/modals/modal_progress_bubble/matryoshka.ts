import config from "@/config";
import mx from '@ali/pcom-mx';
import { getTaskAward, checkTaskFinished } from '@/pages/index/components/TaskPop/TaskList/util';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import dispatch from '@/logic/store';
import { ifShowTask } from '@/logic/store/models/task/helper';

// 获取套娃任务
export const getMatryoshkaTask = () => {
  const task = mx.store.get('task');
  const resource = mx.store.get('resource');
  let taskList = resource?.multiResource?.[config.getMatryoshkaTaskCode]?.taskList || [];
  taskList = taskList?.filter(item => {
    return !checkTaskFinished(item);
  });
  // 过滤完成的任务,广告任务没有配置扩展字段、 加载失败的广告任务,
  const recommendTaskList = task?.recommendTaskList?.filter((item) => {
    return !checkTaskFinished(item) && ifShowTask(item);
  });
  // 只下发任务列表里面有的
  taskList = taskList?.filter(itemA => {
    return recommendTaskList?.some(itemB => itemA?.id === itemB?.id);
  });
  const taskInfo = taskList?.[0];
  if (taskInfo) {
    const diamondRecommendTaskPlan = resource?.multiResource?.[config.getMatryoshkaTaskCode]?.attributes?.diamondRecommendTaskPlan || [];
    const ruleInfo = diamondRecommendTaskPlan?.find(item => item?.taskId === `${taskInfo?.id}`);
    if (!ruleInfo?.abbreviation) {
      return false
    }
    let name = `${ruleInfo?.abbreviation}再得${getTaskRewardItem(taskInfo)?.name}`;
    dispatch.highValueTask.resourceExposure(taskInfo, 'EXPOSURE', config?.getMatryoshkaTaskCode);
    // dispatch.resource.queryResource({});
    return {name, taskInfo}
  }
  return false
}

export const convertUnitWan = (amount: number) => {
  return amount >= 10000 ? `${(amount / 10000).toFixed(2)}万` : `${amount}`
};

export const convertUnitYuan = (amount: number) => {
  const yuan = amount / 100;
  return yuan % 1 === 0 ? `${yuan}` : `${yuan.toFixed(2)}`;
};

export const getTaskRewardItem = (task: TaskInfo) => {
  let detail = Object.assign({}, task?.rewardItems?.[0] || { amount: Number(getTaskAward(task))});
  switch (detail.mark) {
    case 'cash':
      detail.name = `${convertUnitYuan(Number(detail.amount))}元`;
      break;
    case 'point':
      detail.name = `${convertUnitWan(Number(detail.amount))}肥料`;
      break;
    default:
      detail.name = `${convertUnitWan(Number(detail.amount))}肥料`;
      break;
  }
  return detail
};
