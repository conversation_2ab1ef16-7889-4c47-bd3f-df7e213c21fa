import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import CloseIcon from '../assets/modal-close.png';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import MysteriousGiftImg from './images/mysterious-gift.png';
import ProgressBubbleStarImg from '../assets/progress-bubble-star.png';
import RedPacketImg from './images/red-packet.png';
import ManureImg from '../assets/manure.png';
import ArrowImg from './images/arrow.png';
import { getMatryoshkaTask } from './matryoshka';
import { dongfengTaskReport, taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import stat from '@/lib/stat';
import {getTaskAward, isAdVideoTask, logToFinishTask} from '@/pages/index/components/TaskPop/TaskList/util';
import mx from '@ali/pcom-mx';
import { TASK_LOCATION, TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import dispatch from '@/logic/store';

interface IProps {
  isReward: boolean;
  needTimes?: number;
  pointAmount: number;
  rewardMark?: string;
  modalType?: 'taskBubble' | 'desktopTask';
  curTask?: TaskInfo;
  subheadingText?: string;
}

const popSourceMap = new Map(
  [
    ['resourceNiche', 'balloon'],
    ['taskBubble', 'bubble_task'],
    ['progress', 'progress_bubble'],
    ['list', 'task_list'],
    ['desktopTask', 'widget']
  ]
)

const Index = (props: IProps) => {
  const task = mx.store.get('task');
  const { isReward, needTimes, pointAmount, rewardMark, modalType, curTask = {} as unknown as TaskInfo, subheadingText = '' } = props;
  const curTaskDetail = curTask || {};
  const [flag, setFlag] = useState<boolean>(false);
  const taskRef: any = useRef();
  const cashReward = rewardMark === 'cash';
  console.log('curTaskDetail===>', curTaskDetail, curTask, task, modalType);
  useEffect(() => {
    if (isReward) {
      let result = getMatryoshkaTask();
      taskRef.current = result;
      stat.exposure('resource_exposure', {
        c: 'pop',
        d: 'matrioska',
        resource_location: 'matrioska_pop',
        task_filling: result ? 1 : 0,
      })
      if (result) {
        dongfengTaskReport(taskRef?.current.taskInfo, 'expose');
        stat.exposure('task_exposure', {
          c: 'pop',
          d: 'matrioska',
          pop_source: popSourceMap.get(modalType || 'progress') || 'default',
          task_id: taskRef?.current?.taskInfo?.id,
          task_name: taskRef?.current?.taskInfo?.name,
          taskclassify: taskRef?.current?.taskInfo?.taskClassify,
          groupcode: taskRef?.current?.taskInfo?.groupCode,
          award_amount: getTaskAward(taskRef?.current?.taskInfo),
          task_progress: taskRef?.current?.taskInfo?.dayTimes?.progress || '',
          resource_location: 'matrioska_pop'
        });
      }
    }
    setFlag(true);
  }, []);
  // 关闭
  const handleClose = (addFact = true) => {
    if (addFact && isReward) {
      let result = taskRef.current;
      stat.click('resource_click', {
        c: 'pop',
        d: 'matrioska',
        resource_location: 'matrioska_pop',
        task_filling: result ? 1 : 0,
        click_area: 'close'
      })
    }
    baseModal.close(MODAL_ID.PROGRESS_BUBBLE);
  };
  // 获取二级标题
  const getSubheadingText = (value) => {
    switch (value) {
      case 'taskBubble':
        return <div className="subheading">{`做任务领肥料，加速种树`}</div>
      case 'list':
        return <div className="subheading">{`做任务领肥料，加速种树`}</div>
      case 'desktopTask':
        return <div className="subheading">{subheadingText}</div>
      default:
        return <div className="subheading">{isReward ? `施肥次数越多，礼包越大哟~` : `礼包内含红包、优惠券或超多肥料`}</div>
    }
  }
  // 按钮文案
  const getButtonText = () => {
    if (!isReward) {
      return '去施肥';
    }
    // 判断是否推荐任务
    let result = taskRef.current;
    if (result) {
      return <span>{result?.name}</span>;
    }
    return '继续施肥';
  };
  // 提交按钮
  const handleSubmit = () => {
    if (isReward) {
      // 判断是否推荐任务
      let result = taskRef.current;
      stat.click('resource_click', {
        c: 'pop',
        d: 'matrioska',
        resource_location: 'matrioska_pop',
        task_filling: result ? 1 : 0,
        click_area: result ? 'function' : 'fertilize',
      })
      // 气泡任务弹窗
      if (result) {
        // 东风检测曝光
        dongfengTaskReport(taskRef?.current.taskInfo, 'click');
        stat.click('task_click', {
          c: 'pop',
          d: 'matrioska',
          pop_source: popSourceMap.get(modalType || 'progress'),
          task_id: result?.taskInfo?.id,
          task_name: result?.taskInfo?.name,
          taskclassify: result?.taskInfo?.taskClassify,
          groupcode: result?.taskInfo?.groupCode,
          award_amount: getTaskAward(result?.taskInfo),
          task_progress: result?.taskInfo?.dayTimes?.progress || '',
          resource_location: 'matrioska_pop'
        });
        logToFinishTask(result?.taskInfo, TASK_LOCATION.MATRIOSKA_POP);
        const requestId = geneTaskRequestId();
        taskActionHandler(result?.taskInfo, requestId);

        setTimeout(() => {
          // 如果是激励视频相关任务，做任务后更新批量资源位
          if (isAdVideoTask(result?.taskInfo)) {
            dispatch.resource.queryResource({});
          }
        }, 500);
      }
      handleClose(false);
    } else {
      handleClose(false);
    }
  };
  const rewardIcon = cashReward ? RedPacketImg : ManureImg;
  const rewardMsg = cashReward ? `${pointAmount / 100}元` : `${pointAmount}肥料`;
  const rewardTitle = cashReward ? '恭喜你领到红包现金' : '恭喜你领到大量肥料';
  return (
    <div className={`progress-bubble ${isReward ? 'progress-bubble-bg' : ''}`}>
      <div className={`title ${isReward ? 'title-color-yellow' : ''}`}>
        {isReward ? (
          rewardTitle
        ) : (
          <span>
            再施<span className="title-number-color">{needTimes}次</span>可拆礼包
          </span>
        )}
      </div>
      {getSubheadingText(modalType)}
      {isReward && <img className="star" src={ProgressBubbleStarImg} alt="" />}
      <div className="images">
        <img src={isReward ? rewardIcon : MysteriousGiftImg} alt="" />
        <div className="manure">{isReward ? rewardMsg : `神秘奖励`}</div>
      </div>
      <div onClick={handleSubmit} className="buttom">
        {flag && getButtonText()}
        {isReward && taskRef?.current && <img className="arrow" src={ArrowImg} alt="" />}
      </div>
      <div className="close-btn" onClick={() => handleClose()}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
};

export default Index;
