import baseModal from '@/lib/modal';
import stat from "@/lib/stat";
import { MODAL_ID } from "@/components/modals/types";
import { preloadDoubleCardImgs } from "@/components/modals/modal_double_card";

export default {
  // 翻倍卡浮层
  openDoubleCardModal: (props: {
    from?: string;
    autoLottery?: boolean; // 自动抽奖
    isMustDouble?: boolean;
    delayLottery?: boolean; // 延迟3秒自动抽奖
  }) => {
    // 加载弹窗内图片
    preloadDoubleCardImgs();
    stat.custom('fdcard_open_modal', {
      from: props.from,
      fdcard_status: props.isMustDouble ? '0' : '1',
    })
    baseModal.open(MODAL_ID.DOUBLE_CARD_LOTTERY, {
      delayLottery: true,
      ...props,
    });
  }
}
