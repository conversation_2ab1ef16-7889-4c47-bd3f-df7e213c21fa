import baseModal from '@/lib/modal';
import { MODAL_ID } from "@/components/modals/types";
import { preloadDoubleCardImgs } from "@/components/modals/modal_double_card";

export default {
  // 翻倍卡浮层
  openDoubleCardModal: (props: {
    from?: string;
    autoLottery?: boolean; // 自动抽奖
    isMustDouble?: boolean;
    delayLottery?: boolean; // 延迟3秒自动抽奖
  }) => {
    // 加载弹窗内图片
    preloadDoubleCardImgs();
    baseModal.open(MODAL_ID.DOUBLE_CARD_LOTTERY, {
      delayLottery: true,
      ...props,
    });
  }
}
