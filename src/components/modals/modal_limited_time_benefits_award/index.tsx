import React, { Fragment, useEffect, useRef } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import CloseIcon from '../assets/modal-close.png';
import SuccessAuraImg from './images/success-aura.png';
import FailAuraImg from './images/fail-aura.png';
import SuccessTitle from './images/success-title.png';
import LightImg from './images/light-award-circle.png';
import Lottie from 'lottie-web';
import Fact from '@/components/Fact';
import config from '@/config';
import { openPage } from '@/lib/ucapi';
import dispatch from '@/logic/store';

interface IProps {
  awardFlag: boolean;
  giftUrl: string;
  giftName: string;
  titleText: string;
  mark: string;
  lpDisable?: boolean; // 低端机
}

// 限时任务奖励弹窗
const Index = (props: IProps) => {
  const { awardFlag, giftUrl, giftName, titleText, mark, lpDisable = false } = props;
  const containerRef = useRef(null);
  // 关闭
  const handleClose = () => {
    baseModal.close(MODAL_ID.LIMITED_TIME_BENEFITS_AWARD);
    dispatch.timeLimitTask.closeLimitDialog();
  };
  // 去施肥
  const toFertilization = () => {
    // 奖品为红包时跳转到提现页
    if (awardFlag && mark === 'cash') {
      openPage(config.walletUrl);
    }
    handleClose();
  };

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }
    const anim = Lottie.loadAnimation({
      container: containerRef.current, // 指定动画渲染的DOM元素,
      renderer: 'canvas',
      loop: false,
      autoplay: false,
      path: 'https://image.uc.cn/s/uae/g/1y/animate/202409/d330f2/data.json',
      rendererSettings: {
        preserveAspectRatio: 'xMidYMid meet', // SVG 的视图保持宽高比
        progressiveLoad: true,
      },
    });
    anim.addEventListener('DOMLoaded', () => {
      anim.play();
    });
  }, []);

  const renderHeader = () => {
    if (!awardFlag) {
      return <img className="benefits-aura-bg" src={FailAuraImg} alt="" />;
    }
    if (lpDisable) {
      return <img className="benefits-aura-bg" src={SuccessAuraImg} alt="" />;
    }

    return (
      <div className="benefits-aura-animation">
        <img className="header" src={LightImg} alt="" />
        <div
          id="benefits-star-lottie"
          ref={containerRef}
          style={{
            width: '100vw',
          }}
        />
        <img className="title-img" src={SuccessTitle} alt="" />
      </div>
    );
  };
  // 获取按钮文案
  const getText = () => {
    if (awardFlag) {
      return mark === 'cash' ? '立即提现' : '立即施肥';
    }
    return '好的';
  };
  return (
    <Fact c="panel" d="task" expoLogkey={`${awardFlag ? 'award_task_success' : 'award_task_fail'}`}>
      <div className={`modal-limited-time-benefits-award ${awardFlag ? '' : 'benefits-award-fail-bg'}`}>
        {renderHeader()}
        <div className="title">{titleText}</div>
        <div className="gift-images">
          <img src={giftUrl} alt="" />
          <div className={`manure ${awardFlag ? '' : 'manure-fail'}`}>{giftName}</div>
        </div>
        <div onClick={toFertilization} className="btn">
          {getText()}
        </div>
        <div className="close-btn" onClick={handleClose}>
          <img src={CloseIcon} className="close-icon" alt="" />
        </div>
      </div>
    </Fact>
  );
};

export default Index;
