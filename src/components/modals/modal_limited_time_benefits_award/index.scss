.modal-limited-time-benefits-award {

  position: relative;
  box-sizing: border-box;
  width: 590rpx;
  border-radius: 44rpx;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  background-image: url(./images/benefits-success-bg.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  padding-top: 64rpx;
  padding-bottom: 64rpx;
  box-sizing: border-box;
  .benefits-aura-bg {
    position: absolute;
    top: -162rpx;
    width: 716rpx;
    height: 226rpx;
    background-size: cover;
   }

  .title {
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    color: #364047;
    margin-bottom: 30rpx;
    z-index: 1000;
  }
  .gift-images {
    position: relative;
    img {
      width: 300rpx;
      height: 300rpx;
      background-size: cover;
    }
    .manure {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: -48rpx;
      background: #ffeab8;
      border-radius: 24rpx;
      font-family: PingFangSC-Semibold;
      font-size: 42rpx;
      color: #8b553a;
      text-align: center;
      font-weight: 700;
      padding: 10rpx 40rpx;
      line-height: 60rpx;
      white-space: nowrap;
    }
    .manure-fail {
      background: #E0E5E8;
      color: #859199;
    }
  }
  .btn {
    margin-top: 101rpx;
    width: 470rpx;
    height: 100rpx;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 600;
    line-height: 100rpx;
  }
  .close-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -112rpx;
    .close-icon {
      width: 100%;
    }
  }
}

.benefits-award-fail-bg {
  background-image: url(./images/benefits-fail-bg.png);
}


.benefits-aura-animation {
  position: absolute;
  top: -270rpx;
  left: -80rpx;
  width: 100vw;
  height: 600rpx;
  background-size: cover;
  .header {
    z-index: 998;
    margin-top: 40rpx;
    width: 100vw;
    height: 600rpx;
    animation: benefitsHeaderRotate 4.23s infinite, benefitsHeaderOpacity 4.23s infinite;
  }

  #benefits-star-lottie {
    position: absolute;
    left: 0;
    top: 100rpx;
    width: 100vw;
    height: 380rpx;
    z-index: 999;
  }

  .title-img {
    position: absolute;
    left: 50%;
    top: 280rpx;
    transform: translate(-50%, -50%);
    height: 69rpx;
    width: 369rpx;
    z-index: 1000;
  }
}
@keyframes benefitsHeaderRotate {
  0% {
    transform: rotate(0deg);
    animation-timing-function: cubic-bezier(0.167, 0.167, 0.833, 0.833);
  }
  46.46% {
    transform: rotate(-30deg);
    animation-timing-function: cubic-bezier(0.167, 0.167, 0.833, 0.833);
  }
  93.7% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(0deg);
  }
}
@keyframes benefitsHeaderOpacity {
  0% {
    opacity: 0.3;
    animation-timing-function: cubic-bezier(0.167, 0.167, 0.833, 0.833);
  }
  31.5% {
    opacity: 0.6;
    animation-timing-function: cubic-bezier(0.167, 0.167, 0.833, 0.833);
  }
  46.46% {
    opacity: 0.3;
    animation-timing-function: cubic-bezier(0.167, 1, 0.833, 1);
  }
  93.7% {
    opacity: 0.3;
  }
  to {
    opacity: 0.3;
  }
}

