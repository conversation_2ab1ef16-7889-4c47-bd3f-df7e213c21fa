import cz from 'classnames';
import './index.scss';
import React, { Fragment, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { MODAL_ID } from '@/components/modals/types';
import baseModal from '@/lib/modal';
import mx from '@ali/pcom-mx';
import { preloadImg } from '@/lib/utils/preloadImg';
import dispatch from "@/logic/store";
import { execWithLock } from "@/lib/utils/lock";
import useMxState from "@/hooks/useMxState";
import { QueryCardInfoRes } from "@/api/doublePointsCard/typings";
import { IFarmHelpPlantHomeResponse } from "@/logic/store/models/farmHelpPlant/type";
import { MainAPI } from "@/logic/type/event";
import { getDoubleCardFactParams, TasklistSource } from "@/pages/index/utils";
import stat from "@/lib/stat";

import NoWinBg from './images/double-card-head-no-win-bg.png';
import FirstDraw from './images/double-card-first-draw.png';
import FirstDrawTitle from './images/double-card-first-draw-title.png';
import MustDoubleTitle from './images/must-double-title.png';
import IdleBg from './images/double-card-head-normal-bg.png';
import CloseImg from './images/close.png';
import WinContentBg from './images/double-card-content-bg.png';
import DoubleBg from './images/double-card-mini-bg.png';
import DoubleCardImg from './images/double-card.png';
import DoubleCardMaxImg from './images/double-card-max.png';
import HelpPlantManureCard from './images/double-card-help-plant-manure.png';
import HelpPlantRedPacket from './images/double-card-help-plant-red-packet.png';
import DoubleBgMax from './images/double-bg-max.png';
import DividerImg from './images/divider.png';
import TipText from './images/double-card-tip-text.png';
import TipArrow from './images/tip-arrow.png';
import { openPage } from '@/lib/ucapi';
import Fact from "@/components/Fact";
import toast from "@/lib/universal-toast/component/toast";
import { DRAW_TYPE } from "@/logic/store/models/doublePointsCard/typings";
import { getParam } from '@/lib/qs';
import NoAward from "@/components/modals/modal_double_card/NoAward";
import Lottie, { AnimationItem } from 'lottie-web';
import Win from './Win';
import DoubleCardHeader from './components/DoubleCardHeader';
import { checkIsShowAwardTip } from './util';

interface IProps {
  onClose?: () => void;
  onConfirm?: () => void;
  autoLottery?: boolean; // 自动进行抽奖
  from?: EDoubleFactFromParam | string;
  isMustDouble?: boolean; // 必中人群
  delayLottery?: boolean; // 延迟3s自动抽奖
}

const preLoadImgList = [
  FirstDraw,
  FirstDrawTitle,
  MustDoubleTitle,
  DoubleCardImg,
  NoWinBg,
  WinContentBg,
  DoubleBg,
  DividerImg,
  DoubleBgMax,
  DoubleCardMaxImg,
  HelpPlantManureCard,
  HelpPlantRedPacket,
];

let hasPreload = false;
export const preloadDoubleCardImgs = () => {
  if (!hasPreload) {
    preloadImg(preLoadImgList);
    hasPreload = true;
  }
}

// 访问来源
export const enum EDoubleFactFromParam {
  BUBBLE = 'bubble', // 首页入口气泡
  GUIDE_POP = 'guide_pop', // 宣传弹窗
  SHARED_POP = 'shared_pop', // 助力结果自动打开弹窗
  TASK_ENTRY = 'task_list', // 任务列表入口
  DO_TASK = 'do_task', // 做任务
}
export const enum ELotteryStatus {
  START = 'start', // 未抽奖
  LOADING = 'loading', // 抽奖中
  WIN = 'win', // 有奖获奖态
  NO_WIN = 'no_win', // 无奖态
}
const ModalDoubleCard = (props: IProps) => {
  const [doublePointsCard] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { todayCardInfo, drawInfo, frontData } = doublePointsCard;
  const { drawChance = 0, drawTimes = 0, refreshTimes = 0 } = drawInfo || {};
  const [helpPlantHomeInfo] = useMxState<IFarmHelpPlantHomeResponse>('farmHelpPlant.helpPlantHome');
  const { onClose, onConfirm, autoLottery, from, delayLottery, isMustDouble } = props;
  const { doubleNum = 100, doubleNumLimit = 0, doubleAmountLimit = 0, canReceiveDoubleAmount = 0 } = todayCardInfo || {};
  let initStatus = ELotteryStatus.START;
  if (doubleNum > 0) {
    initStatus = ELotteryStatus.WIN;
  } else if (drawInfo?.totalDrawTimes > 0 || !drawChance) {
    initStatus = ELotteryStatus.NO_WIN;
  }
  const hasDraw = !!drawInfo?.totalDrawTimes
  const showCardAni = useRef<boolean>(false);
  const hasAutoLottery = useRef(false);
  const hasDelayLottery = useRef(false);
  const [lotteryStatus, setLotteryStatus] = useState<ELotteryStatus>(initStatus);
  // 倒计时相关状态
  const [countdown, setCountdown] = useState<number>(0);
  const [isCountingDown, setIsCountingDown] = useState<boolean>(false);
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [showDrawLottie, setShowDrawLottie] = useState<Boolean>(false);
  const drawAniRef = useRef<AnimationItem | null>(null);
  const [isLeaving, setIsLeaving] = useState(false);
  const [modalHeadBg, setModalHeadBg] = useState<String>(initStatus === ELotteryStatus.NO_WIN ? NoWinBg : IdleBg)
  // 抽中卡片倍数
  const [cardTimes, setCardTimes] = useState<number>(doubleNum / 100);
  // 刷新倍数标识
  const refreshCardTimes = useRef(false);
  // 当前是否为最大倍数
  const isMaxMultiplier = cardTimes >= (doubleNumLimit / 100);
  // 最高倍数限制
  const reachDoubleNumLimit = doubleNum >= doubleNumLimit;
  // 达到肥料最高限额
  const reachAmountLimit = !!(canReceiveDoubleAmount && canReceiveDoubleAmount >= doubleAmountLimit);
  // 有卡刷新次数限制
  const reachDrawTimesLimit = doubleNum > 0 && drawTimes >= refreshTimes;
  const drawLimit = reachDoubleNumLimit || reachDrawTimesLimit || reachAmountLimit;

  const { unReceiveAward = 0, rankingAward = 0 } = helpPlantHomeInfo || {};
  const hasStatCustom = useRef(false);

  const closeTypeRef = useRef<'close' | 'confirm' | ''>('');
  const getCardStatus = () => {
    let status = 1;
    if (drawChance <= 0) {
      // 无抽奖机会
      if (unReceiveAward > 0) {
        status = 2;
      } else if (rankingAward > 0) {
        status = 3;
      }
    }
    return status
  }
  const baseFactParams = useMemo(() => {
    return {
      ...getDoubleCardFactParams(),
      from: from || getParam('from') || ''
    }
  }, [from, doublePointsCard])

  // 清除倒计时
  const clearCountdownTimer = () => {
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
    setIsCountingDown(false);
    setCountdown(0);
  };

  const handleClose = useCallback(() => {
    stat.click('fdcard_card__close', {
      c: 'fdcard',
      d: 'award',
      ...baseFactParams,
    })
    closeTypeRef.current = 'close';
    setIsLeaving(true);
    // 清除倒计时
    clearCountdownTimer();
    mx.event.emit(MainAPI.CLOSE_DOUBLE_CARD_POP);
  }, [baseFactParams]);

  useEffect(() => {
    if (!hasStatCustom.current && (lotteryStatus === ELotteryStatus.NO_WIN || lotteryStatus === ELotteryStatus.WIN)) {
      // 用户事件监测 - 已抽卡-未抽中卡/抽中卡 自定义打点
      stat.custom('fdcard_status', {
        event_id: '19999',
        status: '1'
      });
      hasStatCustom.current = true;
    }
  }, [lotteryStatus])

  useEffect(() => {
    Lottie.loadAnimation({
      name: 'hand',
      container: document.getElementById('guidance-hand') as HTMLElement,
      renderer: 'canvas',
      loop: true,
      autoplay: true,
      animationData: require('@/lib/animation/hand.json'),
      assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202405/6c4713/images/',
    });
  }, [])

  useEffect(() => {
    drawAniRef.current = Lottie.loadAnimation({
      name: 'drawLottie',
      container: document.getElementById('drawLottie') as HTMLElement,
      renderer: 'canvas',
      loop: false,
      autoplay: false,
      animationData: require('./ani/draw-lottie.json'),
      assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202507/ab0f36/images/',
    });
  }, [])

  const handlePlayDrawAni = (win, drawDoubleNum) => {
    const handleComplete = () => {
      setShowDrawLottie(false);
      showCardAni.current = true;
      if (win && drawDoubleNum) {
        setLotteryStatus(ELotteryStatus.WIN);
        setModalHeadBg(IdleBg);
      } else {
        setLotteryStatus(ELotteryStatus.NO_WIN);
        setModalHeadBg(NoWinBg);
      }
    };
    drawAniRef.current?.removeEventListener('complete', handleComplete);
    drawAniRef.current?.addEventListener('complete', handleComplete);
    drawAniRef.current?.goToAndStop(0, true);
    drawAniRef.current?.play();
  };

  const handleLottery = async (refresh = false, isAuto = false) => {
    if (!refresh) {
      const firstDraw = lotteryStatus === ELotteryStatus.START;
      stat.click(firstDraw ? 'fdcard_firstgachapop_click' : 'fdcard_noawardpop_click', {
        c: 'fdcard',
        d: 'gacha',
        click_position: firstDraw ? (isAuto ? 3 : 1) : 2,
        status: getCardStatus(),
        fdcard_status: isMustDouble ? '0' : '1',
        ...baseFactParams,
      })
      // 用户事件监测 - 首次抽奖自定义打点
      firstDraw && stat.custom('fdcard_status', {
        event_id: '19999',
        status: '0'
      });
    } else {
      clickWinRegularCardStat(3);
    }
    execWithLock('doublePointsCard.drawCard', async (unlock) => {
      const drawRes = await dispatch.doublePointsCard.drawCard();
      const { win, doubleNum: drawDoubleNum, maxDrawTimes, drawType } = drawRes || {};
      if (refresh && !(win && drawDoubleNum)) {
        // 异常场景，刷新倍数不中
        toast.show('刷新倍数失败了, 请重新试试');
        unlock();
        return;
      }
      // 清空卡片内容, 显示抽奖动画
      setLotteryStatus(ELotteryStatus.LOADING);
      setShowDrawLottie(true);
      handlePlayDrawAni(win, drawDoubleNum);
      if (win && drawDoubleNum) {
        setCardTimes(drawDoubleNum / 100);
        const drawResFactParams = {
          if_top: drawRes && drawRes?.doubleNum >= doubleNumLimit ? '1' : getIfTopStat(),
          assist_value: drawDoubleNum > 0 ? 1 : 0,
          final_multiplier: drawDoubleNum > 0 ? drawDoubleNum / 100 : '-1', // 没抽中-1
        }
        stat.exposure('fdcard_awardpop_exposure', {
          c: 'fdcard',
          d: 'award',
          base_multiplier: drawDoubleNum / 100,
          ...baseFactParams,
          ...drawResFactParams,
          type: doubleNum > 0 ? '2' : '1' // 1抽卡 2提高倍数
        })
        setTimeout(() => {
          if (drawType === DRAW_TYPE.HAS_CARD && maxDrawTimes) {
            toast.show('今日刷新倍数机会已达上限，去做任务赚更多肥料吧', {
              style: {
                bottom: '910rpx',
                maxWidth: '450rpx',
              },
            })
          }
        }, 3000);
      } else {
        stat.exposure('fdcard_noawardpop_exposure', {
          c: 'fdcard',
          d: 'award',
          status: getCardStatus(),
          ...baseFactParams,
        })
      }
      unlock();
    });
  };

  // 延迟抽奖处理
  const handleDelayLottery = () => {
    setIsCountingDown(true);
    setCountdown(frontData?.countdownNum || 3);

    countdownTimerRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          // 倒计时结束，执行抽奖
          clearCountdownTimer();
          handleLottery(false, true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 自动抽奖
  useEffect(() => {
    if (autoLottery && drawChance && !hasAutoLottery.current && !delayLottery) {
      handleLottery(false, true);
      hasAutoLottery.current = true
    }
  }, [autoLottery, drawChance, delayLottery])

  // 延迟3秒自动抽奖
  useEffect(() => {
    if (delayLottery && drawChance && !hasDraw && !hasDelayLottery.current) {
      handleDelayLottery();
      hasDelayLottery.current = true
    }
  }, [delayLottery, drawChance, autoLottery])

  // 组件卸载时清理倒计时
  useEffect(() => {
    return () => {
      clearCountdownTimer();
    };
  }, []);

  const getIfTopStat = () => {
    if (reachAmountLimit) {
      return '2';
    }
    if (reachDoubleNumLimit) {
      return '1';
    }
    return '0';
  };
  const clickWinRegularCardStat = (position: number) => {
    stat.click('fdcard_card__click', {
      c: 'fdcard',
      d: 'award',
      base_multiplier: cardTimes,
      if_top: getIfTopStat(),
      if_max: reachAmountLimit ? 1 : 0,
      click_position: position,
      ...baseFactParams,
    })
  }
  const clickNoWinCardStat = (position: number) => {
    stat.click('fdcard_noawardpop_click', {
      c: 'fdcard',
      d: 'award',
      base_multiplier: cardTimes,
      click_position: position,
      status: getCardStatus(),
      ...baseFactParams,
    })
  }
  const clickInviteStat = () => {
    if (lotteryStatus === ELotteryStatus.WIN) {
      clickWinRegularCardStat(2);
    }
    if (lotteryStatus === ELotteryStatus.NO_WIN) {
      clickNoWinCardStat(1);
    }
  }

  const handleToTask = async () => {
    clickWinRegularCardStat(1)
    baseModal.close(MODAL_ID.DOUBLE_CARD_LOTTERY);
    mx.event.emit(MainAPI.ShowTaskPop, {tasklist_source: TasklistSource.double_card})
  }

  const handleAnimationEnd = useCallback(() => {
    if (!isLeaving) return;
    const type = closeTypeRef.current;
    baseModal.close(MODAL_ID.DOUBLE_CARD_LOTTERY);
    if (type === 'close') {
      onClose?.();
    } else if (type === 'confirm') {
      onConfirm?.();
    }
  }, [isLeaving, onClose, onConfirm]);

  const modalClassName = useMemo(
    () =>
      cz('modal-double-card', {
        leave: isLeaving,
      }),
    [isLeaving],
  );

  const closeButtonProps = useMemo(
    () => ({
      className: 'double-card-close',
      src: CloseImg,
      onClick: handleClose,
    }),
    [handleClose],
  );
  const handleFactStartClick = () => {
    stat.click('fdcard_firstgachapop_click', {
      c: 'fdcard',
      d: 'gacha',
      click_position: 2,
      fdcard_status: isMustDouble ? '0' : '1',
      ...baseFactParams,
    })
  }
  // 处理点击
  const handleButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isCountingDown) {
      // 如果正在倒计时，点击后取消倒计时，按常规逻辑执行
      handleLottery(false);
      clearCountdownTimer();
    } else {
      // 常规逻辑
      handleLottery(false);
    }
  }

  // 常规：未抽奖
  const renderStart = () => {
    const getButtonText = () => {
      if (delayLottery) {
        return isCountingDown ? `立即领取(${countdown})` : '立即领取';
      }
      return '立即抽卡';
    };
    const getTitleImg = () => {
      if (isMustDouble) {
        return MustDoubleTitle
      }
      return frontData?.titleImgUrl ?? MustDoubleTitle
    }

    return (
      <Fact
        noUseClick
        onClick={handleFactStartClick}
        expoLogkey="fdcard_firstgachapop_exposure"
        c="fdcard"
        d="gacha"
        expoExtra={{
          ...baseFactParams,
          fdcard_status: isMustDouble ? '0' : '1',
          show_award: checkIsShowAwardTip() ? '0' : '1', // 是否展示次日奖励到账
        }}
        className="idle-wrapper wrapper"
      >
        <img className={`title-img mx-auto ${isMustDouble ? 'must-double-title' : ''}`} src={getTitleImg()} alt="" />
        <div className="subtitle mx-auto flex-center">
          {
            isMustDouble ?
            <Fragment>
              膨胀后<span className="strong">最高可得{doubleAmountLimit}肥料</span>
            </Fragment> :
            <Fragment>
              今日赚取的所有肥料 <span className="strong">最高可膨胀{doubleNumLimit / 100}倍</span>
            </Fragment>
          }
        </div>
        <img className="first-draw mx-auto" src={FirstDraw} alt="" />
        <div className="btn-wrapper mx-auto">
          <button className="lottery-btn glow-btn flex-center" onClick={handleButtonClick}>
            <span>{getButtonText()}</span>
            {/* <div className="common-tip flex-center">最高可达{doubleNumLimit / 100}倍</div> */}
            <div className="guidance">
                <div id="guidance-hand" />
            </div>
          </button>
        </div>
      </Fact>
    );
  };

  // 抽奖中，动画
  const renderLoading = () => {
    return null;
  };

  // 发起邀请
  const handleInvite = () => {
    clickInviteStat();
    dispatch.share.doubleOpenSharePanel();
  }
  // 处理抽奖或发起邀请
  const handleClickDraw = async (refresh: boolean, chance: number) => {
    if (chance > 0) {
      refreshCardTimes.current = refresh;
      handleLottery(refresh);
      return
    }
    handleInvite();
  };

  const handleToHelpPlant = () => {
    clickNoWinCardStat(unReceiveAward ? 3 : 4);
    handleClose();
    dispatch.farmHelpPlant.openActivityPage('doubleCard');
  }

  const renderWin = () => {
    return (
      <Win
        showCardAni={showCardAni.current}
        drawLimit={drawLimit}
        isMaxMultiplier={isMaxMultiplier}
        cardTimes={cardTimes}
        getIfTopStat={getIfTopStat}
        refreshCardTimes={refreshCardTimes.current}
        baseFactParams={baseFactParams}
        handleDraw={handleClickDraw}
        handleToTask={handleToTask}
      />
    )
  }
  // 抽奖结果：未抽中
  const renderNoWin = () => {
    return (
      <NoAward
        showCardAni={showCardAni.current}
        baseFactParams={baseFactParams}
        rankingAward={rankingAward}
        unReceiveAward={unReceiveAward}
        clickNoWinCardStat={clickNoWinCardStat}
        handleDraw={handleClickDraw}
        handleToHelpPlant={handleToHelpPlant}
      />
    )
  };

  const renderRule = () => {
    const toRulePage = () => {
      const ruleLink = frontData.ruleLink;
      if (ruleLink) {
        stat.click('fdcard_card__rules', {
          c: 'fdcard',
          d: 'award',
          ...baseFactParams,
        })
        openPage(ruleLink);
      }
    };
    return (
      <div className="rule-wrap" onClick={toRulePage}>
        <div className="rule rule-container" />
        <span className="rule rule-text">规 则</span>
      </div>
    );
  };
  const renderMain = () => {
    switch (lotteryStatus) {
      case ELotteryStatus.START:
        return renderStart();
      case ELotteryStatus.LOADING:
        return renderLoading();
      case ELotteryStatus.WIN:
        return renderWin();
      case ELotteryStatus.NO_WIN:
        return renderNoWin();
      default:
        return null;
    }
  };
  return (
    <div className="modal-double-card-wrapper">
      <div className={modalClassName} onAnimationEnd={handleAnimationEnd}>
        <img {...closeButtonProps} />
        <DoubleCardHeader lotteryStatus={lotteryStatus} />
        <div id="drawLottie" style={{visibility: showDrawLottie ? 'visible' : 'hidden'}} />
        <img className="modal-header-bg" src={modalHeadBg} alt="" />
        {renderRule()}
        {renderMain()}
      </div>
    </div>
  );
};

ModalDoubleCard.displayName = 'ModalDoubleCard';

export default memo(ModalDoubleCard);
