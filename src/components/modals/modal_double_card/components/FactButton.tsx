import React, { Fragment, useEffect } from 'react';
import stat from '@/lib/stat';

/**
 ***=1 邀请好友
 ***=2 再次抽卡
 ***=3 做任务
 ***=4 再抽一次必得
 ***=5 任务列表
 ***=6 去领取
 ***=7 去看看
 ***=8 提高倍数
 */
export const enum EBtnStateNum {
  INVITE = '1',
  DRAW = '2',
  DO_TASK = '3',
  MUST_DOUBLE = '4',
  TASK_LIST = '5',
  GO_RECEIVE = '6',
  GO_LOOK = '7',
  INCREASE_TIMES = '8',
}

export const FactDoubleButton = ({
  children,
  user_status,
  icon_status,
}: {
  children: React.ReactNode;
  user_status: 'nocard' | 'havecard';
  icon_status: EBtnStateNum;
}) => {
  useEffect(() => {
    stat.exposure('fdcard_button_exposure', {
      c: 'fdcard',
      d: 'award',
      user_status,
      icon_status,
    });
  }, [icon_status, user_status]);
  return <Fragment>{children}</Fragment>;
};
