import React, { useEffect, useState } from 'react';
import useMxState from '@/hooks/useMxState';
import { QueryCardInfoRes } from '@/api/doublePointsCard/typings';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import TipText from '../images/double-card-tip-text.png';
import TipArrow from '../images/tip-arrow.png';
import AwardArrow from '../images/award-arrow.png';
import UUImg from '../images/uu.png';
import { ELotteryStatus } from '..';
import dispatch from '@/logic/store';
import { checkIsShowAwardTip } from '../util';

function DoubleCardHeader(props: { lotteryStatus: ELotteryStatus }) {
  const { lotteryStatus } = props;
  const [doublePointsCard] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const yesterdayCardInfo = doublePointsCard?.yesterdayCardInfo || {}
  const isShowAward = checkIsShowAwardTip()
  const [showType] = useState<'tip' | 'award' | ''>(isShowAward ? 'award' : 'tip');
  useEffect(() => {
    if (showType === 'award') {
      const curTime = doublePointsCard.curTime || Date.now();
      localStorage.setItem(LocalStorageKey.DOUBLE_CARD_YESTERDAY_AWARD, `${curTime}`);
    }
  }, []);

  if (!showType) return;
  if (showType === 'tip') {
    return (
      lotteryStatus === ELotteryStatus.START && (
        <div className="start-tip">
          <img className="tip-text" src={TipText} />
          <span>每天抽卡</span>
          <img className="tip-arrow" src={TipArrow} />
          <span>做任务收肥料</span>
          <img className="tip-arrow" src={TipArrow} />
          <span>次日领膨胀肥料</span>
        </div>
      )
    );
  }
  return (
    <div className="award-tip">
      <img className="award-arrow" src={AwardArrow} />
      <span className="tip-text">昨日膨胀奖励肥料已到账</span>
      <div className="award-num">
        <span className="shadow-num">{yesterdayCardInfo?.canReceiveDoubleAmount}</span>
        <span className="num">{yesterdayCardInfo?.canReceiveDoubleAmount}</span>
      </div>
      <img className="uu-img" src={UUImg} alt="" />
    </div>
  );
}

export default DoubleCardHeader;
