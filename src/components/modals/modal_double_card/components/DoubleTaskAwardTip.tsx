import React, { Fragment, useEffect } from 'react';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import {
  checkTaskCountDown,
  getTargetTimeDiff,
  getTaskPointAwardText,
} from '@/pages/index/components/TaskPop/TaskList/util';
import dayjs from 'dayjs';
import CountDown from '@/components/common/CountDown';
import { formatLeftTime } from '@/lib/utils/date';
import dispatch from '@/logic/store';

export const DoubleTaskAwardTip = ({
  curTaskInfo,
  curTime,
  tipText,
}: {
  curTaskInfo: TaskInfo;
  curTime: number;
  tipText?: string;
}) => {
  const { diff } = getTargetTimeDiff(curTaskInfo?.beginTime, curTime);
  useEffect(() => {
    if (checkTaskCountDown(curTaskInfo, curTime)) {
      dispatch.resource.queryResource({ firstInit: false });
      dispatch.task.queryTaskList(false);
    }
  }, [curTaskInfo, curTime]);
  if (checkTaskCountDown(curTaskInfo, curTime)) {
    const { isSameDay } = getTargetTimeDiff(curTaskInfo?.beginTime, curTime);
    const endOfD = dayjs(curTime).endOf('day');
    const isToday = isSameDay && endOfD.diff(curTaskInfo?.beginTime, 's') > 60;
    if (!isToday) {
      return '明日再来';
    }
    return (
      <div className="num-text">
        <CountDown
          diff={diff}
          formatFunc={(time) => {
            const { hour, min, second } = formatLeftTime(time * 1000);
            return `${hour}:${min}:${second}`;
          }}
          onComplete={() => {
            dispatch.resource.queryResource({ firstInit: false });
            dispatch.task.queryTaskList(false);
          }}
        />
      </div>
    );
  }
  return (
    <Fragment>
      {tipText || (
        <Fragment>
          完成得<div className="num-text">{getTaskPointAwardText(curTaskInfo)}</div>肥料
        </Fragment>
      )}
    </Fragment>
  );
};
