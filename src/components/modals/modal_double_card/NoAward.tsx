import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import useMxState from "@/hooks/useMxState";
import { QueryCardInfoRes } from "@/api/doublePointsCard/typings";
import Fact from "@/components/Fact";

import NoCardImg from './images/double-card-no-award-card.png';
import BbzManure from './images/double-card-help-plant-manure.png';
import BbzPacket from './images/double-card-help-plant-red-packet.png';

interface IProps {
  showCardAni: boolean; // 是否显示卡片缩放位移动化, 浮层首次打开时不显示
  unReceiveAward: number;
  rankingAward: number;
  baseFactParams: any;
  handleDraw: () => void;
  handleToHelpPlant: () => void;
}

function NoAward(props: IProps) {
  const [doublePointsCard] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { drawInfo } = doublePointsCard;
  const [showTitle, setShowTitle] = useState<boolean>(false);
  const [startMove, setStartMove] = useState<boolean>(false);
  const [showHelpPlantCards, setShowHelpPlantCards] = useState<boolean>(false);
  const { drawChance = 0, drawTimes = 0, luckyTimes = 0 } = drawInfo || {};
  const { showCardAni, unReceiveAward, rankingAward, baseFactParams, handleDraw, handleToHelpPlant } = props;
  const showSingle = drawChance > 0 || (!unReceiveAward && !rankingAward);
  const timeoutRef = useRef<NodeJS.Timeout[]>([]);
  useEffect(() => {
    if (!showCardAni) {
      setShowTitle(true);
      setShowHelpPlantCards(true);
    } else if (showSingle) {
      const timer1 = setTimeout(() => {
        setShowTitle(true);
      }, 200);
      timeoutRef.current.push(timer1);
    } else {
      const timer2 = setTimeout(() => {
        setStartMove(true);
      }, 200)
      timeoutRef.current.push(timer2);
      const timer3 = setTimeout(() => {
        setShowTitle(true);
        setShowHelpPlantCards(true);
      }, 600);
      timeoutRef.current.push(timer3);
    }
    return () => {
      timeoutRef.current.forEach((timerId) => clearTimeout(timerId));
      timeoutRef.current = [];
    };
  }, []);

  const renderHelpPlantCards = () => {
    if (showSingle) {
      return null
    }
    return showTitle ? (
      <div className="card help-card" onClick={handleToHelpPlant}>
        {
          unReceiveAward ?
            <div className="card-content">
              <img src={BbzManure} />
              <div className="manure-amount din-num">{unReceiveAward}</div>
              <div className="card-desc">24点未领取失效</div>
            </div>
            :
            <div className="card-content">
              <img src={BbzPacket} />
              <div className="cash-amount din-num">
                {rankingAward / 100}
                <span className="cash-unit">元</span>
              </div>
              <div className="card-desc">保持排名至24点可得</div>
            </div>
        }
      </div>
    ) : null
  }

  let subTitle = '抽中即可膨胀当日赚取的所有肥料!';
  let cardStatus = 1;
  if (drawChance <= 0) {
    // 无抽奖机会
    if (unReceiveAward > 0) {
      cardStatus = 2;
      subTitle = '别气馁，还可领取“帮帮种”肥料哟!';
    } else if (rankingAward > 0) {
      cardStatus = 3;
      subTitle = '加油，还有机会得“帮帮种”红包哟!';
    }
  }

  const luckyChance = (luckyTimes - drawTimes) <= 1 && luckyTimes > 0;

  const getCardClass = () => {
    if (showSingle) {
      return '';
    }
    return showCardAni && startMove ? 'card-slide-left' : 'static-card-translate-x'
  }

  return (
    <Fact expoLogkey="fdcard_noawardpop_exposure" c="fdcard" d="award" noUseClick expoExtra={{
      status: cardStatus,
      ...baseFactParams,
    }} className="no-win-wrapper wrapper"
    >
      <div className="title-wrapper" style={{opacity: showTitle ? 1 : 0}}>
        <div className="title mx-auto">还可继续抽膨胀卡</div>
        <div className="subtitle mx-auto">
          { subTitle }
        </div>
      </div>
      <div className={`no-win-content`}>
        <div className={`card ${showCardAni ? 'no-award-card-ani' : ''} ${getCardClass()}`}>
          <img src={NoCardImg} />
        </div>
        {renderHelpPlantCards()}
      </div>
      <div className={`btn-wrapper mx-auto ${showSingle ? 'j-center' : ''}`} style={{opacity: showTitle ? 1 : 0}}>
        <button className={`lottery-btn glow-btn ${drawChance > 0 ? 'yellow-btn' : ''} ${showSingle ? '' : 'lottery-btn-min'}`} onClick={() => handleDraw(false, drawChance)}>
          <span>{ drawChance > 0 ? `继续抽卡*${drawChance}` : '邀1人再抽1次' }</span>
          {
            luckyChance && <div className="common-tip flex-center">再抽一次必得</div>
          }
          {
            luckyTimes > 0 && <div className="btn-desc mx-auto">最多抽{luckyTimes}次必得膨胀卡</div>
          }
        </button>
        {
          !showSingle && showHelpPlantCards ?
            <button className="white-btn" onClick={handleToHelpPlant}>
              <span>{unReceiveAward ? '去领取肥料' : '去看看排名'} </span>
            </button> : null
        }
      </div>
    </Fact>
  );
}

export default NoAward;
