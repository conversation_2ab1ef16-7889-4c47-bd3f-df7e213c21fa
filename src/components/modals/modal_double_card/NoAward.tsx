import React, { Fragment, useEffect, useRef, useState } from 'react';
import './index.scss';
import useMxState from "@/hooks/useMxState";
import { QueryCardInfoRes } from "@/api/doublePointsCard/typings";
import Fact from "@/components/Fact";

import NoCardImg from './images/double-card-no-award-card.png';
import BbzManure from './images/double-card-help-plant-manure.png';
import BbzPacket from './images/double-card-help-plant-red-packet.png';
import { IResourceState } from '@/logic/store/models/resource/typings';
import { ITaskState } from '@/logic/store/models/task/typing';
import config from '@/config';
import { checkTaskCountDown, checkTaskFinished, getTargetTimeDiff, isAdVideoTask, logToFinishTask } from '@/pages/index/components/TaskPop/TaskList/util';
import { useAdPlayInit } from '@/hooks/useAdPlayInit';
import stat from '@/lib/stat';
import toast from "@/lib/universal-toast/component/toast";
import { geneTaskRequestId } from '@/api/utils';
import { execWithLock } from '@/lib/utils/lock';
import { TASK_LOCATION } from '@/pages/index/components/TaskPop/TaskList/types';
import { LocalStorageKey } from '@/lib/utils/localStorage_constant';
import { taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import dayjs from 'dayjs';
import CountDown from '@/components/common/CountDown';
import { formatLeftTime } from '@/lib/utils/date';
import dispatch from '@/logic/store';
interface IProps {
  showCardAni: boolean; // 是否显示卡片缩放位移动化, 浮层首次打开时不显示
  unReceiveAward: number;
  rankingAward: number;
  baseFactParams: any;
  handleDraw: (refresh: boolean, chance: number) => void;
  handleToHelpPlant: () => void;
}

function NoAward(props: IProps) {
  const [doublePointsCard] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { drawInfo } = doublePointsCard;
  const [showTitle, setShowTitle] = useState<boolean>(false);
  const [startMove, setStartMove] = useState<boolean>(false);
  const [showHelpPlantCards, setShowHelpPlantCards] = useState<boolean>(false);
  const { drawChance = 0, drawTimes = 0, luckyTimes = 0 } = drawInfo || {};
  const { showCardAni, unReceiveAward, rankingAward, baseFactParams, handleDraw, handleToHelpPlant } = props;
  const showSingle = drawChance > 0 || (!unReceiveAward && !rankingAward);
  const timeoutRef = useRef<NodeJS.Timeout[]>([]);

  // 看激励视频得翻倍卡抽奖机会资源位
  const [resource] = useMxState<IResourceState>('resource');
  const [task] = useMxState<ITaskState>('task');
  const multiResourceCurTime = resource?.multiResource?.__meta?.['timestamp'] ?? task?.curTime
  const drawChanceTaskResourceList = resource?.multiResource?.[config.drawChanceTaskResourceCode]?.taskList ?? [];
  const adTaskBtnText = resource?.multiResource?.[config.drawChanceTaskResourceCode]?.attributes?.btnText || '看广告得抽奖次数'

  // 可做任务列表
  const adTaskList = drawChanceTaskResourceList?.filter((item) => {
    return !checkTaskFinished(item) && isAdVideoTask(item);
  });
  const { taskList: displayAdTaskList } = useAdPlayInit({
    taskList: adTaskList,
  });

  const showAdTask = displayAdTaskList && displayAdTaskList?.length > 0; // 符合做任务获取抽奖次数的条件
  const curTaskInfo = displayAdTaskList?.[0] || {};
  // console.log({
  //   adTaskList,
  //   showAdTask,
  //   curTaskInfo,
  //   drawChanceTaskResourceList,
  // })
  const handleTask = () => {
    stat.click('', {
      c: '',
      d: '',
      ...baseFactParams,
    })
    // 已完成
    if (!curTaskInfo.id) {
      return;
    }
    // 待解锁
    if (checkTaskCountDown(curTaskInfo, multiResourceCurTime)) {
      return toast.show('倒计时结束解锁，先去做其它的吧');
    }
    const requestId = geneTaskRequestId();
    execWithLock(
      'finish_task_lock',
      async () => {
        logToFinishTask(curTaskInfo, TASK_LOCATION.DOUBLE_NO_AWARD);
        localStorage.setItem(LocalStorageKey.FINISH_TASK_FROM, TASK_LOCATION.DOUBLE_NO_AWARD);
        await taskActionHandler(curTaskInfo, requestId, { location: TASK_LOCATION.DOUBLE_NO_AWARD });
      },
      2000,
    );
  };

  useEffect(() => {
    if (!showCardAni) {
      setShowTitle(true);
      setShowHelpPlantCards(true);
    } else if (showSingle) {
      const timer1 = setTimeout(() => {
        setShowTitle(true);
      }, 200);
      timeoutRef.current.push(timer1);
    } else {
      const timer2 = setTimeout(() => {
        setStartMove(true);
      }, 200)
      timeoutRef.current.push(timer2);
      const timer3 = setTimeout(() => {
        setShowTitle(true);
        setShowHelpPlantCards(true);
      }, 600);
      timeoutRef.current.push(timer3);
    }
    return () => {
      timeoutRef.current.forEach((timerId) => clearTimeout(timerId));
      timeoutRef.current = [];
    };
  }, []);

  const renderHelpPlantCards = () => {
    if (showSingle) {
      return null
    }
    return showTitle ? (
      <div className="card help-card" onClick={handleToHelpPlant}>
        {
          unReceiveAward ?
            <div className="card-content">
              <img src={BbzManure} />
              <div className="manure-amount din-num">{unReceiveAward}</div>
              <div className="card-desc">24点未领取失效</div>
            </div>
            :
            <div className="card-content">
              <img src={BbzPacket} />
              <div className="cash-amount din-num">
                {rankingAward / 100}
                <span className="cash-unit">元</span>
              </div>
              <div className="card-desc">保持排名至24点可得</div>
            </div>
        }
      </div>
    ) : null
  }

  let subTitle = '抽中即可膨胀当日赚取的所有肥料!';
  let cardStatus = 1;
  if (drawChance <= 0) {
    // 无抽奖机会
    if (unReceiveAward > 0) {
      cardStatus = 2;
      subTitle = '别气馁，还可领取“帮帮种”肥料哟!';
    } else if (rankingAward > 0) {
      cardStatus = 3;
      subTitle = '加油，还有机会得“帮帮种”红包哟!';
    }
  }

  const luckyChance = (luckyTimes - drawTimes) <= 1 && luckyTimes > 0;

  const getCardClass = () => {
    if (showSingle) {
      return '';
    }
    return showCardAni && startMove ? 'card-slide-left' : 'static-card-translate-x'
  }

  const renderTaskAwardTip = () => {
    const { diff } = getTargetTimeDiff(curTaskInfo?.beginTime, multiResourceCurTime);
    if (checkTaskCountDown(curTaskInfo, multiResourceCurTime)) {
      const { isSameDay } = getTargetTimeDiff(curTaskInfo?.beginTime, multiResourceCurTime);
      const endOfD = dayjs(multiResourceCurTime).endOf('day');
      const isToday = isSameDay && endOfD.diff(curTaskInfo?.beginTime, 's') > 60;
      if (!isToday) {
        return '明日再来';
      }
      return (
        <div className="num-text">
          <CountDown
            diff={diff}
            formatFunc={(time) => {
              const { hour, min, second } = formatLeftTime(time * 1000);
              return `${hour}:${min}:${second}`;
            }}
            onComplete={() => {
              dispatch.resource.queryResource({ firstInit: false });
              dispatch.task.queryTaskList(false);
            }}
          />
        </div>
      );
    }
    return (
      <Fragment>
        完成得抽奖次数
      </Fragment>
    );
  }

  const renderCommonBtn = () => {
    return (
      <button
        className={`lottery-btn glow-btn ${drawChance > 0 ? 'yellow-btn' : ''} ${showSingle ? '' : 'lottery-btn-min'}`}
        onClick={() => handleDraw(false, drawChance)}
      >
        <span>{drawChance > 0 ? `继续抽卡*${drawChance}` : '邀1人再抽1次'}</span>
        {luckyChance && <div className="common-tip flex-center">再抽一次必得</div>}
        {luckyTimes > 0 && <div className="btn-desc mx-auto">最多抽{luckyTimes}次必得膨胀卡</div>}
      </button>
    );
  };
  const renderAdTaskBtn = () => {
    return (
      <button className={`lottery-btn glow-btn ${showSingle ? '' : 'lottery-btn-min'}`} onClick={handleTask}>
        <span>{adTaskBtnText}</span>
        <div className="common-tip flex-center">{renderTaskAwardTip()}</div>
      </button>
    );
  };
  const renderLeftBtn = () => {
    if (drawChance || !showAdTask) {
      return renderCommonBtn();
    }
    return renderAdTaskBtn();
  };
  return (
    <Fact expoLogkey="fdcard_noawardpop_exposure" c="fdcard" d="award" noUseClick expoExtra={{
      status: cardStatus,
      ...baseFactParams,
    }} className="no-win-wrapper wrapper"
    >
      <div className="title-wrapper" style={{opacity: showTitle ? 1 : 0}}>
        <div className="title mx-auto">还可继续抽膨胀卡</div>
        <div className="subtitle mx-auto">
          { subTitle }
        </div>
      </div>
      <div className={`no-win-content`}>
        <div className={`card ${showCardAni ? 'no-award-card-ani' : ''} ${getCardClass()}`}>
          <img src={NoCardImg} />
        </div>
        {renderHelpPlantCards()}
      </div>
      <div className={`btn-wrapper mx-auto ${showSingle ? 'j-center' : ''}`} style={{opacity: showTitle ? 1 : 0}}>
        {renderLeftBtn()}
        {
          !showSingle && showHelpPlantCards ?
            <button className="white-btn" onClick={handleToHelpPlant}>
              <span>{unReceiveAward ? '去领取肥料' : '去看看排名'} </span>
            </button> : null
        }
      </div>
    </Fact>
  );
}

export default NoAward;
