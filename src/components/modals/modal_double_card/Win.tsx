import React, { <PERSON><PERSON><PERSON>, Animation<PERSON><PERSON><PERSON>and<PERSON>, Fragment, useEffect, useRef, useState } from 'react';
import './index.scss';
import useMxState from '@/hooks/useMxState';
import { QueryCardInfoRes } from '@/api/doublePointsCard/typings';
import Fact from '@/components/Fact';
import cardImg from './images/double-card.png';
import maxCardImg from './images/double-card-max.png';
import Lottie, { AnimationItem } from 'lottie-web';
import InfoImg from './images/info.png';
import DividerImg from './images/divider.png';
import Times from './images/times.png'
import Equal from './images/double-card-equal.png'
import toast from "@/lib/universal-toast/component/toast";
import NumberScroll from "@/pages/index/components/FertilizerNotify/components/NumberScroll";

interface IProps {
  baseFactParams: any;
  getIfTopStat: () => string;
  cardTimes: number;
  isMaxMultiplier: boolean;
  refreshCardTimes: boolean;
  drawLimit: boolean;
  handleDraw: (refresh: boolean, chance: number) => void;
  handleToTask: () => void;
  showCardAni?: boolean;
}

function Win(props: IProps) {
  const [doublePointsCard] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { todayCardInfo, drawInfo } = doublePointsCard;
  const { drawChance = 0 } = drawInfo || {};
  const { doubleNum = 100, doubleNumLimit = 0, doubleAmountLimit = 0, totalAmount = 0, canReceiveDoubleAmount = 0 } = todayCardInfo || {};
  // 达到肥料最高限额
  const reachAmountLimit = !!(canReceiveDoubleAmount && canReceiveDoubleAmount >= doubleAmountLimit);
  const [showCard, setShowCard] = useState<boolean>(false);
  const [showMulti, setShowMulti] = useState<boolean>(false);
  const [showRegular, setShowRegular] = useState<boolean>(false);
  const [startMove, setStartMove] = useState<boolean>(false);
  const [startJump, setStartJump] = useState<boolean>(false);
  const cardHighlightAnim = useRef<AnimationItem | null>(null);
  const ribbonAnim = useRef<AnimationItem | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout[]>([]);
  const [showCardHighlightAnim, setShowCardHighlightAnim] = useState<boolean>(false);
  const [showNumberScroll, setShowNumberScroll] = useState<boolean>(false);
  // 当前卡膨胀倍数
  const currentTimes = doubleNum / 100;
  const {
    baseFactParams,
    getIfTopStat, cardTimes, isMaxMultiplier, refreshCardTimes, drawLimit,
    handleDraw,
    handleToTask,
    showCardAni
  } = props;
  useEffect(() => {
    setShowCard(true);
    if (!showCardAni) {
      setShowMulti(true);
      setShowRegular(true);
      return;
    }
    const timer1 = setTimeout(() => setShowMulti(true), 300);
    const timer2 = setTimeout(() => setStartMove(true), 800);
    const timer3 = setTimeout(() => setShowRegular(true), 1200);
    timeoutRef.current.push(timer1, timer2, timer3);
  }, []);

  useEffect(() => {
    // 清理
    return () => {
      timeoutRef.current.forEach((timerId) => clearTimeout(timerId));
      timeoutRef.current = [];
      cardHighlightAnim.current?.destroy()
      cardHighlightAnim.current = null
      ribbonAnim.current?.destroy()
      ribbonAnim.current = null
    };
  }, []);

  const handleSmallCardAnimComplete = () => {
    setShowCardHighlightAnim(false)
  }
  useEffect(() => {
    if (!showCardAni) return;
    if (!showRegular) return;
    cardHighlightAnim.current = Lottie.loadAnimation({
      name: 'highlight-shine',
      container: document.getElementById('highlight-shine') as HTMLElement,
      renderer: 'canvas',
      loop: false,
      autoplay: false,
      animationData: require('./ani/highlight.json'),
      assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202507/5d06dc/images/',
    });
    cardHighlightAnim.current.addEventListener('complete', handleSmallCardAnimComplete)
    return () => {
      cardHighlightAnim.current?.removeEventListener('complete', handleSmallCardAnimComplete)
    }
  }, [showRegular])
  const handleCardAnimationStart = (e: AnimationEvent<HTMLDivElement>) => {
    if (e.animationName === 'winCardAnimation') {
      const timer1 = setTimeout(() => {
        setStartJump(true);
        setShowNumberScroll(true);
        const timer2 = setTimeout(() => {
          // 小卡高光动画
          setShowCardHighlightAnim(true);
          cardHighlightAnim.current?.play();
        }, 200);
        timeoutRef.current.push(timer2);
      }, 560);
      timeoutRef.current.push(timer1);
    }
  };

  useEffect(() => {
    if (!showCardAni) return;
    // 彩带动画
    if (isMaxMultiplier) {
      ribbonAnim.current = Lottie.loadAnimation({
        name: 'ribbon-max',
        container: document.getElementById('ribbon-anim') as HTMLElement,
        renderer: 'canvas',
        loop: false,
        autoplay: true,
        animationData: require('./ani/ribbon-max.json'),
        assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202507/3f12c8/images/',
      });
    } else if (refreshCardTimes) {
      ribbonAnim.current = Lottie.loadAnimation({
        name: 'ribbon-upgrade',
        container: document.getElementById('ribbon-anim') as HTMLElement,
        renderer: 'canvas',
        loop: false,
        autoplay: true,
        animationData: require('./ani/ribbon-upgrade.json'),
        assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202507/6947ec/images/',
      });
    } else {
      ribbonAnim.current = Lottie.loadAnimation({
        name: 'ribbon',
        container: document.getElementById('ribbon-anim') as HTMLElement,
        renderer: 'canvas',
        loop: false,
        autoplay: true,
        animationData: require('./ani/ribbon.json'),
        assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202507/787946/images/',
      });
    }
  }, []);

  const handleLimitToast = () => {
    if (!reachAmountLimit) {
      return;
    }
    toast.show('膨胀肥料已到达上限，做任务肥料奖励正常发放，但超出部分不再膨胀', {
      style: {
        maxWidth: '544rpx',
        bottom: '316rpx',
        lineHeight: '40rpx',
      }
    });
  }

  // 抽奖结果：1. 有奖，展示【常规态】
  const renderWinRegular = () => {
    function formatMillionNum(number: number) {
      if (number >= 1e6) {
        return (
          <Fragment>
            {Math.floor(number / 10000)}
            <span className="wan-text">万</span>
          </Fragment>
        );
      }
      // 小于100万时直接返回整数部分
      return Math.floor(number).toString();
    }
    // 有卡刷新次数限制
    const isReachOneMillion = canReceiveDoubleAmount >= 10e5
    return (
      <Fact expoLogkey="fdcard_card_exposure" c="fdcard" d="award" expoExtra={{
        base_multiplier: cardTimes,
        if_top: getIfTopStat(),
        if_max: reachAmountLimit ? 1 : 0,
        ...baseFactParams,
      }} className="win-wrapper wrapper regular-content"
      >
        <div className="title  mx-auto">膨胀卡生效中</div>
        <div className="subtitle  mx-auto">今日所赚的肥料均可膨胀</div>
        <div className="content-wrapper  mx-auto">
          <div className="award-value  mx-auto">
            {
              (!showCardAni || canReceiveDoubleAmount === 0) ? (canReceiveDoubleAmount || 0) : null
            }
            {
              showCardAni && showNumberScroll && canReceiveDoubleAmount > 0 ?
                <div className="card-fertilizer-num-wrapper">
                  <NumberScroll duration={500} value={canReceiveDoubleAmount} delayPerDigit={50} digitHeight={50} />
                </div> : null
            }
            {
              (!showCardAni || showNumberScroll) &&
              <div className={`tip ${isReachOneMillion ? 'tip-relative' : ''}`} onClick={handleLimitToast}>
                <span>{reachAmountLimit ? '已达上限' : '多赚多得'}</span>
                {reachAmountLimit && <img className="info-icon" src={InfoImg} alt="" />}
              </div>
            }
          </div>
          <div className="desc mx-auto">
            <span className="strong">明日到访</span>
            即发放
          </div>
          <div className="border-line  mx-auto">
            <img src={DividerImg} alt="" />
          </div>
          <div className="calculate mx-auto">
            <div className="number left">
              {formatMillionNum(totalAmount)}
              <div className="label">今日已得肥料</div>
            </div>
            <img className="operator-img operator-x" src={Times} alt="乘号" />
            <div className={`number ${isMaxMultiplier ? 'middle-max' : ''} middle ${startJump ? 'jump-anim' : ''}`}>
              <span className="highlight">{currentTimes}</span>
              <div className="label">{isMaxMultiplier ? '已达最高倍' : '膨胀倍数'}</div>
              <div id="highlight-shine" className={`${showCardHighlightAnim ? '' : 'opacity-0'}`} />
            </div>
            <img className="operator-img operator-equals" src={Equal} alt="" />
            <div className="number right">
              {formatMillionNum(totalAmount + canReceiveDoubleAmount)}
              <div className="label">膨胀后总数</div>
            </div>
          </div>
        </div>
        <div className="btn-wrapper mx-auto">
          <button className={`flex-center btn ${drawLimit ? 'task-btn mx-auto glow-btn' : 'white-btn'}`} onClick={handleToTask}>
            <span>做任务赚肥料</span>
          </button>
          {!drawLimit &&
            (drawChance > 0 ? (
              // 有次数
              <button className="glow-btn yellow-btn btn flex-center" onClick={() => handleDraw(true, drawChance)}>
                <span>马上提升倍数*{drawChance}</span>
              </button>
            ) : (
              // 无次数
              <button className="glow-btn invite btn" onClick={() => handleDraw(true, drawChance)}>
                <span>邀1人提高倍数</span>
                <div className="common-tip flex-center">最高可达{doubleNumLimit / 100}倍</div>
              </button>
            ))}
        </div>
        <div className="delay-desc mx-auto flex-center">数据更新存在延迟</div>
      </Fact>
    );
  };

  return (
    <Fragment>
      {
        !showRegular &&
        <Fact
          expoLogkey="fdcard_awardpop_exposure"
          c="fdcard"
          d="award"
          expoExtra={{
            base_multiplier: cardTimes,
            if_top: getIfTopStat(),
            type: doubleNum > 0 ? '2' : '1', // 1抽卡 2提高倍数
            ...baseFactParams,
          }}
          className="win-wrapper wrapper"
        >
          <div className={`title mx-auto ${showMulti ? '' : 'opacity-0'}`}>
            恭喜!{refreshCardTimes ? '倍数已升级' : '获得膨胀卡'}
          </div>
          <div className={`subtitle mx-auto ${showMulti ? '' : 'opacity-0'}`}>
            {isMaxMultiplier ? (
              `已获最高倍数，今日赚取的肥料膨胀${cardTimes}倍`
            ) : (
              <Fragment>
                今日赚取的所有肥料
                <span className="strong">膨胀{cardTimes}倍啦!</span>
              </Fragment>
            )}
          </div>
          <div id="ribbon-anim" />
        </Fact>
      }
      {
        showRegular && renderWinRegular()
      }
      {showCard && showCardAni && (
        <div onAnimationStart={handleCardAnimationStart} className={`card win-card ${isMaxMultiplier ? 'card-max' : ''} ${startMove ? 'card-move' : ''}`}>
          <img className="card-img" src={isMaxMultiplier ? maxCardImg : cardImg} alt="" />
          {showMulti && (
            <span className={`multiplier ${isMaxMultiplier ? 'max-multiplier' : 'common-multiplier'}`}>
              <i>x</i>{isMaxMultiplier ? doubleNumLimit / 100 : cardTimes}
            </span>
          )}
        </div>
      )}
    </Fragment>
  );
}

export default Win;
