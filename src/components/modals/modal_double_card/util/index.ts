import { isSameDay } from "@/lib/utils/date";
import { LocalStorageKey } from "@/lib/utils/localStorage_constant";
import { mx } from "@ali/pcom-iz-use";

export const checkIsShowAwardTip = () => {
  const doublePointsCard = mx.store.get('doublePointsCard')
  const curTime = doublePointsCard.curTime || Date.now();
  const yesterdayCardInfo = doublePointsCard?.yesterdayCardInfo
  const checkIsShow = () => {
    const showTime = localStorage.getItem(LocalStorageKey.DOUBLE_CARD_YESTERDAY_AWARD);
    if (!showTime) {
      return false;
    }
    return isSameDay(Number(showTime), curTime);
  }
  const isShowAward = yesterdayCardInfo && yesterdayCardInfo.received && yesterdayCardInfo?.canReceiveDoubleAmount > 10 && !checkIsShow();
  return isShowAward
};
