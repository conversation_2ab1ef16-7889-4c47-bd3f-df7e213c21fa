@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(120%);
  }
}

.modal-double-card-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  .opacity-0 {
    opacity: 0 !important;
  }
  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }
  .flex-center {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .justify-center {
    justify-content: center !important;
  }

  .rule-wrap {
    z-index: 3;
    .rule-container {
      top: 69rpx;
      height: 101rpx;
      opacity: 0.25;
      background-image: linear-gradient(179deg, #ffc032 2%, rgba(255, 192, 50, 0.12) 100%);
      border: 0 solid #ffffff;
      border-radius: 0 15rpx 15rpx 0;
    }
    .rule {
      position: absolute;
      z-index: 2;
      left: 0;
      width: 52rpx;
    }
    .rule-text {
      top: 82rpx;
      white-space: wrap;
      text-wrap: wrap;
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #6d2500;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }

  .common-tip {
    position: absolute;
    top: -33rpx;
    right: 0;
    height: 48rpx;
    background-image: linear-gradient(269deg, #7f2500 0%, #2f0e00 96%), linear-gradient(90deg, #ffe956 6%, #ffcb00 100%),
      linear-gradient(228deg, #ff9832 0%, #ffc47d 90%);
    border: 2rpx solid #ffffff;
    border-radius: 34rpx 34rpx 34rpx 6rpx;
    padding: 0 25.5rpx;
    font-family: PingFangSC-Medium;
    font-size: 24rpx;
    color: #ffe5c3;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
  }
  .modal-double-card {
    position: relative;
    width: 100vw;
    height: 909rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 48rpx 48rpx 0 0 !important;
    animation: slideInUp 0.3s ease-out forwards;
    background: #fff;
    .modal-header-bg {
      position: absolute;
      left: 0;
      top: -21rpx;
      width: 100%;
      height: 293rpx;
      z-index: 1;
    }

    &.leave {
      animation: slideOutDown 0.3s ease-in forwards;
    }
    .glow-btn {
      &::after {
        content: '';
        z-index: 1;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('images/double-card-glow-bg.png');
        background-repeat: no-repeat;
        background-size: contain;
        background-position: bottom -36rpx right 50%;
        uc-perf-stat-ignore: image;
      }
    }
    .wrapper {
      width: 100%;
      height: 100%;
      background-size: contain;
      background-repeat: no-repeat;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 48rpx 48rpx 0 0;
    }
    .idle-bg {
      position: absolute;
      top: -21rpx;
      left: 0;
      width: 100%;
      height: 293rpx;
    }
    .award-tip {
      position: absolute;
      top: -147rpx;
      left: 134rpx;
      width: 503rpx;
      height: 115rpx;
      background-image: linear-gradient(90deg, rgba(92,116,134,0.81) 2%, rgba(75,98,115,0.77) 82%);
      border-radius: 24rpx;
      .uu-img {
        width: 163rpx;
        height: 145rpx;
        position: absolute;
        left: -19rpx;
        top: -30rpx;
      }
      .tip-text {
        position: absolute;
        font-family: FZLanTingYuanS-Extra-Bold;
        left: 146rpx;
        top: 20rpx;
        font-size: 28rpx;
        color: #FFFFFF;
        letter-spacing: 0;
        font-weight: 400;
      }
      .award-arrow {
        position: absolute;
        top: 67rpx;
        left: 146rpx;
        height: 29rpx;
      }
      .award-num {
        position: absolute;
        top: 53rpx;
        left: 203rpx;
        .shadow-num {
          z-index: 1;
          position: absolute;
          top: 0;
          left: 0;
          font-family: D-DIN-Bold;
          font-size: 54rpx;
          letter-spacing: 0;
          color: transparent;
          text-shadow: 0 3rpx 0 #8A5038;
        }
        .num {
          z-index: 2;
          position: absolute;
          top: 0;
          left: 0;
          font-family: D-DIN-Bold;
          font-size: 54rpx;
          letter-spacing: 0;
          background-image: linear-gradient(180deg, #FFDF80 0%, #FFFFFF 100%);
          background-clip: text;
          color: transparent;
        }
      }
    }
    .start-tip {
      position: absolute;
      top: -96rpx;
      left: 44rpx;
      width: 662rpx;
      height: 64rpx;
      display: flex;
      flex-direction: row;
      background-image: linear-gradient(90deg, rgba(92, 116, 134, 0.81) 2%, rgba(75, 98, 115, 0.77) 82%);
      border-radius: 40px;
      align-items: center;
      justify-content: center;
      font-family: FZLanTingYuanS-Extra-Bold;
      font-size: 26rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      .tip-text {
        width: 70rpx;
        height: 32rpx;
        margin-right: 14rpx;
      }
      .tip-arrow {
        width: 24rpx;
        height: 24rpx;
        margin: 0 10rpx;
      }
    }
    .idle-wrapper {
      position: relative;
      .title-img {
        height: 140rpx;
        margin-top: 45rpx;
        z-index: 2;
      }
      .must-double-title {
        height: 123rpx !important;
      }
      .subtitle {
        margin-top: 5rpx;
        background: rgb(250, 140, 37, 0.12);
        width: fit-content;
        height: 57rpx;
        border-radius: 28rpx;
        padding: 0 30rpx;
        text-align: center;
        letter-spacing: 0;
        color: #010101;
        font-weight: 400;
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        z-index: 1;
        .strong {
          color: #fe3e22;
        }
      }
      .first-draw {
        margin-top: 13rpx;
        width: 500rpx;
        height: 440rpx;
      }

      #guidance-hand {
        position: absolute;
        right: -20rpx !important;
        top: -19.5rpx !important;
        width: 232rpx !important;
        height: 232rpx !important;
        transform-origin: 0 0;
      }

      .lottery-btn {
        position: relative;
        background-image: linear-gradient(180deg, #ff7049 0%, #ff3707 37%, #ff5c1d 77%, #fe9555 100%);
        border-radius: 55rpx;
        width: 526rpx;
        height: 110rpx;
        border: none;
        span {
          font-family: PingFangSC-Semibold;
          font-size: 40rpx;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 600;
        }
      }
    }
    .win-wrapper {
      padding-top: 55rpx;
      box-sizing: border-box;
      #ribbon-anim {
        position: absolute;
        z-index: 4 !important;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 0;
      }
      .title {
        z-index: 2;
        font-family: FZLanTingYuanS-Extra-Bold;
        font-size: 55rpx;
        background-image: linear-gradient(180deg, rgba(1, 1, 1, 1) 0%, rgba(1, 1, 1, 1) 20%, #76000a 100%);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        line-height: 64rpx;
        // text-shadow: -2rpx -2rpx 0 #fff, 2rpx -2rpx 0 #fff, -2rpx 2rpx 0 #fff, 2rpx 2rpx 0 #fff;
      }
      .subtitle {
        z-index: 2;
        margin-top: 12rpx;
        font-family: PingFangSC-Regular;
        font-size: 30rpx;
        color: #12161a;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        line-height: 42rpx;
        .strong {
          color: #fa6425 !important;
        }
      }
      .content-bg {
        position: relative;
        width: 604.21rpx;
        height: 560rpx;
        margin-top: 13rpx;

        .card {
          position: absolute;
          top: 62rpx;
          left: 127.32rpx;
          width: 355rpx;
          height: 455rpx;
          z-index: 2;
          .card-img {
            width: 100%;
            height: 100%;
          }
        }
        .card-max {
        }
      }
      .content-wrapper {
        width: 690rpx;
        height: 428rpx;
        z-index: 2;
        margin-top: 25rpx;
        border-radius: 46rpx;
        background: #fff0ea;
        padding-top: 40rpx;
        box-sizing: border-box;
        .award-value {
          position: relative;
          font-family: D-DIN-Bold;
          font-size: 100rpx;
          color: #fa6425;
          width: fit-content;
          letter-spacing: -1.67rpx;
          text-align: center;
          display: flex;
          flex-direction: row;
          height: 100rpx;
          .tip-relative {
            position: relative !important;
            margin-top: 15rpx !important;
            margin-left: 8.5rpx !important;
            top: 0 !important;
            left: 0 !important;
          }
          .tip {
            position: absolute;
            left: calc(100% + 8.5rpx);
            top: 15rpx;
            height: 45rpx;
            padding: 0 15rpx;
            width: fit-content;
            background: #fa6425;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            border-radius: 34rpx 34rpx 34rpx 6rpx;
            // border-radius: 50rpx 50rpx 50rpx 12rpx;
            padding-top: 2rpx;
            white-space: nowrap;
            span {
              font-family: PingFangSC-Medium;
              font-size: 24rpx;
              color: #ffffff;
              letter-spacing: 0;
              font-weight: 500;
            }
            .info-icon {
              width: 24rpx;
              height: 24rpx;
              margin-left: 3rpx;
            }
          }
        }
        .desc {
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          color: #12161a;
          letter-spacing: 0;
          line-height: 37rpx;
          font-weight: 400;
          margin-top: 5rpx;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          .strong {
            color: #fa6425 !important;
          }
        }
        .border-line {
          margin-top: 29rpx;
          position: relative;
          width: 570rpx;
          height: 15rpx;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .calculate {
          margin-top: 11rpx;
          height: 100rpx;
          display: flex;
          flex-direction: row;
          align-items: center;
          width: fit-content;
          .number {
            font-family: D-DIN-Bold;
            font-size: 60rpx;
            letter-spacing: -2.57rpx;
            text-align: center;
            height: 100%;
            text-align: center;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            .wan-text {
              font-family: FZLanTingYuanS-Extra-Bold;
              font-size: 40rpx;
              color: #12161a;
              margin-left: 5rpx;
              margin-top: 5.65rpx;
              letter-spacing: 0;
              text-align: center;
              font-weight: 400;
            }
          }
          .operator {
            font-family: FZLanTingYuanS-Extra-Bold;
            font-size: 40rpx;
            color: #341c00;
            letter-spacing: 0;
          }
          .operator-img {
            width: 36rpx;
            height: 36rpx;
          }
          .operator-x {
            margin-left: 13rpx;
            margin-right: 11rpx;
          }
          .operator-equals {
            margin-left: 14rpx;
            margin-right: 25rpx;
          }
          .label {
            position: absolute;
            left: 50%;
            bottom: -36rpx;
            transform: translateX(-50%);
            font-family: PingFangSC-Regular;
            font-size: 26rpx;
            color: #12161a;
            letter-spacing: 0;
            text-align: center;
            font-weight: 400;
            text-wrap: nowrap;
            white-space: nowrap;
          }
          .left {
            color: #12161a;
            position: relative;
          }
          .right {
            color: #12161a;
            position: relative;
          }
          .middle-max {
            background-image: url('./images/double-bg-max.png') !important;
            uc-perf-stat-ignore: image;
          }
          .middle {
            position: relative;
            height: 100rpx;
            width: 140rpx;
            background-image: url('images/double-card-mini-bg.png');
            uc-perf-stat-ignore: image;
            background-size: contain;
            background-repeat: no-repeat;
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            .highlight {
              font-family: Gilroy-ExtraBoldItalic;
              font-size: 45rpx;
              background-image: linear-gradient(180deg, #ffffff 0%, #fffdaa 50%, #fffdaa 100%);
              -webkit-background-clip: text;
              background-clip: text;
              color: transparent;
              letter-spacing: 0;
              text-align: center;
              // text-shadow: 0 3rpx 2rpx rgba(199, 76, 0, 0.65);
              margin-left: -3rpx;
              margin-top: 4rpx;
            }
            #highlight-shine {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translateX(-50%) translateY(-50%);
              width: 85%;
              height: 80%;
              margin: auto;
              z-index: 3;
            }
          }
        }
      }
      .btn-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 54rpx;
        width: 670rpx;
        justify-content: space-between;
        .invite {
          background-image: linear-gradient(180deg, #ff7049 0%, #ff3707 37%, #ff5c1d 77%, #fe9555 100%);
          border-radius: 55rpx;
        }
        .task-btn {
          background-image: linear-gradient(180deg, #ff7049 0%, #ff3707 37%, #ff5c1d 77%, #fe9555 100%);
          border-radius: 58rpx;
          width: 526rpx !important;
        }
        .btn {
          position: relative;
          border-radius: 55rpx;
          width: 366rpx;
          height: 110rpx;
          border: none;
          span {
            font-family: PingFangSC-Semibold;
            font-size: 34rpx;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            font-weight: 600;
          }
          .tip {
            position: absolute;
            top: -33rpx;
            right: 0;
            height: 48rpx;
            background-image: linear-gradient(90deg, #ffe956 6%, #ffcb00 100%),
              linear-gradient(227deg, #ff9832 0%, #ffc47d 90%);
            border: 2rpx solid #ffffff;
            border-radius: 34rpx 34rpx 34rpx 6rpx;
            padding: 0 25.5rpx;
            font-family: PingFangSC-Medium;
            font-size: 24rpx;
            color: #6d0000;
            letter-spacing: 0;
            text-align: center;
            font-weight: 500;
          }
        }
        .white-btn {
          background: #ffffff;
          border: 3rpx solid #ff5d30;
          border-radius: 55rpx;
          width: 284rpx;
          height: 110rpx;
          span {
            font-family: PingFangSC-Semibold;
            font-size: 34rpx;
            color: #fa3600;
            letter-spacing: 0;
            text-align: center;
            font-weight: 600;
          }
        }
      }
      .delay-desc {
        text-align: center;
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #aab5bb;
        letter-spacing: 0;
        text-align: center;
        margin-top: 20rpx;
        font-weight: 400;
      }
    }
    .regular-content {
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      .card-fertilizer-num-wrapper {
        width: 100%;
        // 折叠屏居中
        text-align: center;
        display: flex;
        flex-direction: row;
        align-items: center;
        .number-strip-container {
          margin: 0;
          .number-strip-window {
            height: 50px;
          }
        }
        .number-item {
          font-family: D-DIN-Bold;
          height: 50px;
          width: auto;
          font-size: 50px;
          color: #fa6425 !important;
          user-select: none;
          letter-spacing: -2px;
        }
      }
    }
    #drawLottie {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3;
    }
    .draw-result {
      width: 100%;
      height: 100%;
    }
    .no-win-wrapper {
      padding-top: 55rpx;
      .no-win-bg {
        position: absolute;
        top: -20rpx;
        left: 0;
        width: 100%;
        height: 273rpx;
      }
      .title {
        z-index: 2;
        font-family: FZLanTingYuanS-Extra-Bold;
        font-size: 55rpx;
        color: #000000;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
      }
      .subtitle {
        z-index: 2;
        margin-top: 10rpx;
        font-family: PingFangSC-Regular;
        font-size: 32rpx;
        color: #12161a;
        line-height: 42rpx;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        .strong {
          font-family: PingFangSC-Semibold;
          font-size: 32rpx;
          color: #fa6425;
          letter-spacing: 0;
          text-align: center;
          line-height: 45rpx;
          font-weight: 600;
        }
      }
      .no-win-content {
        z-index: 2;
        margin-top: 48rpx;
        display: flex;
        flex-direction: row;
        width: 660rpx;
        align-items: center;
        justify-content: center;
        position: relative;
        .card {
          width: 320rpx;
          height: 409rpx;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
          .card-content {
            position: relative;
            width: 100%;
            height: 100%;
            .manure-amount {
              position: absolute;
              width: 100%;
              text-align: center;
              top: 193rpx;
              font-size: 60rpx;
              color: #7b4932;
              letter-spacing: -2.73rpx;
            }
            .cash-amount {
              position: absolute;
              width: 100%;
              text-align: center;
              top: 200rpx;
              font-size: 67rpx;
              letter-spacing: -1.05rpx;
              color: #fef2de;
              .cash-unit {
                font-size: 28rpx;
              }
            }
            .card-desc {
              position: absolute;
              width: 100%;
              bottom: 28rpx;
              text-align: center;
              font-family: PingFangSC-Regular;
              font-size: 26rpx;
              color: #ffffff;
              letter-spacing: 0;
              font-weight: 400;
            }
          }
        }
        .help-card {
          position: absolute;
          right: 0;
        }
      }
      .j-center {
        justify-content: center !important;
      }
      .btn-wrapper {
        margin-top: 70rpx;
        width: 670rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .white-btn {
          background: #ffffff;
          border: 3rpx solid #ff5d30;
          border-radius: 55rpx;
          width: 322rpx;
          height: 110rpx;
          span {
            font-family: PingFangSC-Semibold;
            font-size: 34rpx;
            color: #fa3600;
            letter-spacing: 0;
            text-align: center;
            font-weight: 600;
          }
        }
        .lottery-btn {
          position: relative;
          background-image: linear-gradient(180deg, #ff7049 0%, #ff3707 37%, #ff5c1d 77%, #fe9555 100%);
          border-radius: 55rpx;
          width: 470rpx;
          height: 110rpx;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          border: none;
          span {
            font-family: PingFangSC-Semibold;
            font-size: 38rpx;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            font-weight: 600;
          }
        }
        .lottery-btn-min {
          width: 328rpx;
          span {
            font-size: 34rpx !important;
          }
        }
      }
      .btn-desc {
        position: absolute;
        bottom: -45rpx;
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #859199;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        line-height: 33rpx;
      }
    }
    .double-card-close {
      position: absolute;
      top: 26rpx;
      right: 26rpx;
      width: 60rpx;
      height: 60rpx;
      background-size: cover;
      z-index: 20;
    }

    .lottery-wrapper {
      height: 420rpx;
    }
    .yellow-btn {
      background-image: linear-gradient(0deg, #ffe844 0%, #ffe31c 25%, #ffcf28 100%),
        linear-gradient(222deg, #ff9832 0%, #ffc47d 90%) !important;
      span {
        color: #6d0000 !important;
      }
    }
  }
  .title-wrapper {
    z-index: 1;
  }
  .win-card {
    position: absolute;
    top: 247.5rpx;
    transform-origin: top center;
    width: 355rpx;
    height: 455rpx;
    overflow: hidden;
    z-index: 3;
    img {
      width: 100%;
      height: 100%;
    }
    .multiplier {
      position: absolute;
      padding: 0 8rpx;
      left: 50%;
      transform: translateX(-50%);
      font-weight: bold;
      font-family: Gilroy-ExtraBoldItalic;
      background-image: radial-gradient(circle, rgb(255, 238, 98) 0%, rgba(255, 250, 199, 1) 50%, rgb(255, 238, 98) 100%);
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      font-size: 92.45rpx;
      i {
        font-size: 78rpx;
        margin-right: 6rpx;
      }
    }
    .common-multiplier {
      bottom: 25rpx;
      font-size: 92.45rpx;
    }
    .max-multiplier {
      bottom: 43rpx;
      font-size: 92.45rpx;
    }
  }
  .calculate .middle.jump-anim {
    animation: smallCardJump 0.2s cubic-bezier(0.11, 0, 0.5, 0);
  }
  .win-card {
    animation: cardScaleAnimation 0.2s cubic-bezier(0.37, 0, 0.63, 1);
  }
  .win-card.card-move {
    animation: winCardAnimation 1s cubic-bezier(0.11, 0, 0.5, 0) 0.2s forwards;
    transform-origin: center center;
  }

  .no-award-card-ani {
    transform-origin: top center; /* 动画的缩放中心点 */
    animation: cardScaleAnimation 0.2s cubic-bezier(0.37, 0, 0.63, 1);
  }

  /* 第二个动画的类 */
  .card-slide-left {
    animation: cardSlideLeft 0.3s ease-out 0.16s forwards; /* 第二个动画：向左平移，延迟0.17秒 */
  }

  .static-card-translate-x {
    transform: translateX(-170rpx);
  }
}


/* 定义第一个动画：缩放和位移 */
@keyframes cardScaleAnimation {
  0% {
    transform: scale(1.28) translateY(-74px); /* 初始状态：缩放128%，向上位移74rpx */
  }
  100% {
    transform: scale(1) translateY(0); /* 结束状态：缩放100%，位移0 */
  }
}
/* 定义第二个动画：向左平移 */
@keyframes cardSlideLeft {
  0% {
    transform: translateX(0); /* 初始状态：不平移 */
  }
  100% {
    transform: translateX(-170rpx); /* 结束状态：向左平移170px */
  }
}

@keyframes winCardAnimation {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  13% {
    transform: translateY(40rpx) scale(1);
    opacity: 1;
  }
  26% {
    transform: translateY(-247rpx);
    opacity: 1;
  }
  46% {
    transform: translateY(-160rpx) scale(0.3);
    opacity: 1;
  }
  56% {
    transform: translateY(-60rpx) scale(0.3);
    opacity: 0;
  }
  66% {
    transform: translateY(0) scale(0.3);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scale(0.3);
    opacity: 0;
  }
}

@keyframes smallCardJump {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(13rpx);
  }
  100% {
    transform: translateY(0);
  }
}
