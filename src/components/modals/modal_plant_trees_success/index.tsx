import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { openPage } from '@/lib/ucapi/index';
import { MODAL_ID } from '@/components/modals/types';
import giftImg from './assets/gift_success.png';
import { TaobaoRouseApp } from '@/lib/utils/rouseApp'
// import modalCloseImg from '../assets/modal-close.png';

// 种成
const Index = (props) => {
  const { completeFruit, seedName, seedSubTitle, exchangeUrl } = props;
  useEffect(() => {
    setTimeout(() => {
      plantTreesSuccess();
    }, 3000);
  }, []);
  // 种成
  const plantTreesSuccess = () => {
    if (exchangeUrl) TaobaoRouseApp(exchangeUrl, "reap")
    close();
  };
  // 关闭弹窗
  const close = () => {
    baseModal.close(MODAL_ID.PLANT_TREES_SUCCESS);
  };
  return (
    <div className="plant-trees-success">
      <div className="plant-trees-gift-panl">
        <img className="giftImg-bg" src={giftImg} alt="" />
        <span className="manure">
          {seedName}
          {seedSubTitle}
        </span>
        <span className="plant-trees-gift-title">感谢你第{completeFruit}次种成!</span>
      </div>
      <div className="schedule loading-dots">页面跳转中</div>
      {/* <div onClick={plantTreesSuccess} className="get-plant-trees-gift">
        立即兑换
      </div> */}
      {/* <img onClick={close} className="close-img" src={modalCloseImg} alt="" /> */}
    </div>
  );
};

export default Index;
