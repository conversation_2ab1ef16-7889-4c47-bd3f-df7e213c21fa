.plant-trees-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .plant-trees-gift-panl {
    position: relative;
    .giftImg-bg {
      width: 670rpx;
      height: 653rpx;
      background-size: cover;
    }
    .manure {
      position: absolute;
      left: 50%;
      bottom: 139rpx;
      transform: translateX(-50%);
      padding: 10rpx 40rpx;
      background: #ffeab8;
      border-radius: 24rpx;
      font-family: PingFangSC-Semibold;
      font-size: 42rpx;
      color: #8b553a;
      text-align: center;
      font-weight: 700;
      white-space: nowrap;
    }
    .plant-trees-gift-title {
      position: absolute;
      left: 50%;
      bottom: 40rpx;
      transform: translateX(-50%);
      font-family: PingFangSC-Semibold;
      font-size: 42rpx;
      color: #08881f;
      text-align: center;
      font-weight: 700;
      white-space: nowrap;
    }
  }
  .schedule {
    width: 150rpx;
    margin-top: 32rpx;
    overflow: hidden;
    text-overflow: clip;
    white-space: nowrap;
    animation: ellipsisScroll 5s infinite step-end;
    font-family: PingFangSC-Medium;
    font-size: 24rpx;
    color: #ffffff;
    font-weight: 500;
  }
  // .get-plant-trees-gift {
  //   margin-top: 40rpx;
  //   width: 470rpx;
  //   height: 100rpx;
  //   line-height: 100rpx;
  //   background: #2ac638;
  //   border-radius: 50rpx;
  //   font-family: PingFangSC-Semibold;
  //   font-size: 36rpx;
  //   color: #ffffff;
  //   text-align: center;
  //   font-weight: 700;
  // }
  // .close-img {
  //   margin-top: 64rpx;
  //   width: 72rpx;
  //   height: 72rpx;
  //   background-size: cover;
  //   border-radius: 50%;
  // }
}

.loading-dots {
  display: inline-block;
  overflow: hidden;
  width: fit-content;
  white-space: nowrap;
}

.loading-dots::after {
  content: '...';
  animation: dots-loading 2s linear infinite;
}

@keyframes dots-loading {
  0%, 20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60% {
    content: '...';
  }
  80%, 100% {
    content: '';
  }
}

