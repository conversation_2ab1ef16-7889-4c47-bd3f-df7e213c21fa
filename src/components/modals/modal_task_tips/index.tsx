import React, {useEffect} from 'react';
import './index.scss';
import baseModal from '@/lib/modal';

import Img from './images/manure-taobao.png';
import CloseIcon from '../assets/modal-close.png';

interface IDataInfo {
  title: string;
  awardCount: number;
  btnText: string;
  img: any;
}
interface IProps {
  id: string;
  data: {
    dataInfo: IDataInfo;
    confirm: () => void;
    didMount: () => void;
  };
}

export default function Index(props: IProps) {
  const dataInfo = props?.data?.dataInfo || {};
  
  useEffect(() => {
    if (props?.data?.didMount) {
      props?.data?.didMount()
    }
  }, [])

  const handleConfirm = () => {
    if (props?.data?.confirm) {
      props?.data?.confirm();
    }
    handleClose();
  }
  
  const handleClose = () => {
    baseModal.close(props?.id);
  }

  return (
    <div className="task-modal-comp">
      <div className="title">{dataInfo?.title}</div>
      <div className="img">
        <img src={dataInfo?.img || Img} className="" alt="肥料" />
        <div className="manure-num">{dataInfo?.awardCount}肥料</div>
      </div>
      <div className="btn" onClick={handleConfirm}>{dataInfo?.btnText}</div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
}
