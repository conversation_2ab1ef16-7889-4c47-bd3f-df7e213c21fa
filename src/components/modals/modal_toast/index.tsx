import React, { useState, useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import stat from '@/lib/stat';
import { HELP_CODE_MAP } from '@/pages/index/utils';

import ToastSuccess from './images/toast-success.png';
import ToastFail from './images/toast-fail.png';

export interface IToast {
  id: string;
  toastData: {
    type: 'success' | 'fail';
    title: string;
    subTitle: string;
    delay?: number;
    shareType?: 'double' | 'normal';
  };
  errCode: string;
}

export default function Index(props: IToast) {
  const [isOut, setIsOut] = useState(false);
  const { toastData, errCode } = props;

  useEffect(() => {
    if (toastData.shareType === 'double') {
      stat.exposure('fdcard_shared_exposure', {
        c: 'fdcard',
        d: 'shared',
        share_status: toastData.type,
        share_fail_reason: HELP_CODE_MAP[errCode]?.status || errCode,
      });
      return;
    }

    // 原先的埋点逻辑
    // 分享者
    if (toastData?.type === 'success') {
      stat.exposure('sharer_toast_exposure', {
        c: 'toast',
        d: 'sharer',
        share_status: 1,
      });
    } else {
      // 助力者
      stat.exposure('shared_toast_exposure', {
        c: 'toast',
        d: 'shared',
        share_status: HELP_CODE_MAP[errCode]?.status || errCode,
      });
    }
  }, []);
  useEffect(() => {
    setTimeout(() => {
      setIsOut(true);
    }, toastData?.delay || 3000);
  }, []);

  const listenerAniEnd = (envent) => {
    if (envent?.animationName === 'modalToastOut') {
      baseModal.close(props?.id);
    }
  };

  const handleClickToast = () => {
    if (toastData.shareType === 'double') {
      stat.click('fdcard_shared_click', {
        c: 'fdcard',
        d: 'shared',
        share_status: toastData.type,
        share_fail_reason: HELP_CODE_MAP[errCode]?.status || errCode,
        click_position: 4,
      });
    }
  };
  return (
    <div onClick={handleClickToast} className={`moda-toast-card-comp `}>
      <div className={`toast-content ${isOut ? 'modal-toast-out' : ''}`} onAnimationEnd={listenerAniEnd}>
        <img src={toastData?.type === 'success' ? ToastSuccess : ToastFail} className="toast-icon" />
        <div className="right-wrap">
          <div className="title">{toastData?.title}</div>
          <div className="sub-title">{toastData?.subTitle}</div>
        </div>
      </div>
    </div>
  );
}
