.moda-toast-card-comp {
  z-index: 9999;
  position: relative;

  .toast-content {
    width: auto;
    height: 118rpx;
    border-radius: 24rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    position: absolute;
    top: 36vw;
    left: 50%;
    transform: translateX(-50%) translateY(0);
    animation: modalToastIn 0.5s ease-in forwards;

    .toast-icon {
      width: 88rpx;
      margin-right: 10rpx;
      uc-perf-stat-ignore: image;
    }

    .title {
      font-family: PingFangSC-Medium;
      font-size: 28rpx;
      color: #327714;
      letter-spacing: 0;
      font-weight: 500;
      word-wrap: break-word;
      white-space: nowrap;
      max-width: 500rpx;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .sub-title {
      margin-top: 4rpx;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #327714;
      letter-spacing: 0;
      font-weight: 400;
      word-wrap: break-word;
      white-space: nowrap;
      max-width: 500rpx;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}


.moda-toast-card-comp .modal-toast-out {
  animation: modalToastOut 0.5s ease-in forwards;
}

@keyframes modalToastIn {
  from {
    transform:translateX(-50%) translateY(0);
    opacity: 0;
  }

  to {
    transform: translateX(-50%) translateY(-80rpx);
    opacity: 1;
  }
}

@keyframes modalToastOut {
  from {
    transform: translateX(-50%) translateY(-80rpx);
    opacity: 1;
  }

  to {
    transform: translateX(-50%) translateY(-40rpx);
    opacity: 0;
  }
}
