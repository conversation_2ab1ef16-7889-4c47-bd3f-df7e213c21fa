import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import <PERSON>tie from 'lottie-web';
import baseModal from '@/lib/modal';
import { ISignPrize, SignType } from '@/logic/store/models/task/typing';
import stat from '@/lib/stat';
import { getMatryoshkaTask } from '../modal_progress_bubble/matryoshka';
import { taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import { getTaskAward } from '@/pages/index/components/TaskPop/TaskList/util';

import CloseIcon from '../assets/modal-close.png';

const sign_three = 'https://image.uc.cn/s/uae/g/1y/animate/202403/1f2d80/data.json';
const sign_seven = 'https://image.uc.cn/s/uae/g/1y/animate/202403/47fba4/data.json';

interface IProps {
  id: string;
  prizeInfo: ISignPrize;
  signDay: number;
  showRecommendTask: boolean;
  signType: SignType;
}

export default function Index(props: IProps) {
  let openBoxAnimation: any = useRef(null);
  const [ifShowManure, setIfShowManure] = useState(false);
  const [showBtn, setShowBtn] = useState<boolean>(false);
  const taskRef: any = useRef();

  const { prizeInfo, signDay, showRecommendTask, signType } = props;
  const rewardAmount = prizeInfo?.rewardItem?.amount;

  useEffect(() => {
    stat.exposure('signin_pop_exposure', {
      c: 'pop',
      d: 'signin',
      sign_number: signDay,
      sign_type: signType,
    });
    let result = getMatryoshkaTask();
    console.log('result===', result);

    if (showRecommendTask && result) {
      taskRef.current = result;
      stat.exposure('resource_exposure', {
        c: 'pop',
        d: 'matrioska',
        resource_location: 'matrioska_pop',
        sign_type: signType,
      })
      stat.exposure('task_exposure', {
        c: 'pop',
        d: 'matrioska',
        pop_source: 'sign_in',
        task_id: taskRef?.current?.taskInfo?.id,
        task_name: taskRef?.current?.taskInfo?.name,
        taskclassify: taskRef?.current?.taskInfo?.taskClassify,
        groupcode: taskRef?.current?.taskInfo?.groupCode,
        award_amount: getTaskAward(taskRef?.current?.taskInfo),
        task_progress: taskRef?.current?.taskInfo?.dayTimes?.progress || '',
        resource_location: 'matrioska_pop',
        sign_type: signType,
      });
    }

    setShowBtn(true);

    // eslint-disable-next-line react-hooks/exhaustive-deps
    openBoxAnimation = Lottie.loadAnimation({
      container: document.getElementById('open-box-lottie') as HTMLElement,
      renderer: 'canvas',
      loop: false,
      autoplay: true,
      path: signDay === 7 ? sign_seven : sign_three,
    });

    // 注册 enterFrame 回调函数
    let lastFrame = -1;
    openBoxAnimation.addEventListener('enterFrame', (event) => {
      const currentFrame = event.currentTime.toFixed();
      if (Number(currentFrame) > 10 && lastFrame !== currentFrame) {
        // 当前帧为11时,开始展示肥料
        setIfShowManure(true)
        // 更新lastFrame，防止同一帧多次执行
        lastFrame = currentFrame;
      }
    });

    return () => {
      openBoxAnimation?.destroy();
    }
  }, [])

  const handleClose = () => {
    baseModal.close(props?.id)
  }

  const getBtnText = () => {
    let result = taskRef.current;
    if (showRecommendTask && result) {
      return (
        <span>
          {result?.name}
        </span>
      );
    }
    return '我知道了'
  };

  const handleConfirm = () => {
    let result = taskRef.current;
    if (showRecommendTask && result) {
      stat.click('resource_click', {
        c: 'pop',
        d: 'matrioska',
        resource_location: 'matrioska_pop',
        sign_type: signType,
      })
      stat.click('task_click', {
        c: 'pop',
        d: 'matrioska',
        pop_source: 'sign_in',
        task_id: result?.taskInfo?.id,
        task_name: result?.taskInfo?.name,
        taskclassify: result?.taskInfo?.taskClassify,
        groupcode: result?.taskInfo?.groupCode,
        award_amount: getTaskAward(result?.taskInfo),
        task_progress: result?.taskInfo?.dayTimes?.progress || '',
        resource_location: 'matrioska_pop',
        sign_type: signType,
      });
      const requestId = geneTaskRequestId();
      taskActionHandler(result?.taskInfo, requestId);
    }
    handleClose();
  }

  const getTitleText = () => {
    const text = (signDay === 3 || signDay === 7) ? '宝箱' : '奖励';
    return `恭喜获得第${signDay}天${text}`
  }

  return (
    <div className="modal-continuous-sign">
      <div id="open-box-lottie" />
      <div className="title">{getTitleText()}</div>
      <div className="sub-title">连续签到第3、7天得宝箱</div>
      {ifShowManure && <div className="award-text">{rewardAmount}肥料</div>}
      {showBtn && <div className="confirm-btn" onClick={handleConfirm}>{getBtnText()}</div>}
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  )
}
