import React, { Fragment, MouseEvent, useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import CloseIcon from '../assets/modal-close-1.png';
import ArrowIcon from './images/arrow.png';
import stat from '@/lib/stat';
import dispatch from '@/logic/store';
import useMxState from '@/hooks/useMxState';
import { QueryCardInfoRes } from '@/api/doublePointsCard/typings';
import { EDoubleFactFromParam } from '../modal_double_card';
import modals from "@/components/modals";

interface IProps {
  id: string;
}

export default function Index(props: IProps) {
  const [doublePointsCard] = useMxState<QueryCardInfoRes>('doublePointsCard');
  const { frontData } = doublePointsCard;
  const handleConfirm = (e?: MouseEvent<HTMLDivElement>) => {
    e && e?.stopPropagation()
    handleClickStat(1);
    baseModal.close(props?.id);
    dispatch.doublePointsCard.queryCardInfo();
    modals.openDoubleCardModal({
      autoLottery: true,
      from: EDoubleFactFromParam.GUIDE_POP,
    });
  };

  const handleClickStat = (position: number) => {
    stat.click('fdcard_guide_click', {
      c: 'fdcard',
      d: 'guide',
      click_position: position,
    });
  };
  const handleClose = (e: MouseEvent<HTMLDivElement>) => {
    e?.stopPropagation()
    handleClickStat(2);
    baseModal.close(props?.id);
  };

  useEffect(() => {
    stat.exposure('fdcard_guide_exposure', {
      c: 'fdcard',
      d: 'guide',
    });
  }, []);

  return (
    <div onClick={() => handleConfirm()} className="modal-double-card-publicity">
      <div className="content-wrap">
        {frontData?.guidePopText ? (
          <div className="text-content mx-auto">{frontData?.guidePopText}</div>
        ) : (
          <Fragment>
            <div className="content mx-auto">天天抽肥料膨胀卡</div>
            <div className="content mx-auto">
              暑期赚的肥料均
              <span className="strong">有机会膨胀翻倍!</span>
            </div>
          </Fragment>
        )}
      </div>
      <div className={`confirm-btn`} onClick={handleConfirm}>
        立即抽卡
        <img className="arrow" src={ArrowIcon} alt="" />
      </div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
}
