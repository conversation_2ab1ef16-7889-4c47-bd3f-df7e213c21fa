.modal-double-card-publicity {
  background-image: url('./images/modal-bg.png');
  uc-perf-stat-ignore: image;
  background-size: contain;
  background-repeat: no-repeat;
  width: 590rpx;
  height: 890rpx;
  // min-height: 400rpx;
  // background: transparent;
  border-radius: 44rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: 195rpx;
  // padding-bottom: 64rpx;
  align-items: center;
  .content-wrap {
    margin-top: 545rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .content {
    display: flex;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    color: #12161a;
    letter-spacing: 0;
    text-align: center;
    line-height: 53rpx;
    font-weight: 400;
    .strong {
      color: #fa6425;
    }
  }
  .text-content {
    width: 470rpx;
    max-height: 159rpx;
    overflow: auto;
    text-overflow: ellipsis;
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    color: #12161a;
    letter-spacing: 0;
    line-height: 53rpx;
    font-weight: 400;

  }
  .subtitle {
    position: relative;
    font-family: PingFangSC-Regular;
    letter-spacing: 0;
    // text-align: center;
    line-height: 55rpx;
    margin-top: 22rpx;
    padding: 0 50rpx;
    font-size: 32rpx;
    color: #12161a;
    font-weight: normal;
    align-self: flex-start;
    // margin-left: 50rpx;
    span {
      opacity: 0;
    }
  }
  .confirm-btn {
    margin-top: 40rpx;
    width: 470rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    font-weight: 600;
    .arrow {
      width: 32rpx;
      height: 32rpx;
      margin-left: 8rpx;
      uc-perf-stat-ignore: image;
    }
  }

  .close-btn {
    width: 60rpx;
    height: 60rpx;
    // border-radius: 50%;
    overflow: hidden;
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    .close-icon {
      width: 100%;
      uc-perf-stat-ignore: image;
    }
  }
}

@media screen and (min-width: 500px) {
  .manure-unit {
    transform: translateY(-1.5rpx);
  }
}
