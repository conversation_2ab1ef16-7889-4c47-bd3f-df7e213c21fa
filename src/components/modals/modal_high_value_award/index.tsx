import React from 'react';
import './index.scss';
import TitleImg from './images/title-img.png';
import CloseIcon from './../assets/modal-close.png';
import ColouredRibbonIconImg from './images/coloured-ribbon-Icon.png';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import mx from '@ali/pcom-mx';
import config from '@/config';
import { openPage } from '@/lib/ucapi';
import HighValueAwardImg from './images/high_value_award-bg.png';
import {handleLogin} from '@/pages/index/components/TaskPop/TaskList/help';
import { convertCentsToYuan } from '@/lib/utils/formatNumber';

interface Props {
  icon: string;
  name: string;
  mark: string;
  amount: string;
  taskName: string;
  taskId: number;
  // 标识奖品是否配置随机发放
  randomAmount?: boolean;
  bindTaobao: boolean;
}
// 高价值奖励弹窗
const Index = (props: Props) => {
  const highValueTask = mx.store.get('highValueTask');
  const { icon, name, mark, amount, taskName, randomAmount, taskId, bindTaobao } = props;
  // 点击按钮
  const confirm = async () => {
    const flag = handleLogin();
    stat.click('reward_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      click_area: 'withdrawal',
      task_id: taskId,
      taskName: taskName,
      isLogin: flag
    });
    if (!flag) {
      baseModal.removeCacheModal(MODAL_ID.BINDING_TAOBAO);
      baseModal.removeCacheModal(MODAL_ID.HIGH_VALUE_TASK);
      baseModal.close(MODAL_ID.HIGH_VALUE_AWARD);
      return;
    }
    // 如果是红包奖励 跳转到提现页面
    if (mark === 'cash') {
      openPage(config?.walletUrl);
    }
    baseModal.close(MODAL_ID.HIGH_VALUE_AWARD);
  };
  // 关闭
  const handleClose = () => {
    stat.click('reward_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      click_area: 'close',
    });
    bindTaobao && baseModal.removeCacheModal(MODAL_ID.BINDING_TAOBAO);
    baseModal.close(MODAL_ID.HIGH_VALUE_AWARD);
  };

  const getBtnText = (key: string) => {
    const text = highValueTask?.resourceConfig?.dialogSuccessBtnTextMap[key || 'point'];
    return !bindTaobao ? `登录并${text}` : text;
  }

  return (
    <Fact
      c="home"
      d="pop"
      expoLogkey="reward_exposure"
      expoExtra={{
        resource_location: 'high_value_pop',
        task_id: taskId,
        task_name: taskName
      }}
    >
      <div
        style={{
          backgroundImage: `url(${
            highValueTask?.resourceConfig?.successTopBgImg
              ?? HighValueAwardImg
          })`,
        }}
        className="modal-high-value-award"
      >
        <div className="container-bg" />
        <img className="title-img" src={TitleImg} alt="" />
        <img className="coloured-ribbon" src={ColouredRibbonIconImg} alt="" />
        <div className="task-name">{taskName}</div>
        <img className="award-icon" src={icon} alt="" />
        <div className="award-name">
          {!bindTaobao && randomAmount ? '最高' : '' }
          {mark === 'cash' ? convertCentsToYuan(Number(amount)) : amount}
          {name}
        </div>
        <div onClick={confirm} className="confirm">
          {getBtnText(mark)}
        </div>
        <div className="close-btn" onClick={handleClose}>
          <img src={CloseIcon} className="close-icon" alt="" />
        </div>
      </div>
    </Fact>
  );
};

export default Index;
