.modal-help-success{
  width: 590rpx;
  height: 885rpx;
  border-radius: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  .help-bg{
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: -1;
    border-radius: 40rpx;
  }

  .mark-img{
    margin-top: 113rpx;
    width: 664rpx;
  }

  .content-card{
    margin-top: 68rpx;
    width: 520rpx;
    height: 146rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    padding-left: 19rpx;
    
    .user-info{
      position: relative;
      height: 110rpx;
      .avatar{
        width: 90rpx;
        height: 90rpx;
        border-radius: 50%;
      }
      .user-name{
        width: 100rpx;
        height: 30rpx;
        background: #FFD19B;
        border-radius: 15rpx;
        font-size: 20rpx;
        color: #662A02;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 0rpx;
        left: 50%;
        transform: translateX(-50%);
      }
    }
    .right-text{
      margin-left: 24rpx;
      .title{
        font-size: 32rpx;
        color: #662A02;
        letter-spacing: 0;
        font-weight: 400;
      }
      .award-text{
        font-size: 32rpx;
        color: #2AC638;
        letter-spacing: 0;
        font-weight: 400;
      }
    }
  }

  .confirm-btn{
    margin-top: 22rpx;
    width: 470rpx;
    height: 100rpx;
    background: #2AC638;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #FFFFFF;
    font-weight: 700;
  }
  .close-btn{
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -120rpx;
    .close-icon{
      width: 100%;
    }
  }
}