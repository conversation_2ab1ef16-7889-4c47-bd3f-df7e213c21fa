import React, {useEffect} from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { IFinishShareTaskRes } from '@/logic/store/models/task/typing';
import { desensitizeName } from '@/logic/store/models/user/help';
import { defaultNickname } from '@/constants/default';
import stat from '@/lib/stat';

import MarkImg from './images/modal-mark.png';
import HelpBg from './images/modal-help-bg.jpg';
import CloseIcon from '../assets/modal-close.png';
import DefaultAvatar from '@/assets/uu_default.png';

interface IProps {
  id: string;
  data: IFinishShareTaskRes;
}

export default function Index(props: IProps) {
  const { data } = props;
  const rewardAmount = props?.data?.inviteePrizes?.[0]?.rewardItem?.amount;

  useEffect(() => {
    stat.exposure('shared_toast_exposure', {
      c: 'toast',
      d: 'shared',
      share_status: 1,
      task_id: data?.inviteeTask?.id || '',
      task_name: data?.inviteeTask?.name || '',
      taskclassify: data?.inviteeTask?.taskClassify || '',
      groupcode: data?.inviteeTask?.groupCode || '',
      award_amount: rewardAmount || '',
    })
  }, []);

  const handleClose = () => {
    baseModal.close(props?.id)
  }

  return (
    <div className="modal-help-success">
      <img src={HelpBg} className="help-bg" alt="" />
      <img src={MarkImg} className="mark-img" alt="" />
      <div className="content-card">
        <div className="user-info">
          <img src={data?.inviterAvatar || DefaultAvatar} className="avatar" />
          <div className="user-name">{desensitizeName(data?.inviterNickname || defaultNickname)}</div>
        </div>
        <div className="right-text">
          <div className="title">谢谢你的助力，你也得到</div>
          <div className="award-text">{rewardAmount}肥料！</div>
        </div>
      </div>
      <div className="confirm-btn" onClick={handleClose}>去种果树</div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  )
}
