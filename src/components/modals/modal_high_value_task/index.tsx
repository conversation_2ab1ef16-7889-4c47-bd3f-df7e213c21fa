import React, { Fragment, useEffect } from 'react';
import './index.scss';
import CloseIcon from './../assets/modal-close.png';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import ColouredRibbonImg from './images/coloured-ribbon.png';
import { checkTaskFinished } from '@/pages/index/components/TaskPop/TaskList/util';
import HighValueBg from './../assets/high-value-bg.png';
import HighBottomBg from './images/high-bottom-bg.png';
import mx from '@ali/pcom-mx';
import dispatch from '@/logic/store';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import LightPng from './../modal_high_value_task/images/light-high.png';
import LightCircleHigh from './../modal_high_value_task/images/light-circle-high.png';
import CheckedImg from './images/checked-img.png';
import { bindObserver } from '@/lib/utils/help';
import config from '@/config';
import { convertCentsToYuan } from '@/lib/utils/formatNumber';

let getRewardType = new Map([
  ['cash', 'cash'],
  ['point', 'fertilizer'],
]);

// 高价值任务弹窗
const Index = () => {
  const highValueTask = mx.store.get('highValueTask');
  const user = mx.store.get('user');
  // 是否有子任务
  const hasPreTaskList = highValueTask?.currentTaskInfo?.preTaskList?.length > 0;
  // 奖励类型
  const reward_type = highValueTask?.currentTaskInfo?.rewardItems?.[0]?.mark;
  // 奖励icon
  const awardIcon = highValueTask?.currentTaskInfo?.rewardItems?.[0]?.icon;

  // 素材配置
  const resourceConfig = highValueTask?.resourceConfig;
  
  useEffect(() => {
    const container = document.getElementById('high-value-task-dialog') as HTMLElement;
    container &&
        bindObserver(
          container,
          () => {
            // 弹窗曝光
            dispatch.highValueTask.resourceExposure(highValueTask?.currentTaskInfo, 'EXPOSURE', config?.taskResourceCode);
            dispatch.highValueTask.rtaTaskExposure();
          },
          () => {},
        );
  }, []);

  // 获取确认按钮文本
  const getConfirmText = (task) => {
    if (task?.preTaskList?.length > 0) {
      let resulttask = task?.preTaskList?.filter((item) => {
        return !checkTaskFinished(item);
      });
      return resulttask[0]?.btnName ?? '去完成';
    }
    return task?.btnName ?? '去完成';
  };
  // 过w肥料转换
  const convertCentsToPoint = (number) => {
    if (number >= 10000) {
      let result = (number / 10000).toString();
      // 去掉多余的小数位
      if (result.indexOf('.') !== -1) {
        result = parseFloat(result).toString();
      }
      return `${result}`;
    }
    return number.toString();
  };
  // 获取奖励数文案
  const getAwardText = (info) => {
    let detail = Object.assign({}, info);
    switch (detail.mark) {
      case 'cash':
        return (
          <Fragment>
            {detail.randomAmount ? '最高' : '必得'}
            <span>{convertCentsToYuan(Number(detail.amount))}</span>
            {detail?.name}
          </Fragment>
        );
      case 'point':
        return (
          <Fragment>
            {detail.randomAmount ? '最高' : '必得'}
            <span>{convertCentsToPoint(Number(detail.amount))}</span>
            {Number(detail.amount) >= 10000 && '万'}
            {detail?.name}
          </Fragment>
        );
      default:
        return (
          <Fragment>
            {detail.randomAmount ? '最高' : '必得'}
            <span>{detail.amount}</span>
            {detail?.name}
          </Fragment>
        );
    }
  };
  // 第几步埋点
  const getStep = () => {
    let factObj = {
      high_value_pop_style: 'ordinary',
      step: '',
    };
    if (hasPreTaskList) {
      factObj.high_value_pop_style = 'step';
      const index = highValueTask?.currentTaskInfo?.preTaskList?.findIndex((item) => !checkTaskFinished(item));
      if (index !== -1) {
        factObj.step = index === 0 ? 'one' : 'two';
      }
    }
    return factObj;
  };
  // 获取第几部
  const getResidueStep = () => {
    let result = highValueTask?.currentTaskInfo?.preTaskList?.filter((item) => !checkTaskFinished(item));
    return result?.length
  }
  // 确认按钮
  const confirm = () => {
    stat.click('task_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      taskclassify: highValueTask?.currentTaskInfo?.taskClassify,
      groupcode: highValueTask?.currentTaskInfo?.groupCode,
      ...getStep(),
    });
    dispatch.highValueTask.resourceExposure(highValueTask?.currentTaskInfo, 'CLICK', config?.taskResourceCode);
    dispatch.highValueTask.dialogConfirmEvent();
  };
  // 取消按钮
  const cancel = () => {
    stat.click('resource_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      click_area: 'give_up',
      ...getStep(),
    });
    baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
    dispatch.highValueTask?.dialogCancelEvent();
  };
  // 关闭
  const handleClose = () => {
    stat.click('resource_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      click_area: 'close',
      ...getStep(),
    });    
    baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
    dispatch.highValueTask?.dialogCancelEvent();
  };
  return (
    <Fact
      c="home"
      d="pop"
      expoLogkey="resource_exposure"
      expoExtra={{
        resource_location: 'high_value_pop',
        reward_type: getRewardType?.get(reward_type) || reward_type,
        task_id: highValueTask?.currentTaskInfo?.id,
        task_name: highValueTask?.currentTaskInfo?.name,
        ...getStep(),
      }}
    >
      <Fact
        c="home"
        d="pop"
        expoLogkey="task_exposure"
        expoExtra={{
          resource_location: 'high_value_pop',
          reward_type: getRewardType?.get(reward_type) || reward_type,
          task_id: highValueTask?.currentTaskInfo?.id,
          task_name: highValueTask?.currentTaskInfo?.name,
          page_status: user?.bindTaobao ? 1 : 0,
          taskclassify: highValueTask?.currentTaskInfo?.taskClassify,
          groupcode: highValueTask?.currentTaskInfo?.groupCode,
          ...getStep(),
        }}
      >
        <div
          style={{
            backgroundImage: `url(${
              resourceConfig?.dialogStyle?.topBgImg ?? HighValueBg
            })`,
          }}
          id="high-value-task-dialog"
          className={`modal-high-value-task ${hasPreTaskList ? '' : 'modal-high-value-task-noSchedule'}`}
        >
          <div className="animation">
            <img className="animation-circle" src={LightCircleHigh} alt="" />
            <img className="animation-light" src={LightPng} alt="" />
          </div>
          <div
            style={{
              color: resourceConfig?.dialogStyle?.containerBgColor
                ?? '#fff9f3',
            }}
            className={`container-bg ${hasPreTaskList ? '' : 'container-bg-noSchedule'}`}
          />
          <img className="award-icon" src={awardIcon} alt="" />
          <img className="coloured-ribbon" src={ColouredRibbonImg} alt="" />
          <div className="title">{resourceConfig?.dialogTitle}</div>
          <div className="subheading">{getAwardText(highValueTask?.currentTaskInfo?.rewardItems?.[0])}</div>
          <div className="lose-time">今日失效</div>
          {hasPreTaskList && (
            <div className="schedule">
              <div className="task-explain">
                只需完成{getResidueStep()}步即可领取福利
              </div>
              <div className="task-detail">
                <div className="schedule-continer">
                  {highValueTask?.currentTaskInfo?.preTaskList?.map((item, index) => {
                    return (
                      <Fact
                        key={index}
                        c="home"
                        d="pop"
                        expoLogkey="task_exposure"
                        expoExtra={{
                          resource_location: 'high_value_pop',
                          reward_type: getRewardType?.get(reward_type) || reward_type,
                          task_id: item?.id,
                          task_name: item?.name,
                          page_status: user?.bindTaobao ? 1 : 0,
                          taskclassify: item?.taskClassify,
                          groupcode: item?.groupCode,
                          ...getStep(),
                        }}
                      >
                        <div className="schedule-item">
                          <div className="schedule-item-left">
                            <div className={`circle ${checkTaskFinished(item) ? 'circle-checked' : ''}`}>
                              {index + 1}
                              {checkTaskFinished(item) && <img className="checked-img" src={CheckedImg} alt="" />}
                            </div>
                            {highValueTask?.currentTaskInfo?.preTaskList?.length !== index + 1 && (
                              <div className={`line ${checkTaskFinished(item) ? 'line-checked' : ''}`} />
                            )}
                          </div>
                          <div
                            className={`schedule-item-text ${
                              checkTaskFinished(item) ? 'schedule-item-text-checked' : ''
                            }`}
                          >
                            {item.name}
                          </div>
                        </div>
                      </Fact>
                    );
                  })}
                </div>
              </div>
            </div>
          )}
          <div
            style={{
              backgroundImage: `url(${
                resourceConfig?.dialogStyle?.bottomBgImg ?? HighBottomBg
              })`,
            }}
            className="btn-conainer"
          >
            <div onClick={confirm} className="confirm">
              {getConfirmText(highValueTask?.currentTaskInfo) || '去完成'}
            </div>
            <div onClick={cancel} className="cancel">
              {highValueTask?.resourceConfig?.dialogCancelBtnText}
            </div>
          </div>
          <div className="close-btn" onClick={handleClose}>
            <img src={CloseIcon} className="close-icon" alt="" />
          </div>
        </div>
      </Fact>
    </Fact>
  );
};

export default Index;
