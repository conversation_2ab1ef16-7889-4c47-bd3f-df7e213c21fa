import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import classNames from 'classnames';
import { getNumberLength, convertCentsToPoint, convertToPoint } from '@/lib/utils/formatNumber';
import useMxState from '@/hooks/useMxState';
import { IFarmHelpPlantState } from '@/logic/store/models/farmHelpPlant/type';
import dispatch from '@/logic/store';
import stat from '@/lib/stat';
import { getFarmHelpPlantFactParams } from '@/pages/index/utils';

import CloseIcon from '../assets/modal-close.png';
import TitleImg from './images/bbz-attend-activity-title.png';
import { MODAL_ID } from '../types';

interface IProps {
  id: string;
  curTime: number;
}

export default function Index(props: IProps) {
  const [farmHelpPlant] = useMxState<IFarmHelpPlantState>('farmHelpPlant');
  const { rankRewardList = [] } = farmHelpPlant.helpPlantHome;

  const topOnePrizes = Number(rankRewardList?.[0]?.prizeValue);
  const topTwoPrizes = Number(rankRewardList?.[1]?.prizeValue);
  const topThreePrizes = Number(rankRewardList?.[2]?.prizeValue);

  // 蓄水任务的肥料
  const calculateRewardAmount = convertCentsToPoint(farmHelpPlant.calculateRewardAmount ?? 0, false);
  // 蓄水任务的第一阶段肥料
  const oneAwardPoint = convertToPoint(farmHelpPlant.accumulatedTaskList?.[0]?.rewardItems?.[0]?.amount);

  const handleConfirm = () => {
    stat.click('bbz_guide_click', {
      c: 'bbzhong',
      d: 'guide',
      click_position: 1,
      ...getFarmHelpPlantFactParams(),
    })
    dispatch.farmHelpPlant.openActivityPage('guide_pop');
    baseModal.close(props?.id);
  }
  useEffect(() => {
    stat.exposure('bbz_guide_exposure', {
      c: 'bbzhong',
      d: 'guide',
      ...getFarmHelpPlantFactParams(),
    })
    dispatch.dialog.closePopup(MODAL_ID.BBZ_ATTEND_ACTIVITY, props.curTime);
  }, [])

  const handleClose = () => {
    stat.click('bbz_guide_click', {
      c: 'bbzhong',
      d: 'guide',
      click_position: 2,
      ...getFarmHelpPlantFactParams(),
    })
    baseModal.close(props?.id);
  }

  const formatCash = (value: number) => {
    if (!value) return '--';
    return value / 100;
  }

  return (
    <div className="modal-bbz-attend-activity-comp">
      <img src={TitleImg} className="title-img" alt="" />
      <div className="attend-activity-wrap row">
        <div className="head-wrap">
          <div className={classNames('top-one value', { 'one-small': getNumberLength(topOnePrizes) > 2 })}>
            {formatCash(topOnePrizes)}
          </div>
          <div className={classNames('top-two value', { 'two-small': getNumberLength(topTwoPrizes) > 2 })}>
            {formatCash(topTwoPrizes)}
          </div>
          <div className={classNames('top-three value', { 'three-small': getNumberLength(topThreePrizes) > 2 })}>
          {formatCash(topThreePrizes)}
          </div>
        </div>
        <div className="text-wrap">
          <p>邀1人领<span>{oneAwardPoint}</span>千肥料，最高领<span>{calculateRewardAmount}</span>万肥料</p>
          <p>还有最高<span className="unit">￥</span><span>{formatCash(topOnePrizes)}</span>红包等你拿!</p>
        </div>
        <div className="confirm-btn" onClick={handleConfirm}>参与活动</div>
      </div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  )
}
