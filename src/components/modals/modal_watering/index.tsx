import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { BaseModalProps } from '@/lib/modal/typings';
import { MODAL_ID } from '@/components/modals/types';
import modalCloseImg from '../assets/modal-close-1.png';
import { TaskInfo, TASK_LOCATION } from "@/pages/index/components/TaskPop/TaskList/types";
import { logToFinishTask, getTaskAward, getTaskAmountType, AMOUNT_TYPE } from "@/pages/index/components/TaskPop/TaskList/util";
import { dongfengTaskReport, taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import { geneTaskRequestId } from "@/logic/store/models/utils";
import stat from "@/lib/stat";
import { useDebounce } from '@/hooks';
import dispatch from "@/logic/store";
import config from "@/config";

// 施肥推荐资源弹窗
interface IProps extends BaseModalProps {
  popImg?: string; // 弹窗图片，活动类弹窗使用
  popTitle?: string; // 弹窗标题
  popSubtitle?: string; // 副标题
  icon?: string; // 奖励图标
  task?: TaskInfo; // 当前等级
  btnText?: string; // 按钮文案
}

const Index = (props: IProps) => {
  const { popTitle, popSubtitle, icon, task, btnText, popImg } = props;
  const taskAward = task ? getTaskAward(task) : 0;
  const awardDesc = task && getTaskAmountType(task) === AMOUNT_TYPE.CASH ? `${taskAward / 100}元现金` : `${taskAward}肥料`;
  
  useEffect(() => {
    stat.exposure('resource_exposure', {
      c: 'pop',
      d: 'resource',
      resource_location: 'manure_pop',
    })
    if (task) {
      dongfengTaskReport(task, 'expose');
      stat.exposure('task_exposure', {
        c: 'pop',
        d: 'resource',
        task_id: task?.id,
        task_name: task?.name,
        taskclassify: task?.taskClassify,
        groupcode: task?.groupCode,
        award_amount: taskAward,
        resource_location: 'manure_pop'
      });
      dispatch.highValueTask.resourceExposure(task, 'EXPOSURE', config.wateringPopResourceCode);
    }
  }, []);
  
  const handleConfirm = useDebounce(() => {
    stat.click('resource_click', {
      c: 'pop',
      d: 'resource',
      resource_location: 'manure_pop',
      click_position: 1,
    })
    if (task) {
      stat.click('task_click', {
        c: 'pop',
        d: 'resource',
        task_id: task?.id,
        task_name: task?.name,
        taskclassify: task?.taskClassify,
        groupcode: task?.groupCode,
        award_amount: taskAward,
        resource_location: 'manure_pop'
      });
      logToFinishTask(task, TASK_LOCATION.WATERING_POP);
      taskActionHandler(task, geneTaskRequestId(), { location: TASK_LOCATION.WATERING_POP });
    }
    close();
  }, 500);
  // 关闭弹窗
  const close = () => {
    stat.click('resource_click', {
      c: 'pop',
      d: 'resource',
      resource_location: 'manure_pop',
      click_position: 2,
    })
    baseModal.close(MODAL_ID.WATERING);
    // 曝光后更新资源位素材
    dispatch.resource.queryResource({});
  };

  return (
    <div className="modal-watering">
      {
        popImg ?
          <div className="modal-watering-activity" onClick={handleConfirm}>
            <img src={popImg} />
          </div>
          :
          <div className="modal-watering-task">
            <div className="title">{ popTitle }</div>
            <div className="sub-title">{ popSubtitle }</div>
            <div className="award-content">
              <img className="award-icon" src={icon} />
              <div className="award-amount">{ awardDesc }</div>
            </div>
            <div onClick={handleConfirm} className="confirm-btn">
              { btnText }
            </div>
          </div>
      }
      <img onClick={close} className="close-img" src={modalCloseImg} alt="" />
    </div>
  );
};

export default Index;
