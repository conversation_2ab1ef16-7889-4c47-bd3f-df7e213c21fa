import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import BingtaobaoImg from './images/bingtaobao-bg.png';
import DoubleCardBg from './images/double-card-bg.png';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import CloseIcon from '../assets/modal-close.png';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { getParam } from '@/lib/qs';
import BindingBubbleImg from './images/binding-bubble.png';
import LoginBubbleImg from './images/login-bubble.png';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';
import { MainAPI } from '@/logic/type/event';
import mx from '@ali/pcom-mx';
import dispatch from '@/logic/store';
import {
  newYearBingtaobaoImg,
  newYearLoginBubbleImg,
  newYearBindingBubbleImg,
  checkPlantActivityCurActPeriod,
} from '@/logic/store/models/app/new_year_time';
import { getIsBangBangMainActivity, getIsDoubleActivity } from '@/logic/store/models/utils';
import BindHelpBubble from './images/bing-help.png';
import LoginHelpBubble from './images/login-help.png';
import BbzImg from './images/bbz-modal-img.png';
import BbzBindTitle from './images/bbz-bind-modal-title.png';
import DoubleBindTitle from './images/double-bind-modal-title.png';

let timer;
const enum BIND_TAOBAO_TYPE {
  NORMAL = 'normal',
  DOUBLE = 'double',
  BANG_BANG = 'bang_bang',
  NOT_INVITE = 'not_invite',
}

// 绑定淘宝弹窗
const Index = (props) => {
  const { modalName } = props;
  const app = mx.store.get('app');
  const loginBindModalTime = app?.frontData?.loginBindModalTime || 10;
  const [waitTime, setWaitTime] = useState(loginBindModalTime);
  const bindTaobao = mx.store.get('user.bindTaobao');
  const waitTimeRef = useRef(waitTime);
  waitTimeRef.current = waitTime;

  const inviteCode = getParam('inviteCode') || '';
  const isBangBangZhongActiviy = getIsBangBangMainActivity();
  const isDoubleCardActivity = getIsDoubleActivity();
  const getBindTaobaoType = () => {
    // 普通助力场景
    if (inviteCode && !isBangBangZhongActiviy && !isDoubleCardActivity) {
      return BIND_TAOBAO_TYPE.NORMAL;
    }
    // 翻倍卡
    if (inviteCode && isDoubleCardActivity) {
      return BIND_TAOBAO_TYPE.DOUBLE;
    }
    // 帮帮种场景
    if (isBangBangZhongActiviy || checkPlantActivityCurActPeriod()) {
      return BIND_TAOBAO_TYPE.BANG_BANG;
    }
    // 非助力场景
    return BIND_TAOBAO_TYPE.NOT_INVITE;
  };

  usePageVisibilityListener((visible: boolean) => {
    if (visible) {
      timer = setInterval(() => {
        if (waitTimeRef.current > 1) {
          setWaitTime(waitTimeRef.current - 1);
        } else {
          loginBindingTaobao('auto')();
          handleClose()();
          clearInterval(timer);
        }
      }, 1000);
    } else {
      clearInterval(timer);
    }
  });

  useEffect(() => {
    // 偶现绑定成功，弹窗出现且倒计时
    if (bindTaobao) {
      baseModal.close(MODAL_ID.BINDING_TAOBAO);
    }
  }, [bindTaobao]);
  // 关闭
  const handleClose = (flag = false) => {
    return () => {
      if (flag) {
        stat.click('binding_pop_click', {
          c: 'binding',
          d: 'pop',
          entry: getParam('entry'),
          click_position: 'close',
          pop_status: modalName === 'login' ? 0 : 1,
        });
      }
      baseModal.close(MODAL_ID.BINDING_TAOBAO);
    };
  };
  // 绑定淘宝
  const loginBindingTaobao = (click_position) => {
    return () => {
      stat.click('binding_pop_click', {
        c: 'binding',
        d: 'pop',
        entry: getParam('entry'),
        click_position,
        pop_status: modalName === 'login' ? 0 : 1,
      });
      if (modalName === 'login') {
        mx.event.emit(MainAPI.OpenLoginProtocolPanel);
      } else {
        // bindThirdPartyAccount('taobao');
        dispatch.user.toBindTaobao(props?.from || 'modal');
      }
      handleClose()();
    };
  };

  const getBubbleImg = () => {
    switch (getBindTaobaoType()) {
      case BIND_TAOBAO_TYPE.NORMAL:
        return modalName === 'login' ? LoginHelpBubble : BindHelpBubble;
      case BIND_TAOBAO_TYPE.DOUBLE:
        return modalName === 'login' ? LoginHelpBubble : DoubleBindTitle;
      case BIND_TAOBAO_TYPE.BANG_BANG: {
        // 非助力场景下不能使用 助力场景下的登陆提示img (助力场景下的有提示助力成功)
        const LoginImg = inviteCode ? LoginHelpBubble : LoginBubbleImg;
        return modalName === 'login' ? LoginImg : BbzBindTitle;
      }
      case BIND_TAOBAO_TYPE.NOT_INVITE:
        return modalName === 'login' ? LoginBubbleImg : BindingBubbleImg;
      default:
        return modalName === 'login' ? LoginHelpBubble : BindHelpBubble;
    }
  };

  const getBindImg = () => {
    if (app?.isSpringPeriod) {
      return newYearBingtaobaoImg;
    }
    switch (getBindTaobaoType()) {
      case BIND_TAOBAO_TYPE.NORMAL:
        return BingtaobaoImg;
      case BIND_TAOBAO_TYPE.DOUBLE:
        return DoubleCardBg;
      case BIND_TAOBAO_TYPE.BANG_BANG:
        return BbzImg;
      case BIND_TAOBAO_TYPE.NOT_INVITE:
        return BingtaobaoImg;
      default:
        return BingtaobaoImg;
    }
  };

  return (
    <Fact
      c="binding"
      d="pop"
      expoLogkey="binding_pop_show"
      expoExtra={{
        pop_status: modalName === 'login' ? 0 : 1,
        entry: getParam('entry'),
      }}
    >
      <div className="modal-binding-taobao">
        {app?.isSpringPeriod ? (
          <img
            className="newyear-bubble"
            src={modalName === 'login' ? newYearLoginBubbleImg : newYearBindingBubbleImg}
            alt=""
          />
        ) : (
          <img
            className={`${getBindTaobaoType() === BIND_TAOBAO_TYPE.DOUBLE ? 'double-bubble' : 'bubble'}`}
            src={getBubbleImg()}
            alt=""
          />
        )}
        <img
          className={`${getBindTaobaoType() === BIND_TAOBAO_TYPE.DOUBLE ? 'double-bg' : 'bingtaobao-bg'}`}
          src={getBindImg()}
          alt=""
        />
        <div onClick={loginBindingTaobao('go_binding')} className="binging-btn">
          {modalName === 'login' ? '登录淘宝账号' : '绑定淘宝账号'}({waitTime})
        </div>
        <div className="close-btn" onClick={handleClose(true)}>
          <img src={CloseIcon} className="close-icon" alt="" />
        </div>
      </div>
    </Fact>
  );
};

export default Index;
