import React from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import mx from '@ali/pcom-mx';
import { BaseModalProps } from '@/lib/modal/typings';
import CloseIcon from '../assets/modal-close.png';
import manureImg from '../assets/manure.png';
import {MainAPI} from "@/logic/type/event";
import { relativeTimeFormat } from "@/gameObject/uc/tomorrowFertilizer/common/utils";
import {StoreName} from "@/logic/type/store";
import {TasklistSource} from '@/pages/index/utils'
import { MODAL_ID } from '@/components/modals/types';

interface IProps extends BaseModalProps {
  receiveTime: number;
  pointAmount: number; // 肥料数
  status: 'NOT_START' | 'ADVANCE_NOTICE';
} 

// 明日施肥
export default function Index(props: IProps) {
  const curTime = mx.store.get(StoreName.CurrentTime) || Date.now()
  // 关闭
  const handleClose = () => {
    baseModal.close(MODAL_ID.RECEIVE_FERTILIZER);
  };
  // 去领更多肥料
  const toMoreManure = () => {
    handleClose()
    setTimeout(() => {
      mx.event.emit(MainAPI.ShowTaskPop, {tasklist_source: TasklistSource.tomorrow_fertilizer})
    }, 400)
  }
  // const timeText = relativeTimeFormat(props.receiveTime, curTime, true)
  
  return (
    <div className="modal-receive-fertilier">
      {/* <div className="title">{timeText}可领</div> */}
      <div className="title">{props.status === 'ADVANCE_NOTICE' ? '明日可领取' : '今日可领取'}</div>
      <div className="sub-title">今日施肥越多，明日可领肥料越多哦</div>
      <img className="manure-img" src={manureImg} alt="" />
      <div className="award-text">{props.pointAmount}肥料</div>
      <div className="confirm-btn" onClick={toMoreManure}>
        去领更多肥料
      </div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
}
