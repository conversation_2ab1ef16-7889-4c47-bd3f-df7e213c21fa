import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import Lottie from 'lottie-web';
import { mx } from '@ali/pcom-iz-use';
import { StoreName } from '@/logic/type/store';
import stat from '@/lib/stat';

const DefaultPath = 'https://image.uc.cn/s/uae/g/1y/animate/202409/1cc7cd/data.json';
const LowPathJSON = 'https://image.uc.cn/s/uae/g/1y/animate/202409/866598/data.json'

// 限时任务触发弹窗
const Index = () => {
  const [hasCompleted, setCompleted] = useState(false);
  const containerRef = useRef(null);
  const lp = mx.store.get(StoreName.DeviceLevel);

  useEffect(() => {
    if (!containerRef.current) {
      return;
    }
    stat.exposure('select_award_trigger_exposure', {
      c: 'pop',
      d: 'award',
    });
    const anim = Lottie.loadAnimation({
      container: containerRef.current, // 指定动画渲染的DOM元素,
      renderer: 'canvas',
      loop: false,
      autoplay: false,
      path: lp === '0.0' ? LowPathJSON : DefaultPath,
      rendererSettings: {
        preserveAspectRatio: 'xMidYMid meet', // SVG 的视图保持宽高比
        progressiveLoad: true
      },
    });
    anim.addEventListener('DOMLoaded', () => {
      anim.play();
      checkLottie();
    });
    anim.addEventListener('complete', () => {
      console.log('动画播放结束');
      setCompleted(true);
    });
    anim.addEventListener('error', () => {
      console.log('动画播放错误');
      setCompleted(true);
    });
  }, []);

  // 检测动画，2s时候是否播放结束
  function checkLottie() {
    setTimeout(() => {
      if (hasCompleted) {
        return;
      }
      setCompleted(true);
    }, 2000);
  }
  useEffect(() => {
    if (!hasCompleted) {
      return;
    }
    baseModal.close(MODAL_ID.LIMITED_TIME_BENEFITS_TRIGGER);
    // 传入选择奖励的列表
    baseModal.open(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
  }, [hasCompleted]);

  return (
    <div ref={containerRef} className="modal-limited-time-benefits-trigger" id="modal-limited-time-benefits-trigger" style={{
      width: '100vw',
      transform: 'scale(1.6)'
    }}
    />
  );
};

export default Index;
