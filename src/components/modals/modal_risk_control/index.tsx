import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import stat from '@/lib/stat';

import WarningImg from '../assets/warning-icon.png';
import CloseIcon from '../assets/modal-close.png';
import { exit, openPage } from '@/lib/ucapi';
import { mx } from '@ali/pcom-iz-use';

interface Props {
  riskRule: string;
  isFarmPage: boolean; // 是否是农场主页
}

export default function Index(props) {
  const { riskRule } = props;
  const { homeData } = mx.store.getStore().helpPlant;

  useEffect(() => {
    stat.exposure('bbz_risk_exposure', {
      c: 'activity',
      d: 'pop',
    });
  }, []);

  const handleDetail = () => {
    openPage(riskRule ?? homeData.frontData?.riskRule);
    clickFact(1);
  };

  const handleLook = () => {
    console.log('我知道了');
    handleClose(2);
  };

  const handleClose = (position: number) => {
    clickFact(position);
    baseModal.close(props?.id);
    exit();
  };

  const clickFact = (position: number) => {
    stat.click('bbz_risk_click', {
      c: 'activity',
      d: 'pop',
      click_position: position,
    });
  };

  return (
    <div className="modal-risk-control-comp">
      <img src={WarningImg} className="warning-img" alt="" />
      <div className="text-wrap row">
        <div className="text">您的账号存在异常行为</div>
        <div className="text">
          无法参与活动 <span onClick={handleDetail}>了解详情</span>
        </div>
      </div>
      <div className="confirm-btn" onClick={handleLook}>
        我知道了
      </div>
      <div className="close-btn" onClick={() => handleClose(3)}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
}
