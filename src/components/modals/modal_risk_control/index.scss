.modal-risk-control-comp {
  width: 590rpx;
  min-height: 500rpx;
  background: #ffffff;
  border-radius: 44rpx;
  background-image: url('../assets/default-modal-head-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 180rpx;
  position: relative;
  padding-top: 32rpx;
  padding-bottom: 64rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -172rpx;
  padding-bottom: 64rpx;

  .warning-img {
    width: 200rpx;
    height: 200rpx;
  }

  .text-wrap {
    width: 470rpx;
    padding-top: 24rpx;
    margin-top: 20rpx;
    box-sizing: border-box;
    flex-direction: column;
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    color: #12161a;
    font-weight: 400;
    line-height: 45rpx;
    text-align: center;

    .over-text {
      line-height: 45rpx;
      max-height: 266rpx;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    span {
      margin-left: 6rpx;
      color: #859199;
      text-decoration-line: underline;
    }
  }

  .confirm-btn{
    margin-top: 60rpx;
    width: 470rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    font-weight: 600;
  }

  .close-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -120rpx;
    .close-icon {
      width: 100%;
    }
  }
}
