.modal-bbz-beginner-guidance{
  box-sizing: border-box;
  position: relative;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .beginner-guidance-content{
    width: 720rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: -120rpx;
    position: relative;

    .head-wrap{
      flex-direction: column;
      align-items: center;
      .title-img{
        width: 638rpx;
        height: 86rpx;
      }

      .title-text{
        margin-top: 7rpx;
        font-family: PingFangSC-Semibold;
        font-size: 30rpx;
        color: #fff;
        font-weight: 600;
      }
    }

    .img-wrap{
      width: 720rpx;
      background-repeat: no-repeat;
      background-size: 100%;
      position: relative;

      .text-wrap{
        position: absolute;

        .tips-text{
          font-family: PingFangSC-Semibold;
          font-size: 34rpx;
          color: #12161A;
          font-weight: 600;
        }

        .award-tips{
          margin-top: 1rpx;
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          color: #12161A;
          font-weight: 400;
          line-height: 1;

          span {
            font-family: D-DIN-Bold;
            font-size: 36rpx;
            color: #F7534F;
          }

          .unit{
            font-size: 24rpx;
          }
        }
      }
    }

    .banner1-wrap{
      margin-top: 32rpx;
      height: 300rpx;
      background-image: url('./images/bbz-guide-img1.png');

      .text-wrap{
        top: 133rpx;
        left: 87rpx;
      }
    }

    .banner2-wrap{
      margin-top: -26rpx;
      height: 284rpx;
      background-image: url('./images/bbz-guide-img2.png');

      .text-wrap{
        top: 132rpx;
        left: 331rpx;
      }

    }

    .join-btn{
      margin-top: 43rpx;
      width: 470rpx;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #2AC638;
      border-radius: 50rpx;
      font-family: PingFangSC-Semibold;
      font-size: 36rpx;
      color: #FFFFFF;
      font-weight: 600;
    }

    .close-btn{
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      overflow: hidden;
      position: absolute;
      bottom: -120rpx;
      .close-icon{
        width: 100%;
      }
    }
  }

  .animation {
    position: absolute;
    top: 23%;
    left: 0;
    width: 100vw;
    height: 48%;
    z-index: 0;
    transform: scale(1.4);
    .animation-circle {
      width: 100%;
      height: 100%;
      opacity: 0;
      animation: circleFadeIn 0.67s forwards;
    }
    .animation-light {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      animation: lightFadeInOpacity 0.67s forwards, lightFadeInRotate 2s linear infinite;
    }
  }
}
