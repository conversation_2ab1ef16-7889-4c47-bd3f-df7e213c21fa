import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import useMxState from '@/hooks/useMxState';
import { Rankings, MyInfo } from '@/api/helpPlant/typings';
import { calculateTotalAmount } from '@/logic/store/models/utils';
import config from "@/config";
import stat from '@/lib/stat';

import Title from './images/bbz-guide-title.png';
import CloseIcon from '../assets/modal-close.png';
import dispatch from '@/logic/store';
import { convertCentsToPoint } from '@/lib/utils/formatNumber';

export default function Index(props) {
  const [multiResource] = useMxState<Rankings[]>('helpPlant.multiResource', []);
  const [rankRewardList] = useMxState<MyInfo>('helpPlant.homeData.rankRewardList', []);
  const [displayRankNum] = useMxState<number>('helpPlant.rankingList.displayRankNum');
  const resourceTaskList = multiResource?.[config.bbzResourceCode]?.taskList || [];
  const topOnePrizes = Number(rankRewardList?.[0]?.prizeValue) / 100;

  // 蓄水任务的肥料
  const totalPoint = calculateTotalAmount(resourceTaskList)
  const calculateRewardAmount = convertCentsToPoint(totalPoint ?? 0, false);

  useEffect(() => {
    stat.exposure('bbz_nu_exposure', {
      c: 'activity',
      d: 'pop'
    })
  }, [])

  const handleJoinBtn = () => {
    handleClose(1);
  }

  const handleClose = (position: number) => {
    stat.click('bbz_nu_click', {
      c: 'activity',
      d: 'pop',
      click_position: position,
    })
    baseModal.close(props?.id);
    dispatch.helpPlant.set({
      showHandGuide: true
    });
    const departmentSection = document.querySelector('.tips-comp');
    departmentSection?.scrollIntoView({ behavior: 'smooth' });
  }

  return (
    <div className="modal-bbz-beginner-guidance">
      <div className="beginner-guidance-content">
        <div className="head-wrap row">
          <img src={Title} className="title-img" alt="" />
          <div className="title-text">大奖天天有，随时都能玩</div>
        </div>
        <div className="banner1-wrap img-wrap">
          <div className="text-wrap">
            <p className="tips-text">邀1人至少2助力值</p>
            <p className="award-tips">
              助力值叠加领最高
              <span>{calculateRewardAmount}</span>
              <span className="unit">万</span>
              肥料
            </p>
          </div>
        </div>
        <div className="banner2-wrap img-wrap">
          <div className="text-wrap">
            <p className="tips-text">助力值排名再奖现金</p>
            <p className="award-tips">
              进入前{displayRankNum || 100}名奖励最高
              <span>{topOnePrizes}</span>
              <span className="unit">元</span>
            </p>
          </div>
        </div>
        <div className="join-btn" onClick={handleJoinBtn}>立即参加</div>
        <div className="close-btn" onClick={() => handleClose(2)}>
          <img src={CloseIcon} className="close-icon" alt="" />
        </div>
      </div>
    </div>
  )
}
