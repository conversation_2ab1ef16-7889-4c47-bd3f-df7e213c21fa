import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import CloseIcon from '../assets/modal-close.png';
import BenefitsTopBgImg from './images/benefits-top-bg.png';
import { uniqueDateFormat } from '@/lib/utils/date';
import { checkTaskFinished, logToFinishTask, getTaskAward, hideReceiveBtnTask } from '@/pages/index/components/TaskPop/TaskList/util';
import { taskActionHandler } from '@/pages/index/components/TaskPop/TaskList/help';
import { geneTaskRequestId } from '@/logic/store/models/utils';
import mx from '@ali/pcom-mx';
import { MainAPI } from '@/logic/type/event';
import { TasklistSource } from '@/pages/index/utils';
import { TASK_EVENT_TYPE, TASK_LOCATION, TASK_STATUS, TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import Fact from '@/components/Fact';
import useMxState from '@/hooks/useMxState';
import { ITimeLimitTaskState } from '@/logic/store/models/limit/types';
import dispatch from '@/logic/store';
import { getShowLimitTaskList } from '@/logic/store/models/limit/utils';

// 限时任务弹窗
const Index = () => {
  const [timeLimitTask] = useMxState<ITimeLimitTaskState>('timeLimitTask');
  const highValueTask = mx.store.get('highValueTask');
  const { endTime } = timeLimitTask;

  const showTaskList = getShowLimitTaskList()

  const taskNumber = (showTaskList || []).filter((item) => {
    return !checkTaskFinished(item);
  }).length;

  // 关闭
  const handleClose = () => {
    baseModal.close(MODAL_ID.LIMITED_TIME_BENEFITS_TASK);
    dispatch.timeLimitTask.closeLimitDialog();
  };

  // 去做任务
  const toDoTask = (item, index) => {
    const { event, state } = item;
    // 添加任务完成监控
    logToFinishTask(item, TASK_LOCATION.LIMITED_TIME_BENEFITS);
    return async () => {
      // 任务完成后，统一打开任务面板
      if (checkTaskFinished(item)) {
        handleClose();
        mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: TasklistSource.award_task });
        return;
      }

      // 下载类任务
      if (state === TASK_STATUS.TASK_NOT_COMPLETED && [TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD].includes(item.event)) {
        const result = await dispatch.task.checkAppDownloadFinish(item, highValueTask.currentTaskInfo?.id !== item.id);
        if (result) {
          return;
        }
      }
      // 完成待领取
      if (state === TASK_STATUS.TASK_COMPLETED) {
        console.log('领取奖励')
        dispatch.task.finishTask({
          taskId: item?.id,
          type: "award",
          useUtCompleteTask: !!item?.useUtCompleteTask,
          publishId: item?.publishId
        });
        return;
      }
      switch (event) {
        // 去施肥
        case TASK_EVENT_TYPE.TASK_SIGN_SUB_STORE:
          handleClose();
          return;
        // 去签到
        case TASK_EVENT_TYPE.UCLITE_SIGN:
          handleClose();
          mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source: TasklistSource.award_task });
          return;
        default:
          await taskActionHandler(item, geneTaskRequestId());
      }
    };
  };

  const getTaskBtn = (taskInfo: TaskInfo) => {
    const { state, event } = taskInfo;
    if (state === TASK_STATUS.TASK_NOT_COMPLETED && [TASK_EVENT_TYPE.RTA_CALL_TAOBAO, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU, TASK_EVENT_TYPE.RTA_CALL_TAOBAO, TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD, TASK_EVENT_TYPE.CALL_APP_DOWNLOAD].includes(event)) {
      return <span className="btn-receive">待核销</span>;
    }
    if (state === TASK_STATUS.TASK_COMPLETED) {
      return <span className="btn-receive">领取</span>;
    }
    return checkTaskFinished(taskInfo) ? '领肥料' : '去完成'
  };

  return (
    <Fact c="pop" d="task" expoLogkey="daily_task_list_exposure">
      <div className="modal-limited-time-benefits-task">
        <img className="benefits-top-bg" src={BenefitsTopBgImg} alt="" />
        <div className="title">限时福利任务</div>
        <div className="subhead">
          {taskNumber > 0
            ? `${uniqueDateFormat(endTime)}日截止,今天${taskNumber}项任务待完成`
            : `今日挑战已完成，明天再来吧！`}
        </div>
        <div className="benefits-task-list">
          {showTaskList?.map((item, index) => {
            return (
              <div key={index} className="benefits-task-item">
                <div className="benefits-task-name">
                  <div className="name">{item?.name}</div>
                  <div>{item?.dayTimes?.target ? `(${item?.dayTimes?.progress}/${item?.dayTimes?.target})` : ''}</div>
                </div>
                <Fact
                  c={`card${index + 1}`}
                  d="task"
                  expoLogkey="daily_task_exposure"
                  logkey="daily_task_click"
                  expoExtra={{
                    task_id: item?.id,
                    task_name: item?.name,
                    isfinish: checkTaskFinished(item) ? 1 : 0,
                    task_count: item?.dayTimes?.progress,
                    award_amount: getTaskAward(item),
                    task_progress: item?.dayTimes?.progress || '',
                  }}
                  ckExtra={{
                    task_id: item?.id,
                    task_name: item?.name,
                    isfinish: checkTaskFinished(item) ? 1 : 0,
                    task_count: item?.dayTimes?.progress,
                    award_amount: getTaskAward(item),
                    task_progress: item?.dayTimes?.progress || '',
                  }}
                  onClick={toDoTask(item, index)}
                  className={`benefits-task-button ${checkTaskFinished(item) ? 'benefits-task-button-check' : ''}`}
                >
                  {getTaskBtn(item)}
                </Fact>
              </div>
            );
          })}
        </div>
        <div className="close-btn" onClick={handleClose}>
          <img src={CloseIcon} className="close-icon" alt="" />
        </div>
      </div>
    </Fact>
  );
};

export default Index;
