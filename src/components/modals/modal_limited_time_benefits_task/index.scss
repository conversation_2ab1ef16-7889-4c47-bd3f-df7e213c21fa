.modal-limited-time-benefits-task {
  position: relative;
  box-sizing: border-box;
  width: 660rpx;
  border-radius: 44rpx;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  background-image: url(./images/limited_time_benefits.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  padding-top: 34rpx;
  padding-bottom: 25rpx;
  box-sizing: border-box;
  .benefits-top-bg {
    position: absolute;
    top: -176rpx;
    width: 660rpx;
    height: 176rpx;
    background-size: cover;
  }
  .title {
    font-family: PingFangSC-Semibold;
    font-size: 48rpx;
    color: #FF4600;
    font-weight: 600;
    line-height: 67rpx;
  }
  .subhead {
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #000000;
    line-height: 42rpx;
    padding: 16rpx 0;
  }
  .benefits-task-list {
    width: 100%;
    max-height: 400rpx;
    overflow-y: scroll;
    .benefits-task-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 132rpx;
      margin: 0 42rpx;
      border-bottom: 1rpx solid #E0E5E8;
      .benefits-task-name {
        display: flex;
        font-family: PingFangSC-Medium;
        font-size: 32rpx;
        color: #12161a;
        font-weight: 700;
        .name {
          max-width: 320rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .benefits-task-button {
        width: 140rpx;
        height: 60rpx;
        background: #2ac638;
        border-radius: 100rpx;
        font-family: PingFangSC-Semibold;
        font-size: 28rpx;
        color: #ffffff;
        font-weight: 600;
        line-height: 60rpx;
        text-align: center;
        &:has( .btn-receive){
          background: #F9C84E;
        }
      }
      .benefits-task-button-check {
        background: #D4DADE;
      }
      &:nth-last-child(1) {
        border-bottom: none;
      }
    }
  }
  .close-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -112rpx;
    .close-icon {
      width: 100%;
    }
  }
}
