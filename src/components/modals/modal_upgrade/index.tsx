import React from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { BaseModalProps} from '@/lib/modal/typings';
import { MODAL_ID } from '@/components/modals/types';
import modalCloseImg from '../assets/modal-close.png';
import arrowIcon from './assets/<EMAIL>';

// 果树升级弹窗
interface IProps extends BaseModalProps {
  prevStageLevel: number; // 之前等级
  prevSeedImg: string; // 之前图片
  stageLevel: number; // 当前等级
  upgradeImg: string; // 当前树图片
}

const Index = (props: IProps) => {
  const { prevStageLevel, prevSeedImg, stageLevel, upgradeImg } = props
  console.log('props:', props)
  const handleConfirm = () => {
    close();
  };
  // 关闭弹窗
  const close = () => {
    baseModal.close(MODAL_ID.UPGRADE);
  };

  return (
    <div className="modal-upgrade">
      <div className="modal-upgrade-content">
        <div className="tree-wrapper row j-center">
          <div className="tree-before column align-c">
            <img src={prevSeedImg} className="tree-img" />
            <div className="grade-wrap grade-before">{prevStageLevel}级</div>
          </div>
          <img className="upgrade-arrow" src={arrowIcon} />
          <div className="tree-after column align-c">
            <img src={upgradeImg} className="tree-img" />
            <div className="grade-wrap grade-after">{stageLevel}级</div>
          </div>
        </div>
        <div className="upgrade-title">
          果树升级啦!
        </div>
        <div className="upgrade-subtitle">
          加油升级果树，免费水果包邮到家
        </div>
      </div>
      <div onClick={handleConfirm} className="confirm-btn">
        继续施肥
      </div>
      <img onClick={close} className="close-img" src={modalCloseImg} alt="" />
    </div>
  );
};

export default Index;
