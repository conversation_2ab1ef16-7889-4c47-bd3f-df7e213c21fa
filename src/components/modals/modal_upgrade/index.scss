.modal-upgrade {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .modal-upgrade-content {
    position: relative;
    width: 670rpx;
    height: 679rpx;
    background-image: url("./assets/<EMAIL>");
    background-size: 100% auto;
    background-repeat: no-repeat;
    padding-top: 100rpx;
    box-sizing: border-box;
    .lottie-area {
      z-index: 1;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }
  }
  .tree-wrapper {
    position: relative;
    width: 459rpx;
    justify-content: space-between;
    margin: 0 auto;
    .tree-before {
      .tree-img {
        width: 180rpx;
        height: 220rpx;
      }
    }
    .tree-after {
      .tree-img {
        width: 225rpx;
        height: 275rpx;
      }
    }

    .grade-wrap {
      margin-top: 5rpx;
      width: 140rpx;
      height: 80rpx;
      background: #E3F8E7;
      border-radius: 24rpx;
      font-size: 42rpx;
      letter-spacing: 0;
      text-align: center;
      font-weight: 700;
      line-height: 80rpx;
    }
    .grade-before {
      color: #18AA32;
    }
    .grade-after {
      background: #FFEAB8;
      color: #8B553A;
    }
    .upgrade-arrow {
      position: absolute;
      width: 68rpx;
      height: 68rpx;
      bottom: 3rpx;
      left: 184rpx;
    }
    .tree-before, .tree-after {
      justify-content: flex-end;
    }
  }
  .upgrade-title {
    margin-top: 32rpx;
    font-size: 42rpx;
    line-height: 59rpx;
    color: #08881F;
    letter-spacing: 0;
    text-align: center;
    font-weight: 700;
  }
  .upgrade-subtitle {
    margin-top: 16rpx;
    font-size: 32rpx;
    line-height: 45rpx;
    color: #12161A;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
  .confirm-btn {
    margin-top: 40rpx;
    width: 470rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
  }
  .close-img {
    margin-top: 64rpx;
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
  }
}
