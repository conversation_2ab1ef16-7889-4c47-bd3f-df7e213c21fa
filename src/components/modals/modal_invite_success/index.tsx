import React, {Fragment, useEffect} from 'react';
import './index.scss';
import NewUserGiftIcon from './images/new-user-award-icon.png';
import mx from '@ali/pcom-mx';
import baseModal from '@/lib/modal';
import { handleShareTask } from '@/pages/index/components/TaskPop/TaskList/help';
import { ITaskState } from '@/logic/store/models/task/typing';
import UserGiftIcon from './images/user-award-icon.png';
import { USER_TYPE_TEXT_MAP, ICodeAssistListItem, IAssistUserType } from '@/logic/store/models/share/typing';
import stat from '@/lib/stat';
import { TASK_EVENT_TYPE } from '@/pages/index/components/TaskPop/TaskList/types';
import { checkTaskFinished, getTaskAward } from '@/pages/index/components/TaskPop/TaskList/util';
import { MainAPI } from '@/logic/type/event';
import { IUserState } from '@/logic/store/models/user/typings';
import { geneTaskRequestId } from '@/logic/store/models/utils';

interface IProps {
  id: string;
  allNeedPopList: ICodeAssistListItem[];
  assistUserTypeList: Map<IAssistUserType, ICodeAssistListItem[]>;
}

export default function Index(props: IProps) {
  const { allNeedPopList = [], id, assistUserTypeList } = props;
  const count = allNeedPopList?.length;
  const taskStore: ITaskState = mx.store.getStore().task;
  const user: IUserState = mx.store.getStore().user;
  // 取出未完成的分享任务
  const shareList = taskStore?.taskList?.filter((item) => item?.event === TASK_EVENT_TYPE.SHARE && !checkTaskFinished(item));
  const shareTask = shareList && shareList.length > 0 ? shareList[0] : null;

  // 用户展示助力用户的顺序
  const assistUserList: IAssistUserType[] = ['BIZ_NEW', 'RECALL_USER', 'BIZ_OLD', 'ALL_USER'];
  
  useEffect(() => {
    stat.exposure('sharer_toast_exposure', {
      c: 'toast',
      d: 'sharer',
      share_status: 1
    })
  }, [])

  const handleInvite = () => {
    baseModal.close(id);
    const tasklist_source = 'invite_success';
    if (shareTask) {
      handleShareTask(shareTask);

      // 补打分享任务曝光&点击
      const requestId = geneTaskRequestId();
      const defaultParams = {
        c: `card-1`,
        d: 'task',
        task_id: shareTask?.id,
        task_name: shareTask?.name,
        isfinish: checkTaskFinished(shareTask) ? 1 : 0,
        task_count: shareTask?.dayTimes?.progress,
        page_status: user?.bindTaobao ? 1 : 0,
        tasklist_source: tasklist_source,
        taskclassify: shareTask?.taskClassify,
        groupcode: shareTask?.groupCode,
        award_amount: getTaskAward(shareTask),
        task_progress: shareTask?.dayTimes?.progress || '',
        requestId,
      }
      stat.exposure('task_exposure', defaultParams);
      stat.click('task_click', defaultParams);
      return;
    }
    mx.event.emit(MainAPI.ShowTaskPop, { tasklist_source });
  };

  const getRewardAmount = (list: ICodeAssistListItem[]) => {
    let amount = 0;
    amount = list?.reduce((pre, next) => {
      // eslint-disable-next-line no-return-assign
      return pre += next?.inviterPrizes?.[0]?.rewardItem?.amount
    }, 0)
    return amount
  }

  return (
    <div className="modal-invite-success column">
      <div className="title">助力奖励已到账</div>
      <div className="sub-title">{count}位好友助力，共获得<span>{getRewardAmount(allNeedPopList)}</span>肥料</div>
      <div className="award-list">
        {
          assistUserList?.map((userType, index) => {
            if (assistUserTypeList.get(userType)) {
              return (
                <div className="award-list-item row" key={userType}>
                  <div className="award-info-wrap row">
                    <img src={userType === 'BIZ_NEW' ? NewUserGiftIcon : UserGiftIcon} className="gift-icon" alt="" />
                    <div className="user-type">{USER_TYPE_TEXT_MAP[userType] || '老用户'}{`x${assistUserTypeList.get(userType)?.length}`}</div>
                  </div>
                  <div className="award-amount">+{getRewardAmount(assistUserTypeList.get(userType) ?? [])}</div>
                </div>
              )
            }
            return <Fragment />
          })
        }
      </div>
      <div className="invite-btn flex-center" onClick={handleInvite}>{shareTask ? '继续邀请' : '看看其他'}</div>
    </div>
  )
}
