.modal-bbz-help-fail-comp{
  width: 590rpx;
  min-height: 600rpx;
  background: #FFFFFF;
  border-radius: 44rpx;
  background-image: url('../assets/modal-gray-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 180rpx;
  position: relative;
  padding-top: 130rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -172rpx;
  padding-bottom: 64rpx;

  .head-wrap{
    width: 716rpx;
    height: 226rpx;
    background-image: url('../assets/help-fail-bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    position: absolute;
    top: -153rpx;
    left: 50%;
    transform: translateX(-50%);

    flex-direction: column;
    align-items: center;

    .bbz-text{
      margin-top: 79rpx;
      width: 189rpx;
    }

    .title-img{
      margin-top: 11rpx;
      width: 369rpx;
      height: 69rpx;
    }
  }

  .text-wrap{
    width: 452rpx;
    height: 112rpx;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    span{
      font-family: PingFangSC-Semibold;
      font-size: 40rpx;
      color: #12161A;
      font-weight: 600;
    }
  }

  .tips{
    margin-top: 36rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #859199;
    font-weight: 400;
    position: relative;

    &::before,
    &::after{
      content: '';
      width: 120rpx;
      height: 1rpx;
      background-color: #E0E5E8;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    &::before{
      left: -128rpx;
    }

    &::after{
      right: -128rpx;
    }
  }

  .award-wrap{
    margin-top: 32rpx;
    align-items: center;
    .award-icon{
      width: 145rpx;
      height: 143rpx;
      box-sizing: border-box;
    }
    .point-icon{
      margin-right: 72rpx;
    }

    .cash-wrap{
      background-image: url('../assets/bbz-cash-icon.png');
      background-repeat: no-repeat;
      background-size: cover;
      position: relative;

      .cash-value{
        width: 57rpx;
        height: 40rpx;
        position: absolute;
        top: 34rpx;
        left: 52rpx;
        font-family: Gilroy-Bold;
        font-size: 36rpx;
        color: #FF500F;
        text-align: center;
        line-height: 40rpx;
      }

      .cash-value-small{
        font-size: 26rpx;
      }
    }
  }

  .look-btn{
    margin-top: 47rpx;
    width: 470rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2AC638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #FFFFFF;
    font-weight: 600;
  }

  .close-btn{
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -120rpx;
    .close-icon{
      width: 100%;
    }
  }
}

.help-fail-success-statue{
  background-image: url('../assets/modal-yellow-bg.png');
  .head-wrap{
    background-image: url('../assets/color-bar-bg.png');
  }
}