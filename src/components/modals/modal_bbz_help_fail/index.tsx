import React, { useState, useRef, useEffect } from 'react';
import './index.scss';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';
import baseModal from '@/lib/modal';
import { HELP_CODE_MAP, getFarmHelpPlantFactParams } from '@/pages/index/utils';
import { getNumberLength } from '@/lib/utils/formatNumber';
import useMxState from '@/hooks/useMxState';
import { IFarmHelpPlantState } from '@/logic/store/models/farmHelpPlant/type';
import classNames from 'classnames';
import stat from '@/lib/stat';

import BbzText from '@/assets/bbz-banner-title.png';
import TitleImg from '../assets/help-fail-title.png';
import PointImg from '../assets/bbz-point-icon.png';
import CloseIcon from '../assets/modal-close.png';
import dispatch from '@/logic/store';
import SuccTitleImg from '../assets/helped-fail-title.png';

let timer;

interface IAssistFailProps {
  id: string;
  toastData: {
    type: 'success' | 'fail';
    title: string;
    subTitle: string;
    delay?: number;
  };
  errCode: string;
}
export default function Index(props: IAssistFailProps) { 
  const {toastData, errCode} = props; 
  const [farmHelpPlant] = useMxState<IFarmHelpPlantState>('farmHelpPlant');
  const [waitTime, setWaitTime] = useState(10);
  const waitTimeRef = useRef(waitTime);
  waitTimeRef.current = waitTime;

  const { prizeValue } = farmHelpPlant.topOnePrizeInfo;
  const maxRankAward = Number(prizeValue);
  // 接口错误状态码code
  const titles = (HELP_CODE_MAP[errCode]?.subTitle ?? '网络太繁忙，请重新试试吧')?.split('，'); 
  // 助力过使用成功的氛围, 不使用失败的氛围 
  const showSuccStatus = [
    'INVITE:ALREADY_BIND_CODE', 
    'INVITE:BIND_CODE_LIMIT',
    'MULTI_ASSIST_DAILY_LIMIT'
  ].includes(errCode)

  usePageVisibilityListener((visible: boolean) => {
    if (visible) {
      timer = setInterval(() => {
        if (waitTimeRef.current > 1) {
          setWaitTime(waitTimeRef.current - 1);
        } else {
          handleLook(2);
          clearInterval(timer);
        }
      }, 1000);
    } else {
      clearInterval(timer);
    }
  });

  useEffect(() => {
    stat.exposure('bbz_shared_exposure', {
      c: 'bbzhong',
      d: 'shared',
      share_status: 'fail',
      share_fail_reason: HELP_CODE_MAP[errCode]?.status || errCode,
      ...getFarmHelpPlantFactParams(),
    })

    return () => {
      clearInterval(timer);
    }
  }, [])

  const clickFact = (position: number) => {
    stat.click('bbz_shared_click', {
      c: 'bbzhong',
      d: 'shared',
      share_status: 'fail',
      click_position: position,
      share_fail_reason: HELP_CODE_MAP[errCode]?.status || errCode,
      ...getFarmHelpPlantFactParams(),
    })
  }

  const handleLook = (position: number) => {
    clickFact(position);
    dispatch.farmHelpPlant.openActivityPage('shared_pop');
    baseModal.close(props?.id);
  }

  const handleClose = () => {
    clickFact(3);
    baseModal.close(props?.id);
  }

  return (
    <div className={classNames('modal-bbz-help-fail-comp', {'help-fail-success-statue': showSuccStatus})}>
      <div className="head-wrap row">
        <img src={BbzText} className="bbz-text" alt="" />
        <img src={showSuccStatus ? SuccTitleImg : TitleImg} className="title-img" alt="" />
      </div>
      <div className="text-wrap row">
        {titles?.map((titleText, index) => <span key={index}>{titleText}</span>)}
      </div>
      <div className="tips">参与活动有机会领取</div>
      <div className="award-wrap row">
        <img src={PointImg} className="award-icon point-icon" />
        <div className="award-icon cash-wrap">
          <div className={classNames('cash-value', {'cash-value-small': getNumberLength(maxRankAward) > 2})}>
            {maxRankAward / 100}
          </div>
        </div>
      </div>
      <div className="look-btn" onClick={() => handleLook(1)}>去看看({waitTime})</div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  )
}
