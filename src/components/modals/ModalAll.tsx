import React, { Fragment, useRef } from 'react';
import BaseModal from '@/lib/modal/modal';
import ModalSelectSeed from './modal_select_seed';
import ModalNewbieGiftPack from './modal_newbie_gift_pack';
import ModalContinuousSign from './modal_continuous_sign';
import ModalPlantTreesSuccess from './modal_plant_trees_success';
import ModalHelpSuccess from './modal_help_success';
import ModalReceiveFertilizer from './modal_receive_fertilizer';
import ModalUpgrade from '@/components/modals/modal_upgrade';
import ModalWatering from '@/components/modals/modal_watering';
import ModalToast from './modal_toast';
import ModalProgressBubbleFinished from './modal_progress_bubble';
import CollectFertilizer from '@/components/common/CollectFertilizer';
// import ModalTaskTips from '@/components/modals/modal_task_tips';
// import ModalUc20Retention from '@/components/modals/modal_uc20_retention';
import ModalBindingTaobao from '@/components/modals/modal_binding_taobao';
import ModalLimitedTimeBenefitsTask from '@/components/modals/modal_limited_time_benefits_task';
import ModalLimitedTimeBenefitsAward from '@/components/modals/modal_limited_time_benefits_award';
import ModalLimitedTimeBenefitsSelect from '@/components/modals/modal_limited_time_benefits_select';
import ModalTanxAd from "@/components/modals/modal_tanx_ad";
// import ModalLimitedTimeBenefitsTrigger from '@/components/modals/modal_limited_time_benefits_trigger';
import ModalHighValueAward from '@/components/modals/modal_high_value_award';
import ModalHighValueTask from '@/components/modals/modal_high_value_task';
import ModalBackIntercept from './modal_back_intercept';
import ModalInviteSuccess from './modal_invite_success';
import ModalBbzHelpSuccess from './modal_bbz_help_success';
import ModalBbzHelpFail from './modal_bbz_help_fail';
import ModalBbzProgressChallengeAward from './modal_bbz_progress_challenge_award';
import ModalBbzRankingAward from './modal_bbz_ranking_award';
import ModalRiskControl from './modal_risk_control';
import ModalBbzAttendActivity from './modal_bbz_attend_activity';
import ModalBbzBeginnerGuidance from './modal_bbz_beginner_guidance';
import ModalBbzNotice from './modal_bbz_notice';
import ModalDoubleCard from './modal_double_card';
import ModalDoubleCardPublicity from './modal_double_card_publicity';

import { MODAL_ID } from '@/components/modals/types';

// 挪到@/components/modals/types
// export const enum MODAL_ID {
//   SELECT_SEED = 'SELECT_SEED', // 选择种子
//   NEWBIE_GIFT_PACK = 'NEWBIE_GIFT_PACK', // 新人礼包
//   CONTINUOUS_SIGN = 'CONTINUOUS_SIGN', // 连续签到
//   PLANT_TREES_SUCCESS = 'PLANT_TREES_SUCCESS', // 种成
//   HELP_SUCCESS = 'HELP_SUCCESS', // 助力成功
//   RECEIVE_FERTILIZER = 'RECEIVE_FERTILIZER', // 明日领取肥料
//   UPGRADE = 'UPGRADE', // 果树升级弹窗
//   TOAST = 'TOAST', // toast
//   TASK_TIPS = 'TASK_TIPS',
//   PROGRESS_BUBBLE = 'PROGRESS_BUBBLE', // 进度气泡
//   UC20_RETENTION = 'UC20_RETENTION', // 挽留弹窗
//   BINDING_TAOBAO = 'BINDING_TAOBAO', // 绑定淘宝弹窗
//   LIMITED_TIME_BENEFITS_TRIGGER = 'LIMITED_TIME_BENEFITS_TRIGGER', // 限时福利任务弹窗触发
//   LIMITED_TIME_BENEFITS_TASK = 'LIMITED_TIME_BENEFITS_TASK', // 限时福利任务弹窗
//   LIMITED_TIME_BENEFITS_AWARD = 'LIMITED_TIME_BENEFITS_AWARD', // 限时福利任务奖励弹窗
//   LIMITED_TIME_BENEFITS_SELECT = 'LIMITED_TIME_BENEFITS_SELECT', // 限时福利任务选择奖励弹窗
//   HIGH_VALUE_AWARD = 'HIGH_VALUE_AWARD', // 高价值奖励弹窗
//   HIGH_VALUE_TASK = 'HIGH_VALUE_TASK', // 高价值任务弹窗
//   BACK_INTERCEPT = 'BACK_INTERCEPT', // 返回拦截弹窗
//   TANX_AD = 'TANX_AD', // Tanx进阶弹窗
//   INVITE_SUCCESS = 'INVITE_SUCCESS', // 要求助力成功弹框
//   BBZ_HELP_SUCCESS = 'BBZ_HELP_SUCCESS', // 帮帮种助力成功弹框
// }

// 进入弹窗动画
const bodyEnterAnimaitonOptions = [
  {
    style: { transform: 'scale(1)', opacity: 1 },
    options: { timingFunction: 'cubic-bezier(0.34, 2.8, 0.68, 1)', delay: 0, duration: 500 },
  },
];
// 退出弹窗动画
// const bodyLeaveAnimationOptions = [
//   { style: { transform: 'scale(0.4)', opacity: 0 }, options: { timingFunction: 'ease', delay: 0, duration: 300 } },
// ];

function ModalAll(modalData: any) {
  const collectFertilizerRef = useRef<any>(null);
  // 关闭新人礼包弹窗回调
  const isShowFertillzerAnimation = () => {
    collectFertilizerRef?.current?.playFertilizer();
  };
  return (
    <Fragment>
      <CollectFertilizer refProps={collectFertilizerRef} />
      <BaseModal id={MODAL_ID.SELECT_SEED} isMaskCanClose>
        <ModalSelectSeed />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.NEWBIE_GIFT_PACK}
        isMaskCanClose
        isShowStar
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        maskClose={isShowFertillzerAnimation}
      >
        <ModalNewbieGiftPack isShowFertillzerAnimation={isShowFertillzerAnimation} />
      </BaseModal>
      <BaseModal id={MODAL_ID.PLANT_TREES_SUCCESS} isShowStar bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalPlantTreesSuccess />
      </BaseModal>
      <BaseModal id={MODAL_ID.RECEIVE_FERTILIZER} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalReceiveFertilizer {...modalData} />
      </BaseModal>
      <BaseModal id={MODAL_ID.CONTINUOUS_SIGN} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalContinuousSign {...modalData} id={MODAL_ID.CONTINUOUS_SIGN} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.HELP_SUCCESS}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
      >
        <ModalHelpSuccess {...modalData} id={MODAL_ID.HELP_SUCCESS} />
      </BaseModal>
      <BaseModal id={MODAL_ID.UPGRADE} isMaskCanClose isShowStar bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalUpgrade {...modalData} />
      </BaseModal>
      <BaseModal id={MODAL_ID.WATERING} isMaskCanClose={false} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalWatering {...modalData} />
      </BaseModal>
      {/* <BaseModal
        id={MODAL_ID.TASK_TIPS}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
      >
        <ModalTaskTips {...modalData} id={MODAL_ID.TASK_TIPS} />
      </BaseModal> */}
      <BaseModal id={MODAL_ID.TOAST} isShowMask={false} disableEnterAnimation disableContainerPointerEvent>
        <ModalToast {...modalData} id={MODAL_ID.TOAST} />
      </BaseModal>
      <BaseModal id={MODAL_ID.PROGRESS_BUBBLE} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalProgressBubbleFinished {...modalData} />
      </BaseModal>
      <BaseModal id={MODAL_ID.TANX_AD} isMaskCanClose={false} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalTanxAd {...modalData} />
      </BaseModal>
      {/* <BaseModal id={MODAL_ID.UC20_RETENTION} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalUc20Retention />
      </BaseModal> */}
      <BaseModal id={MODAL_ID.BINDING_TAOBAO} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalBindingTaobao />
      </BaseModal>
      {/* <BaseModal id={MODAL_ID.LIMITED_TIME_BENEFITS_TRIGGER} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalLimitedTimeBenefitsTrigger />
      </BaseModal> */}
      <BaseModal id={MODAL_ID.LIMITED_TIME_BENEFITS_TASK} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalLimitedTimeBenefitsTask />
      </BaseModal>
      <BaseModal id={MODAL_ID.LIMITED_TIME_BENEFITS_AWARD} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalLimitedTimeBenefitsAward {...modalData} />
      </BaseModal>
      <BaseModal id={MODAL_ID.LIMITED_TIME_BENEFITS_SELECT} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}>
        <ModalLimitedTimeBenefitsSelect />
      </BaseModal>
      <BaseModal id={MODAL_ID.HIGH_VALUE_AWARD} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions} isMaskCanClose={false}>
        <ModalHighValueAward {...modalData} />
      </BaseModal>
      <BaseModal id={MODAL_ID.HIGH_VALUE_TASK} bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions} isMaskCanClose={false}>
        <ModalHighValueTask />
      </BaseModal>
      <BaseModal id={MODAL_ID.BACK_INTERCEPT} disableEnterAnimation showBodyLeaveAnimation={false} isMaskCanClose={false}>
        <ModalBackIntercept {...modalData} />
      </BaseModal>
      <BaseModal id={MODAL_ID.DOUBLE_CARD_LOTTERY} disableEnterAnimation showBodyLeaveAnimation={false} isMaskCanClose={false}>
        <ModalDoubleCard {...modalData} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.DOUBLE_CARD_PUBLICITY}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalDoubleCardPublicity {...modalData} id={MODAL_ID.DOUBLE_CARD_PUBLICITY} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.INVITE_SUCCESS}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
      >
        <ModalInviteSuccess {...modalData} id={MODAL_ID.INVITE_SUCCESS} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.BBZ_HELP_SUCCESS}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalBbzHelpSuccess {...modalData} id={MODAL_ID.BBZ_HELP_SUCCESS} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.BBZ_HELP_FAIL}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalBbzHelpFail {...modalData} id={MODAL_ID.BBZ_HELP_FAIL} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.BBZ_PROGRESS_CHALLENGE_AWARD}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalBbzProgressChallengeAward {...modalData} id={MODAL_ID.BBZ_PROGRESS_CHALLENGE_AWARD} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.BBZ_RANKING_AWARD}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalBbzRankingAward {...modalData} id={MODAL_ID.BBZ_RANKING_AWARD} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.RISK_CONTROL}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalRiskControl {...modalData} id={MODAL_ID.RISK_CONTROL} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.BBZ_ATTEND_ACTIVITY}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalBbzAttendActivity {...modalData} id={MODAL_ID.BBZ_ATTEND_ACTIVITY} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.BBZ_BEGINNER_GUIDANCE}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalBbzBeginnerGuidance {...modalData} id={MODAL_ID.BBZ_BEGINNER_GUIDANCE} />
      </BaseModal>
      <BaseModal
        id={MODAL_ID.BBZ_NOTICE}
        bodyEnterAnimaitonOptions={bodyEnterAnimaitonOptions}
        isMaskCanClose={false}
      >
        <ModalBbzNotice {...modalData} id={MODAL_ID.BBZ_NOTICE} />
      </BaseModal>
    </Fragment>
  );
}

export default ModalAll;
