.modal-ranking-award-comp{
  width: 590rpx;
  min-height: 400rpx;
  background: #FFFFFF;
  border-radius: 44rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 88rpx;
  padding-bottom: 64rpx;

  .head-img{
    width: 750rpx;
    height: 350rpx;
    background-image: url('../assets/bbz-rank-award-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: -170rpx;
    left: 50%;
    transform: translateX(-50%);
  }

  .title-wrap{
    position: relative;
    height: 46rpx;
    line-height: 46rpx;
    align-items: center;
    .date-text{
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #12161A;
      font-weight: 400;
    }
    .help-icon{
      margin: 0 1rpx;
      margin-left: 4rpx;
      width: 24rpx;
      height: 24rpx;
    }
    .help-value{
      font-family: D-DIN-Bold;
      font-size: 31rpx;
      color: #FA6425;
    }
  }
  .ranking-text{
    height: 46rpx;
    line-height: 46rpx;
    position: relative;
    margin-top: 12rpx;
    font-family: PingFangSC-Semibold;
    font-size: 40rpx;
    color: #12161A;
    font-weight: 600;

    span{
      color: #FA6425;
    }
  }
  .tips{
    margin-top: 24rpx;
    margin-bottom: 8rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #AAB5BB;
    font-weight: 400;
    position: relative;

    &::before,
    &::after{
      content: '';
      width: 169rpx;
      height: 1rpx;
      background-color: #E0E5E8;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    &::before{
      left: -178rpx;
    }

    &::after{
      right: -178rpx;
    }
  }

  .award-wrap{
    justify-content: center;
    .award-item{
      width: 160rpx;
      height: 160rpx;
      position: relative;
      .award-img{
        width: 100%;
      }

      .value-wrap{
        display: inline-block;
        background: #FFEAB8;
        border-radius: 20rpx;
        padding: 10rpx 32rpx 10rpx 32rpx;
        position: absolute;
        bottom: -44rpx;
        left: 50%;
        transform: translateX(-50%);
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        color: #662A02;
        font-weight: 600;
        white-space: nowrap;
      }
    }
    .award-item:nth-child(2){
      margin-left: 82rpx;
    }
  }

  .confirm-btn{
    margin-top: 87rpx;
    width: 470rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2AC638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #FFFFFF;
    font-weight: 600;
  }

  .close-btn{
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -112rpx;
    .close-icon{
      width: 100%;
    }
  }
}
