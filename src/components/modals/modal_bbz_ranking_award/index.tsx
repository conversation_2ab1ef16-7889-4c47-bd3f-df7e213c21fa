import React, { useEffect } from 'react';
import './index.scss';
import { formatTimestamp } from '@/lib/utils/date';
import baseModal from '@/lib/modal';
import HelpIcon from '@/assets/red-love-icon.png';
import CloseIcon from '../assets/modal-close.png';
import dispatch from '@/logic/store';
import { MODAL_ID } from '../types';
import { openPage } from '@/lib/ucapi';
import config from '@/config';
import stat from '@/lib/stat';
import { getFarmHelpPlantFactParams } from '@/pages/index/utils';

interface IProps {
  id: string;
  /** 上榜获奖信息 */
  rankAwardInfo: {
    timestamp: number;
    score: number;
    prizeAmount: number;
    prizeName: number;
    ranking: number;
    prizeMark: string;
    prizeIcon: string;
  };
  curTime: number;
  // 是否农场
  isFarmPage: boolean;
}

export default function Index(props: IProps) {
  const { ranking, timestamp, score = 0, prizeAmount = 0, prizeMark = '', prizeIcon = '', } = props?.rankAwardInfo ?? {};

  const handleWithdraw = () => {
    stat.click('bbz_award_click', {
      c: 'bbzhong',
      d: 'award',
      click_position: 1,
      assist_rank_reward: prizeMark === 'cash' ? Number(prizeAmount / 100) : prizeAmount,
      ...(props?.isFarmPage ? getFarmHelpPlantFactParams() : {}),
    })
    openPage(config?.walletUrl);
    baseModal.close(props?.id);
  };

  const handleClose = () => {
    stat.click('bbz_award_click', {
      c: 'bbzhong',
      d: 'award',
      click_position: 2,
      assist_rank_reward: prizeMark === 'cash' ? Number(prizeAmount / 100) : prizeAmount,
      ...(props?.isFarmPage ? getFarmHelpPlantFactParams() : {}),
    })
    baseModal.close(props?.id);
  };

  const getPrizeValue = () => {
    switch (prizeMark) {
      case 'cash':
        return `¥${Number(prizeAmount / 100)}红包`;
      case 'lottery':
        return `${prizeAmount}张抽奖券`;
      default:
        return `${prizeAmount}肥料`;
    }
  };

  useEffect(() => {    
    stat.exposure('bbz_award_exposure', {
      c: 'bbzhong',
      d: 'award',
      assist_rank_reward: prizeMark === 'cash' ? Number(prizeAmount / 100) : prizeAmount,
      ...(props?.isFarmPage ? getFarmHelpPlantFactParams() : {}),
    })
    dispatch.dialog.closePopup(MODAL_ID.BBZ_RANKING_AWARD, props.curTime);
  }, []);

  return (
    <div className="modal-ranking-award-comp">
      <div className="head-img" />
      <div className="title-wrap row">
        <span className="date-text">{formatTimestamp(timestamp, 'MM月DD日', false)}累计助力值</span>
        <img src={HelpIcon} className="help-icon" alt="" />
        <span className="help-value">{score}</span>
      </div>
      <div className="ranking-text">
        荣获芭芭助力榜<span>第{ranking}名</span>
      </div>
      <div className="tips">获得奖励</div>
      <div className="award-wrap row">
        <div className="award-item">
          <img src={prizeIcon} className="award-img" alt="" />
          <span className="value-wrap">{getPrizeValue()}</span>
        </div>
      </div>
      <div className="confirm-btn" onClick={handleWithdraw}>
        立即提现
      </div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
}
