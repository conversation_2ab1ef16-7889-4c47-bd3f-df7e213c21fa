import { useEffect, useCallback } from 'react';
import stat from '@/lib/stat';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { getTaskAward, checkTaskFinished } from '@/pages/index/components/TaskPop/TaskList/util';

export const useTracker = (taskInfo: TaskInfo) => {
  const { id, name, taskClassify, groupCode, progress } = taskInfo;
  // 初始曝光埋点
  useEffect(() => {
    stat.exposure('resource_exposure', {
      c: 'exposure',
      d: 'resource',
      resource_location: 'retain_popup'
    });
    stat.exposure('task_exposure', {
      c: 'exposure',
      d: 'task',
      task_id: id,
      task_name: name,
      taskclassify: taskClassify,
      groupcode: groupCode,
      award_amount: getTaskAward(taskInfo),
      task_count: progress || '',
      isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
      resource_location: 'retain_popup'
    });
  }, []);

  // 点击埋点
  const trackClick = useCallback((click_area: string) => {
    stat.click('resource_click', {
      c: 'click',
      d: 'resource',
      resource_location: 'retain_popup',
      click_area,
    });
  }, [id, name, taskClassify, groupCode, progress, taskInfo]);

  // 关闭埋点
  const trackClose = useCallback((type: string) => {
    stat.click('back_intercept_click', {
      c: 'modal',
      d: type,
      // task_id: id,
    });
  }, []);

  return {
    trackClick,
    trackClose
  };
};
