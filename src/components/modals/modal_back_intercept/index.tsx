import cz from 'classnames';
import './index.scss';
import { Carousel } from '@/components/Carousel';
import React, { memo, useCallback, useMemo, useRef, useState, useEffect } from 'react';
import { MODAL_ID } from '@/components/modals/types';
import baseModal from '@/lib/modal';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import { useTracker } from './useTracker';
import { getTaskAward, getTaskAmountType, AMOUNT_TYPE } from '@/pages/index/components/TaskPop/TaskList/util';
import config from '@/config';
import dispatch from '@/logic/store';

interface IProps {
  onClose?: () => void;
  onConfirm?: () => void;
  imgs?: Array<{ img: string }>;
  title?: string;
  leftIcon?: string;
  taskInfo: TaskInfo & { autoPlay: number; duration: number };
  mainName?: string;
  subName?: string;
}

const ModalBackIntercept = (props: IProps) => {
  const {
    onClose,
    onConfirm,
    imgs = [],
    title = '',
    leftIcon = '',
    taskInfo,
    mainName,
    subName,
  } = props;
  const [isLeaving, setIsLeaving] = useState(false);
  const closeTypeRef = useRef<'close' | 'confirm' | ''>('');
  const { btnName } = taskInfo;
  const { trackClick, trackClose } = useTracker(taskInfo);

  useEffect(() => {
    dispatch.highValueTask.resourceExposure(taskInfo, 'EXPOSURE', config.getBackInterceptCode)
  }, [])

  const handleClose = useCallback(() => {
    closeTypeRef.current = 'close';
    trackClick('close');
    setIsLeaving(true);
  }, [trackClick]);

  const handleConfirm = useCallback(() => {
    closeTypeRef.current = 'confirm';
    trackClick('function');
    setIsLeaving(true);
  }, [trackClick]);

  const handleAnimationEnd = useCallback(() => {
    if (!isLeaving) return;

    const type = closeTypeRef.current;
    trackClose(type);
    baseModal.close(MODAL_ID.BACK_INTERCEPT);

    if (type === 'close') {
      onClose?.();
    } else if (type === 'confirm') {
      onConfirm?.();
    }
  }, [isLeaving, trackClose, onClose, onConfirm]);

  const modalClassName = useMemo(
    () =>
      cz('modal-back-intercept', {
        leave: isLeaving,
      }),
    [isLeaving],
  );

  const taskAmount = useMemo(() => {
    const rawAmount = getTaskAward(taskInfo);
    const amountType = getTaskAmountType(taskInfo);
    if (rawAmount > 0 && mainName) {
      const amount = amountType === AMOUNT_TYPE.CASH ? (rawAmount / 100).toFixed(2) : rawAmount;
      return `${mainName}，得${amount}${amountType === AMOUNT_TYPE.CASH ? '元' : '肥料'}`;
    }
    return '';
  }, [taskInfo, mainName]);

  const closeButtonProps = useMemo(
    () => ({
      className: 'intercept-close',
      src: 'https://gw.alicdn.com/imgextra/i4/O1CN01ePmdAL1RB1fv8Z1Qs_!!6000000002072-2-tps-32-32.png',
      onClick: handleClose,
    }),
    [handleClose],
  );

  const buttonProps = useMemo(
    () => ({
      text: btnName || '去完成',
      onClick: handleConfirm,
    }),
    [btnName, handleConfirm],
  );

  const carouselProps = useMemo(() => ({
    className: 'preview-img',
    imgs,
    autoplay: !!(+(taskInfo.autoPlay ?? 1)),
    interval: taskInfo.duration || 3000,
  }), [imgs, taskInfo]);

  return (
    <div className="modal-back-intercept-wrapper">
      <div className={modalClassName} onAnimationEnd={handleAnimationEnd}>
      <img className="intercept-logo" src={leftIcon || 'https://gw.alicdn.com/imgextra/i4/O1CN01uT3Zrz1O05MfCTlne_!!6000000001642-2-tps-750-654.png'} alt="" />
      <img {...closeButtonProps} />
        <div className="intercept-header">
          <div className="intercept-header-title">{title || '先别走! 还有肥料等你领!'}</div>
        </div>
        <div className="slideshow">
          <Carousel {...carouselProps} />
        </div>
        <div className="intercept-content">
          <div className="intercept-title">{taskAmount}</div>
          {!!subName && <div className="intercept-desc">{subName}</div>}
        </div>
        <div className="intercept-btn" onClick={buttonProps.onClick}>
          <div className="btn-text">{buttonProps.text}</div>
        </div>
      </div>
    </div>
  );
};

ModalBackIntercept.displayName = 'ModalBackIntercept';

export default memo(ModalBackIntercept);
