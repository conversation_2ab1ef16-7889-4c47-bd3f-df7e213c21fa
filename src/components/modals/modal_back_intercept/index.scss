@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideOutDown {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(120%);
  }
}

.modal-back-intercept-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.modal-back-intercept {
  position: relative;
  width: 100vw;
  background: #ffffff;
  background-image: url(https://gw.alicdn.com/imgextra/i3/O1CN01fC7xcl1KLpi8DE8r4_!!6000000001148-0-tps-1500-428.jpg);
  background-size: 100% 214rpx;
  background-position: center top;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: calc(40rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  border-radius: 48rpx 48rpx 0 0 !important;
  animation: slideInUp 0.3s ease-out forwards;

  &.leave {
    animation: slideOutDown 0.3s ease-in forwards;
  }
  .intercept-logo {
    position: absolute;
    left: 6rpx;
    top: -95rpx;
    width: 250rpx;
    height: 218rpx;
    background-size: cover;
  }
  .intercept-close {
    position: absolute;
    top: 15rpx;
    right: 15rpx;
    width: 32rpx;
    height: 32rpx;
    background-size: cover;
    opacity: 30%;
    padding: 15rpx;
  }
  .intercept-header {
    width: 100%;
    display: flex;
    padding-top: 44rpx;
    padding-bottom: 40rpx;
    .intercept-header-title {
      width: 410rpx;
      white-space: nowrap;
      overflow: hidden;
      margin-left: 256rpx;
      font-family: PingFangSC-Semibold;
      font-size: 36rpx;
      color: #005210;
      font-weight: 700;
      line-height: 50rpx;
    }
  }

  .slideshow {
    padding: 0 59rpx;
    .intercept-preview {
      width: 100%;
      height: 428rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      .preview-img {
        width: 100%;
      }
    }
  }

  .intercept-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 24rpx;

    .intercept-title {
      width: 630rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-family: PingFangSC-Semibold;
      font-size: 40rpx;
      color: #12161a;
      text-align: center;
      font-weight: 700;
      line-height: 56rpx;
    }

    .intercept-desc {
      width: 630rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-top: 16rpx;
      font-family: PingFangSC-Regular;
      font-size: 32rpx;
      color: #859199;
      text-align: center;
      line-height: 45rpx;
    }
  }

  .intercept-btn {
    width: 622rpx;
    height: 88rpx;
    background: linear-gradient(270deg, #ff4b4b 0%, #ff6b6b 100%);
    background: #2ac638;
    border-radius: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40rpx;

    .btn-text {
      font-family: PingFangSC-Semibold;
      font-size: 34rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      line-height: 88rpx;
    }
  }
}
