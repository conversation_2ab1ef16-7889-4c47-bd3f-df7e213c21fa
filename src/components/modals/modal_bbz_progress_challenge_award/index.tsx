import React, { Fragment, useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { TaskInfo } from '@/pages/index/components/TaskPop/TaskList/types';
import stat from '@/lib/stat';

import TitleImg from '../assets/gain-help-value-title.png';
import CloseIcon from '../assets/modal-close.png';
import PointImg from '../assets/manure.png';
import RedGreenIcon from '@/assets/red-love-icon.png';
import dispatch from '@/logic/store';
import classNames from 'classnames';

interface IProps {
  id: string;
  dataInfo: {
    /** 累计肥料奖励 */
    totalPoint: number;
    /** 当前助力值  */
    curHelpValue: number;
    /** 距离下一阶段所需助力值  */
    nextHelpValue: number;
    /** 下一阶段奖励 */
    nextAward: number;
    /** 是否已完成所有挑战 */
    finishAllTask: boolean;
    /** 榜1奖励 */
    rankMaxAward: number;
  };
  /** 来源任务 - 埋点用 */
  sourceTaskList: TaskInfo[];
}

export default function Index(props: IProps) {
  const { totalPoint, curHelpValue, nextAward, nextHelpValue, finishAllTask, rankMaxAward } = props?.dataInfo ?? {};
  const sourceTaskList = props?.sourceTaskList || [];

  useEffect(() => {
    stat.exposure('bbz_awardpop_exposure', {
      c: 'activity',
      d: 'award',
      task_reward: totalPoint,
    });
    // eslint-disable-next-line array-callback-return
    sourceTaskList?.map((task) => {
      stat.exposure('task_exposure', {
        c: 'activity',
        d: 'award',
        resource_location: 'bbzhong',
        task_id: task.id || '',
        task_name: task.name || '',
        task_count: task.dayTimes?.progress || 0,
        taskclassify: task.taskClassify || '',
        groupcode: task?.groupCode,
      });
    });
  }, []);

  const clickFact = (position: number) => {
    stat.click('bbz_awardpop_click', {
      c: 'activity',
      d: 'award',
      task_reward: totalPoint,
      click_position: position,
    });
    // eslint-disable-next-line array-callback-return
    sourceTaskList?.map((task) => {
      stat.click('task_click', {
        c: 'activity',
        d: 'award',
        resource_location: 'bbzhong',
        task_id: task.id || '',
        task_name: task.name || '',
        task_count: task.dayTimes?.progress || 0,
        taskclassify: task.taskClassify || '',
        groupcode: task?.groupCode,
      });
    });
  };

  const handleInvite = () => {
    clickFact(1);
    baseModal.close(props?.id);
    dispatch.share.plantOpenSharePanel('reward_pop', false);
  };

  const handleClose = () => {
    clickFact(2);
    baseModal.close(props?.id);
  };

  // 完成所有任务
  const renderBottomByFinishAllTask = () => {
    return (
      <Fragment>
        <div className="line" />
        <div className="help-value row">
          <span>已解锁所有助力值奖励</span>
        </div>
        <div className="help-tips row">
          <Fragment>
            <span>加油! 继续冲击最高</span>
            <span className="value-red din-num">¥{rankMaxAward}</span>
            <span>排名奖励!</span>
          </Fragment>
        </div>
      </Fragment>
    );
  };
  const renderBottomByFinishSomeTask = () => {
    // 助力值为空
    if (!curHelpValue) {
      return <div className="line" />;
    }
    return (
      <Fragment>
        <div className="line" />
        <div className="help-value row">
          <span>当前助力值</span>
          <img src={RedGreenIcon} alt="" />
          <span className="value din-num">{curHelpValue}</span>
        </div>
        <div className="help-tips row">
          <Fragment>
            <span>仅差</span>
            <img src={RedGreenIcon} alt="" />
            <span className="value din-num">{nextHelpValue}</span>
            <span className="tips-value">助力值可再领</span>
            <span className="din-num">{nextAward}</span>
            <span>肥料!</span>
          </Fragment>
        </div>
      </Fragment>
    );
  };

  return (
    <div className={classNames('modal-bbz-progress-challenge-award-comp', !finishAllTask && !curHelpValue && 'modal-bbz-progress-challenge-no-help-value')}>
      <div className="head-wrap row">
        <img src={TitleImg} className="title-img" alt="" />
      </div>
      <div className="award-wrap">
        <img src={PointImg} className="award-img" alt="" />
        <div className="award-value">{totalPoint}肥料</div>
      </div>
      {finishAllTask ? renderBottomByFinishAllTask() : renderBottomByFinishSomeTask()}
      <div className="invite-btn" onClick={handleInvite}>
        继续邀请
      </div>
      <div className="tips">助力值奖励每日清空 24点前记得回来领哦</div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
}
