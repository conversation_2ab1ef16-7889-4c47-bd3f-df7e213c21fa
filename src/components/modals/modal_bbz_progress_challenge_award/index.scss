.modal-bbz-progress-challenge-award-comp{
  width: 590rpx;
  min-height: 681rpx;
  background: #FFFFFF;
  border-radius: 44rpx;
  background-image: url('../assets/modal-yellow-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 180rpx;
  position: relative;
  padding-top: 64rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -172rpx;
  padding-bottom: 64rpx;

  .head-wrap{
    width: 716rpx;
    height: 226rpx;
    background-image: url('../assets/color-bar-bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    position: absolute;
    top: -153rpx;
    left: 50%;
    transform: translateX(-50%);
    flex-direction: column;
    align-items: center;

    .title-img{
      margin-top: 130rpx;
      width: 420rpx;
      height: 69rpx;
    }
  }

  .title-text{
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    color: #12161A;
    font-weight: 400;
  }

  .award-wrap{
    width: 300rpx;
    height: 300rpx;
    position: relative;
    img{
      width: 100%;
    }

    .award-value{
      width: 283rpx;
      height: 80rpx;
      background: #FFEAB8;
      border-radius: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      bottom: -48rpx;
      left: 50%;
      transform: translateX(-50%);
      font-family: PingFangSC-Semibold;
      font-size: 42rpx;
      color: #8B553A;
      font-weight: 600;
    }
  }

  .line{
    margin-top: 88rpx;
    width: 470rpx;
    height: 1rpx;
    background-color: #E0E5E8 ;
    margin-bottom: 32rpx;
  }

  .help-value,
  .help-tips{
    align-items: center;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #12161A;
    span, img{
      display: block;
    }
    img{
      margin: 0 3rpx;
      width: 24rpx;
      height: 24rpx;
    }

    .value{
      color: #FA6425;
      font-size: 32rpx;
    }

    .value-red{
      margin-left: 4rpx;
      color: #F7534F;
      font-size: 32rpx;
    }

    .tips-value{
      font-weight: normal;
    }
  }

  .din-num{
    font-size: 32rpx;
  }

  .help-value{
    margin-bottom: 8rpx;
  }

  .invite-btn{
    margin-top: 40rpx;
    width: 470rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2AC638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #FFFFFF;
    font-weight: 600;
  }

  .tips{
    width: 100%;
    opacity: 0.7;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    color: #FFFFFF;
    text-align: center;
    font-weight: 400;
    position: absolute;
    bottom: -70rpx;
  }

  .close-btn{
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -182rpx;
    .close-icon{
      width: 100%;
    }
  }
}