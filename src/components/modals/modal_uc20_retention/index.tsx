import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import RetentionCloseImg from './images/retention-close.png';
import { exit } from '@/lib/ucapi';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { getParam } from '@/lib/qs';
import dispatch from '@/logic/store';

// 挽留弹窗
const Index = () => {
  useEffect(() => {
    dispatch.app.set({ uc20ExitFlag: false });
  }, []);
  // 关闭
  const handleClose = (click_position) => {
    return () => {
      stat.click('retain_pop_click', {
        c: 'module',
        d: '20th',
        entry: getParam('entry'),
        click_position,
      });
      baseModal.close(MODAL_ID.UC20_RETENTION);
    };
  };
  // 退出
  const handleBack = () => {
    stat.click('retain_pop_click', {
      c: 'module',
      d: '20th',
      entry: getParam('entry'),
      click_position: 'return_main',
    });
    exit();
  };
  return (
    <Fact
      c="module"
      d="20th"
      expoLogkey="retain_pop_exposure"
      expoExtra={{
        entry: getParam('entry'),
      }}
    >
      <div className="modal-retention">
        <div className="subheading">去施肥最快一周可得一箱水果 1分钱，还包邮！</div>
        <div className="btn">
          <div onClick={handleBack} className="exit-btn">
            返回主会场
          </div>
          <div onClick={handleClose('go_fertilize')} className="cancel-btn">
            去施肥
          </div>
        </div>
        <img onClick={handleClose('close')} className="close" src={RetentionCloseImg} alt="" />
      </div>
    </Fact>
  );
};

export default Index;
