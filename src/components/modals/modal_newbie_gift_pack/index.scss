.newbie-gift-pack {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .newbie-gift-pack-panl {
    position: relative;
    .giftImg-bg {
      width: 670rpx;
      height: 643rpx;
      background-size: cover;
      uc-perf-stat-ignore: image;
    }
    .manure {
      position: absolute;
      left: 50%;
      bottom: 139rpx;
      transform: translateX(-50%);
      padding: 10rpx 40rpx;
      background: #ffeab8;
      border-radius: 24rpx;
      font-family: PingFangSC-Semibold;
      font-size: 42rpx;
      color: #8b553a;
      text-align: center;
      font-weight: 700;
      white-space: nowrap;
    }
    .newbie-gift-pack-title {
      position: absolute;
      left: 50%;
      bottom: 40rpx;
      transform: translateX(-50%);
      font-family: PingFangSC-Semibold;
      font-size: 42rpx;
      color: #08881f;
      text-align: center;
      font-weight: 700;
      white-space: nowrap;
    }
  }
  .get-newbie-gift-pack {
    margin: 40rpx auto 0 auto;
    width: 470rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
  }
  .close {
    margin: 64rpx auto 0 auto;
    display: flex;
    justify-content: center;
    .close-img {
      width: 72rpx;
      height: 72rpx;
      background-size: cover;
      border-radius: 50%;
      uc-perf-stat-ignore: image;
    }
  }
}
