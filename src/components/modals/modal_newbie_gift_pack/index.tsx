import React, { useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import giftImg from './assets/gift.png';
import modalCloseImg from '../assets/modal-close.png';
import dispatch from '@/logic/store';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { IActivityData } from '@/logic/store/models/app/typings';

// 新人礼包
const Index = (props) => {
  const { isShowFertillzerAnimation } = props;
  const newUserGitData: IActivityData['newUserGift'] = props?.newUserGitData || {};
  
  useEffect(() => {
    getNewbieGiftPack();
  }, []);
  // 获取新人礼包
  const getNewbieGiftPack = async () => {
    stat.click('newcomer_gift_click', {
      c: 'pop',
      d: 'newcomer',
      task_id: newUserGitData?.curTask?.id || '',
      task_name: newUserGitData?.curTask?.name || '',
      taskclassify: newUserGitData?.curTask?.taskClassify || '',
      groupcode: newUserGitData?.curTask?.groupCode || '',
      award_amount: newUserGitData?.pointAmount,
    });
    await dispatch.task.drawCustomTaskAward('NEW_USER_GIFT');
  };
  // 关闭弹窗
  const close = () => {
    isShowFertillzerAnimation();
    baseModal.close(MODAL_ID.NEWBIE_GIFT_PACK);
  };
  return (
    <div className="newbie-gift-pack">
      <Fact 
      c="pop" 
      d="newcomer" 
      expoLogkey="newcomer_gift_exposure"
      expoExtra={{
        task_id: newUserGitData?.curTask?.id,
        task_name: newUserGitData?.curTask?.name,
        taskclassify: newUserGitData?.curTask?.taskClassify,
        groupcode: newUserGitData?.curTask?.groupCode,
        award_amount: newUserGitData?.pointAmount,
      }}
      >
        <div className="newbie-gift-pack-panl">
          <img className="giftImg-bg" src={giftImg} alt="" />
          <span className="manure">送你{newUserGitData?.pointAmount}肥料</span>
          <span className="newbie-gift-pack-title">欢迎来到UC·芭芭农场!</span>
        </div>
        <div onClick={close} className="get-newbie-gift-pack">
          点击领取新人礼包
        </div>
        <div className="close">
          <img onClick={close} className="close-img" src={modalCloseImg} alt="" />
        </div>
      </Fact>
    </div>
  );
};

export default Index;
