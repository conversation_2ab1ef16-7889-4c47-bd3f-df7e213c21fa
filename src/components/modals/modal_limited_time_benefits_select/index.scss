.modal_limited_time_benefits_select {
  box-sizing: border-box;
  position: relative;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  opacity: 1;
  .benefits_select_content {
    position: relative;
    width: 100%;
    z-index: 2;
    margin-bottom: 205rpx;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 634rpx;
        height: 74rpx;
        background-size: cover;
      }
    }
    .condition {
      margin: 16rpx 0 64rpx 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #ffffff;
      text-align: center;
      line-height: 33rpx;
      img {
        margin-left: 4rpx;
        width: 24rpx;
        height: 24rpx;
        background-size: cover;
      }
    }
    .select-award {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      width: 670rpx;
      margin: 0 auto;
      .select-award-item {
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;
        width: 208rpx;
        background-image: linear-gradient(180deg, #f7f186 58%, #e7ffc9 88%);
        border-radius: 35rpx;
        .select-award-item-hd {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          background-color: #fff;
          border-radius: 33rpx;
          .award-icon {
            margin-top: 12rpx;
            width: 162rpx;
            height: 162rpx;
            background-size: cover;
          }
          .award-name {
            margin: 8rpx 0 12rpx 0;
            font-family: PingFangSC-Semibold;
            font-size: 24rpx;
            color: #8a500f;
            text-align: center;
            font-weight: 600;
            line-height: 33rpx;
          }
        }
        .select-award-item-btn {
          margin: 24rpx auto;
          width: 176rpx;
          height: 56rpx;
          font-family: PingFangSC-Semibold;
          font-size: 28rpx;
          color: #ffffff;
          text-align: center;
          font-weight: 600;
          background: #2ac638;
          border-radius: 28rpx;
          line-height: 56rpx;
        }
      }
    }
    .select-award-two {
      width: 490rpx;
    }
    .select-award-one {
      width: 208rpx;
    }
    .close-btn {
      margin: 0 auto;
      margin-top: 135rpx;
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
      .close-icon {
        width: 100%;
      }
    }
  }
  .animation {
    position: absolute;
    top: 23%;
    left: 0;
    width: 100vw;
    height: 48%;
    z-index: 0;
    .animation-circle {
      width: 100%;
      height: 100%;
      opacity: 0;
      animation: circleFadeIn 0.67s forwards;
    }
    .animation-light {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      animation: lightFadeInOpacity 0.67s forwards, lightFadeInRotate 2s linear infinite;
    }
  }
  .select-award-ani {
    animation: FadeOut 0.3s linear forwards;
  }
  .select-award-ani-img {
    display: none;
    position: absolute;
    width: 162rpx;
    height: 162rpx;
    background-size: cover;
    animation: diagonalShrink 300ms forwards, fadeOut2 300ms forwards;
    animation-duration: 170ms;
    animation-timing-function: linear;
    animation-iteration-count: 3;
  }
}

@keyframes circleFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes lightFadeInOpacity {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.6;
  }
}

@keyframes lightFadeInRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes FadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    pointer-events: none;
  }
}

@keyframes diagonalShrink {
  0% {
    transform: translate(0, 0) scale(1);
  }

  100% {
    transform: translate(var(--translateX), var(--translateY)) scale(0.62);
  }
}

@keyframes fadeOut2 {
  57% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
