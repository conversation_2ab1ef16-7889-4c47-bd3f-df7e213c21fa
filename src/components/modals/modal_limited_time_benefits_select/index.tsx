import React, { useEffect, useRef, useState } from 'react';
import './index.scss';
import mx from '@ali/pcom-mx';
import { MainAPI } from '@/logic/type/event';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
// import CloseIcon from '../assets/modal-close.png';
import LimitedTitleImg from './images/limited-title.png';
import InfoIconImg from './images/info-icon.svg';
import LightCircle from './images/light-circle.png';
import LightPng from './images/light.png';
import toast from '@/lib/universal-toast/component/toast';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';
import dispatch from '@/logic/store';
import { openPage } from '@/lib/ucapi';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { IAppState } from '@/logic/store/models/app/typings';

let getWidthStyle = new Map([
  [1, 'select-award-one'],
  [2, 'select-award-two'],
  [3, ''],
]);

const COUNT_DOWN_TIME = 0;
let timer;

const Index = (props) => {
  const { closeShowMask } = props;
  const app: IAppState = mx.store.get('app');
  const timeLimitTask = mx.store.get('timeLimitTask');
  const [waitTime, setWaitTime] = useState(5);
  const waitTimeRef = useRef(waitTime);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [selectAwardAni, setSelectAwardAni] = useState(false);
  const selectAwardAniRef = useRef<any>();
  const imgMoveRef = useRef<any>();
  const [iconProperty, setIconProperty] = useState<any>();
  const [finalAwardProperty, setFinalAwardProperty] = useState<any>();
  waitTimeRef.current = waitTime;
  
  useEffect(() => {
    mx.event.emit(MainAPI.FuzzyBackground, true);
  }, []);
  usePageVisibilityListener((visible: boolean) => {
    if (timeLimitTask?.showRewardList?.length !== 1) return;
    if (visible) {
      timer = setInterval(() => {
        if (COUNT_DOWN_TIME < waitTimeRef.current) {
          setWaitTime(waitTimeRef.current - 1);
        } else {
          selectAward(0);
          stat.click('select_award_click', {
            c: 'pop',
            d: 'award',
            award_name: timeLimitTask?.showRewardList?.[0]?.name,
          });
          clearInterval(timer);
        }
      }, 1000);
    } else {
      clearInterval(timer);
    }
  });
  // 选择奖品
  const selectAward = async (index) => {
    const res = await dispatch.timeLimitTask.selectLimitAward(index + 1);
    if (res?.code === 'ok') {
      setCurrentIndex(index);
      setSelectAwardAni(true);
      mx.event.emit(MainAPI.FuzzyBackground, false);
      selectAwardAniRef?.current?.addEventListener('animationend', () => {
        // 肥料袋动画
        closeShowMask();
        const awardIconElement = document.querySelectorAll('.award-icon')[index];
        const finalAwardElement = document.querySelector('#final-award-img-ani .manure-icon');
        setIconProperty(awardIconElement?.getBoundingClientRect());
        setFinalAwardProperty(finalAwardElement?.getBoundingClientRect());
        imgMoveRef.current.style.display = 'block';
      });
      imgMoveRef?.current?.addEventListener('animationend', () => {
        handleClose(true);
      });
    } else {
      toast.show(res?.data?.msg);
    }
  };
  // 跳转现实任务规则页
  const toLimitedTimeBenefitsrRule = () => {
    const limitedTimeBenefitsrRule = app?.mainInfo?.frontData?.limitedTimeBenefitsrRule;
    if (limitedTimeBenefitsrRule) {
      openPage(limitedTimeBenefitsrRule);
    }
  };
  // 关闭
  const handleClose = (isSelectFlag: boolean) => {
    mx.event.emit(MainAPI.FuzzyBackground, false);
    baseModal.close(MODAL_ID.LIMITED_TIME_BENEFITS_SELECT);
    mx.event.emit(isSelectFlag ? MainAPI.LimitTaskPedantsHide : MainAPI.LimitTaskPedantsShow);
  };
  // 点击后隐藏未选中的item
  const judgmentHiding = (index) => {
    if (selectAwardAni && currentIndex !== index) {
      return 'select-award-ani';
    }
    return '';
  };
  // 点击遮挡层
  const handleBackgroundClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose(false);
    }
  };
  // 获得标题
  return (
    <Fact
      c="pop"
      d="award"
      expoLogkey="select_award_exposure"
      expoExtra={{
        award_number: timeLimitTask?.showRewardList?.length,
        award_name: timeLimitTask?.showRewardList?.map((obj) => obj.name).join(','),
      }}
    >
      <div ref={selectAwardAniRef} onClick={handleBackgroundClick} className="modal_limited_time_benefits_select">
        <div className={`animation ${currentIndex !== -1 ? 'select-award-ani' : ''}`}>
          <img className="animation-circle" src={LightCircle} alt="" />
          <img className="animation-light" src={LightPng} alt="" />
        </div>
        <div className="benefits_select_content">
          <div className={`${currentIndex !== -1 ? 'select-award-ani' : ''}`}>
            <div className="title">
              <img src={timeLimitTask?.icon || LimitedTitleImg} alt="" />
            </div>
            <div className="condition">
              {timeLimitTask?.dayRange}天完成{timeLimitTask?.target}天“每日挑战”即领奖
              <img onClick={toLimitedTimeBenefitsrRule} src={InfoIconImg} alt="" />
            </div>
          </div>
          <div className={`select-award ${getWidthStyle?.get(timeLimitTask?.showRewardList?.length)}`}>
            {timeLimitTask?.showRewardList?.map((item, index) => {
              return (
                <div
                  key={index}
                  style={{ display: iconProperty && index === currentIndex ? 'none' : 'block' }}
                  className={`select-award-item ${judgmentHiding(index)}`}
                >
                  <div className="select-award-item-hd">
                    <img className="award-icon" src={item?.icon} alt="" />
                    <div className="award-name">{item?.name}</div>
                  </div>
                  <Fact
                    c="pop"
                    d="award"
                    logkey="select_award_click"
                    ckExtra={{
                      award_name: timeLimitTask?.showRewardList?.[index]?.name,
                    }}
                    onClick={() => selectAward(index)}
                    className="select-award-item-btn"
                  >
                    {timeLimitTask?.showRewardList?.length === 1 ? `开始挑战${waitTime}` : `选我`}
                  </Fact>
                </div>
              );
            })}
          </div>
          {/* 产品说有按钮损失太高 暂时去掉 */}
          {/* <Fact
            c="pop"
            d="award"
            logkey="select_award_click"
            ckExtra={{
              award_name: 0,
            }}
            className="close-btn"
            onClick={() => handleClose(false)}
          >
            <img
              className={`close-icon ${currentIndex !== -1 ? 'select-award-ani' : ''}`}
              ref={selectAwardAniRef}
              src={CloseIcon}
              alt=""
            />
          </Fact> */}
        </div>
        <img
          ref={imgMoveRef}
          style={{
            top: iconProperty?.top,
            left: iconProperty?.left,
            '--translateX': `${
              finalAwardProperty?.left + finalAwardProperty?.width / 2 - (iconProperty?.left + iconProperty?.width / 2)
            }px`,
            '--translateY': `${
              finalAwardProperty?.top + finalAwardProperty?.height / 2 - (iconProperty?.top + iconProperty?.height / 2)
            }px`,
          }}
          className="select-award-ani-img"
          src={timeLimitTask?.showRewardList?.[currentIndex]?.icon}
          alt=""
        />
      </div>
    </Fact>
  );
};

export default Index;
