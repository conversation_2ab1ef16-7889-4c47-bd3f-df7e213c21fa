.modal-selectSeed {
  box-sizing: border-box;
  position: relative;
  width: 100vw;
  min-height: 100vh;
  overflow-y: auto;
  .select-seed-head {
    position: absolute;
    left: 29rpx;
    top: 89rpx;
    display: flex;
    align-items: center;
    .back {
      width: 60rpx;
      height: 60rpx;
      background-size: cover;
    }
    .title {
      font-family: PingFangSC-Semibold;
      font-size: 34rpx;
      color: #ffffff;
      text-align: center;
      font-weight: 700;
      font-family: PingFangSC-Medium;
      margin-left: 20rpx;
    }
  }
  .seed-content {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 288rpx;
    width: 600rpx;
    background-image: linear-gradient(180deg, #fef2de 0%, #fef2de 99%);
    border: 15rpx solid #ce8538;
    border-radius: 36rpx;
    .seed-container {
      position: relative;
      border-radius: 20rpx;
      border: 10rpx solid #efb76f;
      .bulletin-board {
        position: absolute;
        top: -134rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 506rpx;
        height: 183rpx;
        background-size: cover;
      }
      .seed-info {
        padding: 54rpx 35rpx 20rpx 35rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .seed-name {
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #9A6531;
          letter-spacing: 0;
          font-weight: 500;
          width: 260rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .address {
          display: flex;
          justify-content: center;
          align-items: center;
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #9A6531;
          text-align: center;
          font-weight: 500;
          .address-info {
            max-width: 140rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .more-img {
            width: 32rpx;
            height: 32rpx;
            background-size: cover;
          }
        }
      }
      .seed-list {
        display: flex;
        flex-wrap: wrap;
        padding: 0 40rpx;
        justify-content: space-between;
        max-height: 554rpx;
        overflow-y: scroll;
        .seed {
          box-sizing: border-box;
          position: relative;
          display: flex;
          align-items: center;
          flex-direction: column;
          width: 234rpx;
          height: 244rpx;
          background: #ffffff;
          border-radius: 15rpx;
          margin-bottom: 15rpx;
          overflow: hidden;
          border: 6rpx solid #fef2de;
          padding-top: 18rpx;
          .checkbox-img {
            position: absolute;
            right: 10rpx;
            top: 10rpx;
            width: 40rpx;
            height: 40rpx;
            background-size: cover;
          }
          .product-img {
            width: 180rpx;
            height: 180rpx;
            background-size: cover;
          }
          .jump-address {
            position: absolute;
            left: 50%;
            bottom: 60rpx;
            transform: translateX(-50%);
            width: 200rpx;
            height: 40rpx;
            line-height: 40rpx;
            font-family: PingFangSC-Medium;
            font-size: 22rpx;
            color: #ffffff;
            text-align: center;
            font-weight: 500;
            background: #D4DADE;
            border-radius: 20rpx;
          }
          .product-name {
            position: absolute;
            bottom: 0;
            width: 100%;
            font-family: PingFangSC-Medium;
            font-size: 26rpx;
            color: #9A6531;
            text-align: center;
            font-weight: 500;
            background: #f8d6a3;
            line-height: 50rpx;
          }
        }
        .select-more {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          display: flex;
          height: 86rpx;
          align-items: center;
          justify-content: center;
          font-family: PingFangSC-Medium;
          font-size: 26rpx;
          color: #9A6531;
          text-align: center;
          font-weight: 500;
          border-radius: 0 0 20rpx 20rpx;
          background-image: linear-gradient(179deg, rgba(255, 255, 255, 0) 0%, #ffffff 50%);
          .select-more-img {
            width: 32rpx;
            height: 32rpx;
            background-size: cover;
          }
        }
        .margin-seed {
          margin-bottom: 80rpx;
        }
        .checked {
          border: 6rpx solid #2ac638;
        }
      }
      .max-Height700 {
        max-height: 700rpx;
      }
    }
  }
  .submit-seed {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 1060rpx;
    margin: 0 auto;
    width: 470rpx;
    height: 90rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
    line-height: 90rpx;
    background: #2ac638;
    border-radius: 100rpx;
  }
  .ou-submit {
    top: 1195rpx;
  }
  .close {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 1345rpx;
    img {
      width: 60rpx;
      height: 60rpx;
      background-size: cover;
      border-radius: 50%;
    }
  }
  .close-top {
    top: 1195rpx;
  }
  .seed-tip {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 1235rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .tip-img {
      img {
        width: 130rpx;
        height: 155rpx;
        background-size: cover;
      }
    }
    .tip-content {
      box-sizing: border-box;
      position: relative;
      margin-left: 30rpx;
      padding: 12rpx 12rpx 12rpx 20rpx;
      width: 434rpx;
      background: #ffefcc;
      border-radius: 10rpx;
      font-family: PingFangSC-Medium;
      font-size: 26rpx;
      color: #693f16;
      font-weight: 500;
      .triangle {
        position: absolute;
        top: 15%;
        left: -36rpx;
        width: 0;
        height: 0;
        border: 20rpx solid transparent;
        border-right: 20rpx solid #ffefcc;
      }
    }
  }
}
