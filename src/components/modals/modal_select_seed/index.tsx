import React, { Fragment, useEffect, useRef, useState } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import { MODAL_ID } from '@/components/modals/types';
import backImg from './assets/back.png';
import bulletinBoardImg from './assets/bulletin-board.png';
import checkboxonImg from './assets/checkboxon.png';
import checkboxoffImg from './assets/checkboxoff.png';
import selectMoreImg from './assets/select-more.png';
import moreImg from './assets/more.png';
import orchardWorkerImg from './assets/orchard-worker.png';
import closePng from './assets/close.png';
import useMxState from '@/hooks/useMxState';
import { ISeedState, Product } from '@/logic/store/models/seed/typings';
import { IAppState } from '@/logic/store/models/app/typings';
import toast from '@/lib/universal-toast/component/toast';
import mx from '@ali/pcom-mx';
import { MainAPI } from '@/logic/type/event';
import dispatch from '@/logic/store';
import config from '@/config';
import { openPage } from '@/lib/ucapi';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { appVersion, isLatestVersion, isAndroid } from '@/lib/universal-ua';
import wormholeData from '@/lib/wormhole-data';
import {usePageVisibilityListener} from '@/hooks/useVisibilitychange';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { useDebounce } from '@/hooks';
import tracker from '@/lib/tracker';

const link = wormholeData?.page?.['10010']?.link || {};

// 选择种子
const Index = (props) => {
  const { flag, onSuccess, btnTitle } = props;
  const moreRef = useRef<any>();
  const [seed] = useMxState<ISeedState>('seed');
  const [app] = useMxState<IAppState>('app');
  const [fruitsList, setFruitsList] = useState<Product[]>([]);
  const [currentItem, setCurrentItem] = useState<any>(null);
  const [seedListFlag, setSeedListFlag] = useState<boolean>(false);
  useEffect(() => {
    mx.event.emit(MainAPI.FuzzyBackground, true);
    moreRef.current.addEventListener('scroll', seedListScroll);
  }, []);
  useEffect(() => {
    const defaultItem = {
      area_restrict: false, // 地域限制
      category: '', // 分类
      has_inventory: true, // 是否有库存
      inventory_ratio: '', // 库存比例
      order: '',
      red_packet: true,
      season_seed: false, // 是否季节性
      seedCode: '-1', // 种子编码
      seedPic: 'https://img.alicdn.com/imgextra/i4/O1CN01U60oIR1aKwiLwu7IQ_!!6000000003312-2-tps-270-270.png', // 种子图片
      seed_subTitle: '', // 种子副标题
      seedTitle: '敬请期待', // 种子标题
    };
    let unlimitSeedList = seed?.unlimitSeedList?.filter((item) => {
      return item?.hasInventory;
    });
    let list = padArray(unlimitSeedList, defaultItem, 4);
    if (list[0]?.seedCode !== '-1') {
      if (choosable(list[0])) {
        setCurrentItem(list[0]);
      }
      // 如果是奇数个 给最后增加默认图
      if (list?.length % 2 !== 0) {
        list.push(defaultItem);
      }
    }
    setFruitsList(list);
  }, [seed]);
  // 种子列表是否全部加载
  const seedListScroll = () => {
    if (moreRef.current.scrollHeight - moreRef.current.scrollTop - 100 <= moreRef.current.clientHeight) {
      setSeedListFlag(true);
    } else {
      setSeedListFlag(false);
    }
  };
  // 返回
  const back = () => {
    closeModal('back');
  };
  // 选择种子
  const selectSeed = (item, index) => {
    return () => {
      if (!seed?.address && !item?.redPacket && item?.seedCode !== '-1') {
        if (jumpudgmentVersion()) {
          selectAddress();
        } else {
          toast.show('升级到最新版本可用');
        }
        return;
      }
      if (item?.seedCode === '-1') {
        toast.show('努力招募果农中 请期待更多');
      } else {
        setCurrentItem(item);
      }
    };
  };

  usePageVisibilityListener((visible) => {
    if (visible) {
      dispatch.seed.querySeedList();
    }
  });

  const getSupportAllVisibilityListener = () => {
    // 安卓支持
    if (isAndroid) {
      return true;
    }
    // IOS 只有主站 & 版本>=16.5.9 才支持事件UCEVT_Global_CompassPageStateChanged
    if (app?.pr === 'UCMobile' && appVersion && isLatestVersion(appVersion, '16.5.9')) {
      return true;
    }
    return false;
  }
  // 选择地址
  const selectAddress = () => {
    if (!jumpudgmentVersion()) {
      return;
    }
    stat.click('address_click', {
      c: 'pop',
      d: 'seed',
      isaddress: seed?.address ? 1 : 0,
      selection_status: flag ? 'newuser' : 'harvest',
    });
    if (window?.compass && getSupportAllVisibilityListener()) {
      tracker.log({
        category: 133, // 系统自动生成，请勿修改
        msg: '地址管理打开方式', // 将根据msg字段聚合展示在平台的top上报内容中
        c1: '半屏', // 自定义字段c1 对应 半屏
      });
      (window as any)?.compass.router.open({
        url: config?.selectTaobaoAddressUrl,
        type: 'panel',
        height: 0.8,
        radius: 20,
        bgcolor: '#aarrggbb',
        close_outside: true
      });
    } else {
      tracker.log({
        category: 133, // 系统自动生成，请勿修改
        msg: '地址管理打开方式', // 将根据msg字段聚合展示在平台的top上报内容中
        c1: '全屏', // 自定义字段c1 对应 半屏
      });
      openPage(link?.selectTaobaoAddressUrl || config?.selectTaobaoAddressUrl)
    }
  };
  // 种下果树
  const plantTrees = useDebounce(async () => {
    console.log(currentItem);
    if (!currentItem) {
      stat.click('cultivate_click_no_fruit', {
        selection_status: flag ? 'newuser' : 'harvest',
        c: 'pop',
        d: 'seed',
      });
      return toast.show('未选中果树');
    }
    stat.click('cultivate_click', {
      c: 'pop',
      d: 'seed',
      fruit_number: seed?.unlimitSeedList?.filter((item) => {
        return item?.hasInventory;
      }).length,
      fruit_id: currentItem?.seedCode,
      fruit_type: currentItem?.category,
      fruit_name: currentItem?.seedTitle,
      isaddress: seed?.address ? 1 : 0,
      change_seed: currentItem?.seedCode !== fruitsList[0]?.seedCode ? '更换种子' : '未更换种子',
      selection_status: flag ? 'newuser' : 'harvest',
    });
    const res = await dispatch.seed.selectSeed(currentItem?.seedCode, seed?.address);
    if (res.code === 'ok') {
      mx.event.emit('update_home_data');
      closeModal('api_success');
      stat.updateParam({
        tree_level: 1,
      })
      onSuccess && onSuccess();
    } else {
      console.log(res);
      if (res?.data?.code === 'AlreadyInPlantingError') {
        mx.event.emit('update_home_data');
        closeModal('api_fail');
        return;
      }
      toast.show(res?.data?.msg || '请求失败，请重新试试');
    }
  }, 500);
  /**
   * 关闭弹窗
   * @param action
   * 'click': 默认值
   * 'api_success': 接口成功
   * 'api_fail': 接口失败
   * 'click_out_shadow': 点击阴影
   * 'close': 关闭按钮
   * 'back': 返回按钮
   */
  const closeModal = (action: 'click' | 'api_success' | 'api_fail' | 'click_out_shadow' | 'close' | 'back' = 'click') => {
    mx.event.emit(MainAPI.FuzzyBackground, false);
    baseModal.close(MODAL_ID.SELECT_SEED);
    stat.custom('select_seed_exposure_close', {
      selection_status: flag ? 'newuser' : 'harvest',
      action
    });
  };
  // 点击阴影
  const shadowClick = (event) => {
    event.stopPropagation();
    if (event?.target?.id === 'modal-selectSeed') {
      closeModal('click_out_shadow');
    }
  };
  // 是否可选
  const choosable = (item) => {
    if (item?.seedCode === '-1') return false;
    if (item?.redPacket) return true;
    if (!seed?.hasAddress) return false;
    return true;
  };
  // 判断地址版本
  const jumpudgmentVersion = () => {
    if (isAndroid || app?.pr === 'UCLite') {
      return true;
    }
    if (appVersion && isLatestVersion(appVersion, '16.4.8')) {
      return true;
    }
    return false;
  };
  return (
    <div onClick={shadowClick} id="modal-selectSeed" className="modal-selectSeed">
      <Fact
        c="pop"
        d="seed"
        expoLogkey="select_seed_exposure"
        expoExtra={{
          fruit_number: seed?.unlimitSeedList?.filter((item) => {
            return item?.hasInventory;
          }).length,
          isaddress: seed?.address ? 'yes' : 'no',
          selection_status: flag ? 'newuser' : 'harvest',
        }}
      >
        {flag && (
          <div className="select-seed-head" onClick={back}>
            <img className="back" src={backImg} alt="" />
            <span className="title">
              返回农场
            </span>
          </div>
        )}
        <div className="content-wrap">
          <div className="seed-content ou-seed-content">
            <div className="seed-container">
              <img className="bulletin-board" src={bulletinBoardImg} alt="" />
              <div className="seed-info">
                <div className="seed-name">
                  你选中:{currentItem ? currentItem?.seedTitle : '水果补货中种成可兑换其它水果'}
                </div>
                <div onClick={selectAddress} className="address">
                  {seed?.hasAddress && seed?.address ? '地址:' : jumpudgmentVersion() ? '点击设置收货地址' : ''}
                  {seed?.hasAddress && seed?.address && <div className="address-info">{seed?.address}</div>}
                  {jumpudgmentVersion() && <img className="more-img" src={moreImg} alt="" />}
                </div>
              </div>
              <div className={`seed-list ${flag ? '' : 'max-Height700'}`} ref={moreRef}>
                {fruitsList?.map((item, index) => {
                  return (
                    <div
                      key={index}
                      onClick={selectSeed(item, index)}
                      style={{ backgroundColor: item?.seedCode === '-1' ? '#F6E7D2' : '' }}
                      className={`seed ${index > 3 && index === fruitsList?.length - 1 ? 'margin-seed' : ''} ${
                        currentItem?.seedCode === item?.seedCode ? 'checked' : ''
                      }`}
                    >
                      {choosable(item) && (
                        <img
                          className="checkbox-img"
                          src={currentItem?.seedCode === item?.seedCode ? checkboxonImg : checkboxoffImg}
                          alt=""
                        />
                      )}
                      <LazyLoadImage className="product-img" src={item?.seedPic} alt="" visibleByDefault={!(index > 4)} />
                      {!seed?.address && !item?.redPacket && item?.seedCode !== '-1' && jumpudgmentVersion() && (
                        <div className="jump-address">请设置收货地址</div>
                      )}
                      <div className="product-name">{item?.seedTitle}</div>
                    </div>
                  );
                })}
                {fruitsList?.length > 4 && (
                  <div className="select-more">
                    {seedListFlag ? (
                      <span>没有更多了</span>
                    ) : (
                      <Fragment>
                        <span>上滑选择更多</span>
                        <img className="select-more-img" src={selectMoreImg} alt="" />
                      </Fragment>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
          <div onClick={plantTrees} className={`submit-seed ${flag ? '' : fruitsList?.length > 4 ? 'ou-submit' : ''}`}>
            {btnTitle || '立即种下你的果树'}
          </div>
          {!flag && (
            <div onClick={() => closeModal('close')} className={`close ${fruitsList?.length > 4 ? '' : 'close-top'}`}>
              <img src={closePng} alt="" />
            </div>
          )}
          {flag && (
            <div className="seed-tip">
              <div className="tip-img">
                <LazyLoadImage src={orchardWorkerImg} alt="" />
              </div>
              <div className="tip-content">
                <div className="triangle" />
                {seed?.hasAddress
                  ? '受季节、距离等影响，如果果树种成后无法发货，您也可以兑换其他可选奖励'
                  : '当前未设置收货地址，设置地址后可选择水果种子，种成后也可兑换其他可选奖励'}
              </div>
            </div>
          )}
        </div>
      </Fact>
    </div>
  );
};

// 补齐数组
function padArray(arr, item, length) {
  while (arr.length < length) {
    arr.push(item);
  }
  return arr;
}

export default Index;
