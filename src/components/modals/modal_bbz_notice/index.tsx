import React, { Fragment, useEffect } from 'react';
import './index.scss';
import baseModal from '@/lib/modal';
import CloseIcon from '../assets/modal-close.png';
import stat from '@/lib/stat';
import { IHelpPlantFrontData } from '@/api/helpPlant/typings';
import classNames from 'classnames';
import dispatch from '@/logic/store';
import { MODAL_ID } from '../types';

type NoticeConfig = IHelpPlantFrontData['noticeConfig'];

interface IProps {
  id: string;
  // 主标题
  title?: NoticeConfig['title'];
  // 副标题
  subtitle?: NoticeConfig['subtitle'];
  prize: NoticeConfig['prizeConfig'];
  curTime: number;
}

export default function Index(props: IProps) {
  const { title, prize, subtitle, curTime } = props;
  const hasPrize = !!(prize?.manure || prize?.cash);

  const handleWithdraw = () => {
    stat.click('bbz_campaignpop_click', {
      c: 'activity',
      d: 'pop',
      click_position: 1,
    });
    baseModal.close(props?.id);
  };

  const handleClose = () => {
    stat.click('bbz_campaignpop_click', {
      c: 'activity',
      d: 'pop',
      click_position: 2,
    });
    baseModal.close(props?.id);
  };
  const buildManureValue = (manure: number) => {
    if (!manure) return '';
    if (manure < 10000) {
      return `${manure}`;
    }
    if (manure > 999000) {
      manure = 999000;
    }
    return (
      <Fragment>
        {(manure / 10000).toFixed(1).replace(/\.0$/, '')}
        <span className="manure-unit">万</span>
      </Fragment>
    );
  };
  const buildCashValue = (cash: number) => {
    if (!cash) return '';
    if (cash > 999) {
      return 999;
    }
    return cash;
  };
  useEffect(() => {
    stat.exposure('bbz_campaignpop_exposure', {
      c: 'activity',
      d: 'pop',
    });
    dispatch.dialog.closePopup(MODAL_ID.BBZ_NOTICE, curTime);
  }, []);

  return (
    <div className="modal-notice-award-comp">
      <div className="head-img" />
      <div className="notice-title">{title || '帮帮种·领现金'}</div>
      <div className="notice-subtitle">
        <span>{subtitle || ''}</span>
        <div className="subtitle">{subtitle || ''}</div>
      </div>
      {hasPrize && (
        <Fragment>
          <div className="tips">参与有机会领取</div>
          <div className="award-wrap row">
            {prize?.manure && (
              <div className={`award-icon manure-wrap ${prize?.cash ? 'point-icon' : ''}`}>
                <div className="manure-value">{buildManureValue(prize?.manure)}</div>
              </div>
            )}
            {prize?.cash && (
              <div className="award-icon cash-wrap">
                <div className={classNames('cash-value', { 'cash-value-small': `${prize?.cash}`.length > 2 })}>
                  {buildCashValue(prize?.cash)}
                </div>
              </div>
            )}
          </div>
        </Fragment>
      )}
      <div className={`confirm-btn ${hasPrize ? 'prize-mt' : ''}`} onClick={handleWithdraw}>
        我知道了
      </div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  );
}
