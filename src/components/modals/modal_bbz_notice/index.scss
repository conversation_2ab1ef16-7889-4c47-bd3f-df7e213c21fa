.modal-notice-award-comp {
  width: 590rpx;
  // min-height: 400rpx;
  background: #ffffff;
  border-radius: 44rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-top: 88rpx;
  padding-bottom: 64rpx;
  align-items: center;
  .prize-mt {
    margin-top: 40rpx !important;
  }
  .head-img {
    width: 750rpx;
    height: 350rpx;
    background-image: url('../assets/bbz-notice-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: -170rpx;
    left: 50%;
    transform: translateX(-50%);
  }

  .title-wrap {
    position: relative;
    height: 46rpx;
    line-height: 46rpx;
    align-items: center;
    .date-text {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #12161a;
      font-weight: 400;
    }
    .help-icon {
      margin: 0 1rpx;
      margin-left: 4rpx;
      width: 24rpx;
      height: 24rpx;
    }
    .help-value {
      font-family: D-DIN-Bold;
      font-size: 31rpx;
      color: #fa6425;
    }
  }
  .notice-title {
    // height: 46rpx;
    line-height: 46rpx;
    position: relative;
    margin-top: 2rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #12161a;
    font-weight: 600;
    text-align: start;
    align-self: flex-start;
    margin-left: 50rpx;
    span {
      color: #fa6425;
    }
  }
  .notice-subtitle {
    position: relative;
    font-family: PingFangSC-Regular;
    letter-spacing: 0;
    // text-align: center;
    line-height: 55rpx;
    margin-top: 22rpx;
    padding: 0 50rpx;
    font-size: 32rpx;
    color: #12161a;
    font-weight: normal;
    align-self: flex-start;
    // margin-left: 50rpx;
    span {
      opacity: 0;
    }
    .subtitle {
      position: absolute;
      top: 0;
      left: 0;
      font-family: PingFangSC-Regular;
      letter-spacing: 0;
      line-height: 55rpx;
      padding: 0 50rpx;
      font-size: 32rpx;
      color: #12161a;
      font-weight: normal;
      align-self: flex-start;
    }
  }
  .tips {
    margin-top: 40rpx;
    margin-bottom: 10rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #aab5bb;
    font-weight: 400;
    position: relative;

    &::before,
    &::after {
      content: '';
      width: 159rpx;
      height: 1rpx;
      background-color: #e0e5e8;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    &::before {
      left: -168rpx;
    }

    &::after {
      right: -168rpx;
    }
  }

  .award-wrap {
    margin-top: 22rpx;
    align-items: center;
    .award-icon {
      width: 145rpx;
      height: 143rpx;
      box-sizing: border-box;
    }
    .point-icon {
      margin-right: 72rpx;
    }
    .manure-wrap {
      background-image: url('../assets/bbz-manure-icon.png');
      background-repeat: no-repeat;
      background-size: cover;
      position: relative;

      .manure-value {
        width: 57rpx;
        height: 40rpx;
        position: absolute;
        top: 52rpx;
        left: 45.5rpx;
        font-family: Gilroy-Bold;
        font-size: 24rpx;
        color: #0a9724;
        text-align: center;
        line-height: 40rpx;
        letter-spacing: -1rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }
      .manure-unit {
        font-family: FZLanTingYuanS-Bold;
        font-size: 20rpx;
        line-height: 40rpx;
        margin-left: 1rpx;
        text-align: center;
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 100%;
      }
    }

    .cash-wrap {
      background-image: url('../assets/bbz-cash-icon.png');
      background-repeat: no-repeat;
      background-size: cover;
      position: relative;

      .cash-value {
        width: 57rpx;
        height: 40rpx;
        position: absolute;
        top: 34rpx;
        left: 52rpx;
        font-family: Gilroy-Bold;
        font-size: 36rpx;
        color: #ff500f;
        text-align: center;
        line-height: 40rpx;
      }

      .cash-value-small {
        font-size: 26rpx;
      }
    }
  }

  .confirm-btn {
    margin-top: 60rpx;
    width: 470rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    font-weight: 600;
  }

  .close-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -112rpx;
    .close-icon {
      width: 100%;
    }
  }
}

@media screen and (min-width: 500px) {
  .manure-unit {
    transform: translateY(-1.5rpx);
  }
}
