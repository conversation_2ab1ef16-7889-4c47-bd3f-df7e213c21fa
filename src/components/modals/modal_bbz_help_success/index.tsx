import React, { useState, useRef, useEffect } from 'react';
import './index.scss';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';
import baseModal from '@/lib/modal';
import { getNumberLength } from '@/lib/utils/formatNumber';
import useMxState from '@/hooks/useMxState';
import { IFarmHelpPlantState } from '@/logic/store/models/farmHelpPlant/type';
import classNames from 'classnames';
import stat from '@/lib/stat';
import { getFarmHelpPlantFactParams } from '@/pages/index/utils';

import BbzText from '@/assets/bbz-banner-title.png';
import TitleImg from '../assets/help-success-title.png';
import RedLoveIcon from '@/assets/red-love-icon.png';
import PointImg from '../assets/bbz-point-icon.png';
import CloseIcon from '../assets/modal-close.png';
import dispatch from '@/logic/store';

let timer;
interface Props {
  id: string;
  data: {
    assistUserType: string;
    inviterAvatar: string;
    inviterNickname: string;
    inviterScore: number;
    score: number;
    totalAward: number;
  };
}
export default function Index(props: Props) {
  const [farmHelpPlant] = useMxState<IFarmHelpPlantState>('farmHelpPlant');
  const [waitTime, setWaitTime] = useState(10);
  const waitTimeRef = useRef(waitTime);
  waitTimeRef.current = waitTime;

  const { prizeValue } = farmHelpPlant.topOnePrizeInfo;
  const maxRankAward = Number(prizeValue);

  const helpValue = props?.data?.score ?? 2;

  usePageVisibilityListener((visible: boolean) => {
    if (visible) {
      timer = setInterval(() => {
        if (waitTimeRef.current > 1) {
          setWaitTime(waitTimeRef.current - 1);
        } else {
          handleLook(2);
          clearInterval(timer);
        }
      }, 1000);
    } else {
      clearInterval(timer);
    }
  });

  useEffect(() => {
    stat.exposure('bbz_shared_exposure', {
      c: 'bbzhong',
      d: 'shared',
      share_status: 'succ',
      ...getFarmHelpPlantFactParams(),
    })

    return () => {
      clearInterval(timer);
    }
  }, [])

  const clickFact = (position: number) => {
    stat.click('bbz_shared_click', {
      c: 'bbzhong',
      d: 'shared',
      share_status: 'succ',
      click_position: position,
      ...getFarmHelpPlantFactParams(),
    })
  }

  const handleLook = (position: number) => {
    clickFact(position);
    dispatch.farmHelpPlant.openActivityPage('shared_pop');
    baseModal.close(props?.id);
  }

  const handleClose = () => {
    clickFact(3);
    baseModal.close(props?.id);
  }

  return (
    <div className="modal-bbz-help-success-comp">
      <div className="head-wrap row">
        <img src={BbzText} className="bbz-text" alt="" />
        <img src={TitleImg} className="title-img" alt="" />
      </div>
      <div className="value-wrap row">
        <img src={RedLoveIcon} className="num-icon" alt="" />
        <span>{helpValue}</span>
      </div>
      <div className="text">你也获得助力值</div>
      <div className="tips">参与活动有机会领取</div>
      <div className="award-wrap row">
        <img src={PointImg} className="award-icon point-icon" />
        <div className="award-icon cash-wrap">
          <div className={classNames('cash-value', { 'cash-value-small': getNumberLength(maxRankAward) > 2 })}>
            {maxRankAward / 100}
          </div>
        </div>
      </div>
      <div className="look-btn" onClick={() => handleLook(1)}>去看看({waitTime})</div>
      <div className="close-btn" onClick={handleClose}>
        <img src={CloseIcon} className="close-icon" alt="" />
      </div>
    </div>
  )
}
