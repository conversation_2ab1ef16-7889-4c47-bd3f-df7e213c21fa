import React, {useEffect, useState, useRef} from 'react';
import './index.scss';
import { TaskInfo } from "@/pages/index/components/TaskPop/TaskList/types";
import {IPreloadRes} from "@/lib/adPlayer/ad_video";
import stat from "@/lib/stat";
import mx from '@ali/pcom-mx';
import { finishIncentiveAdTask } from "@/pages/index/components/TaskPop/TaskList/help";

import StarImg from '../assets/progress-bubble-star.png';
import ManureIcon from '../assets/manure.png';
import Arrow from './assets/arrow.png';
import baseModal from "@/lib/modal";
import { ITanxOrderTaskDialog } from "@/logic/store/models/cms/typings";

interface IProps {
  id: string;
  adTask: TaskInfo; // 激励视频任务
  adTaskPreloadResult: IPreloadRes;
  requestId: string;
}

function ModalTanxAd(props: IProps) {
  const orderTask = mx.store.get('task.progressiveIncentiveOrderTask');
  const tanxOrderTaskDialog: ITanxOrderTaskDialog = mx.store.get('cms')['tanx_order_task_dialog']?.items?.[0] || {countdownSeconds: '5'};
  const countdownSeconds = Number(tanxOrderTaskDialog.countdownSeconds) || 5;
  const [leftTime, setLeftTime] = useState<number>(countdownSeconds);
  const adTaskAward = props.adTask.rewardItems?.[0]?.amount || 0;
  const orderTaskAward = orderTask.rewardItems?.[0]?.amount || 0;
  const timer = useRef();
  
  const getAdRewardInfo = () => {
    const rewardInfo = props.adTaskPreloadResult.reward_info_list?.[0] || {};
    return {
      adn_id: rewardInfo?.adn_id || '',
      sid: rewardInfo?.sid || '',
      price: rewardInfo?.price || '',
      pid: rewardInfo?.pid || '',
    }
  }
  
  useEffect(() => {
    timer.current = setInterval(() => {
      setLeftTime(prev => {
        if (prev === 0) {
          clearInterval(timer.current);
          timer.current = null;
          handleToGet('countdown');
          return prev;
        }
        return prev - 1;
      });
    }, 1000); 
  }, [])
  
  useEffect(() => {
    if (orderTask) {
      stat.custom('tanx_pop_exposure', {
        task_id: orderTask.id,
        task_name: orderTask.name,
        taskclassify: orderTask.taskClassify,
        groupcode: orderTask.groupCode,
        award_amount: orderTask.rewardItems?.[0].amount,
        task_progress: props.adTask.dayTimes?.progress || 0,
        ...getAdRewardInfo(),
      }) 
    }
  }, [orderTask]);
  
  const handleToGet = (click_area: 'pickup' | 'countdown') => {
    stat.custom('tanx_pop_click', {
      task_id: orderTask.id,
      task_name: orderTask.name,
      taskclassify: orderTask.taskClassify,
      groupcode: orderTask.groupCode,
      award_amount: orderTask.rewardItems?.[0].amount,
      task_progress: props.adTask.dayTimes?.progress || 0,
      click_area,
      ...getAdRewardInfo(),
    })
    finishIncentiveAdTask(props.adTask, props.requestId);
    baseModal.close(props.id)
  }

  return (
    <div className="modal-tanx-ad">
      <div className="title">膨胀福利砸中你啦</div>
      <div className="sub-title">淘宝广告专属福利</div>
      <img className="star-img" src={StarImg} alt="" />
      <div className="task-award row">
        <div className="ad-task-award column">
          <img src={ManureIcon} className="award-icon-s" />
          <div className="award-num-bg award-num-s j-center align-c">
            +{adTaskAward}
          </div>
          <div className="desc">浏览得</div>
        </div>
        <img src={Arrow} className="arrow-icon" />
        <div className="order-task-award column">
          <img src={ManureIcon} className="award-icon-l" />
          <div className="award-num-bg award-num-l j-center align-c">
            +{orderTaskAward}
          </div>
          <div className="desc">下单再得</div>
        </div>
      </div>
      <div className="btn-confirm" onClick={() => handleToGet('pickup')}>
        去领取({leftTime}s)
      </div>
    </div>
  );
}

export default ModalTanxAd;
