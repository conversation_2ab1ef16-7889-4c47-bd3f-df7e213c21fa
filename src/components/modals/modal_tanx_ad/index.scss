.modal-tanx-ad {
  position: relative;
  box-sizing: border-box;
  width: 590rpx;
  height: 742rpx;
  border-radius: 44rpx;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: url('../assets/modal-yellow-bg.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  padding-top: 34rpx;
  .star-img {
    position: absolute;
    top: -52rpx;
    width: 716rpx;
    height: 222rpx;
    background-size: cover;
  }
  .title {
    font-family: PingFangSC-Semibold;
    font-size: 48rpx;
    opacity: 0.75;
    color: #cc6220;
    text-align: center;
    font-weight: 700;
    letter-spacing: 0;
    line-height: 67rpx;
    margin-bottom: 16rpx;
  }
  .task-award {
    width: 460rpx;
    height: 319rpx;
    margin-top: 41rpx;
    position: relative;
    justify-content: space-between;
    align-items: end;
    margin-bottom: 56rpx;
    .arrow-icon {
      position: absolute;
      top: 120rpx;
      left: 168rpx;
      width: 72rpx;
      height: 72rpx;
    }
    .ad-task-award, .order-task-award {
      justify-content: center;
      align-items: center;
    }
    .award-icon-s {
      width: 160rpx;
      height: 160rpx;
    }
    .award-icon-l {
      width: 216rpx;
      height: 216rpx;
    }
    .award-num-bg {
      display: flex;
      background: #FFEAB8;
      border-radius: 20rpx;
      height: 54rpx;
      text-align: center;
      font-family: PingFangSC-Semibold;
      font-size: 32rpx;
      color: #662A02;
      letter-spacing: 0;
      font-weight: 700;
      margin-top: -14rpx;
      margin-bottom: 16rpx;
    }
    .desc {
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      color: #12161A;
      letter-spacing: 0;
      text-align: center;
      font-weight: 700;
    }
    .award-num-s {
      width: 160rpx;
    }
    .award-num-l {
      width: 198rpx;
    }
  }
}