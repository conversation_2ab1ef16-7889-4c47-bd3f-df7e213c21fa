import React from 'react';
import wormholeData from '@/lib/wormhole-data';
import defaultConfig from '@/config/config.default';
import { data as wormholeContext } from '@ali/wormhole-context';

function Document(props) {
  const { initialProps, inject, children } = props;
  const injectCssEle = ((inject && inject.css) || [])
    .map((item) => <link rel="stylesheet" key={item} href={item} />);
  const injectScriptEle = ((inject && inject.js) || [])
    .map((item) => <script key={item} src={item} />);

  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta
          name="viewport"
          content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover"
        />
        <meta name="wpk-bid" content={defaultConfig.WPK_BID} />
        <meta name="wpk-rel" content={PUBLISH_VERSION} />
        <meta name="wpk-c2" content={window?.__lp_disable} />
        <meta name="wpk-wv_traceid" content="" />
        <meta name="nightmode" content="disable" />
        <meta name="customizetype" content="reader" />
        <meta content="disable" name="transparentmode" />
        <meta name="screen-orientation" content="portrait" />
        <meta name="ut-on" />
        <meta name="exposure-on" />
        <title>{wormholeContext?.pageInfo?.title || 'UCx芭芭农场'}</title>
        <script
          dangerouslySetInnerHTML={{
            __html:
              "if((new Date()).getHours()<6||(new Date()).getHours()>=18){document.documentElement.setAttribute('farm-type','night')}",
          }}
        />
        <script
          dangerouslySetInnerHTML={{
            __html:
              'var screenHeight=window.screen.height;var screenWidth=window.screen.width;document.documentElement.style.setProperty("--screenWidth",screenWidth);document.documentElement.style.setProperty("--screenHeight",screenHeight);if(screenHeight/screenWidth>2 && screenHeight>780){document.documentElement.setAttribute("device-type","high");}',
          }}
        />
        {injectCssEle}
      </head>
      <body>
        <div id="root">{children}</div>
        <script dangerouslySetInnerHTML={{ __html: `window.__wh_data__ = ${JSON.stringify(wormholeData) || '{}'}` }} />
        <script dangerouslySetInnerHTML={{ __html: `window.__INITIAL_PROPS__ = ${JSON.stringify(initialProps) || '{}'}` }} />
        <script
          dangerouslySetInnerHTML={{
            __html: `
           window.__SHARE_CONFIG__ = { planId: ${JSON.stringify(initialProps?.sharePlanId)}}
          `,
          }}
        />
        {injectScriptEle}
      </body>
    </html>
  );
}

export default Document;
