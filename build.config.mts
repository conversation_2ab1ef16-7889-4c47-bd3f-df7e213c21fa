import { IConfig, IPluginAPI } from '@ali/compass-app';
import pluginSetup from '@ali/compass-plugin-def';
import parser from 'yargs-parser';
import pkgInfo from './package.json';
// import webpackAnalyzer from './build-plugins/webpack-bundle-analyzer';

const config: IConfig = {
  useBuiltIns: false,
  devTools: true,
  alias: {
    // react: './node_modules/react',
    // 'react-dom': './node_modules/react-dom',
    // '@ali/act-base-toolkit': './node_modules/@ali/act-base-toolkit',
  },
  baseConfig: {
    designWidth: 750,
  },
  plugins: [
    // webpackAnalyzer(),
    {
      name: 'plugin-def',
      setup: (api) => {
        const buildArgv = parser(process.env.BUILD_ARGV_STR || '') ?? {};
        const version = buildArgv?.def_publish_version ?? '0';
        pluginSetup(api, {
          type: 'npm',
          names: ['csr', 'ssr'],
          opts: {
            devPublicPath: `https://broccoli-static.uc.cn/daily/code/npm/${pkgInfo.name}/${version}/`,
            publicPath: `https://broccoli-static.uc.cn/prod/code/npm/${pkgInfo.name}/${version}/`,
          },
        });
      },
    },
    '@ali/compass-plugin-broccoli',
    './build-plugins/index.js',
  ],
};

export default config;
