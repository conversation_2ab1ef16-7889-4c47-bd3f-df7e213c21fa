import { BrowserEnv as BaseBrowserEnv } from '@ali/broccoli-simulate-browser-env';
import * as chalk from 'chalk';

interface IOption {
  env: 'dev' | 'prod';
  headers: { [key: string]: string };
  /**
   * 请求地址，可能是主文档docUrl，也可能是并行ssr时的接口请求地址，faas函数中不使用该值，脚手架中使用
   */
  href: string;
  ctx?: {
    logger: any;
    request: any;
  };
}

interface WormholeJavaScriptContext {
  data: {
    pageInfo: any;
    page: any;
    packId?: string;
    whHistoryId?: string;
    mock?: any;
  };
}

function isObject(obj) {
  return obj && Object.prototype.toString.call(obj) === '[object Object]';
}

// function notSupport(key: string, defaultValue: number | string) {
//   console.warn(`property ${key} is not support, default value ${defaultValue}`);
// }

function noop() { }

const noopWithWarn = (fnName) => () => {
  console.log(
    chalk.yellow(`warning ${fnName} will not run in node runtime, use if (isWeb) { setTimeout) } to remove it`),
  );
};

const blackKeys = ['pegasus_data', '__propsNodeId'];

export function createBrowserEnv(options: IOption): any {
  class CustomBrowserEnv {
    location;
    localStorage;
    sessionStorage;
    screen;
    fetch: any;
    __wormhole__: WormholeJavaScriptContext;
    protected console;
    protected Image;
    protected navigator;
    protected document;
    protected global;
    protected window;

    constructor() {
      const { headers = {}, href, env = 'dev' } = options;
      /** TODO 强制写死了https，因为faas网关没透传给faas框架，@卢令 网关同学，@张挺 框架同学 */
      // const url = 'https:' + '//' + ctx.host + ctx.url;
      /**
       * 脚手架中取href，非脚手架中取custom-doc-url，custom-doc-url来源
       * + brccoli传入
       * + 直访faas(包括本地和线上)时，来源于faasUrl，看调用createBrowserEnv处
       */
      const docUrl = headers['custom-doc-url'] || href;
      const baseBrowserEnv = new BaseBrowserEnv({
        env,
        url: docUrl,
        cookie: headers['cookie'],
        userAgent: headers['user-agent'],
        headers,
        /** 以下部分配置依赖客户端补全参数，暂时写默认值，建议端不要使用 */
        /**
         * 设备的像素大小的比率
         * 示例：2
         */
        // devicePixelRatio?: number;
        /**
         * 屏幕的宽度
         * 示例：750
         */
        get screenWidth() {
          // notSupport('screenWidth', 750);
          return 750;
        },
        /**
         * 屏幕的宽度
         * 示例：1280
         */
        get screenHeight() {
          // notSupport('screenHeight', 1280);
          return 1280;
        },
        /**
         * 页面地址
         * 示例："https://developer.mozilla.org/zh-CN/docs/Web/API/URL"
         */
      });
      // 默认屏蔽 console
      this.Image = class { };
      this.console = baseBrowserEnv.console;
      this.fetch = baseBrowserEnv.fetch;
      this.navigator = baseBrowserEnv.navigator;
      this.location = baseBrowserEnv.location;
      this.localStorage = baseBrowserEnv.localStorage;
      this.sessionStorage = baseBrowserEnv.sessionStorage;
      this.screen = baseBrowserEnv.screen;
      this.document = baseBrowserEnv.document;
      this.window = Object.assign(baseBrowserEnv.window, {
        document: this.document,
        // addEventListener(name, fn) {},
      });
      this.global = this.window;
    }

    getGlobalVariableNameList(): string[] {
      return [
        'console',
        'Image',
        'navigator',
        'document',
        'global',
        'top',
        'self',
        'window',
        'location',
        'localStorage',
        'sessionStorage',
        'screen',
        'fetch',
        '__wh_data__',
        'require',
        '__ctx__',
        // '__ctx__',  // 正常不应该注入，但以防万一，先留个口子

        'setTimeout',
        'clearTimeout',
        'setInterval',
        'clearInterval',
        'setImmediate',
        'CustomEvent',
        'WebSocket', // 先注入，后续再看是否合适
        'requestAnimationFrame',
      ];
    }

    getGlobalVariableList(): any[] {
      return [
        this.console,
        this.Image,
        this.navigator,
        this.document,
        this.global,
        this.global,
        this.global,
        this.window,
        this.location,
        this.localStorage,
        this.sessionStorage,
        this.screen,
        this.fetch,
        this.getWhData(),
        require, // 提供node内建模块
        options.ctx,
        // this.getCtx(),

        noopWithWarn('setTimeout'),
        noop,
        noop,
        noop,
        noop,
        noop,
        noop,
        noop,
      ];
    }

    formatWhPageData(data) {
      let result = data;
      if (isObject(data)) {
        result = {};
        if (typeof data.__data !== 'undefined') {
          /** 表示此层级为声明类型，真实数据在__data中 */
          if (isObject(data.__data) && data.__data.__propsNodeId) {
            // eslint-disable-next-line
            delete data.__data.__propsNodeId;
          }
          return data.__data;
        }
        for (const key in data) {
          // eslint-disable-next-line
          if (data.hasOwnProperty(key) && blackKeys.indexOf(key) === -1) {
            result[key] = this.formatWhPageData(data[key]);
          }
        }
      } else if (Array.isArray(data)) {
        /** a、这个逻辑要关注下页面配置未来是否有变更，测试来看 */
        result = this.formatWhPageData(data[0]) || [];
      } else {
        result = data;
      }
      return result;
    }

    getWhData() {
      const {
        page,
        packId,
        // whHistoryId,
        mock = undefined,
        // pageInfo
      } = this.__wormhole__?.data || {};
      /** TODO 此处需要过滤非必要字段 */
      if (page || packId || mock) {
        return {
          page: this.formatWhPageData(page),
          packId,
          mock,
          /** whHistoryId不应该暴露到渲染过程中，以防到时前端同时传该字段 */
          // whHistoryId,
        };
      }
      return undefined;
    }
  }
  return CustomBrowserEnv as any;
}

// location: Location;
// document: Document;
// navigator: Navigator;
// localStorage: Storage;
// sessionStorage: Storage;
// screen: Screen;
// window: Window;
