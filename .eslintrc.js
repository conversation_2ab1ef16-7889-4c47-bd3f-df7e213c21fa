module.exports = {
  extends: [
    require.resolve('eslint-config-ali/typescript/react'),
    // For some ci and jest test env, we chose require.resolve instead 'plugin:@iceworks/best-practices/react-ts'
    require.resolve('@iceworks/eslint-plugin-best-practices/src/configs/react-ts'),
    // 解决eslint和prettier冲突问题
    // 'plugin:prettier/recommended'
  ],
  rules: {
    'no-useless-escape': 'off',
    'no-console': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@iceworks/best-practices/no-js-in-ts-project': 'off',
    'react/no-danger': 'off',
    "react/jsx-indent": "off",
    '@typescript-eslint/restrict-plus-operands': 'off',
    '@iceworks/best-practices/recommend-functional-component': 'off',
    'max-len': 'off',
    "@typescript-eslint/ban-ts-comment": 'off',
    "no-bitwise": "off",
    "no-mixed-operators": "off",
    "@typescript-eslint/no-require-imports": "off",
    "comma-dangle": "off",
    "object-curly-spacing": "off",
    "no-trailing-spaces": "off",
    "jsx-closing-tag-location": "off",
    "prefer-destructuring": "off",
    "arrow-parens": "off",
    "@typescript-eslint/semi": "off",
    "@typescript-eslint/quotes": "off",
    "react/jsx-first-prop-new-line": "off",
    "react/jsx-indent-props": "off",
    'react/jsx-max-props-per-line': 'off',
    'no-nested-ternary': 'off',
    'object-shorthand': 'off',
    'prefer-const': 'off',
    'max-lines': 'off',
    "react/forbid-elements": [
      "warn",
      {
        "forbid": [{
          "element": "img",
          "message": "首屏&SSR片段不支持,其他场景推荐使用react-lazy-load-image-component(https://anpm.alibaba-inc.com/package/react-lazy-load-image-component)图片懒加载模式"
        }]
      }
    ]
  }
}
